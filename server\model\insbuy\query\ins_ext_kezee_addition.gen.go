// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtKezeeAddition(db *gorm.DB, opts ...gen.DOOption) insExtKezeeAddition {
	_insExtKezeeAddition := insExtKezeeAddition{}

	_insExtKezeeAddition.insExtKezeeAdditionDo.UseDB(db, opts...)
	_insExtKezeeAddition.insExtKezeeAdditionDo.UseModel(&insbuy.InsExtKezeeAddition{})

	tableName := _insExtKezeeAddition.insExtKezeeAdditionDo.TableName()
	_insExtKezeeAddition.ALL = field.NewAsterisk(tableName)
	_insExtKezeeAddition.Id = field.NewUint(tableName, "id")
	_insExtKezeeAddition.AppId = field.NewString(tableName, "app_id")
	_insExtKezeeAddition.Type = field.NewString(tableName, "type")
	_insExtKezeeAddition.ItemId = field.NewString(tableName, "item_id")
	_insExtKezeeAddition.ItemName = field.NewString(tableName, "item_name")
	_insExtKezeeAddition.Ext = field.NewField(tableName, "ext")
	_insExtKezeeAddition.CreatedAt = field.NewTime(tableName, "created_at")

	_insExtKezeeAddition.fillFieldMap()

	return _insExtKezeeAddition
}

type insExtKezeeAddition struct {
	insExtKezeeAdditionDo

	ALL       field.Asterisk
	Id        field.Uint
	AppId     field.String
	Type      field.String
	ItemId    field.String
	ItemName  field.String
	Ext       field.Field
	CreatedAt field.Time

	fieldMap map[string]field.Expr
}

func (i insExtKezeeAddition) Table(newTableName string) *insExtKezeeAddition {
	i.insExtKezeeAdditionDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtKezeeAddition) As(alias string) *insExtKezeeAddition {
	i.insExtKezeeAdditionDo.DO = *(i.insExtKezeeAdditionDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtKezeeAddition) updateTableName(table string) *insExtKezeeAddition {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.AppId = field.NewString(table, "app_id")
	i.Type = field.NewString(table, "type")
	i.ItemId = field.NewString(table, "item_id")
	i.ItemName = field.NewString(table, "item_name")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")

	i.fillFieldMap()

	return i
}

func (i *insExtKezeeAddition) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtKezeeAddition) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.Id
	i.fieldMap["app_id"] = i.AppId
	i.fieldMap["type"] = i.Type
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["item_name"] = i.ItemName
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
}

func (i insExtKezeeAddition) clone(db *gorm.DB) insExtKezeeAddition {
	i.insExtKezeeAdditionDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtKezeeAddition) replaceDB(db *gorm.DB) insExtKezeeAddition {
	i.insExtKezeeAdditionDo.ReplaceDB(db)
	return i
}

type insExtKezeeAdditionDo struct{ gen.DO }

func (i insExtKezeeAdditionDo) Debug() *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtKezeeAdditionDo) WithContext(ctx context.Context) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtKezeeAdditionDo) ReadDB() *insExtKezeeAdditionDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtKezeeAdditionDo) WriteDB() *insExtKezeeAdditionDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtKezeeAdditionDo) Session(config *gorm.Session) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtKezeeAdditionDo) Clauses(conds ...clause.Expression) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtKezeeAdditionDo) Returning(value interface{}, columns ...string) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtKezeeAdditionDo) Not(conds ...gen.Condition) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtKezeeAdditionDo) Or(conds ...gen.Condition) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtKezeeAdditionDo) Select(conds ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtKezeeAdditionDo) Where(conds ...gen.Condition) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtKezeeAdditionDo) Order(conds ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtKezeeAdditionDo) Distinct(cols ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtKezeeAdditionDo) Omit(cols ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtKezeeAdditionDo) Join(table schema.Tabler, on ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtKezeeAdditionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtKezeeAdditionDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtKezeeAdditionDo) Group(cols ...field.Expr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtKezeeAdditionDo) Having(conds ...gen.Condition) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtKezeeAdditionDo) Limit(limit int) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtKezeeAdditionDo) Offset(offset int) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtKezeeAdditionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtKezeeAdditionDo) Unscoped() *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtKezeeAdditionDo) Create(values ...*insbuy.InsExtKezeeAddition) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtKezeeAdditionDo) CreateInBatches(values []*insbuy.InsExtKezeeAddition, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtKezeeAdditionDo) Save(values ...*insbuy.InsExtKezeeAddition) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtKezeeAdditionDo) First() (*insbuy.InsExtKezeeAddition, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeAddition), nil
	}
}

func (i insExtKezeeAdditionDo) Take() (*insbuy.InsExtKezeeAddition, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeAddition), nil
	}
}

func (i insExtKezeeAdditionDo) Last() (*insbuy.InsExtKezeeAddition, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeAddition), nil
	}
}

func (i insExtKezeeAdditionDo) Find() ([]*insbuy.InsExtKezeeAddition, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtKezeeAddition), err
}

func (i insExtKezeeAdditionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtKezeeAddition, err error) {
	buf := make([]*insbuy.InsExtKezeeAddition, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtKezeeAdditionDo) FindInBatches(result *[]*insbuy.InsExtKezeeAddition, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtKezeeAdditionDo) Attrs(attrs ...field.AssignExpr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtKezeeAdditionDo) Assign(attrs ...field.AssignExpr) *insExtKezeeAdditionDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtKezeeAdditionDo) Joins(fields ...field.RelationField) *insExtKezeeAdditionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtKezeeAdditionDo) Preload(fields ...field.RelationField) *insExtKezeeAdditionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtKezeeAdditionDo) FirstOrInit() (*insbuy.InsExtKezeeAddition, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeAddition), nil
	}
}

func (i insExtKezeeAdditionDo) FirstOrCreate() (*insbuy.InsExtKezeeAddition, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeAddition), nil
	}
}

func (i insExtKezeeAdditionDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtKezeeAddition, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtKezeeAdditionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtKezeeAdditionDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtKezeeAdditionDo) Delete(models ...*insbuy.InsExtKezeeAddition) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtKezeeAdditionDo) withDO(do gen.Dao) *insExtKezeeAdditionDo {
	i.DO = *do.(*gen.DO)
	return i
}
