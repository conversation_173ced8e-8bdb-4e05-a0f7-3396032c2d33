// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTrade(db *gorm.DB, opts ...gen.DOOption) insTrade {
	_insTrade := insTrade{}

	_insTrade.insTradeDo.UseDB(db, opts...)
	_insTrade.insTradeDo.UseModel(&insbuy.InsTrade{})

	tableName := _insTrade.insTradeDo.TableName()
	_insTrade.ALL = field.NewAsterisk(tableName)
	_insTrade.Id = field.NewUint64(tableName, "id")
	_insTrade.BusinessId = field.NewUint(tableName, "business_id")
	_insTrade.CreatedAt = field.NewTime(tableName, "created_at")
	_insTrade.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTrade.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTrade.StoreId = field.NewUint(tableName, "store_id")
	_insTrade.PaidAmount = field.NewFloat64(tableName, "paid_amount")
	_insTrade.TradeType = field.NewInt(tableName, "trade_type")
	_insTrade.ShallAmount = field.NewFloat64(tableName, "shall_amount")
	_insTrade.DiscountAmount = field.NewFloat64(tableName, "discount_amount")
	_insTrade.RealAmount = field.NewFloat64(tableName, "real_amount")
	_insTrade.PlayerAmount = field.NewFloat64(tableName, "player_amount")
	_insTrade.CouponAmount = field.NewFloat64(tableName, "coupon_amount")
	_insTrade.ServiceFee = field.NewFloat64(tableName, "service_fee")
	_insTrade.ErasePrice = field.NewFloat64(tableName, "erase_price")
	_insTrade.TradeStatus = field.NewInt(tableName, "trade_status")
	_insTrade.BusinessDay = field.NewTime(tableName, "business_day")

	_insTrade.fillFieldMap()

	return _insTrade
}

type insTrade struct {
	insTradeDo

	ALL            field.Asterisk
	Id             field.Uint64
	BusinessId     field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	StoreId        field.Uint
	PaidAmount     field.Float64
	TradeType      field.Int
	ShallAmount    field.Float64
	DiscountAmount field.Float64
	RealAmount     field.Float64
	PlayerAmount   field.Float64
	CouponAmount   field.Float64
	ServiceFee     field.Float64
	ErasePrice     field.Float64
	TradeStatus    field.Int
	BusinessDay    field.Time

	fieldMap map[string]field.Expr
}

func (i insTrade) Table(newTableName string) *insTrade {
	i.insTradeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTrade) As(alias string) *insTrade {
	i.insTradeDo.DO = *(i.insTradeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTrade) updateTableName(table string) *insTrade {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint64(table, "id")
	i.BusinessId = field.NewUint(table, "business_id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.PaidAmount = field.NewFloat64(table, "paid_amount")
	i.TradeType = field.NewInt(table, "trade_type")
	i.ShallAmount = field.NewFloat64(table, "shall_amount")
	i.DiscountAmount = field.NewFloat64(table, "discount_amount")
	i.RealAmount = field.NewFloat64(table, "real_amount")
	i.PlayerAmount = field.NewFloat64(table, "player_amount")
	i.CouponAmount = field.NewFloat64(table, "coupon_amount")
	i.ServiceFee = field.NewFloat64(table, "service_fee")
	i.ErasePrice = field.NewFloat64(table, "erase_price")
	i.TradeStatus = field.NewInt(table, "trade_status")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insTrade) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTrade) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.Id
	i.fieldMap["business_id"] = i.BusinessId
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["paid_amount"] = i.PaidAmount
	i.fieldMap["trade_type"] = i.TradeType
	i.fieldMap["shall_amount"] = i.ShallAmount
	i.fieldMap["discount_amount"] = i.DiscountAmount
	i.fieldMap["real_amount"] = i.RealAmount
	i.fieldMap["player_amount"] = i.PlayerAmount
	i.fieldMap["coupon_amount"] = i.CouponAmount
	i.fieldMap["service_fee"] = i.ServiceFee
	i.fieldMap["erase_price"] = i.ErasePrice
	i.fieldMap["trade_status"] = i.TradeStatus
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insTrade) clone(db *gorm.DB) insTrade {
	i.insTradeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTrade) replaceDB(db *gorm.DB) insTrade {
	i.insTradeDo.ReplaceDB(db)
	return i
}

type insTradeDo struct{ gen.DO }

func (i insTradeDo) Debug() *insTradeDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeDo) WithContext(ctx context.Context) *insTradeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeDo) ReadDB() *insTradeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeDo) WriteDB() *insTradeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeDo) Session(config *gorm.Session) *insTradeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeDo) Clauses(conds ...clause.Expression) *insTradeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeDo) Returning(value interface{}, columns ...string) *insTradeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeDo) Not(conds ...gen.Condition) *insTradeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeDo) Or(conds ...gen.Condition) *insTradeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeDo) Select(conds ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeDo) Where(conds ...gen.Condition) *insTradeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeDo) Order(conds ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeDo) Distinct(cols ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeDo) Omit(cols ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeDo) Join(table schema.Tabler, on ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeDo) Group(cols ...field.Expr) *insTradeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeDo) Having(conds ...gen.Condition) *insTradeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeDo) Limit(limit int) *insTradeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeDo) Offset(offset int) *insTradeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeDo) Unscoped() *insTradeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeDo) Create(values ...*insbuy.InsTrade) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeDo) CreateInBatches(values []*insbuy.InsTrade, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeDo) Save(values ...*insbuy.InsTrade) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeDo) First() (*insbuy.InsTrade, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTrade), nil
	}
}

func (i insTradeDo) Take() (*insbuy.InsTrade, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTrade), nil
	}
}

func (i insTradeDo) Last() (*insbuy.InsTrade, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTrade), nil
	}
}

func (i insTradeDo) Find() ([]*insbuy.InsTrade, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTrade), err
}

func (i insTradeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTrade, err error) {
	buf := make([]*insbuy.InsTrade, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeDo) FindInBatches(result *[]*insbuy.InsTrade, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeDo) Attrs(attrs ...field.AssignExpr) *insTradeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeDo) Assign(attrs ...field.AssignExpr) *insTradeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeDo) Joins(fields ...field.RelationField) *insTradeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeDo) Preload(fields ...field.RelationField) *insTradeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeDo) FirstOrInit() (*insbuy.InsTrade, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTrade), nil
	}
}

func (i insTradeDo) FirstOrCreate() (*insbuy.InsTrade, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTrade), nil
	}
}

func (i insTradeDo) FindByPage(offset int, limit int) (result []*insbuy.InsTrade, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeDo) Delete(models ...*insbuy.InsTrade) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeDo) withDO(do gen.Dao) *insTradeDo {
	i.DO = *do.(*gen.DO)
	return i
}
