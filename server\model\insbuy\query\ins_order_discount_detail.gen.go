// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderDiscountDetail(db *gorm.DB, opts ...gen.DOOption) insOrderDiscountDetail {
	_insOrderDiscountDetail := insOrderDiscountDetail{}

	_insOrderDiscountDetail.insOrderDiscountDetailDo.UseDB(db, opts...)
	_insOrderDiscountDetail.insOrderDiscountDetailDo.UseModel(&insbuy.InsOrderDiscountDetail{})

	tableName := _insOrderDiscountDetail.insOrderDiscountDetailDo.TableName()
	_insOrderDiscountDetail.ALL = field.NewAsterisk(tableName)
	_insOrderDiscountDetail.ID = field.NewUint(tableName, "id")
	_insOrderDiscountDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderDiscountDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderDiscountDetail.DeletedAt = field.NewField(tableName, "deleted_at")
	_insOrderDiscountDetail.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderDiscountDetail.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insOrderDiscountDetail.TradeId = field.NewUint64(tableName, "trade_id")
	_insOrderDiscountDetail.DiscountSourceId = field.NewUint64(tableName, "discount_source_id")
	_insOrderDiscountDetail.DiscountType = field.NewInt(tableName, "discount_type")
	_insOrderDiscountDetail.DiscountPrice = field.NewFloat64(tableName, "discount_price")
	_insOrderDiscountDetail.RealPrice = field.NewFloat64(tableName, "real_price")
	_insOrderDiscountDetail.DiscountFee = field.NewFloat64(tableName, "discount_fee")
	_insOrderDiscountDetail.CouponFee = field.NewFloat64(tableName, "coupon_fee")
	_insOrderDiscountDetail.ServiceFee = field.NewFloat64(tableName, "service_fee")
	_insOrderDiscountDetail.ErasePrice = field.NewFloat64(tableName, "erase_price")
	_insOrderDiscountDetail.PlayerFee = field.NewFloat64(tableName, "player_fee")
	_insOrderDiscountDetail.PlayerTotalPrice = field.NewFloat64(tableName, "player_total_price")
	_insOrderDiscountDetail.DiscountParams = field.NewField(tableName, "discount_params")
	_insOrderDiscountDetail.DiscountResult = field.NewField(tableName, "discount_result")

	_insOrderDiscountDetail.fillFieldMap()

	return _insOrderDiscountDetail
}

type insOrderDiscountDetail struct {
	insOrderDiscountDetailDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	OrderId          field.Uint64
	OrderDetailsId   field.Uint64
	TradeId          field.Uint64
	DiscountSourceId field.Uint64
	DiscountType     field.Int
	DiscountPrice    field.Float64
	RealPrice        field.Float64
	DiscountFee      field.Float64
	CouponFee        field.Float64
	ServiceFee       field.Float64
	ErasePrice       field.Float64
	PlayerFee        field.Float64
	PlayerTotalPrice field.Float64
	DiscountParams   field.Field
	DiscountResult   field.Field

	fieldMap map[string]field.Expr
}

func (i insOrderDiscountDetail) Table(newTableName string) *insOrderDiscountDetail {
	i.insOrderDiscountDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderDiscountDetail) As(alias string) *insOrderDiscountDetail {
	i.insOrderDiscountDetailDo.DO = *(i.insOrderDiscountDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderDiscountDetail) updateTableName(table string) *insOrderDiscountDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.DiscountSourceId = field.NewUint64(table, "discount_source_id")
	i.DiscountType = field.NewInt(table, "discount_type")
	i.DiscountPrice = field.NewFloat64(table, "discount_price")
	i.RealPrice = field.NewFloat64(table, "real_price")
	i.DiscountFee = field.NewFloat64(table, "discount_fee")
	i.CouponFee = field.NewFloat64(table, "coupon_fee")
	i.ServiceFee = field.NewFloat64(table, "service_fee")
	i.ErasePrice = field.NewFloat64(table, "erase_price")
	i.PlayerFee = field.NewFloat64(table, "player_fee")
	i.PlayerTotalPrice = field.NewFloat64(table, "player_total_price")
	i.DiscountParams = field.NewField(table, "discount_params")
	i.DiscountResult = field.NewField(table, "discount_result")

	i.fillFieldMap()

	return i
}

func (i *insOrderDiscountDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderDiscountDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 19)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["discount_source_id"] = i.DiscountSourceId
	i.fieldMap["discount_type"] = i.DiscountType
	i.fieldMap["discount_price"] = i.DiscountPrice
	i.fieldMap["real_price"] = i.RealPrice
	i.fieldMap["discount_fee"] = i.DiscountFee
	i.fieldMap["coupon_fee"] = i.CouponFee
	i.fieldMap["service_fee"] = i.ServiceFee
	i.fieldMap["erase_price"] = i.ErasePrice
	i.fieldMap["player_fee"] = i.PlayerFee
	i.fieldMap["player_total_price"] = i.PlayerTotalPrice
	i.fieldMap["discount_params"] = i.DiscountParams
	i.fieldMap["discount_result"] = i.DiscountResult
}

func (i insOrderDiscountDetail) clone(db *gorm.DB) insOrderDiscountDetail {
	i.insOrderDiscountDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderDiscountDetail) replaceDB(db *gorm.DB) insOrderDiscountDetail {
	i.insOrderDiscountDetailDo.ReplaceDB(db)
	return i
}

type insOrderDiscountDetailDo struct{ gen.DO }

func (i insOrderDiscountDetailDo) Debug() *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderDiscountDetailDo) WithContext(ctx context.Context) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderDiscountDetailDo) ReadDB() *insOrderDiscountDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderDiscountDetailDo) WriteDB() *insOrderDiscountDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderDiscountDetailDo) Session(config *gorm.Session) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderDiscountDetailDo) Clauses(conds ...clause.Expression) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderDiscountDetailDo) Returning(value interface{}, columns ...string) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderDiscountDetailDo) Not(conds ...gen.Condition) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderDiscountDetailDo) Or(conds ...gen.Condition) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderDiscountDetailDo) Select(conds ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderDiscountDetailDo) Where(conds ...gen.Condition) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderDiscountDetailDo) Order(conds ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderDiscountDetailDo) Distinct(cols ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderDiscountDetailDo) Omit(cols ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderDiscountDetailDo) Join(table schema.Tabler, on ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderDiscountDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderDiscountDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderDiscountDetailDo) Group(cols ...field.Expr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderDiscountDetailDo) Having(conds ...gen.Condition) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderDiscountDetailDo) Limit(limit int) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderDiscountDetailDo) Offset(offset int) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderDiscountDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderDiscountDetailDo) Unscoped() *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderDiscountDetailDo) Create(values ...*insbuy.InsOrderDiscountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderDiscountDetailDo) CreateInBatches(values []*insbuy.InsOrderDiscountDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderDiscountDetailDo) Save(values ...*insbuy.InsOrderDiscountDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderDiscountDetailDo) First() (*insbuy.InsOrderDiscountDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderDiscountDetail), nil
	}
}

func (i insOrderDiscountDetailDo) Take() (*insbuy.InsOrderDiscountDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderDiscountDetail), nil
	}
}

func (i insOrderDiscountDetailDo) Last() (*insbuy.InsOrderDiscountDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderDiscountDetail), nil
	}
}

func (i insOrderDiscountDetailDo) Find() ([]*insbuy.InsOrderDiscountDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderDiscountDetail), err
}

func (i insOrderDiscountDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderDiscountDetail, err error) {
	buf := make([]*insbuy.InsOrderDiscountDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderDiscountDetailDo) FindInBatches(result *[]*insbuy.InsOrderDiscountDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderDiscountDetailDo) Attrs(attrs ...field.AssignExpr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderDiscountDetailDo) Assign(attrs ...field.AssignExpr) *insOrderDiscountDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderDiscountDetailDo) Joins(fields ...field.RelationField) *insOrderDiscountDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderDiscountDetailDo) Preload(fields ...field.RelationField) *insOrderDiscountDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderDiscountDetailDo) FirstOrInit() (*insbuy.InsOrderDiscountDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderDiscountDetail), nil
	}
}

func (i insOrderDiscountDetailDo) FirstOrCreate() (*insbuy.InsOrderDiscountDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderDiscountDetail), nil
	}
}

func (i insOrderDiscountDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderDiscountDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderDiscountDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderDiscountDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderDiscountDetailDo) Delete(models ...*insbuy.InsOrderDiscountDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderDiscountDetailDo) withDO(do gen.Dao) *insOrderDiscountDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
