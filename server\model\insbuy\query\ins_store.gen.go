// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStore(db *gorm.DB, opts ...gen.DOOption) insStore {
	_insStore := insStore{}

	_insStore.insStoreDo.UseDB(db, opts...)
	_insStore.insStoreDo.UseModel(&insbuy.InsStore{})

	tableName := _insStore.insStoreDo.TableName()
	_insStore.ALL = field.NewAsterisk(tableName)
	_insStore.ID = field.NewUint(tableName, "id")
	_insStore.Name = field.NewString(tableName, "name")
	_insStore.Manager = field.NewString(tableName, "manager")
	_insStore.Code = field.NewString(tableName, "code")
	_insStore.OfficePhone = field.NewString(tableName, "office_phone")
	_insStore.Address = field.NewString(tableName, "address")
	_insStore.CompanyName = field.NewString(tableName, "company_name")
	_insStore.Logo = field.NewString(tableName, "logo")
	_insStore.Latitude = field.NewFloat64(tableName, "latitude")
	_insStore.Longitude = field.NewFloat64(tableName, "longitude")
	_insStore.StoreDesc = field.NewString(tableName, "store_desc")
	_insStore.BusinessHours = field.NewString(tableName, "business_hours")
	_insStore.FirstOpenDate = field.NewTime(tableName, "first_open_date")
	_insStore.SysInitDate = field.NewTime(tableName, "sys_init_date")
	_insStore.InsCode = field.NewString(tableName, "ins_code")
	_insStore.Ext = field.NewField(tableName, "ext")
	_insStore.CreatedAt = field.NewTime(tableName, "created_at")
	_insStore.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStore.DeletedAt = field.NewField(tableName, "deleted_at")

	_insStore.fillFieldMap()

	return _insStore
}

type insStore struct {
	insStoreDo

	ALL           field.Asterisk
	ID            field.Uint
	Name          field.String
	Manager       field.String
	Code          field.String
	OfficePhone   field.String
	Address       field.String
	CompanyName   field.String
	Logo          field.String
	Latitude      field.Float64
	Longitude     field.Float64
	StoreDesc     field.String
	BusinessHours field.String
	FirstOpenDate field.Time
	SysInitDate   field.Time
	InsCode       field.String
	Ext           field.Field
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field

	fieldMap map[string]field.Expr
}

func (i insStore) Table(newTableName string) *insStore {
	i.insStoreDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStore) As(alias string) *insStore {
	i.insStoreDo.DO = *(i.insStoreDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStore) updateTableName(table string) *insStore {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.Name = field.NewString(table, "name")
	i.Manager = field.NewString(table, "manager")
	i.Code = field.NewString(table, "code")
	i.OfficePhone = field.NewString(table, "office_phone")
	i.Address = field.NewString(table, "address")
	i.CompanyName = field.NewString(table, "company_name")
	i.Logo = field.NewString(table, "logo")
	i.Latitude = field.NewFloat64(table, "latitude")
	i.Longitude = field.NewFloat64(table, "longitude")
	i.StoreDesc = field.NewString(table, "store_desc")
	i.BusinessHours = field.NewString(table, "business_hours")
	i.FirstOpenDate = field.NewTime(table, "first_open_date")
	i.SysInitDate = field.NewTime(table, "sys_init_date")
	i.InsCode = field.NewString(table, "ins_code")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")

	i.fillFieldMap()

	return i
}

func (i *insStore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStore) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 19)
	i.fieldMap["id"] = i.ID
	i.fieldMap["name"] = i.Name
	i.fieldMap["manager"] = i.Manager
	i.fieldMap["code"] = i.Code
	i.fieldMap["office_phone"] = i.OfficePhone
	i.fieldMap["address"] = i.Address
	i.fieldMap["company_name"] = i.CompanyName
	i.fieldMap["logo"] = i.Logo
	i.fieldMap["latitude"] = i.Latitude
	i.fieldMap["longitude"] = i.Longitude
	i.fieldMap["store_desc"] = i.StoreDesc
	i.fieldMap["business_hours"] = i.BusinessHours
	i.fieldMap["first_open_date"] = i.FirstOpenDate
	i.fieldMap["sys_init_date"] = i.SysInitDate
	i.fieldMap["ins_code"] = i.InsCode
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
}

func (i insStore) clone(db *gorm.DB) insStore {
	i.insStoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStore) replaceDB(db *gorm.DB) insStore {
	i.insStoreDo.ReplaceDB(db)
	return i
}

type insStoreDo struct{ gen.DO }

func (i insStoreDo) Debug() *insStoreDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreDo) WithContext(ctx context.Context) *insStoreDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreDo) ReadDB() *insStoreDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreDo) WriteDB() *insStoreDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreDo) Session(config *gorm.Session) *insStoreDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreDo) Clauses(conds ...clause.Expression) *insStoreDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreDo) Returning(value interface{}, columns ...string) *insStoreDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreDo) Not(conds ...gen.Condition) *insStoreDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreDo) Or(conds ...gen.Condition) *insStoreDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreDo) Select(conds ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreDo) Where(conds ...gen.Condition) *insStoreDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreDo) Order(conds ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreDo) Distinct(cols ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreDo) Omit(cols ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreDo) Join(table schema.Tabler, on ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreDo) Group(cols ...field.Expr) *insStoreDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreDo) Having(conds ...gen.Condition) *insStoreDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreDo) Limit(limit int) *insStoreDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreDo) Offset(offset int) *insStoreDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreDo) Unscoped() *insStoreDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreDo) Create(values ...*insbuy.InsStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreDo) CreateInBatches(values []*insbuy.InsStore, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreDo) Save(values ...*insbuy.InsStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreDo) First() (*insbuy.InsStore, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStore), nil
	}
}

func (i insStoreDo) Take() (*insbuy.InsStore, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStore), nil
	}
}

func (i insStoreDo) Last() (*insbuy.InsStore, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStore), nil
	}
}

func (i insStoreDo) Find() ([]*insbuy.InsStore, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStore), err
}

func (i insStoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStore, err error) {
	buf := make([]*insbuy.InsStore, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreDo) FindInBatches(result *[]*insbuy.InsStore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreDo) Attrs(attrs ...field.AssignExpr) *insStoreDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreDo) Assign(attrs ...field.AssignExpr) *insStoreDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreDo) Joins(fields ...field.RelationField) *insStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreDo) Preload(fields ...field.RelationField) *insStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreDo) FirstOrInit() (*insbuy.InsStore, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStore), nil
	}
}

func (i insStoreDo) FirstOrCreate() (*insbuy.InsStore, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStore), nil
	}
}

func (i insStoreDo) FindByPage(offset int, limit int) (result []*insbuy.InsStore, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreDo) Delete(models ...*insbuy.InsStore) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreDo) withDO(do gen.Dao) *insStoreDo {
	i.DO = *do.(*gen.DO)
	return i
}
