package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/apikit"
	apiExport4Partner "github.com/flipped-aurora/gin-vue-admin/server/pkg/export4partner/api"
	pluginService "github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
)

type ApiGroup struct {
	InsStoreApi
	InsSupplierApi
	VipMember InsVipMemberApi
	InsDeskApi
	InsBookApi
	InsBookInApi
	InsWarehouseApi
	InsWarehouseApplyApi
	InsWarehousePurchaseApi
	InsWarehouseImportLogServiceApi
	InsWarehouseInApi
	InsWarehouseOutApi
	InsWarehouseCheckApi
	InsWarehouseEmptiesApi
	Deposit InsDepositApi // 存取酒
	InsSalerApi
	InsPayApi
	InsProductApi
	InsOrderInfoApi
	InsPaymentApi
	InsGiftRulesApi
	InsRechargeRuleApi
	InsSysKitApi
	InsSysPrintApi
	InsMaterialApi
	InsCostCardApi
	InsExcelApi
	HyReportApi
	InsSysDevPrinterApi
	InsAuditApi
	InsActivityApi
	InsSalerStoreApi
	InsSalerCodeApi
	InsGiftQuotaAssignUserApi
	InsStoreTerminalApi
	ExtPay InsExternalPayApi
	Report InsReportApi
	InsQueueApi
	InsNotesApi
	InsCouponApi
	InsEventsLogApi
	InsServiceFeeApi
	InsUserRegisterApi
	InsWechatApi
	InsSysEventApi
	InsConfigApi

	Export4Partner apiExport4Partner.API
}

var (
	insBookService      = service.ServiceGroupApp.InsBuyServiceGroup.InsBookService
	insStoreService     = service.ServiceGroupApp.InsBuyServiceGroup.InsStoreService
	insSupplierService  = service.ServiceGroupApp.InsBuyServiceGroup.InsSupplierService
	insVipMemberService = service.ServiceGroupApp.InsBuyServiceGroup.InsVipMemberService
	insDeskService      = service.ServiceGroupApp.InsBuyServiceGroup.InsDeskService
	insBookInService    = service.ServiceGroupApp.InsBuyServiceGroup.InsBookInService
	insSalerService     = service.ServiceGroupApp.InsBuyServiceGroup.InsSalerService
	// 库存管理
	insWarehouseService          = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseService
	insWarehouseImportLogService = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseImportLogService
	insWarehouseApplyService     = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseApplyService
	insWarehousePurchaseService  = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehousePurchaseService
	insWarehouseInService        = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseInService
	insWarehouseOutService       = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseOutService
	insWarehouseEmptiesService   = service.ServiceGroupApp.InsBuyServiceGroup.InsWarehouseEmptiesService
	//存取酒
	insDepositService = service.ServiceGroupApp.InsBuyServiceGroup.InsDepositService
	//小程序支付接口
	insMicroService = service.ServiceGroupApp.InsBuyServiceGroup.InsMicroService
	//商品管理
	insProductService = service.ServiceGroupApp.InsBuyServiceGroup.InsProductService
	//订单管理
	insOrderInfoService = service.ServiceGroupApp.InsBuyServiceGroup.InsOrderInfoService
	//支付管理
	insPaymentService = service.ServiceGroupApp.InsBuyServiceGroup.InsPaymentService
	// 赠送规则
	insGiftRulesService = service.ServiceGroupApp.InsBuyServiceGroup.InsGiftRulesService
	// 充值规则
	insRechargeRuleService = service.ServiceGroupApp.InsBuyServiceGroup.InsRechargeRuleService
	// 打印相关
	insSysPrintService = &service.ServiceGroupApp.InsBuyServiceGroup.InsSysService.Print
	// 原料管理
	insMaterialService = service.ServiceGroupApp.InsBuyServiceGroup.InsMaterialService
	// 成本卡
	insCostCardService = service.ServiceGroupApp.InsBuyServiceGroup.InsCostCardService
	//excel服务
	insImportService = service.ServiceGroupApp.InsBuyServiceGroup.InsImportService

	hyReportService = &service.ServiceGroupApp.InsBuyServiceGroup.HyReport

	// 系统-设备-打印机
	insSysDevPrinterService = service.ServiceGroupApp.InsBuyServiceGroup.InsSysDevPrinterService

	extPayService = &service.ServiceGroupApp.InsBuyServiceGroup.ExtPay

	insAuditService = service.ServiceGroupApp.InsBuyServiceGroup.InsAuditService

	reportService = service.ServiceGroupApp.InsBuyServiceGroup.Report

	queueService = service.ServiceGroupApp.InsBuyServiceGroup.InsQueue

	currentUserService = service.ServiceGroupApp.InsBuyServiceGroup.InsCurrentUserService

	insActivityService = service.ServiceGroupApp.InsBuyServiceGroup.InsActivityService // 活动服务

	insNotesService = service.ServiceGroupApp.InsBuyServiceGroup.InsNotesService // 备注服务

	insCouponService = service.ServiceGroupApp.InsBuyServiceGroup.InsCouponService // 优惠券服务

	insEventsLogService  = service.ServiceGroupApp.InsBuyServiceGroup.EventsLog            // 事件日志服务
	insServiceFeeService = service.ServiceGroupApp.InsBuyServiceGroup.InsServiceFeeService // 事件日志服务

	insUserRegisterService = service.ServiceGroupApp.InsBuyServiceGroup.InsUserRegisterService
	insWechatService       = service.ServiceGroupApp.InsBuyServiceGroup.Wechat
	eventTrackingService   = service.ServiceGroupApp.InsBuyServiceGroup.EventTracking
	insConfigService       = service.ServiceGroupApp.InsBuyServiceGroup.InsConfigService
)

// 外部模块
var (
	authorityService = service.ServiceGroupApp.SystemServiceGroup.AuthorityService
	menuService      = service.ServiceGroupApp.SystemServiceGroup.MenuService
)

// 插件模块
var (
	organizationService = pluginService.ServiceGroupApp.OrganizationService
)

var (
	GinMustBind  = apikit.GinMustBind
	GinBindAfter = apikit.GinBindAfter
)
