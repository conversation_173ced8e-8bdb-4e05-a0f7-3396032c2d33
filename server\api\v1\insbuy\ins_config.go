package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsConfigApi struct{}

// SaveConfig 保存配置
// @Tags InsConfig
// @Summary 保存配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfigCenterSaveReq true "保存配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"保存成功"}"
// @Router /insConfig/saveConfig [post]
func (api *InsConfigApi) SaveConfig(c *gin.Context) {
	var req insbuyReq.ConfigCenterSaveReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insConfigService.SaveConfig(c.Request.Context(), req); err != nil {
		global.GVA_LOG.Error("保存配置失败!", zap.Error(err))
		response.FailWithMessage("保存配置失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("保存成功", c)
	}
}

// GetConfig 获取配置
// @Tags InsConfig
// @Summary 获取配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ConfigCenterQueryReq true "获取配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insConfig/getConfig [get]
func (api *InsConfigApi) GetConfig(c *gin.Context) {
	var req insbuyReq.ConfigCenterQueryReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if data, err := insConfigService.GetConfig(c.Request.Context(), req); err != nil {
		global.GVA_LOG.Error("获取配置失败!", zap.Error(err))
		response.FailWithMessage("获取配置失败: "+err.Error(), c)
	} else {
		response.OkWithData(data, c)
	}
}

// GetConfigList 获取配置列表
// @Tags InsConfig
// @Summary 获取配置列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ConfigCenterListReq true "获取配置列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insConfig/getConfigList [get]
func (api *InsConfigApi) GetConfigList(c *gin.Context) {
	var req insbuyReq.ConfigCenterListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := insConfigService.GetConfigList(c.Request.Context(), req); err != nil {
		global.GVA_LOG.Error("获取配置列表失败!", zap.Error(err))
		response.FailWithMessage("获取配置列表失败: "+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// DeleteConfig 删除配置
// @Tags InsConfig
// @Summary 删除配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfigCenterDeleteReq true "删除配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insConfig/deleteConfig [delete]
func (api *InsConfigApi) DeleteConfig(c *gin.Context) {
	var req insbuyReq.ConfigCenterDeleteReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insConfigService.DeleteConfig(c.Request.Context(), req); err != nil {
		global.GVA_LOG.Error("删除配置失败!", zap.Error(err))
		response.FailWithMessage("删除配置失败: "+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// ConfigDataList 获取配置数据列表
// @Tags InsConfig
// @Summary 获取配置数据列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ConfigDataListReq true "获取配置数据列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insConfig/configDataList [get]
func (api *InsConfigApi) ConfigDataList(c *gin.Context) {
	var req insbuyReq.ConfigDataListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insConfigService.ConfigDataList(req); err != nil {
		global.GVA_LOG.Error("获取配置数据列表失败!", zap.Error(err))
		response.FailWithMessage("获取配置数据列表失败: "+err.Error(), c)
	} else {
		response.OkWithData(res, c)
	}
	return
}
