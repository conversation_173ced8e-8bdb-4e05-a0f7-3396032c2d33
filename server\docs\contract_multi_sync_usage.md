# 飞书合同多审批代码批量同步功能使用指南

## 概述

本文档介绍了新实现的飞书合同多审批代码批量同步功能的使用方法。该功能支持同时同步多个审批代码的合同数据，提供完整的分页处理、错误处理和数据一致性保证。

## 核心特性

### 🎯 **多审批代码支持**
- 支持一次性同步多个不同类型的合同审批代码
- 每个审批代码独立处理，单个失败不影响其他代码的同步
- 支持最多20个审批代码的批量同步

### 📄 **完整分页处理**
- 自动遍历所有分页数据，确保数据完整性
- 保存每个审批代码的分页token，支持断点续传
- 智能API限流控制，避免触发飞书API限制

### 🔄 **数据一致性保证**
- 基于`InstanceCode`和`Uuid`进行去重处理
- 自动区分新增和更新操作
- 支持事务处理确保数据一致性
- 完整的关联数据同步（评论、任务、时间线、文件）

### 📊 **详细日志记录**
- 结构化日志记录，便于查询和分析
- 记录每个审批代码的同步进度和结果
- 详细的错误信息和异常处理
- 支持同步状态监控和告警

## 接口定义

### 请求结构体

```go
type ContractMultiSyncRequest struct {
    ApprovalCodes []string   `json:"approval_codes" binding:"required"` // 审批定义Code列表，必填
    StartTime     *time.Time `json:"start_time"`                        // 开始时间，默认最近30天
    EndTime       *time.Time `json:"end_time"`                          // 结束时间，默认当前时间
    BatchSize     int        `json:"batch_size"`                        // 详情获取批次大小，默认20
    PageSize      int        `json:"page_size"`                         // 列表分页大小，默认100
    ForceSync     bool       `json:"force_sync"`                        // 是否强制同步，忽略最近同步时间
    MaxRetries    int        `json:"max_retries"`                       // 最大重试次数，默认3
    RetryDelay    int        `json:"retry_delay"`                       // 重试延迟（秒），默认5
}
```

### 响应结构体

```go
type ContractMultiSyncResponse struct {
    Success        bool                       `json:"success"`         // 整体同步是否成功
    TotalCodes     int                        `json:"total_codes"`     // 总审批代码数量
    SuccessCodes   int                        `json:"success_codes"`   // 成功同步的审批代码数量
    FailedCodes    int                        `json:"failed_codes"`    // 失败的审批代码数量
    TotalRecords   int                        `json:"total_records"`   // 总记录数
    NewRecords     int                        `json:"new_records"`     // 新增记录数
    UpdatedRecords int                        `json:"updated_records"` // 更新记录数
    FailedRecords  int                        `json:"failed_records"`  // 失败记录数
    Results        []ContractSyncDetailResult `json:"results"`         // 详细同步结果
    StartTime      string                     `json:"start_time"`      // 同步开始时间
    EndTime        string                     `json:"end_time"`        // 同步结束时间
    Duration       string                     `json:"duration"`        // 同步耗时
    ErrorMsg       string                     `json:"error_msg"`       // 错误信息
}
```

## 使用方法

### 基本用法

```go
package main

import (
    "time"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
    "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
)

func syncMultipleContracts() {
    // 创建服务实例
    contractService := &insbuy.InsContractService{}
    
    // 构建同步请求
    req := request.ContractMultiSyncRequest{
        ApprovalCodes: []string{
            "F523F053-7AC6-4280-A4E7-B35E0C0431B5", // 销售合同
            "A123B456-7890-1234-5678-90ABCDEF1234", // 采购合同
            "C789D012-3456-7890-1234-56789ABCDEF0", // 服务合同
        },
        StartTime:  &time.Time{}, // 可选，默认最近30天
        EndTime:    &time.Time{}, // 可选，默认当前时间
        BatchSize:  20,           // 可选，默认20
        PageSize:   100,          // 可选，默认100
        ForceSync:  false,        // 可选，默认false
        MaxRetries: 3,            // 可选，默认3
        RetryDelay: 5,            // 可选，默认5秒
    }
    
    // 执行同步
    resp, err := contractService.SyncMultipleContractData(req)
    if err != nil {
        log.Printf("同步失败: %v", err)
        return
    }
    
    // 处理结果
    fmt.Printf("同步完成: 总共 %d 个审批代码，成功 %d 个，失败 %d 个\n", 
        resp.TotalCodes, resp.SuccessCodes, resp.FailedCodes)
    fmt.Printf("数据统计: 总记录 %d，新增 %d，更新 %d，失败 %d\n", 
        resp.TotalRecords, resp.NewRecords, resp.UpdatedRecords, resp.FailedRecords)
    fmt.Printf("耗时: %s\n", resp.Duration)
    
    // 查看详细结果
    for _, result := range resp.Results {
        fmt.Printf("审批代码 %s: 成功=%v, 记录数=%d, 页数=%d\n", 
            result.ApprovalCode, result.Success, result.TotalRecords, result.TotalPages)
        
        if !result.Success {
            fmt.Printf("  错误信息: %s\n", result.ErrorMsg)
        }
    }
}
```

### 高级用法

```go
// 自定义时间范围同步
func syncWithTimeRange() {
    contractService := &insbuy.InsContractService{}
    
    // 同步最近7天的数据
    startTime := time.Now().AddDate(0, 0, -7)
    endTime := time.Now()
    
    req := request.ContractMultiSyncRequest{
        ApprovalCodes: []string{
            "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
        },
        StartTime:  &startTime,
        EndTime:    &endTime,
        BatchSize:  50,  // 增大批次大小提高效率
        PageSize:   200, // 使用最大分页大小
        ForceSync:  true, // 强制同步，忽略上次同步时间
        MaxRetries: 5,   // 增加重试次数
        RetryDelay: 3,   // 减少重试延迟
    }
    
    resp, err := contractService.SyncMultipleContractData(req)
    // 处理结果...
}

// 错误处理和重试
func syncWithErrorHandling() {
    contractService := &insbuy.InsContractService{}
    
    req := request.ContractMultiSyncRequest{
        ApprovalCodes: []string{
            "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
            "A123B456-7890-1234-5678-90ABCDEF1234",
        },
        MaxRetries: 3,
        RetryDelay: 10,
    }
    
    maxAttempts := 3
    for attempt := 1; attempt <= maxAttempts; attempt++ {
        resp, err := contractService.SyncMultipleContractData(req)
        
        if err == nil && resp.Success {
            fmt.Println("同步成功")
            break
        }
        
        if attempt < maxAttempts {
            fmt.Printf("第 %d 次尝试失败，%d 秒后重试...\n", attempt, 30)
            time.Sleep(30 * time.Second)
        } else {
            fmt.Printf("所有尝试都失败了: %v\n", err)
        }
    }
}
```

## 参数说明

### 必填参数

- **ApprovalCodes**: 审批定义Code列表，至少包含1个，最多20个

### 可选参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| StartTime | *time.Time | 最近30天 | 同步开始时间 |
| EndTime | *time.Time | 当前时间 | 同步结束时间 |
| BatchSize | int | 20 | 详情获取批次大小（1-50） |
| PageSize | int | 100 | 列表分页大小（1-200） |
| ForceSync | bool | false | 是否强制同步 |
| MaxRetries | int | 3 | 最大重试次数（1-10） |
| RetryDelay | int | 5 | 重试延迟秒数（1-60） |

## 响应字段说明

### 整体统计

- **Success**: 整体同步是否成功（所有审批代码都成功才为true）
- **TotalCodes**: 请求同步的审批代码总数
- **SuccessCodes**: 成功同步的审批代码数量
- **FailedCodes**: 失败的审批代码数量

### 数据统计

- **TotalRecords**: 所有审批代码的总记录数
- **NewRecords**: 新增的记录数
- **UpdatedRecords**: 更新的记录数
- **FailedRecords**: 处理失败的记录数

### 详细结果

每个审批代码的详细同步结果包含：
- 分页处理情况
- 数据处理统计
- 错误信息
- 耗时信息

## 最佳实践

### 1. 合理设置参数

```go
// 推荐配置
req := request.ContractMultiSyncRequest{
    ApprovalCodes: codes,
    BatchSize:     20,  // 平衡效率和稳定性
    PageSize:      100, // 默认值，适合大多数场景
    MaxRetries:    3,   // 足够的重试次数
    RetryDelay:    5,   // 合理的重试间隔
}
```

### 2. 监控同步进度

```go
// 记录同步开始
log.Printf("开始同步 %d 个审批代码", len(req.ApprovalCodes))

resp, err := contractService.SyncMultipleContractData(req)

// 记录同步结果
if resp.Success {
    log.Printf("同步成功: 处理 %d 条记录，耗时 %s", resp.TotalRecords, resp.Duration)
} else {
    log.Printf("同步部分失败: 成功 %d/%d 个审批代码", resp.SuccessCodes, resp.TotalCodes)
}
```

### 3. 错误处理

```go
if !resp.Success {
    // 分析失败原因
    for _, result := range resp.Results {
        if !result.Success {
            log.Printf("审批代码 %s 同步失败: %s", result.ApprovalCode, result.ErrorMsg)
            
            // 可以针对特定错误进行重试或告警
            if strings.Contains(result.ErrorMsg, "API限流") {
                // 延长重试间隔
            } else if strings.Contains(result.ErrorMsg, "网络错误") {
                // 网络重试
            }
        }
    }
}
```

### 4. 性能优化

```go
// 大批量同步时的优化配置
req := request.ContractMultiSyncRequest{
    ApprovalCodes: codes,
    BatchSize:     50,  // 增大批次大小
    PageSize:      200, // 使用最大分页
    RetryDelay:    3,   // 减少延迟
}
```

## 注意事项

1. **API限流**: 飞书API有调用频率限制，系统会自动处理限流，但大量数据同步时请合理安排时间
2. **数据量**: 单次同步建议不超过20个审批代码，时间范围不超过365天
3. **网络稳定性**: 确保网络连接稳定，系统会自动重试网络错误
4. **数据一致性**: 同步过程中避免手动修改相关数据
5. **监控告警**: 建议配置同步失败的监控告警

## 故障排查

### 常见错误及解决方案

1. **参数验证失败**
   - 检查审批代码格式是否正确
   - 确认时间范围设置合理

2. **飞书API调用失败**
   - 检查飞书应用配置
   - 确认API权限设置

3. **数据库操作失败**
   - 检查数据库连接
   - 确认表结构是否正确

4. **部分数据同步失败**
   - 查看详细错误信息
   - 检查数据格式和完整性

通过以上指南，您可以有效地使用飞书合同多审批代码批量同步功能，实现高效、稳定的数据同步。
