package global

// 所有的事件

// 通用事件
const (
	EVENTDBConnectSuccess       = "db.connect.success"        // 数据库连接成功, 参数: *gorm.DB
	EVENTDBRegisterModelSuccess = "db.register.model.success" // 完成数据表注册, 参数: *gorm.DB
	EVENTDBUpgradeVersion       = "db.upgrade-version"        // 数据库升级, 参数: 事务(*gorm.DB), 上次版本（int）

	EVENTSERVICEInitStart  = "service.init.start"  // 服务初始化开始, 参数: ...
	EVENTSERVICEInitFinish = "service.init.finish" // 服务初始化完成, 参数: ...

	EVENTROUTERRegisterInit  = "router.register.init"  // 路由注册开始, 参数: *gin.Engine
	EVENTROUTERRegisterBegin = "router.register.begin" // 路由注册开始, 参数: *gin.Engine, *gin.RouterGroup

	EVENTROUTERRegisterSuccess = "router.register.success" // 路由注册成功, 参数: *gin.Engine, *gin.RouterGroup
)

// 营业相关
const (
	EVENTBUSINESSStart = "business.start" // 营业开始, 参数: insbuy.InsStoreRevenueReport
	EVENTBUSINESSEnd   = "business.end"   // 营业结束, 参数: insbuy.InsStoreRevenueReport
)

// 订单相关
const (
	EVENTORDERCreateSuccess = "order.create.success" // 订单创建成功, 参数: orderID
)

// 桌台相关: 创建、开台、关台、预约、恢复可用
const (
	EVENTDESKAdminCreate   = "desk.admin.create"   // 管理员创建桌台, 参数: ...
	EVENTDESKAdminUpdate   = "desk.admin.update"   // 管理员修改桌台, 参数: ...
	EVENTDESKAdminDel      = "desk.admin.del"      // 管理员删除桌台, 参数: ...
	EVENTDESKOpenSuccess   = "desk.open.success"   // 开台成功, 参数: openDeskId
	EVENTDESKCloseSuccess  = "desk.close.success"  // 关台成功, 参数: openDeskId
	EVENTDESKChangeSuccess = "desk.change.success" // 换台成功, 参数: openDeskId
	EVENTDESKBookSuccess   = "desk.book.success"   // 预约成功, 参数:
	EVENTDESKBookCancel    = "desk.book.cancel"    // 取消预约, 参数:
)

// 会员相关
const (
	EVENTMEMBERAdminCreated      = "member.admin.created"  // 会员创建成功
	EVENTMEMBERAttrBalanceUpdate = "member.balance"        // 会员余额变动成功，参数 memberID, balance
	EVENTMEMBERAttrBalanceDeduct = "member.balance.deduct" //余额扣除
)

// 小票机打印相关
const (
	EVENTPRINTPrintEnd   = "print.print.end"   // 打印机打印结束, 参数: ...
	EVENTPRINTPrintEndV2 = "print.printv2.end" // 打印机打印结束, 参数: ...
)

const (
	SundriesInEND = "Sundries.in.end" //日杂入库结束
)

// 预约活动
const (
	EVENTBOOKDESKACTConfigActive = "bookdeskact.config.active" // 预约活动配置激活, 参数: []bookActID
	EVENTBOOKDESKACTConfigUpdate = "bookdeskact.config.update" // 预约活动配置取消, 参数: []bookActID
)

// 排队相关
const (
	EVENTQUEUEEnqueue = "queue.enqueue" // 取号, 参数: insbuy.InsQueueItem
)

// 订单相关
const (
	//套餐订单重置成功
	EVENTORDERPACKAGEORDERRESETSUCCESS = "order.package.order.reset.success"
)

// 原料相关
const (
	EVENTMATERIALPRICEUPDATE         = "material.price.update"          //原料价格更新
	EVENTSUNDRIESMATERIALPRICEUPDATE = "sundries.material.price.update" //日杂原料价格更新
)
