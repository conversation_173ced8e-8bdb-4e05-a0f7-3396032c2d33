// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreRevenueReport(db *gorm.DB, opts ...gen.DOOption) insStoreRevenueReport {
	_insStoreRevenueReport := insStoreRevenueReport{}

	_insStoreRevenueReport.insStoreRevenueReportDo.UseDB(db, opts...)
	_insStoreRevenueReport.insStoreRevenueReportDo.UseModel(&insbuy.InsStoreRevenueReport{})

	tableName := _insStoreRevenueReport.insStoreRevenueReportDo.TableName()
	_insStoreRevenueReport.ALL = field.NewAsterisk(tableName)
	_insStoreRevenueReport.ID = field.NewUint(tableName, "id")
	_insStoreRevenueReport.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreRevenueReport.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreRevenueReport.StoreId = field.NewUint(tableName, "store_id")
	_insStoreRevenueReport.OpenDate = field.NewTime(tableName, "open_date")
	_insStoreRevenueReport.OpenNum = field.NewInt(tableName, "open_num")
	_insStoreRevenueReport.OrderNum = field.NewInt(tableName, "order_num")
	_insStoreRevenueReport.Revenue = field.NewFloat64(tableName, "revenue")
	_insStoreRevenueReport.GiveAmount = field.NewFloat64(tableName, "give_amount")
	_insStoreRevenueReport.PaidAmount = field.NewFloat64(tableName, "paid_amount")
	_insStoreRevenueReport.UnpaidAmount = field.NewFloat64(tableName, "unpaid_amount")
	_insStoreRevenueReport.BalancePayAmount = field.NewFloat64(tableName, "balance_pay_amount")
	_insStoreRevenueReport.BalanceGiveAmount = field.NewFloat64(tableName, "balance_give_amount")
	_insStoreRevenueReport.DiscountFee = field.NewFloat64(tableName, "discount_fee")
	_insStoreRevenueReport.CouponFee = field.NewFloat64(tableName, "coupon_fee")
	_insStoreRevenueReport.ServiceFee = field.NewFloat64(tableName, "service_fee")
	_insStoreRevenueReport.ErasePrice = field.NewFloat64(tableName, "erase_price")
	_insStoreRevenueReport.PlayerPrice = field.NewFloat64(tableName, "player_price")
	_insStoreRevenueReport.TicketAmount = field.NewFloat64(tableName, "ticket_amount")
	_insStoreRevenueReport.OfflineTicketAmount = field.NewFloat64(tableName, "offline_ticket_amount")
	_insStoreRevenueReport.StoreStatus = field.NewInt(tableName, "store_status")
	_insStoreRevenueReport.StartTime = field.NewTime(tableName, "start_time")
	_insStoreRevenueReport.EndTime = field.NewTime(tableName, "end_time")
	_insStoreRevenueReport.OperatorId = field.NewUint(tableName, "operator_id")

	_insStoreRevenueReport.fillFieldMap()

	return _insStoreRevenueReport
}

type insStoreRevenueReport struct {
	insStoreRevenueReportDo

	ALL                 field.Asterisk
	ID                  field.Uint
	CreatedAt           field.Time
	UpdatedAt           field.Time
	StoreId             field.Uint
	OpenDate            field.Time
	OpenNum             field.Int
	OrderNum            field.Int
	Revenue             field.Float64
	GiveAmount          field.Float64
	PaidAmount          field.Float64
	UnpaidAmount        field.Float64
	BalancePayAmount    field.Float64
	BalanceGiveAmount   field.Float64
	DiscountFee         field.Float64
	CouponFee           field.Float64
	ServiceFee          field.Float64
	ErasePrice          field.Float64
	PlayerPrice         field.Float64
	TicketAmount        field.Float64
	OfflineTicketAmount field.Float64
	StoreStatus         field.Int
	StartTime           field.Time
	EndTime             field.Time
	OperatorId          field.Uint

	fieldMap map[string]field.Expr
}

func (i insStoreRevenueReport) Table(newTableName string) *insStoreRevenueReport {
	i.insStoreRevenueReportDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreRevenueReport) As(alias string) *insStoreRevenueReport {
	i.insStoreRevenueReportDo.DO = *(i.insStoreRevenueReportDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreRevenueReport) updateTableName(table string) *insStoreRevenueReport {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.OpenDate = field.NewTime(table, "open_date")
	i.OpenNum = field.NewInt(table, "open_num")
	i.OrderNum = field.NewInt(table, "order_num")
	i.Revenue = field.NewFloat64(table, "revenue")
	i.GiveAmount = field.NewFloat64(table, "give_amount")
	i.PaidAmount = field.NewFloat64(table, "paid_amount")
	i.UnpaidAmount = field.NewFloat64(table, "unpaid_amount")
	i.BalancePayAmount = field.NewFloat64(table, "balance_pay_amount")
	i.BalanceGiveAmount = field.NewFloat64(table, "balance_give_amount")
	i.DiscountFee = field.NewFloat64(table, "discount_fee")
	i.CouponFee = field.NewFloat64(table, "coupon_fee")
	i.ServiceFee = field.NewFloat64(table, "service_fee")
	i.ErasePrice = field.NewFloat64(table, "erase_price")
	i.PlayerPrice = field.NewFloat64(table, "player_price")
	i.TicketAmount = field.NewFloat64(table, "ticket_amount")
	i.OfflineTicketAmount = field.NewFloat64(table, "offline_ticket_amount")
	i.StoreStatus = field.NewInt(table, "store_status")
	i.StartTime = field.NewTime(table, "start_time")
	i.EndTime = field.NewTime(table, "end_time")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insStoreRevenueReport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreRevenueReport) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 24)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["open_date"] = i.OpenDate
	i.fieldMap["open_num"] = i.OpenNum
	i.fieldMap["order_num"] = i.OrderNum
	i.fieldMap["revenue"] = i.Revenue
	i.fieldMap["give_amount"] = i.GiveAmount
	i.fieldMap["paid_amount"] = i.PaidAmount
	i.fieldMap["unpaid_amount"] = i.UnpaidAmount
	i.fieldMap["balance_pay_amount"] = i.BalancePayAmount
	i.fieldMap["balance_give_amount"] = i.BalanceGiveAmount
	i.fieldMap["discount_fee"] = i.DiscountFee
	i.fieldMap["coupon_fee"] = i.CouponFee
	i.fieldMap["service_fee"] = i.ServiceFee
	i.fieldMap["erase_price"] = i.ErasePrice
	i.fieldMap["player_price"] = i.PlayerPrice
	i.fieldMap["ticket_amount"] = i.TicketAmount
	i.fieldMap["offline_ticket_amount"] = i.OfflineTicketAmount
	i.fieldMap["store_status"] = i.StoreStatus
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insStoreRevenueReport) clone(db *gorm.DB) insStoreRevenueReport {
	i.insStoreRevenueReportDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreRevenueReport) replaceDB(db *gorm.DB) insStoreRevenueReport {
	i.insStoreRevenueReportDo.ReplaceDB(db)
	return i
}

type insStoreRevenueReportDo struct{ gen.DO }

func (i insStoreRevenueReportDo) Debug() *insStoreRevenueReportDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreRevenueReportDo) WithContext(ctx context.Context) *insStoreRevenueReportDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreRevenueReportDo) ReadDB() *insStoreRevenueReportDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreRevenueReportDo) WriteDB() *insStoreRevenueReportDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreRevenueReportDo) Session(config *gorm.Session) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreRevenueReportDo) Clauses(conds ...clause.Expression) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreRevenueReportDo) Returning(value interface{}, columns ...string) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreRevenueReportDo) Not(conds ...gen.Condition) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreRevenueReportDo) Or(conds ...gen.Condition) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreRevenueReportDo) Select(conds ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreRevenueReportDo) Where(conds ...gen.Condition) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreRevenueReportDo) Order(conds ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreRevenueReportDo) Distinct(cols ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreRevenueReportDo) Omit(cols ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreRevenueReportDo) Join(table schema.Tabler, on ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreRevenueReportDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreRevenueReportDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreRevenueReportDo) Group(cols ...field.Expr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreRevenueReportDo) Having(conds ...gen.Condition) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreRevenueReportDo) Limit(limit int) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreRevenueReportDo) Offset(offset int) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreRevenueReportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreRevenueReportDo) Unscoped() *insStoreRevenueReportDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreRevenueReportDo) Create(values ...*insbuy.InsStoreRevenueReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreRevenueReportDo) CreateInBatches(values []*insbuy.InsStoreRevenueReport, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreRevenueReportDo) Save(values ...*insbuy.InsStoreRevenueReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreRevenueReportDo) First() (*insbuy.InsStoreRevenueReport, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreRevenueReport), nil
	}
}

func (i insStoreRevenueReportDo) Take() (*insbuy.InsStoreRevenueReport, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreRevenueReport), nil
	}
}

func (i insStoreRevenueReportDo) Last() (*insbuy.InsStoreRevenueReport, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreRevenueReport), nil
	}
}

func (i insStoreRevenueReportDo) Find() ([]*insbuy.InsStoreRevenueReport, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreRevenueReport), err
}

func (i insStoreRevenueReportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreRevenueReport, err error) {
	buf := make([]*insbuy.InsStoreRevenueReport, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreRevenueReportDo) FindInBatches(result *[]*insbuy.InsStoreRevenueReport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreRevenueReportDo) Attrs(attrs ...field.AssignExpr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreRevenueReportDo) Assign(attrs ...field.AssignExpr) *insStoreRevenueReportDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreRevenueReportDo) Joins(fields ...field.RelationField) *insStoreRevenueReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreRevenueReportDo) Preload(fields ...field.RelationField) *insStoreRevenueReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreRevenueReportDo) FirstOrInit() (*insbuy.InsStoreRevenueReport, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreRevenueReport), nil
	}
}

func (i insStoreRevenueReportDo) FirstOrCreate() (*insbuy.InsStoreRevenueReport, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreRevenueReport), nil
	}
}

func (i insStoreRevenueReportDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreRevenueReport, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreRevenueReportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreRevenueReportDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreRevenueReportDo) Delete(models ...*insbuy.InsStoreRevenueReport) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreRevenueReportDo) withDO(do gen.Dao) *insStoreRevenueReportDo {
	i.DO = *do.(*gen.DO)
	return i
}
