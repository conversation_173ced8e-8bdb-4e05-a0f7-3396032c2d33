// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventoryDetail(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventoryDetail {
	_insWarehouseInventoryDetail := insWarehouseInventoryDetail{}

	_insWarehouseInventoryDetail.insWarehouseInventoryDetailDo.UseDB(db, opts...)
	_insWarehouseInventoryDetail.insWarehouseInventoryDetailDo.UseModel(&insbuy.InsWarehouseInventoryDetail{})

	tableName := _insWarehouseInventoryDetail.insWarehouseInventoryDetailDo.TableName()
	_insWarehouseInventoryDetail.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventoryDetail.ID = field.NewUint(tableName, "id")
	_insWarehouseInventoryDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventoryDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventoryDetail.IsRedFlush = field.NewInt(tableName, "is_red_flush")
	_insWarehouseInventoryDetail.InventoryId = field.NewInt(tableName, "inventory_id")
	_insWarehouseInventoryDetail.SourceId = field.NewInt(tableName, "source_id")
	_insWarehouseInventoryDetail.Type = field.NewInt(tableName, "type")
	_insWarehouseInventoryDetail.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseInventoryDetail.AfterNum = field.NewFloat64(tableName, "after_num")
	_insWarehouseInventoryDetail.Num = field.NewInt(tableName, "num")
	_insWarehouseInventoryDetail.ConvRatio = field.NewFloat64(tableName, "conv_ratio")
	_insWarehouseInventoryDetail.ConvNum = field.NewFloat64(tableName, "conv_num")
	_insWarehouseInventoryDetail.Remark = field.NewString(tableName, "remark")
	_insWarehouseInventoryDetail.BusinessDay = field.NewTime(tableName, "business_day")

	_insWarehouseInventoryDetail.fillFieldMap()

	return _insWarehouseInventoryDetail
}

type insWarehouseInventoryDetail struct {
	insWarehouseInventoryDetailDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	IsRedFlush  field.Int
	InventoryId field.Int
	SourceId    field.Int
	Type        field.Int
	MaterialId  field.Int
	AfterNum    field.Float64
	Num         field.Int
	ConvRatio   field.Float64
	ConvNum     field.Float64
	Remark      field.String
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventoryDetail) Table(newTableName string) *insWarehouseInventoryDetail {
	i.insWarehouseInventoryDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventoryDetail) As(alias string) *insWarehouseInventoryDetail {
	i.insWarehouseInventoryDetailDo.DO = *(i.insWarehouseInventoryDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventoryDetail) updateTableName(table string) *insWarehouseInventoryDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.IsRedFlush = field.NewInt(table, "is_red_flush")
	i.InventoryId = field.NewInt(table, "inventory_id")
	i.SourceId = field.NewInt(table, "source_id")
	i.Type = field.NewInt(table, "type")
	i.MaterialId = field.NewInt(table, "material_id")
	i.AfterNum = field.NewFloat64(table, "after_num")
	i.Num = field.NewInt(table, "num")
	i.ConvRatio = field.NewFloat64(table, "conv_ratio")
	i.ConvNum = field.NewFloat64(table, "conv_num")
	i.Remark = field.NewString(table, "remark")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventoryDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventoryDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["is_red_flush"] = i.IsRedFlush
	i.fieldMap["inventory_id"] = i.InventoryId
	i.fieldMap["source_id"] = i.SourceId
	i.fieldMap["type"] = i.Type
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["after_num"] = i.AfterNum
	i.fieldMap["num"] = i.Num
	i.fieldMap["conv_ratio"] = i.ConvRatio
	i.fieldMap["conv_num"] = i.ConvNum
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insWarehouseInventoryDetail) clone(db *gorm.DB) insWarehouseInventoryDetail {
	i.insWarehouseInventoryDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventoryDetail) replaceDB(db *gorm.DB) insWarehouseInventoryDetail {
	i.insWarehouseInventoryDetailDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryDetailDo struct{ gen.DO }

func (i insWarehouseInventoryDetailDo) Debug() *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryDetailDo) WithContext(ctx context.Context) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryDetailDo) ReadDB() *insWarehouseInventoryDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryDetailDo) WriteDB() *insWarehouseInventoryDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryDetailDo) Session(config *gorm.Session) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryDetailDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryDetailDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryDetailDo) Not(conds ...gen.Condition) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryDetailDo) Or(conds ...gen.Condition) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryDetailDo) Select(conds ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryDetailDo) Where(conds ...gen.Condition) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryDetailDo) Order(conds ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryDetailDo) Distinct(cols ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryDetailDo) Omit(cols ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryDetailDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryDetailDo) Group(cols ...field.Expr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryDetailDo) Having(conds ...gen.Condition) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryDetailDo) Limit(limit int) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryDetailDo) Offset(offset int) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryDetailDo) Unscoped() *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryDetailDo) Create(values ...*insbuy.InsWarehouseInventoryDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryDetailDo) CreateInBatches(values []*insbuy.InsWarehouseInventoryDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryDetailDo) Save(values ...*insbuy.InsWarehouseInventoryDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryDetailDo) First() (*insbuy.InsWarehouseInventoryDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDetail), nil
	}
}

func (i insWarehouseInventoryDetailDo) Take() (*insbuy.InsWarehouseInventoryDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDetail), nil
	}
}

func (i insWarehouseInventoryDetailDo) Last() (*insbuy.InsWarehouseInventoryDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDetail), nil
	}
}

func (i insWarehouseInventoryDetailDo) Find() ([]*insbuy.InsWarehouseInventoryDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventoryDetail), err
}

func (i insWarehouseInventoryDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventoryDetail, err error) {
	buf := make([]*insbuy.InsWarehouseInventoryDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryDetailDo) FindInBatches(result *[]*insbuy.InsWarehouseInventoryDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryDetailDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryDetailDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryDetailDo) Joins(fields ...field.RelationField) *insWarehouseInventoryDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryDetailDo) Preload(fields ...field.RelationField) *insWarehouseInventoryDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryDetailDo) FirstOrInit() (*insbuy.InsWarehouseInventoryDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDetail), nil
	}
}

func (i insWarehouseInventoryDetailDo) FirstOrCreate() (*insbuy.InsWarehouseInventoryDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDetail), nil
	}
}

func (i insWarehouseInventoryDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventoryDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryDetailDo) Delete(models ...*insbuy.InsWarehouseInventoryDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryDetailDo) withDO(do gen.Dao) *insWarehouseInventoryDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
