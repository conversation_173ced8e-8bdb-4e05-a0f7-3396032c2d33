// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReconciliationTask(db *gorm.DB, opts ...gen.DOOption) insReconciliationTask {
	_insReconciliationTask := insReconciliationTask{}

	_insReconciliationTask.insReconciliationTaskDo.UseDB(db, opts...)
	_insReconciliationTask.insReconciliationTaskDo.UseModel(&insbuy.InsReconciliationTask{})

	tableName := _insReconciliationTask.insReconciliationTaskDo.TableName()
	_insReconciliationTask.ALL = field.NewAsterisk(tableName)
	_insReconciliationTask.ID = field.NewUint(tableName, "id")
	_insReconciliationTask.CreatedAt = field.NewTime(tableName, "created_at")
	_insReconciliationTask.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReconciliationTask.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReconciliationTask.StoreId = field.NewUint(tableName, "store_id")
	_insReconciliationTask.TaskSn = field.NewString(tableName, "task_sn")
	_insReconciliationTask.Type = field.NewInt(tableName, "type")
	_insReconciliationTask.Dim = field.NewInt(tableName, "dim")
	_insReconciliationTask.Channel = field.NewString(tableName, "channel")
	_insReconciliationTask.Cycle = field.NewInt(tableName, "cycle")
	_insReconciliationTask.Status = field.NewUint(tableName, "status")
	_insReconciliationTask.StartDate = field.NewTime(tableName, "start_date")
	_insReconciliationTask.EndDate = field.NewTime(tableName, "end_date")
	_insReconciliationTask.RuleId = field.NewUint(tableName, "rule_id")
	_insReconciliationTask.OperatorId = field.NewUint(tableName, "operator_id")
	_insReconciliationTask.Ext = field.NewField(tableName, "ext")

	_insReconciliationTask.fillFieldMap()

	return _insReconciliationTask
}

type insReconciliationTask struct {
	insReconciliationTaskDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StoreId    field.Uint
	TaskSn     field.String
	Type       field.Int
	Dim        field.Int
	Channel    field.String
	Cycle      field.Int
	Status     field.Uint
	StartDate  field.Time
	EndDate    field.Time
	RuleId     field.Uint
	OperatorId field.Uint
	Ext        field.Field

	fieldMap map[string]field.Expr
}

func (i insReconciliationTask) Table(newTableName string) *insReconciliationTask {
	i.insReconciliationTaskDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReconciliationTask) As(alias string) *insReconciliationTask {
	i.insReconciliationTaskDo.DO = *(i.insReconciliationTaskDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReconciliationTask) updateTableName(table string) *insReconciliationTask {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.TaskSn = field.NewString(table, "task_sn")
	i.Type = field.NewInt(table, "type")
	i.Dim = field.NewInt(table, "dim")
	i.Channel = field.NewString(table, "channel")
	i.Cycle = field.NewInt(table, "cycle")
	i.Status = field.NewUint(table, "status")
	i.StartDate = field.NewTime(table, "start_date")
	i.EndDate = field.NewTime(table, "end_date")
	i.RuleId = field.NewUint(table, "rule_id")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insReconciliationTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReconciliationTask) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["task_sn"] = i.TaskSn
	i.fieldMap["type"] = i.Type
	i.fieldMap["dim"] = i.Dim
	i.fieldMap["channel"] = i.Channel
	i.fieldMap["cycle"] = i.Cycle
	i.fieldMap["status"] = i.Status
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["ext"] = i.Ext
}

func (i insReconciliationTask) clone(db *gorm.DB) insReconciliationTask {
	i.insReconciliationTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReconciliationTask) replaceDB(db *gorm.DB) insReconciliationTask {
	i.insReconciliationTaskDo.ReplaceDB(db)
	return i
}

type insReconciliationTaskDo struct{ gen.DO }

func (i insReconciliationTaskDo) Debug() *insReconciliationTaskDo {
	return i.withDO(i.DO.Debug())
}

func (i insReconciliationTaskDo) WithContext(ctx context.Context) *insReconciliationTaskDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReconciliationTaskDo) ReadDB() *insReconciliationTaskDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReconciliationTaskDo) WriteDB() *insReconciliationTaskDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReconciliationTaskDo) Session(config *gorm.Session) *insReconciliationTaskDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReconciliationTaskDo) Clauses(conds ...clause.Expression) *insReconciliationTaskDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReconciliationTaskDo) Returning(value interface{}, columns ...string) *insReconciliationTaskDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReconciliationTaskDo) Not(conds ...gen.Condition) *insReconciliationTaskDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReconciliationTaskDo) Or(conds ...gen.Condition) *insReconciliationTaskDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReconciliationTaskDo) Select(conds ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReconciliationTaskDo) Where(conds ...gen.Condition) *insReconciliationTaskDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReconciliationTaskDo) Order(conds ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReconciliationTaskDo) Distinct(cols ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReconciliationTaskDo) Omit(cols ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReconciliationTaskDo) Join(table schema.Tabler, on ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReconciliationTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReconciliationTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReconciliationTaskDo) Group(cols ...field.Expr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReconciliationTaskDo) Having(conds ...gen.Condition) *insReconciliationTaskDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReconciliationTaskDo) Limit(limit int) *insReconciliationTaskDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReconciliationTaskDo) Offset(offset int) *insReconciliationTaskDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReconciliationTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReconciliationTaskDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReconciliationTaskDo) Unscoped() *insReconciliationTaskDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReconciliationTaskDo) Create(values ...*insbuy.InsReconciliationTask) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReconciliationTaskDo) CreateInBatches(values []*insbuy.InsReconciliationTask, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReconciliationTaskDo) Save(values ...*insbuy.InsReconciliationTask) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReconciliationTaskDo) First() (*insbuy.InsReconciliationTask, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationTask), nil
	}
}

func (i insReconciliationTaskDo) Take() (*insbuy.InsReconciliationTask, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationTask), nil
	}
}

func (i insReconciliationTaskDo) Last() (*insbuy.InsReconciliationTask, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationTask), nil
	}
}

func (i insReconciliationTaskDo) Find() ([]*insbuy.InsReconciliationTask, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReconciliationTask), err
}

func (i insReconciliationTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReconciliationTask, err error) {
	buf := make([]*insbuy.InsReconciliationTask, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReconciliationTaskDo) FindInBatches(result *[]*insbuy.InsReconciliationTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReconciliationTaskDo) Attrs(attrs ...field.AssignExpr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReconciliationTaskDo) Assign(attrs ...field.AssignExpr) *insReconciliationTaskDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReconciliationTaskDo) Joins(fields ...field.RelationField) *insReconciliationTaskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReconciliationTaskDo) Preload(fields ...field.RelationField) *insReconciliationTaskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReconciliationTaskDo) FirstOrInit() (*insbuy.InsReconciliationTask, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationTask), nil
	}
}

func (i insReconciliationTaskDo) FirstOrCreate() (*insbuy.InsReconciliationTask, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationTask), nil
	}
}

func (i insReconciliationTaskDo) FindByPage(offset int, limit int) (result []*insbuy.InsReconciliationTask, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReconciliationTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReconciliationTaskDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReconciliationTaskDo) Delete(models ...*insbuy.InsReconciliationTask) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReconciliationTaskDo) withDO(do gen.Dao) *insReconciliationTaskDo {
	i.DO = *do.(*gen.DO)
	return i
}
