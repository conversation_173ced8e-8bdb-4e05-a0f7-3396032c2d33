package main

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport"
)

func main() {
	fmt.Println("🎯 财务公式表达式系统 - 复合表达式完整演示")
	fmt.Println("表达式：([营业收入] - [营业成本]) / [营业收入] %")
	fmt.Println("=" * 60)
	fmt.Println()

	// 运行复合表达式完整示例
	runComplexFormulaDemo()
}

func runComplexFormulaDemo() {
	// 创建复合表达式示例
	example := insreport.NewComplexFormulaExample()
	
	// 演示完整的复合表达式处理流程
	example.DemoComplexFormula()
	
	fmt.Println("=" * 60)
	
	// 演示分步计算方法
	example.DemoStepByStepCalculation()
	
	fmt.Println("=" * 60)
	fmt.Println("✅ 演示完成！")
	fmt.Println()
	fmt.Println("📋 总结：")
	fmt.Println("1. 复合表达式可以正确解析为表达式树结构")
	fmt.Println("2. 计算结果准确，支持月度和总计数据")
	fmt.Println("3. 自动生成JSON配置，包含完整的元数据")
	fmt.Println("4. 支持分步计算，便于调试和验证")
	fmt.Println("5. 具备完善的错误处理和边界情况处理")
}
