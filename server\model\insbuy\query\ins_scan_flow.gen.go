// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsScanFlow(db *gorm.DB, opts ...gen.DOOption) insScanFlow {
	_insScanFlow := insScanFlow{}

	_insScanFlow.insScanFlowDo.UseDB(db, opts...)
	_insScanFlow.insScanFlowDo.UseModel(&insbuy.InsScanFlow{})

	tableName := _insScanFlow.insScanFlowDo.TableName()
	_insScanFlow.ALL = field.NewAsterisk(tableName)
	_insScanFlow.ID = field.NewUint(tableName, "id")
	_insScanFlow.CreatedAt = field.NewTime(tableName, "created_at")
	_insScanFlow.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insScanFlow.Count_ = field.NewInt(tableName, "count")
	_insScanFlow.PeopleCount = field.NewInt(tableName, "people_count")
	_insScanFlow.BusinessDay = field.NewTime(tableName, "business_day")

	_insScanFlow.fillFieldMap()

	return _insScanFlow
}

type insScanFlow struct {
	insScanFlowDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Count_      field.Int
	PeopleCount field.Int
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insScanFlow) Table(newTableName string) *insScanFlow {
	i.insScanFlowDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insScanFlow) As(alias string) *insScanFlow {
	i.insScanFlowDo.DO = *(i.insScanFlowDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insScanFlow) updateTableName(table string) *insScanFlow {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Count_ = field.NewInt(table, "count")
	i.PeopleCount = field.NewInt(table, "people_count")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insScanFlow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insScanFlow) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["count"] = i.Count_
	i.fieldMap["people_count"] = i.PeopleCount
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insScanFlow) clone(db *gorm.DB) insScanFlow {
	i.insScanFlowDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insScanFlow) replaceDB(db *gorm.DB) insScanFlow {
	i.insScanFlowDo.ReplaceDB(db)
	return i
}

type insScanFlowDo struct{ gen.DO }

func (i insScanFlowDo) Debug() *insScanFlowDo {
	return i.withDO(i.DO.Debug())
}

func (i insScanFlowDo) WithContext(ctx context.Context) *insScanFlowDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insScanFlowDo) ReadDB() *insScanFlowDo {
	return i.Clauses(dbresolver.Read)
}

func (i insScanFlowDo) WriteDB() *insScanFlowDo {
	return i.Clauses(dbresolver.Write)
}

func (i insScanFlowDo) Session(config *gorm.Session) *insScanFlowDo {
	return i.withDO(i.DO.Session(config))
}

func (i insScanFlowDo) Clauses(conds ...clause.Expression) *insScanFlowDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insScanFlowDo) Returning(value interface{}, columns ...string) *insScanFlowDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insScanFlowDo) Not(conds ...gen.Condition) *insScanFlowDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insScanFlowDo) Or(conds ...gen.Condition) *insScanFlowDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insScanFlowDo) Select(conds ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insScanFlowDo) Where(conds ...gen.Condition) *insScanFlowDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insScanFlowDo) Order(conds ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insScanFlowDo) Distinct(cols ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insScanFlowDo) Omit(cols ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insScanFlowDo) Join(table schema.Tabler, on ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insScanFlowDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insScanFlowDo) RightJoin(table schema.Tabler, on ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insScanFlowDo) Group(cols ...field.Expr) *insScanFlowDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insScanFlowDo) Having(conds ...gen.Condition) *insScanFlowDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insScanFlowDo) Limit(limit int) *insScanFlowDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insScanFlowDo) Offset(offset int) *insScanFlowDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insScanFlowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insScanFlowDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insScanFlowDo) Unscoped() *insScanFlowDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insScanFlowDo) Create(values ...*insbuy.InsScanFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insScanFlowDo) CreateInBatches(values []*insbuy.InsScanFlow, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insScanFlowDo) Save(values ...*insbuy.InsScanFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insScanFlowDo) First() (*insbuy.InsScanFlow, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsScanFlow), nil
	}
}

func (i insScanFlowDo) Take() (*insbuy.InsScanFlow, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsScanFlow), nil
	}
}

func (i insScanFlowDo) Last() (*insbuy.InsScanFlow, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsScanFlow), nil
	}
}

func (i insScanFlowDo) Find() ([]*insbuy.InsScanFlow, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsScanFlow), err
}

func (i insScanFlowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsScanFlow, err error) {
	buf := make([]*insbuy.InsScanFlow, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insScanFlowDo) FindInBatches(result *[]*insbuy.InsScanFlow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insScanFlowDo) Attrs(attrs ...field.AssignExpr) *insScanFlowDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insScanFlowDo) Assign(attrs ...field.AssignExpr) *insScanFlowDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insScanFlowDo) Joins(fields ...field.RelationField) *insScanFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insScanFlowDo) Preload(fields ...field.RelationField) *insScanFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insScanFlowDo) FirstOrInit() (*insbuy.InsScanFlow, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsScanFlow), nil
	}
}

func (i insScanFlowDo) FirstOrCreate() (*insbuy.InsScanFlow, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsScanFlow), nil
	}
}

func (i insScanFlowDo) FindByPage(offset int, limit int) (result []*insbuy.InsScanFlow, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insScanFlowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insScanFlowDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insScanFlowDo) Delete(models ...*insbuy.InsScanFlow) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insScanFlowDo) withDO(do gen.Dao) *insScanFlowDo {
	i.DO = *do.(*gen.DO)
	return i
}
