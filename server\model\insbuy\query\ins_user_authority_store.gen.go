// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsUserAuthorityStore(db *gorm.DB, opts ...gen.DOOption) insUserAuthorityStore {
	_insUserAuthorityStore := insUserAuthorityStore{}

	_insUserAuthorityStore.insUserAuthorityStoreDo.UseDB(db, opts...)
	_insUserAuthorityStore.insUserAuthorityStoreDo.UseModel(&insbuy.InsUserAuthorityStore{})

	tableName := _insUserAuthorityStore.insUserAuthorityStoreDo.TableName()
	_insUserAuthorityStore.ALL = field.NewAsterisk(tableName)
	_insUserAuthorityStore.UserId = field.NewUint(tableName, "user_id")
	_insUserAuthorityStore.AuthorityId = field.NewUint(tableName, "authority_id")
	_insUserAuthorityStore.StoreId = field.NewUint(tableName, "store_id")

	_insUserAuthorityStore.fillFieldMap()

	return _insUserAuthorityStore
}

type insUserAuthorityStore struct {
	insUserAuthorityStoreDo

	ALL         field.Asterisk
	UserId      field.Uint
	AuthorityId field.Uint
	StoreId     field.Uint

	fieldMap map[string]field.Expr
}

func (i insUserAuthorityStore) Table(newTableName string) *insUserAuthorityStore {
	i.insUserAuthorityStoreDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insUserAuthorityStore) As(alias string) *insUserAuthorityStore {
	i.insUserAuthorityStoreDo.DO = *(i.insUserAuthorityStoreDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insUserAuthorityStore) updateTableName(table string) *insUserAuthorityStore {
	i.ALL = field.NewAsterisk(table)
	i.UserId = field.NewUint(table, "user_id")
	i.AuthorityId = field.NewUint(table, "authority_id")
	i.StoreId = field.NewUint(table, "store_id")

	i.fillFieldMap()

	return i
}

func (i *insUserAuthorityStore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insUserAuthorityStore) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 3)
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["authority_id"] = i.AuthorityId
	i.fieldMap["store_id"] = i.StoreId
}

func (i insUserAuthorityStore) clone(db *gorm.DB) insUserAuthorityStore {
	i.insUserAuthorityStoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insUserAuthorityStore) replaceDB(db *gorm.DB) insUserAuthorityStore {
	i.insUserAuthorityStoreDo.ReplaceDB(db)
	return i
}

type insUserAuthorityStoreDo struct{ gen.DO }

func (i insUserAuthorityStoreDo) Debug() *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Debug())
}

func (i insUserAuthorityStoreDo) WithContext(ctx context.Context) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insUserAuthorityStoreDo) ReadDB() *insUserAuthorityStoreDo {
	return i.Clauses(dbresolver.Read)
}

func (i insUserAuthorityStoreDo) WriteDB() *insUserAuthorityStoreDo {
	return i.Clauses(dbresolver.Write)
}

func (i insUserAuthorityStoreDo) Session(config *gorm.Session) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Session(config))
}

func (i insUserAuthorityStoreDo) Clauses(conds ...clause.Expression) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insUserAuthorityStoreDo) Returning(value interface{}, columns ...string) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insUserAuthorityStoreDo) Not(conds ...gen.Condition) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insUserAuthorityStoreDo) Or(conds ...gen.Condition) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insUserAuthorityStoreDo) Select(conds ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insUserAuthorityStoreDo) Where(conds ...gen.Condition) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insUserAuthorityStoreDo) Order(conds ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insUserAuthorityStoreDo) Distinct(cols ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insUserAuthorityStoreDo) Omit(cols ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insUserAuthorityStoreDo) Join(table schema.Tabler, on ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insUserAuthorityStoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insUserAuthorityStoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insUserAuthorityStoreDo) Group(cols ...field.Expr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insUserAuthorityStoreDo) Having(conds ...gen.Condition) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insUserAuthorityStoreDo) Limit(limit int) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insUserAuthorityStoreDo) Offset(offset int) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insUserAuthorityStoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insUserAuthorityStoreDo) Unscoped() *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insUserAuthorityStoreDo) Create(values ...*insbuy.InsUserAuthorityStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insUserAuthorityStoreDo) CreateInBatches(values []*insbuy.InsUserAuthorityStore, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insUserAuthorityStoreDo) Save(values ...*insbuy.InsUserAuthorityStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insUserAuthorityStoreDo) First() (*insbuy.InsUserAuthorityStore, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserAuthorityStore), nil
	}
}

func (i insUserAuthorityStoreDo) Take() (*insbuy.InsUserAuthorityStore, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserAuthorityStore), nil
	}
}

func (i insUserAuthorityStoreDo) Last() (*insbuy.InsUserAuthorityStore, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserAuthorityStore), nil
	}
}

func (i insUserAuthorityStoreDo) Find() ([]*insbuy.InsUserAuthorityStore, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsUserAuthorityStore), err
}

func (i insUserAuthorityStoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsUserAuthorityStore, err error) {
	buf := make([]*insbuy.InsUserAuthorityStore, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insUserAuthorityStoreDo) FindInBatches(result *[]*insbuy.InsUserAuthorityStore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insUserAuthorityStoreDo) Attrs(attrs ...field.AssignExpr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insUserAuthorityStoreDo) Assign(attrs ...field.AssignExpr) *insUserAuthorityStoreDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insUserAuthorityStoreDo) Joins(fields ...field.RelationField) *insUserAuthorityStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insUserAuthorityStoreDo) Preload(fields ...field.RelationField) *insUserAuthorityStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insUserAuthorityStoreDo) FirstOrInit() (*insbuy.InsUserAuthorityStore, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserAuthorityStore), nil
	}
}

func (i insUserAuthorityStoreDo) FirstOrCreate() (*insbuy.InsUserAuthorityStore, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserAuthorityStore), nil
	}
}

func (i insUserAuthorityStoreDo) FindByPage(offset int, limit int) (result []*insbuy.InsUserAuthorityStore, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insUserAuthorityStoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insUserAuthorityStoreDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insUserAuthorityStoreDo) Delete(models ...*insbuy.InsUserAuthorityStore) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insUserAuthorityStoreDo) withDO(do gen.Dao) *insUserAuthorityStoreDo {
	i.DO = *do.(*gen.DO)
	return i
}
