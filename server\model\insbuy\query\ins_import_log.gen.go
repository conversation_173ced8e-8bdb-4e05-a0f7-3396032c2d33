// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsImportLog(db *gorm.DB, opts ...gen.DOOption) insImportLog {
	_insImportLog := insImportLog{}

	_insImportLog.insImportLogDo.UseDB(db, opts...)
	_insImportLog.insImportLogDo.UseModel(&insbuy.InsImportLog{})

	tableName := _insImportLog.insImportLogDo.TableName()
	_insImportLog.ALL = field.NewAsterisk(tableName)
	_insImportLog.ID = field.NewUint(tableName, "id")
	_insImportLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insImportLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insImportLog.Filename = field.NewString(tableName, "filename")
	_insImportLog.SuccessNum = field.NewInt(tableName, "success_num")
	_insImportLog.FailNum = field.NewInt(tableName, "fail_num")
	_insImportLog.Data = field.NewString(tableName, "data")
	_insImportLog.ExportLink = field.NewString(tableName, "export_link")

	_insImportLog.fillFieldMap()

	return _insImportLog
}

type insImportLog struct {
	insImportLogDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	Filename   field.String
	SuccessNum field.Int
	FailNum    field.Int
	Data       field.String
	ExportLink field.String

	fieldMap map[string]field.Expr
}

func (i insImportLog) Table(newTableName string) *insImportLog {
	i.insImportLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insImportLog) As(alias string) *insImportLog {
	i.insImportLogDo.DO = *(i.insImportLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insImportLog) updateTableName(table string) *insImportLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Filename = field.NewString(table, "filename")
	i.SuccessNum = field.NewInt(table, "success_num")
	i.FailNum = field.NewInt(table, "fail_num")
	i.Data = field.NewString(table, "data")
	i.ExportLink = field.NewString(table, "export_link")

	i.fillFieldMap()

	return i
}

func (i *insImportLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insImportLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["filename"] = i.Filename
	i.fieldMap["success_num"] = i.SuccessNum
	i.fieldMap["fail_num"] = i.FailNum
	i.fieldMap["data"] = i.Data
	i.fieldMap["export_link"] = i.ExportLink
}

func (i insImportLog) clone(db *gorm.DB) insImportLog {
	i.insImportLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insImportLog) replaceDB(db *gorm.DB) insImportLog {
	i.insImportLogDo.ReplaceDB(db)
	return i
}

type insImportLogDo struct{ gen.DO }

func (i insImportLogDo) Debug() *insImportLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insImportLogDo) WithContext(ctx context.Context) *insImportLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insImportLogDo) ReadDB() *insImportLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insImportLogDo) WriteDB() *insImportLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insImportLogDo) Session(config *gorm.Session) *insImportLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insImportLogDo) Clauses(conds ...clause.Expression) *insImportLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insImportLogDo) Returning(value interface{}, columns ...string) *insImportLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insImportLogDo) Not(conds ...gen.Condition) *insImportLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insImportLogDo) Or(conds ...gen.Condition) *insImportLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insImportLogDo) Select(conds ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insImportLogDo) Where(conds ...gen.Condition) *insImportLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insImportLogDo) Order(conds ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insImportLogDo) Distinct(cols ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insImportLogDo) Omit(cols ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insImportLogDo) Join(table schema.Tabler, on ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insImportLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insImportLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insImportLogDo) Group(cols ...field.Expr) *insImportLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insImportLogDo) Having(conds ...gen.Condition) *insImportLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insImportLogDo) Limit(limit int) *insImportLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insImportLogDo) Offset(offset int) *insImportLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insImportLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insImportLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insImportLogDo) Unscoped() *insImportLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insImportLogDo) Create(values ...*insbuy.InsImportLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insImportLogDo) CreateInBatches(values []*insbuy.InsImportLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insImportLogDo) Save(values ...*insbuy.InsImportLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insImportLogDo) First() (*insbuy.InsImportLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsImportLog), nil
	}
}

func (i insImportLogDo) Take() (*insbuy.InsImportLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsImportLog), nil
	}
}

func (i insImportLogDo) Last() (*insbuy.InsImportLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsImportLog), nil
	}
}

func (i insImportLogDo) Find() ([]*insbuy.InsImportLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsImportLog), err
}

func (i insImportLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsImportLog, err error) {
	buf := make([]*insbuy.InsImportLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insImportLogDo) FindInBatches(result *[]*insbuy.InsImportLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insImportLogDo) Attrs(attrs ...field.AssignExpr) *insImportLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insImportLogDo) Assign(attrs ...field.AssignExpr) *insImportLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insImportLogDo) Joins(fields ...field.RelationField) *insImportLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insImportLogDo) Preload(fields ...field.RelationField) *insImportLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insImportLogDo) FirstOrInit() (*insbuy.InsImportLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsImportLog), nil
	}
}

func (i insImportLogDo) FirstOrCreate() (*insbuy.InsImportLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsImportLog), nil
	}
}

func (i insImportLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsImportLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insImportLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insImportLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insImportLogDo) Delete(models ...*insbuy.InsImportLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insImportLogDo) withDO(do gen.Dao) *insImportLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
