<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="MySQL" uuid="fd62da4b-4c08-4e8b-922a-04d5ffa98d41">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <configured-by-url>true</configured-by-url>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>rm-uf6jf1u63b1850292.mysql.rds.aliyuncs.com</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="<EMAIL>" uuid="48c2bd90-bedf-492b-bf95-1642ff99f503">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*********************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="测试服-insbuy" uuid="3ffa7ddc-3b16-4054-a682-789fb9068215">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>测试服</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*********************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="inspay-正式服" uuid="6fe7f565-a7a4-412f-bad8-c65ac1b20688">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>inspay-正式服</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*********************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="@localhost" uuid="cf92126f-97eb-4399-9a33-4a457ad8a272">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>本地</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="上海ins只读库" uuid="d8b47c41-e068-44dc-bae9-b44db9a4e618">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>上海ins只读库</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*************************************************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="@localhost [2]" uuid="a3f235ae-facd-45cc-909c-2e89084291d1">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="shanghaiins" uuid="36d7bbba-65e0-457c-9e76-80ed9fc1b627">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <remarks>上海ins只读库</remarks>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***********************************************************************************************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="@cd-ins.ultrasdk.com-dnf" uuid="0f6f2916-8b3e-4316-9f76-0c7941e9b86d">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="@127.0.0.1" uuid="a845fdad-8b66-4530-b1cd-812986fe0b33">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>***************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="yysls_ranking@39.105.54.213" uuid="07b7419a-1ca6-4bb5-b7c0-243aa6ae0ad7">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*********************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>