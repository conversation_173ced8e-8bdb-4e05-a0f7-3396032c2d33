// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSqlReport(db *gorm.DB, opts ...gen.DOOption) insSqlReport {
	_insSqlReport := insSqlReport{}

	_insSqlReport.insSqlReportDo.UseDB(db, opts...)
	_insSqlReport.insSqlReportDo.UseModel(&insbuy.InsSqlReport{})

	tableName := _insSqlReport.insSqlReportDo.TableName()
	_insSqlReport.ALL = field.NewAsterisk(tableName)
	_insSqlReport.ID = field.NewUint(tableName, "id")
	_insSqlReport.CreatedAt = field.NewTime(tableName, "created_at")
	_insSqlReport.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSqlReport.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSqlReport.StoreId = field.NewUint(tableName, "store_id")
	_insSqlReport.Icon = field.NewString(tableName, "icon")
	_insSqlReport.Name = field.NewString(tableName, "name")
	_insSqlReport.CateId = field.NewUint(tableName, "cate_id")
	_insSqlReport.Sort = field.NewInt(tableName, "sort")
	_insSqlReport.IsCache = field.NewUint(tableName, "is_cache")
	_insSqlReport.Status = field.NewUint(tableName, "status")
	_insSqlReport.Remark = field.NewString(tableName, "remark")
	_insSqlReport.Data = field.NewField(tableName, "data")
	_insSqlReport.DataSource = field.NewString(tableName, "data_source")

	_insSqlReport.fillFieldMap()

	return _insSqlReport
}

type insSqlReport struct {
	insSqlReportDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StoreId    field.Uint
	Icon       field.String
	Name       field.String
	CateId     field.Uint
	Sort       field.Int
	IsCache    field.Uint
	Status     field.Uint
	Remark     field.String
	Data       field.Field
	DataSource field.String

	fieldMap map[string]field.Expr
}

func (i insSqlReport) Table(newTableName string) *insSqlReport {
	i.insSqlReportDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSqlReport) As(alias string) *insSqlReport {
	i.insSqlReportDo.DO = *(i.insSqlReportDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSqlReport) updateTableName(table string) *insSqlReport {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Icon = field.NewString(table, "icon")
	i.Name = field.NewString(table, "name")
	i.CateId = field.NewUint(table, "cate_id")
	i.Sort = field.NewInt(table, "sort")
	i.IsCache = field.NewUint(table, "is_cache")
	i.Status = field.NewUint(table, "status")
	i.Remark = field.NewString(table, "remark")
	i.Data = field.NewField(table, "data")
	i.DataSource = field.NewString(table, "data_source")

	i.fillFieldMap()

	return i
}

func (i *insSqlReport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSqlReport) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["icon"] = i.Icon
	i.fieldMap["name"] = i.Name
	i.fieldMap["cate_id"] = i.CateId
	i.fieldMap["sort"] = i.Sort
	i.fieldMap["is_cache"] = i.IsCache
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["data"] = i.Data
	i.fieldMap["data_source"] = i.DataSource
}

func (i insSqlReport) clone(db *gorm.DB) insSqlReport {
	i.insSqlReportDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSqlReport) replaceDB(db *gorm.DB) insSqlReport {
	i.insSqlReportDo.ReplaceDB(db)
	return i
}

type insSqlReportDo struct{ gen.DO }

func (i insSqlReportDo) Debug() *insSqlReportDo {
	return i.withDO(i.DO.Debug())
}

func (i insSqlReportDo) WithContext(ctx context.Context) *insSqlReportDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSqlReportDo) ReadDB() *insSqlReportDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSqlReportDo) WriteDB() *insSqlReportDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSqlReportDo) Session(config *gorm.Session) *insSqlReportDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSqlReportDo) Clauses(conds ...clause.Expression) *insSqlReportDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSqlReportDo) Returning(value interface{}, columns ...string) *insSqlReportDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSqlReportDo) Not(conds ...gen.Condition) *insSqlReportDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSqlReportDo) Or(conds ...gen.Condition) *insSqlReportDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSqlReportDo) Select(conds ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSqlReportDo) Where(conds ...gen.Condition) *insSqlReportDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSqlReportDo) Order(conds ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSqlReportDo) Distinct(cols ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSqlReportDo) Omit(cols ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSqlReportDo) Join(table schema.Tabler, on ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSqlReportDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSqlReportDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSqlReportDo) Group(cols ...field.Expr) *insSqlReportDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSqlReportDo) Having(conds ...gen.Condition) *insSqlReportDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSqlReportDo) Limit(limit int) *insSqlReportDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSqlReportDo) Offset(offset int) *insSqlReportDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSqlReportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSqlReportDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSqlReportDo) Unscoped() *insSqlReportDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSqlReportDo) Create(values ...*insbuy.InsSqlReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSqlReportDo) CreateInBatches(values []*insbuy.InsSqlReport, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSqlReportDo) Save(values ...*insbuy.InsSqlReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSqlReportDo) First() (*insbuy.InsSqlReport, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReport), nil
	}
}

func (i insSqlReportDo) Take() (*insbuy.InsSqlReport, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReport), nil
	}
}

func (i insSqlReportDo) Last() (*insbuy.InsSqlReport, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReport), nil
	}
}

func (i insSqlReportDo) Find() ([]*insbuy.InsSqlReport, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSqlReport), err
}

func (i insSqlReportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSqlReport, err error) {
	buf := make([]*insbuy.InsSqlReport, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSqlReportDo) FindInBatches(result *[]*insbuy.InsSqlReport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSqlReportDo) Attrs(attrs ...field.AssignExpr) *insSqlReportDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSqlReportDo) Assign(attrs ...field.AssignExpr) *insSqlReportDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSqlReportDo) Joins(fields ...field.RelationField) *insSqlReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSqlReportDo) Preload(fields ...field.RelationField) *insSqlReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSqlReportDo) FirstOrInit() (*insbuy.InsSqlReport, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReport), nil
	}
}

func (i insSqlReportDo) FirstOrCreate() (*insbuy.InsSqlReport, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReport), nil
	}
}

func (i insSqlReportDo) FindByPage(offset int, limit int) (result []*insbuy.InsSqlReport, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSqlReportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSqlReportDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSqlReportDo) Delete(models ...*insbuy.InsSqlReport) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSqlReportDo) withDO(do gen.Dao) *insSqlReportDo {
	i.DO = *do.(*gen.DO)
	return i
}
