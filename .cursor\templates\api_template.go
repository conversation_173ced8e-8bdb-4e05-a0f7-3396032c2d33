package jyhapp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	commonReq "github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type {{ServiceName}}Api struct{}

// Create 创建
// @Tags      {{ServiceName}}
// @Summary   创建
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.{{ServiceName}}Create  true  "创建请求"
// @Success   200   {object}  response.Response{msg=string}  "创建响应"
// @Router    /{{service-name}}/create [post]
func (m *{{ServiceName}}Api) Create(c *gin.Context) {
	var req request.{{ServiceName}}Create
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = {{serviceName}}Service.Create(&req)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// Update 更新
// @Tags      {{ServiceName}}
// @Summary   更新
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.{{ServiceName}}Update  true  "更新请求"
// @Success   200   {object}  response.Response{msg=string}  "更新响应"
// @Router    /{{service-name}}/update [put]
func (m *{{ServiceName}}Api) Update(c *gin.Context) {
	var req request.{{ServiceName}}Update
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = {{serviceName}}Service.Update(&req)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// Delete 删除
// @Tags      {{ServiceName}}
// @Summary   删除
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      commonReq.GetById  true  "删除请求"
// @Success   200   {object}  response.Response{msg=string}  "删除响应"
// @Router    /{{service-name}}/delete [delete]
func (m *{{ServiceName}}Api) Delete(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = {{serviceName}}Service.Delete(req.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// GetDetail 获取详情
// @Tags      {{ServiceName}}
// @Summary   获取详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     commonReq.GetById  true  "获取详情请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取详情响应"
// @Router    /{{service-name}}/detail [get]
func (m *{{ServiceName}}Api) GetDetail(c *gin.Context) {
	var req commonReq.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	data, err := {{serviceName}}Service.GetDetail(req.ID)
	if err != nil {
		global.GVA_LOG.Error("获取详情失败!", zap.Error(err))
		response.FailWithMessage("获取详情失败:"+err.Error(), c)
		return
	}
	response.OkWithData(data, c)
}

// GetList 获取列表
// @Tags      {{ServiceName}}
// @Summary   获取列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     request.{{ServiceName}}Search  true  "获取列表请求"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取列表响应"
// @Router    /{{service-name}}/list [get]
func (m *{{ServiceName}}Api) GetList(c *gin.Context) {
	var req request.{{ServiceName}}Search
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := {{serviceName}}Service.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取列表失败!", zap.Error(err))
		response.FailWithMessage("获取列表失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取列表成功", c)
} 