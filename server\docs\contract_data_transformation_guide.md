# 合同数据转换系统使用指南

## 概述

合同数据转换系统是一个灵活、可配置的数据清洗和转换引擎，用于将飞书审批系统中的原始合同数据转换为标准化的业务数据格式。

## 系统架构

### 核心组件

1. **ContractTransformer** - 主转换引擎
2. **MappingConfigManager** - 映射配置管理器
3. **DataValidator** - 数据验证器接口
4. **DataProcessor** - 数据处理器接口
5. **StandardContractData** - 标准化数据结构

### 架构图

```
飞书合同数据 → ContractTransformer → 标准化合同数据
                      ↓
              MappingConfigManager
                      ↓
              [验证器] → [处理器]
```

## 快速开始

### 1. 基本使用

```go
package main

import (
    "context"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
)

func main() {
    // 创建转换器
    transformer := insbuy.NewContractTransformer()
    
    // 准备飞书合同数据
    contract := &insfinance.ContractDetails{
        ApprovalCode: "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
        ApprovalName: "销售合同审批",
        InstanceCode: "INST-2024-001",
        // ... 其他字段
    }
    
    // 执行转换
    result, err := transformer.Transform(context.Background(), contract)
    if err != nil {
        panic(err)
    }
    
    // 检查转换结果
    if result.Success {
        fmt.Printf("转换成功: %+v\n", result.Data)
    } else {
        fmt.Printf("转换失败: %v\n", result.Errors)
    }
}
```

### 2. 运行示例

```go
// 运行所有示例
err := insbuy.RunContractTransformerExamples()
if err != nil {
    log.Fatal(err)
}
```

## 配置管理

### 1. 映射规则配置

映射规则定义了如何将飞书审批数据的字段映射到标准化数据结构中。

#### YAML 格式配置示例

```yaml
# sales_contract.yaml
F523F053-7AC6-4280-A4E7-B35E0C0431B5:
  approval_code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
  approval_name: "销售合同审批"
  
  field_mappings:
    application_number:
      source_path: "basic.instance_code"
      target_field: "application_number"
      data_type: "string"
      required: true
      description: "申请编号"
    
    payment_reason:
      source_path: "form.field_001.value"
      target_field: "payment_reason"
      data_type: "string"
      required: true
      description: "付款事由"
  
  default_values:
    business_type: "销售合同"
    data_version: "1.0"
  
  required_fields:
    - "application_number"
    - "payment_reason"
```

#### JSON 格式配置示例

```json
{
  "C789D012-3456-7890-1234-56789ABCDEF0": {
    "approval_code": "C789D012-3456-7890-1234-56789ABCDEF0",
    "approval_name": "采购合同审批",
    "field_mappings": {
      "application_number": {
        "source_path": "basic.instance_code",
        "target_field": "application_number",
        "data_type": "string",
        "required": true,
        "description": "申请编号"
      }
    }
  }
}
```

### 2. 配置加载

```go
// 创建配置管理器
configMgr := insbuy.NewMappingConfigManager("./config/contract_mappings")

// 从文件加载
err := configMgr.LoadFromFile("sales_contract.yaml")

// 从目录加载所有配置
err := configMgr.LoadFromDirectory()

// 获取规则
rule, exists := configMgr.GetRule("F523F053-7AC6-4280-A4E7-B35E0C0431B5")
```

## 字段映射配置

### 1. 字段配置结构

```go
type FieldConfig struct {
    SourcePath    string      `json:"source_path"`    // 源字段路径
    TargetField   string      `json:"target_field"`   // 目标字段名
    DataType      string      `json:"data_type"`      // 数据类型
    DefaultValue  interface{} `json:"default_value"`  // 默认值
    Transform     string      `json:"transform"`      // 转换函数名
    Required      bool        `json:"required"`       // 是否必填
    Validation    string      `json:"validation"`     // 验证规则
    Description   string      `json:"description"`    // 字段描述
}
```

### 2. 支持的数据类型

- `string` - 字符串类型
- `int` - 整数类型
- `float` - 浮点数类型
- `bool` - 布尔类型
- `time` - 时间类型
- `array` - 数组类型

### 3. 源路径语法

支持嵌套对象和数组访问：

```yaml
# 基本字段访问
source_path: "basic.instance_code"

# 嵌套对象访问
source_path: "form.field_001.value"

# 数组元素访问
source_path: "timeline[0].create_time"

# 复杂嵌套访问
source_path: "form.field_list[0].options[1].text"
```

## 数据验证

### 1. 内置验证器

系统提供三个内置验证器：

- **RequiredFieldValidator** - 必填字段验证
- **DataFormatValidator** - 数据格式验证
- **BusinessLogicValidator** - 业务逻辑验证

### 2. 自定义验证器

```go
type CustomValidator struct{}

func (v *CustomValidator) Validate(data *insbuy.StandardContractData) []insbuy.ValidationError {
    var errors []insbuy.ValidationError
    
    // 自定义验证逻辑
    if data.ContractSignAmount > 1000000 {
        errors = append(errors, insbuy.ValidationError{
            Field:   "contract_sign_amount",
            Message: "合同金额超过限制",
            Value:   fmt.Sprintf("%.2f", data.ContractSignAmount),
        })
    }
    
    return errors
}

// 注册自定义验证器
transformer.validators = append(transformer.validators, &CustomValidator{})
```

## 数据处理

### 1. 内置处理器

- **TimelineProcessor** - 时间线处理器（计算审批耗时）
- **CurrencyProcessor** - 汇率转换处理器
- **ApprovalFlowProcessor** - 审批流程处理器

### 2. 自定义处理器

```go
type CustomProcessor struct{}

func (p *CustomProcessor) Process(data *insbuy.StandardContractData) error {
    // 自定义处理逻辑
    if data.PaymentCurrency == "USD" && data.UsdAmount > 0 {
        // 执行特殊的美元处理逻辑
        data.Remarks += " [USD处理]"
    }
    
    return nil
}

// 注册自定义处理器
transformer.processors = append(transformer.processors, &CustomProcessor{})
```

## 标准化数据结构

### 1. 完整字段列表

```go
type StandardContractData struct {
    // 基础信息
    ApplicationNumber string    `json:"application_number"`
    Title             string    `json:"title"`
    ApplicationStatus string    `json:"application_status"`
    InitiateTime      time.Time `json:"initiate_time"`
    CompleteTime      time.Time `json:"complete_time"`
    
    // 人员信息
    InitiatorEmployeeId   string `json:"initiator_employee_id"`
    InitiatorUserId       string `json:"initiator_user_id"`
    InitiatorName         string `json:"initiator_name"`
    InitiatorDepartment   string `json:"initiator_department"`
    InitiatorDepartmentId string `json:"initiator_department_id"`
    DepartmentManager     string `json:"department_manager"`
    
    // 审批流程
    HistoryApprovers    []string `json:"history_approvers"`
    HistoryHandlers     []string `json:"history_handlers"`
    ApprovalRecords     []string `json:"approval_records"`
    CurrentHandler      string   `json:"current_handler"`
    ApprovalNode        string   `json:"approval_node"`
    ApproverCount       int      `json:"approver_count"`
    ApprovalDuration    int64    `json:"approval_duration"`
    SerialNumber        string   `json:"serial_number"`
    
    // 业务字段
    PaymentReason     string `json:"payment_reason"`
    PaymentEntity     string `json:"payment_entity"`
    BusinessType      string `json:"business_type"`
    ProjectType       string `json:"project_type"`
    MusicStore        string `json:"music_store"`
    PurchaseType      string `json:"purchase_type"`
    PurchaseStoreName string `json:"purchase_store_name"`
    ExpenseDepartment string `json:"expense_department"`
    
    // 金额信息
    PaymentCurrency      string  `json:"payment_currency"`
    UsdAmount            float64 `json:"usd_amount"`
    EurAmount            float64 `json:"eur_amount"`
    JpyAmount            float64 `json:"jpy_amount"`
    HkdAmount            float64 `json:"hkd_amount"`
    GbpAmount            float64 `json:"gbp_amount"`
    UsdToRmbAmount       float64 `json:"usd_to_rmb_amount"`
    EurToRmbAmount       float64 `json:"eur_to_rmb_amount"`
    JpyToRmbAmount       float64 `json:"jpy_to_rmb_amount"`
    HkdToRmbAmount       float64 `json:"hkd_to_rmb_amount"`
    GbpToRmbAmount       float64 `json:"gbp_to_rmb_amount"`
    ContractSignAmount   float64 `json:"contract_sign_amount"`
    ContractPaidAmount   float64 `json:"contract_paid_amount"`
    CurrentRequestAmount float64 `json:"current_request_amount"`
    
    // 财务信息
    VatInvoiceType       string  `json:"vat_invoice_type"`
    TaxRate              float64 `json:"tax_rate"`
    AmountExcludingTax   float64 `json:"amount_excluding_tax"`
    ContractPaymentTerms string  `json:"contract_payment_terms"`
    
    // 银行信息
    AccountType       string `json:"account_type"`
    BankName          string `json:"bank_name"`
    AccountHolder     string `json:"account_holder"`
    AccountNumber     string `json:"account_number"`
    BankBranch        string `json:"bank_branch"`
    BankRegion        string `json:"bank_region"`
    SwiftCode         string `json:"swift_code"`
    InterBankNumber   string `json:"inter_bank_number"`
    
    // 其他
    ExpectedPaymentDate time.Time `json:"expected_payment_date"`
    Remarks             string    `json:"remarks"`
    Attachments         []string  `json:"attachments"`
    
    // 元数据
    SourceInstanceCode string    `json:"source_instance_code"`
    ApprovalCode       string    `json:"approval_code"`
    TransformTime      time.Time `json:"transform_time"`
    DataVersion        string    `json:"data_version"`
}
```

## 最佳实践

### 1. 配置管理

- 为每种合同类型创建独立的配置文件
- 使用有意义的文件名（如 `sales_contract.yaml`）
- 定期备份配置文件
- 使用版本控制管理配置变更

### 2. 字段映射

- 优先使用描述性的字段路径
- 为所有字段添加描述信息
- 合理设置默认值
- 明确标识必填字段

### 3. 数据验证

- 实现业务相关的自定义验证器
- 提供清晰的错误消息
- 记录验证失败的详细信息

### 4. 错误处理

- 记录详细的转换日志
- 区分警告和错误
- 提供数据修复建议

### 5. 性能优化

- 批量处理多个合同数据
- 缓存常用的映射规则
- 异步处理大量数据

## 故障排除

### 1. 常见问题

**问题**: 字段映射失败
**解决**: 检查源路径是否正确，确认数据结构

**问题**: 数据类型转换错误
**解决**: 验证源数据格式，调整转换逻辑

**问题**: 验证失败
**解决**: 检查验证规则配置，确认数据完整性

### 2. 调试技巧

- 启用详细日志记录
- 使用示例数据进行测试
- 逐步验证每个转换步骤
- 检查配置文件语法

## 扩展开发

### 1. 添加新的合同类型

1. 创建新的配置文件
2. 定义字段映射规则
3. 添加特定的验证逻辑
4. 测试转换效果

### 2. 自定义转换函数

```go
// 在 FieldConfig 中指定转换函数
transform: "customTransformFunction"

// 实现转换函数
func customTransformFunction(value interface{}) interface{} {
    // 自定义转换逻辑
    return transformedValue
}
```

### 3. 集成外部服务

- 汇率API集成
- 用户信息查询
- 部门信息获取
- 审批流程追踪

## 总结

合同数据转换系统提供了一个灵活、可扩展的解决方案，用于处理复杂的数据转换需求。通过配置化的映射规则、可插拔的验证器和处理器，系统能够适应不同类型的合同审批数据，确保数据转换的准确性和完整性。
