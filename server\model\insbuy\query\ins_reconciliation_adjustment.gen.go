// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReconciliationAdjustment(db *gorm.DB, opts ...gen.DOOption) insReconciliationAdjustment {
	_insReconciliationAdjustment := insReconciliationAdjustment{}

	_insReconciliationAdjustment.insReconciliationAdjustmentDo.UseDB(db, opts...)
	_insReconciliationAdjustment.insReconciliationAdjustmentDo.UseModel(&insbuy.InsReconciliationAdjustment{})

	tableName := _insReconciliationAdjustment.insReconciliationAdjustmentDo.TableName()
	_insReconciliationAdjustment.ALL = field.NewAsterisk(tableName)
	_insReconciliationAdjustment.ID = field.NewUint(tableName, "id")
	_insReconciliationAdjustment.CreatedAt = field.NewTime(tableName, "created_at")
	_insReconciliationAdjustment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReconciliationAdjustment.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReconciliationAdjustment.StoreId = field.NewUint(tableName, "store_id")
	_insReconciliationAdjustment.ResultId = field.NewUint64(tableName, "result_id")
	_insReconciliationAdjustment.Amount = field.NewFloat64(tableName, "amount")
	_insReconciliationAdjustment.Reason = field.NewString(tableName, "reason")
	_insReconciliationAdjustment.OperatorId = field.NewUint(tableName, "operator_id")

	_insReconciliationAdjustment.fillFieldMap()

	return _insReconciliationAdjustment
}

type insReconciliationAdjustment struct {
	insReconciliationAdjustmentDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StoreId    field.Uint
	ResultId   field.Uint64
	Amount     field.Float64
	Reason     field.String
	OperatorId field.Uint

	fieldMap map[string]field.Expr
}

func (i insReconciliationAdjustment) Table(newTableName string) *insReconciliationAdjustment {
	i.insReconciliationAdjustmentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReconciliationAdjustment) As(alias string) *insReconciliationAdjustment {
	i.insReconciliationAdjustmentDo.DO = *(i.insReconciliationAdjustmentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReconciliationAdjustment) updateTableName(table string) *insReconciliationAdjustment {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.ResultId = field.NewUint64(table, "result_id")
	i.Amount = field.NewFloat64(table, "amount")
	i.Reason = field.NewString(table, "reason")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insReconciliationAdjustment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReconciliationAdjustment) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["result_id"] = i.ResultId
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["reason"] = i.Reason
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insReconciliationAdjustment) clone(db *gorm.DB) insReconciliationAdjustment {
	i.insReconciliationAdjustmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReconciliationAdjustment) replaceDB(db *gorm.DB) insReconciliationAdjustment {
	i.insReconciliationAdjustmentDo.ReplaceDB(db)
	return i
}

type insReconciliationAdjustmentDo struct{ gen.DO }

func (i insReconciliationAdjustmentDo) Debug() *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Debug())
}

func (i insReconciliationAdjustmentDo) WithContext(ctx context.Context) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReconciliationAdjustmentDo) ReadDB() *insReconciliationAdjustmentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReconciliationAdjustmentDo) WriteDB() *insReconciliationAdjustmentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReconciliationAdjustmentDo) Session(config *gorm.Session) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReconciliationAdjustmentDo) Clauses(conds ...clause.Expression) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReconciliationAdjustmentDo) Returning(value interface{}, columns ...string) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReconciliationAdjustmentDo) Not(conds ...gen.Condition) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReconciliationAdjustmentDo) Or(conds ...gen.Condition) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReconciliationAdjustmentDo) Select(conds ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReconciliationAdjustmentDo) Where(conds ...gen.Condition) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReconciliationAdjustmentDo) Order(conds ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReconciliationAdjustmentDo) Distinct(cols ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReconciliationAdjustmentDo) Omit(cols ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReconciliationAdjustmentDo) Join(table schema.Tabler, on ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReconciliationAdjustmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReconciliationAdjustmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReconciliationAdjustmentDo) Group(cols ...field.Expr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReconciliationAdjustmentDo) Having(conds ...gen.Condition) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReconciliationAdjustmentDo) Limit(limit int) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReconciliationAdjustmentDo) Offset(offset int) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReconciliationAdjustmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReconciliationAdjustmentDo) Unscoped() *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReconciliationAdjustmentDo) Create(values ...*insbuy.InsReconciliationAdjustment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReconciliationAdjustmentDo) CreateInBatches(values []*insbuy.InsReconciliationAdjustment, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReconciliationAdjustmentDo) Save(values ...*insbuy.InsReconciliationAdjustment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReconciliationAdjustmentDo) First() (*insbuy.InsReconciliationAdjustment, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationAdjustment), nil
	}
}

func (i insReconciliationAdjustmentDo) Take() (*insbuy.InsReconciliationAdjustment, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationAdjustment), nil
	}
}

func (i insReconciliationAdjustmentDo) Last() (*insbuy.InsReconciliationAdjustment, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationAdjustment), nil
	}
}

func (i insReconciliationAdjustmentDo) Find() ([]*insbuy.InsReconciliationAdjustment, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReconciliationAdjustment), err
}

func (i insReconciliationAdjustmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReconciliationAdjustment, err error) {
	buf := make([]*insbuy.InsReconciliationAdjustment, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReconciliationAdjustmentDo) FindInBatches(result *[]*insbuy.InsReconciliationAdjustment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReconciliationAdjustmentDo) Attrs(attrs ...field.AssignExpr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReconciliationAdjustmentDo) Assign(attrs ...field.AssignExpr) *insReconciliationAdjustmentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReconciliationAdjustmentDo) Joins(fields ...field.RelationField) *insReconciliationAdjustmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReconciliationAdjustmentDo) Preload(fields ...field.RelationField) *insReconciliationAdjustmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReconciliationAdjustmentDo) FirstOrInit() (*insbuy.InsReconciliationAdjustment, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationAdjustment), nil
	}
}

func (i insReconciliationAdjustmentDo) FirstOrCreate() (*insbuy.InsReconciliationAdjustment, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationAdjustment), nil
	}
}

func (i insReconciliationAdjustmentDo) FindByPage(offset int, limit int) (result []*insbuy.InsReconciliationAdjustment, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReconciliationAdjustmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReconciliationAdjustmentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReconciliationAdjustmentDo) Delete(models ...*insbuy.InsReconciliationAdjustment) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReconciliationAdjustmentDo) withDO(do gen.Dao) *insReconciliationAdjustmentDo {
	i.DO = *do.(*gen.DO)
	return i
}
