// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderInfoDetails(db *gorm.DB, opts ...gen.DOOption) insOrderInfoDetails {
	_insOrderInfoDetails := insOrderInfoDetails{}

	_insOrderInfoDetails.insOrderInfoDetailsDo.UseDB(db, opts...)
	_insOrderInfoDetails.insOrderInfoDetailsDo.UseModel(&insbuy.InsOrderInfoDetails{})

	tableName := _insOrderInfoDetails.insOrderInfoDetailsDo.TableName()
	_insOrderInfoDetails.ALL = field.NewAsterisk(tableName)
	_insOrderInfoDetails.ID = field.NewUint(tableName, "id")
	_insOrderInfoDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderInfoDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderInfoDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insOrderInfoDetails.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderInfoDetails.OrderStatus = field.NewUint(tableName, "order_status")
	_insOrderInfoDetails.DiscountPrice = field.NewFloat64(tableName, "discount_price")
	_insOrderInfoDetails.RealPrice = field.NewFloat64(tableName, "real_price")
	_insOrderInfoDetails.Discount = field.NewFloat64(tableName, "discount")
	_insOrderInfoDetails.DiscountFee = field.NewFloat64(tableName, "discount_fee")
	_insOrderInfoDetails.CouponFee = field.NewFloat64(tableName, "coupon_fee")
	_insOrderInfoDetails.PlayerFee = field.NewFloat64(tableName, "player_fee")
	_insOrderInfoDetails.ErasePrice = field.NewFloat64(tableName, "erase_price")
	_insOrderInfoDetails.ServiceFee = field.NewFloat64(tableName, "service_fee")
	_insOrderInfoDetails.RealTotalPrice = field.NewFloat64(tableName, "real_total_price")
	_insOrderInfoDetails.DiscountTotalFee = field.NewFloat64(tableName, "discount_total_fee")
	_insOrderInfoDetails.CouponTotalPrice = field.NewFloat64(tableName, "coupon_total_price")
	_insOrderInfoDetails.PlayerTotalPrice = field.NewFloat64(tableName, "player_total_price")
	_insOrderInfoDetails.EraseTotalPrice = field.NewFloat64(tableName, "erase_total_price")
	_insOrderInfoDetails.ServiceTotalFee = field.NewFloat64(tableName, "service_total_fee")
	_insOrderInfoDetails.ProductPrice = field.NewFloat64(tableName, "product_price")
	_insOrderInfoDetails.OriginalPrice = field.NewFloat64(tableName, "original_price")
	_insOrderInfoDetails.GivePrice = field.NewFloat64(tableName, "give_price")
	_insOrderInfoDetails.ShipmentStatus = field.NewInt(tableName, "shipment_status")
	_insOrderInfoDetails.Remark = field.NewString(tableName, "remark")
	_insOrderInfoDetails.RemarkExt = field.NewField(tableName, "remark_ext")
	_insOrderInfoDetails.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insOrderInfoDetails.OrderType = field.NewInt(tableName, "order_type")
	_insOrderInfoDetails.ProductName = field.NewString(tableName, "product_name")
	_insOrderInfoDetails.Nums = field.NewInt(tableName, "nums")
	_insOrderInfoDetails.ReturnNum = field.NewInt(tableName, "return_num")
	_insOrderInfoDetails.Unit = field.NewInt(tableName, "unit")
	_insOrderInfoDetails.ProductId = field.NewUint(tableName, "product_id")
	_insOrderInfoDetails.PackageId = field.NewUint(tableName, "package_id")
	_insOrderInfoDetails.OptionItems = field.NewString(tableName, "option_items")
	_insOrderInfoDetails.IsPayOrder = field.NewInt(tableName, "is_pay_order")
	_insOrderInfoDetails.IsPay = field.NewInt(tableName, "is_pay")
	_insOrderInfoDetails.BillId = field.NewInt64(tableName, "bill_id")
	_insOrderInfoDetails.TradeId = field.NewUint64(tableName, "trade_id")
	_insOrderInfoDetails.GiveId = field.NewUint(tableName, "give_id")
	_insOrderInfoDetails.GiftUserId = field.NewUint(tableName, "gift_user_id")
	_insOrderInfoDetails.WaiterId = field.NewUint(tableName, "waiter_id")
	_insOrderInfoDetails.CashierId = field.NewUint(tableName, "cashier_id")
	_insOrderInfoDetails.BusinessDay = field.NewTime(tableName, "business_day")
	_insOrderInfoDetails.EntertainNum = field.NewFloat64(tableName, "entertain_num")
	_insOrderInfoDetails.IsAbnormal = field.NewInt(tableName, "is_abnormal")
	_insOrderInfoDetails.OrderPackage = insOrderInfoDetailsHasManyOrderPackage{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("OrderPackage", "insbuy.InsOrderInfoPackage"),
	}

	_insOrderInfoDetails.fillFieldMap()

	return _insOrderInfoDetails
}

type insOrderInfoDetails struct {
	insOrderInfoDetailsDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	OrderId          field.Uint64
	OrderStatus      field.Uint
	DiscountPrice    field.Float64
	RealPrice        field.Float64
	Discount         field.Float64
	DiscountFee      field.Float64
	CouponFee        field.Float64
	PlayerFee        field.Float64
	ErasePrice       field.Float64
	ServiceFee       field.Float64
	RealTotalPrice   field.Float64
	DiscountTotalFee field.Float64
	CouponTotalPrice field.Float64
	PlayerTotalPrice field.Float64
	EraseTotalPrice  field.Float64
	ServiceTotalFee  field.Float64
	ProductPrice     field.Float64
	OriginalPrice    field.Float64
	GivePrice        field.Float64
	ShipmentStatus   field.Int
	Remark           field.String
	RemarkExt        field.Field
	WarehouseId      field.Int
	OrderType        field.Int
	ProductName      field.String
	Nums             field.Int
	ReturnNum        field.Int
	Unit             field.Int
	ProductId        field.Uint
	PackageId        field.Uint
	OptionItems      field.String
	IsPayOrder       field.Int
	IsPay            field.Int
	BillId           field.Int64
	TradeId          field.Uint64
	GiveId           field.Uint
	GiftUserId       field.Uint
	WaiterId         field.Uint
	CashierId        field.Uint
	BusinessDay      field.Time
	EntertainNum     field.Float64
	IsAbnormal       field.Int
	OrderPackage     insOrderInfoDetailsHasManyOrderPackage

	fieldMap map[string]field.Expr
}

func (i insOrderInfoDetails) Table(newTableName string) *insOrderInfoDetails {
	i.insOrderInfoDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderInfoDetails) As(alias string) *insOrderInfoDetails {
	i.insOrderInfoDetailsDo.DO = *(i.insOrderInfoDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderInfoDetails) updateTableName(table string) *insOrderInfoDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderStatus = field.NewUint(table, "order_status")
	i.DiscountPrice = field.NewFloat64(table, "discount_price")
	i.RealPrice = field.NewFloat64(table, "real_price")
	i.Discount = field.NewFloat64(table, "discount")
	i.DiscountFee = field.NewFloat64(table, "discount_fee")
	i.CouponFee = field.NewFloat64(table, "coupon_fee")
	i.PlayerFee = field.NewFloat64(table, "player_fee")
	i.ErasePrice = field.NewFloat64(table, "erase_price")
	i.ServiceFee = field.NewFloat64(table, "service_fee")
	i.RealTotalPrice = field.NewFloat64(table, "real_total_price")
	i.DiscountTotalFee = field.NewFloat64(table, "discount_total_fee")
	i.CouponTotalPrice = field.NewFloat64(table, "coupon_total_price")
	i.PlayerTotalPrice = field.NewFloat64(table, "player_total_price")
	i.EraseTotalPrice = field.NewFloat64(table, "erase_total_price")
	i.ServiceTotalFee = field.NewFloat64(table, "service_total_fee")
	i.ProductPrice = field.NewFloat64(table, "product_price")
	i.OriginalPrice = field.NewFloat64(table, "original_price")
	i.GivePrice = field.NewFloat64(table, "give_price")
	i.ShipmentStatus = field.NewInt(table, "shipment_status")
	i.Remark = field.NewString(table, "remark")
	i.RemarkExt = field.NewField(table, "remark_ext")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.OrderType = field.NewInt(table, "order_type")
	i.ProductName = field.NewString(table, "product_name")
	i.Nums = field.NewInt(table, "nums")
	i.ReturnNum = field.NewInt(table, "return_num")
	i.Unit = field.NewInt(table, "unit")
	i.ProductId = field.NewUint(table, "product_id")
	i.PackageId = field.NewUint(table, "package_id")
	i.OptionItems = field.NewString(table, "option_items")
	i.IsPayOrder = field.NewInt(table, "is_pay_order")
	i.IsPay = field.NewInt(table, "is_pay")
	i.BillId = field.NewInt64(table, "bill_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.GiveId = field.NewUint(table, "give_id")
	i.GiftUserId = field.NewUint(table, "gift_user_id")
	i.WaiterId = field.NewUint(table, "waiter_id")
	i.CashierId = field.NewUint(table, "cashier_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.EntertainNum = field.NewFloat64(table, "entertain_num")
	i.IsAbnormal = field.NewInt(table, "is_abnormal")

	i.fillFieldMap()

	return i
}

func (i *insOrderInfoDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderInfoDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 47)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_status"] = i.OrderStatus
	i.fieldMap["discount_price"] = i.DiscountPrice
	i.fieldMap["real_price"] = i.RealPrice
	i.fieldMap["discount"] = i.Discount
	i.fieldMap["discount_fee"] = i.DiscountFee
	i.fieldMap["coupon_fee"] = i.CouponFee
	i.fieldMap["player_fee"] = i.PlayerFee
	i.fieldMap["erase_price"] = i.ErasePrice
	i.fieldMap["service_fee"] = i.ServiceFee
	i.fieldMap["real_total_price"] = i.RealTotalPrice
	i.fieldMap["discount_total_fee"] = i.DiscountTotalFee
	i.fieldMap["coupon_total_price"] = i.CouponTotalPrice
	i.fieldMap["player_total_price"] = i.PlayerTotalPrice
	i.fieldMap["erase_total_price"] = i.EraseTotalPrice
	i.fieldMap["service_total_fee"] = i.ServiceTotalFee
	i.fieldMap["product_price"] = i.ProductPrice
	i.fieldMap["original_price"] = i.OriginalPrice
	i.fieldMap["give_price"] = i.GivePrice
	i.fieldMap["shipment_status"] = i.ShipmentStatus
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["remark_ext"] = i.RemarkExt
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["order_type"] = i.OrderType
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["nums"] = i.Nums
	i.fieldMap["return_num"] = i.ReturnNum
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["option_items"] = i.OptionItems
	i.fieldMap["is_pay_order"] = i.IsPayOrder
	i.fieldMap["is_pay"] = i.IsPay
	i.fieldMap["bill_id"] = i.BillId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["give_id"] = i.GiveId
	i.fieldMap["gift_user_id"] = i.GiftUserId
	i.fieldMap["waiter_id"] = i.WaiterId
	i.fieldMap["cashier_id"] = i.CashierId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["entertain_num"] = i.EntertainNum
	i.fieldMap["is_abnormal"] = i.IsAbnormal

}

func (i insOrderInfoDetails) clone(db *gorm.DB) insOrderInfoDetails {
	i.insOrderInfoDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderInfoDetails) replaceDB(db *gorm.DB) insOrderInfoDetails {
	i.insOrderInfoDetailsDo.ReplaceDB(db)
	return i
}

type insOrderInfoDetailsHasManyOrderPackage struct {
	db *gorm.DB

	field.RelationField
}

func (a insOrderInfoDetailsHasManyOrderPackage) Where(conds ...field.Expr) *insOrderInfoDetailsHasManyOrderPackage {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insOrderInfoDetailsHasManyOrderPackage) WithContext(ctx context.Context) *insOrderInfoDetailsHasManyOrderPackage {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insOrderInfoDetailsHasManyOrderPackage) Session(session *gorm.Session) *insOrderInfoDetailsHasManyOrderPackage {
	a.db = a.db.Session(session)
	return &a
}

func (a insOrderInfoDetailsHasManyOrderPackage) Model(m *insbuy.InsOrderInfoDetails) *insOrderInfoDetailsHasManyOrderPackageTx {
	return &insOrderInfoDetailsHasManyOrderPackageTx{a.db.Model(m).Association(a.Name())}
}

type insOrderInfoDetailsHasManyOrderPackageTx struct{ tx *gorm.Association }

func (a insOrderInfoDetailsHasManyOrderPackageTx) Find() (result []*insbuy.InsOrderInfoPackage, err error) {
	return result, a.tx.Find(&result)
}

func (a insOrderInfoDetailsHasManyOrderPackageTx) Append(values ...*insbuy.InsOrderInfoPackage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insOrderInfoDetailsHasManyOrderPackageTx) Replace(values ...*insbuy.InsOrderInfoPackage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insOrderInfoDetailsHasManyOrderPackageTx) Delete(values ...*insbuy.InsOrderInfoPackage) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insOrderInfoDetailsHasManyOrderPackageTx) Clear() error {
	return a.tx.Clear()
}

func (a insOrderInfoDetailsHasManyOrderPackageTx) Count() int64 {
	return a.tx.Count()
}

type insOrderInfoDetailsDo struct{ gen.DO }

func (i insOrderInfoDetailsDo) Debug() *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderInfoDetailsDo) WithContext(ctx context.Context) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderInfoDetailsDo) ReadDB() *insOrderInfoDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderInfoDetailsDo) WriteDB() *insOrderInfoDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderInfoDetailsDo) Session(config *gorm.Session) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderInfoDetailsDo) Clauses(conds ...clause.Expression) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderInfoDetailsDo) Returning(value interface{}, columns ...string) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderInfoDetailsDo) Not(conds ...gen.Condition) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderInfoDetailsDo) Or(conds ...gen.Condition) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderInfoDetailsDo) Select(conds ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderInfoDetailsDo) Where(conds ...gen.Condition) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderInfoDetailsDo) Order(conds ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderInfoDetailsDo) Distinct(cols ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderInfoDetailsDo) Omit(cols ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderInfoDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderInfoDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderInfoDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderInfoDetailsDo) Group(cols ...field.Expr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderInfoDetailsDo) Having(conds ...gen.Condition) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderInfoDetailsDo) Limit(limit int) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderInfoDetailsDo) Offset(offset int) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderInfoDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderInfoDetailsDo) Unscoped() *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderInfoDetailsDo) Create(values ...*insbuy.InsOrderInfoDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderInfoDetailsDo) CreateInBatches(values []*insbuy.InsOrderInfoDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderInfoDetailsDo) Save(values ...*insbuy.InsOrderInfoDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderInfoDetailsDo) First() (*insbuy.InsOrderInfoDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoDetails), nil
	}
}

func (i insOrderInfoDetailsDo) Take() (*insbuy.InsOrderInfoDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoDetails), nil
	}
}

func (i insOrderInfoDetailsDo) Last() (*insbuy.InsOrderInfoDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoDetails), nil
	}
}

func (i insOrderInfoDetailsDo) Find() ([]*insbuy.InsOrderInfoDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderInfoDetails), err
}

func (i insOrderInfoDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderInfoDetails, err error) {
	buf := make([]*insbuy.InsOrderInfoDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderInfoDetailsDo) FindInBatches(result *[]*insbuy.InsOrderInfoDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderInfoDetailsDo) Attrs(attrs ...field.AssignExpr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderInfoDetailsDo) Assign(attrs ...field.AssignExpr) *insOrderInfoDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderInfoDetailsDo) Joins(fields ...field.RelationField) *insOrderInfoDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderInfoDetailsDo) Preload(fields ...field.RelationField) *insOrderInfoDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderInfoDetailsDo) FirstOrInit() (*insbuy.InsOrderInfoDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoDetails), nil
	}
}

func (i insOrderInfoDetailsDo) FirstOrCreate() (*insbuy.InsOrderInfoDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoDetails), nil
	}
}

func (i insOrderInfoDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderInfoDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderInfoDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderInfoDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderInfoDetailsDo) Delete(models ...*insbuy.InsOrderInfoDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderInfoDetailsDo) withDO(do gen.Dao) *insOrderInfoDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
