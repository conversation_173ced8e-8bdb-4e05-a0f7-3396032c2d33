package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreatePackageItemTemplate 创建InsProductPackageItemTemplate
// @Tags InsProductPackageItemTemplate
// @Summary 创建InsProductPackageItemTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PackageItemTemplateReq true "创建InsProductPackageItemTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createPackageItemTemplate [post]
func (insProductApi *InsProductApi) CreatePackageItemTemplate(c *gin.Context) {
	var req insbuyReq.PackageItemTemplateReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsProductPackageItemTemplate(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeletePackageItemTemplate 删除InsProductPackageItemTemplate
// @Tags InsProductPackageItemTemplate
// @Summary 删除InsProductPackageItemTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsProductPackageItemTemplate true "删除InsProductPackageItemTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insProduct/deletePackageItemTemplate [delete]
func (insProductApi *InsProductApi) DeletePackageItemTemplate(c *gin.Context) {
	var insProductPackageItemTemplate insbuy.InsProductPackageItemTemplate
	err := c.ShouldBindJSON(&insProductPackageItemTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductPackageItemTemplate(insProductPackageItemTemplate); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeletePackageItemTemplateByIds 批量删除InsProductPackageItemTemplate
// @Tags InsProductPackageItemTemplate
// @Summary 批量删除InsProductPackageItemTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsProductPackageItemTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insProduct/deletePackageItemTemplateByIds [delete]
func (insProductApi *InsProductApi) DeletePackageItemTemplateByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductPackageItemTemplateByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdatePackageItemTemplate 更新InsProductPackageItemTemplate
// @Tags InsProductPackageItemTemplate
// @Summary 更新InsProductPackageItemTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PackageItemTemplateReq true "更新InsProductPackageItemTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updatePackageItemTemplate [put]
func (insProductApi *InsProductApi) UpdatePackageItemTemplate(c *gin.Context) {
	var req insbuyReq.PackageItemTemplateReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductPackageItemTemplate(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindPackageItemTemplate 用id查询InsProductPackageItemTemplate
// @Tags InsProductPackageItemTemplate
// @Summary 用id查询InsProductPackageItemTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsProductPackageItemTemplate true "用id查询InsProductPackageItemTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findPackageItemTemplate [get]
func (insProductApi *InsProductApi) FindPackageItemTemplate(c *gin.Context) {
	var req insbuyReq.PackageItemTemplateId
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insProductService.GetInsProductPackageItemTemplate(req.GetById.Uint()); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(res, c)
	}
}

// GetPackageItemTemplateList 分页获取InsProductPackageItemTemplate列表
// @Tags InsProductPackageItemTemplate
// @Summary 分页获取InsProductPackageItemTemplate列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductPackageItemTemplateSearch true "分页获取InsProductPackageItemTemplate列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getPackageItemTemplateList [get]
func (insProductApi *InsProductApi) GetPackageItemTemplateList(c *gin.Context) {
	var req insbuyReq.InsProductPackageItemTemplateSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insProductService.GetInsProductPackageItemTemplateInfoList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}
