// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSqlRuleHasReport(db *gorm.DB, opts ...gen.DOOption) insSqlRuleHasReport {
	_insSqlRuleHasReport := insSqlRuleHasReport{}

	_insSqlRuleHasReport.insSqlRuleHasReportDo.UseDB(db, opts...)
	_insSqlRuleHasReport.insSqlRuleHasReportDo.UseModel(&insbuy.InsSqlRuleHasReport{})

	tableName := _insSqlRuleHasReport.insSqlRuleHasReportDo.TableName()
	_insSqlRuleHasReport.ALL = field.NewAsterisk(tableName)
	_insSqlRuleHasReport.ID = field.NewUint(tableName, "id")
	_insSqlRuleHasReport.CreatedAt = field.NewTime(tableName, "created_at")
	_insSqlRuleHasReport.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSqlRuleHasReport.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSqlRuleHasReport.RuleId = field.NewUint(tableName, "rule_id")
	_insSqlRuleHasReport.ReportId = field.NewUint(tableName, "report_id")

	_insSqlRuleHasReport.fillFieldMap()

	return _insSqlRuleHasReport
}

type insSqlRuleHasReport struct {
	insSqlRuleHasReportDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	RuleId    field.Uint
	ReportId  field.Uint

	fieldMap map[string]field.Expr
}

func (i insSqlRuleHasReport) Table(newTableName string) *insSqlRuleHasReport {
	i.insSqlRuleHasReportDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSqlRuleHasReport) As(alias string) *insSqlRuleHasReport {
	i.insSqlRuleHasReportDo.DO = *(i.insSqlRuleHasReportDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSqlRuleHasReport) updateTableName(table string) *insSqlRuleHasReport {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.RuleId = field.NewUint(table, "rule_id")
	i.ReportId = field.NewUint(table, "report_id")

	i.fillFieldMap()

	return i
}

func (i *insSqlRuleHasReport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSqlRuleHasReport) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["report_id"] = i.ReportId
}

func (i insSqlRuleHasReport) clone(db *gorm.DB) insSqlRuleHasReport {
	i.insSqlRuleHasReportDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSqlRuleHasReport) replaceDB(db *gorm.DB) insSqlRuleHasReport {
	i.insSqlRuleHasReportDo.ReplaceDB(db)
	return i
}

type insSqlRuleHasReportDo struct{ gen.DO }

func (i insSqlRuleHasReportDo) Debug() *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Debug())
}

func (i insSqlRuleHasReportDo) WithContext(ctx context.Context) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSqlRuleHasReportDo) ReadDB() *insSqlRuleHasReportDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSqlRuleHasReportDo) WriteDB() *insSqlRuleHasReportDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSqlRuleHasReportDo) Session(config *gorm.Session) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSqlRuleHasReportDo) Clauses(conds ...clause.Expression) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSqlRuleHasReportDo) Returning(value interface{}, columns ...string) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSqlRuleHasReportDo) Not(conds ...gen.Condition) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSqlRuleHasReportDo) Or(conds ...gen.Condition) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSqlRuleHasReportDo) Select(conds ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSqlRuleHasReportDo) Where(conds ...gen.Condition) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSqlRuleHasReportDo) Order(conds ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSqlRuleHasReportDo) Distinct(cols ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSqlRuleHasReportDo) Omit(cols ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSqlRuleHasReportDo) Join(table schema.Tabler, on ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSqlRuleHasReportDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSqlRuleHasReportDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSqlRuleHasReportDo) Group(cols ...field.Expr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSqlRuleHasReportDo) Having(conds ...gen.Condition) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSqlRuleHasReportDo) Limit(limit int) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSqlRuleHasReportDo) Offset(offset int) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSqlRuleHasReportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSqlRuleHasReportDo) Unscoped() *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSqlRuleHasReportDo) Create(values ...*insbuy.InsSqlRuleHasReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSqlRuleHasReportDo) CreateInBatches(values []*insbuy.InsSqlRuleHasReport, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSqlRuleHasReportDo) Save(values ...*insbuy.InsSqlRuleHasReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSqlRuleHasReportDo) First() (*insbuy.InsSqlRuleHasReport, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasReport), nil
	}
}

func (i insSqlRuleHasReportDo) Take() (*insbuy.InsSqlRuleHasReport, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasReport), nil
	}
}

func (i insSqlRuleHasReportDo) Last() (*insbuy.InsSqlRuleHasReport, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasReport), nil
	}
}

func (i insSqlRuleHasReportDo) Find() ([]*insbuy.InsSqlRuleHasReport, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSqlRuleHasReport), err
}

func (i insSqlRuleHasReportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSqlRuleHasReport, err error) {
	buf := make([]*insbuy.InsSqlRuleHasReport, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSqlRuleHasReportDo) FindInBatches(result *[]*insbuy.InsSqlRuleHasReport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSqlRuleHasReportDo) Attrs(attrs ...field.AssignExpr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSqlRuleHasReportDo) Assign(attrs ...field.AssignExpr) *insSqlRuleHasReportDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSqlRuleHasReportDo) Joins(fields ...field.RelationField) *insSqlRuleHasReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSqlRuleHasReportDo) Preload(fields ...field.RelationField) *insSqlRuleHasReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSqlRuleHasReportDo) FirstOrInit() (*insbuy.InsSqlRuleHasReport, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasReport), nil
	}
}

func (i insSqlRuleHasReportDo) FirstOrCreate() (*insbuy.InsSqlRuleHasReport, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasReport), nil
	}
}

func (i insSqlRuleHasReportDo) FindByPage(offset int, limit int) (result []*insbuy.InsSqlRuleHasReport, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSqlRuleHasReportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSqlRuleHasReportDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSqlRuleHasReportDo) Delete(models ...*insbuy.InsSqlRuleHasReport) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSqlRuleHasReportDo) withDO(do gen.Dao) *insSqlRuleHasReportDo {
	i.DO = *do.(*gen.DO)
	return i
}
