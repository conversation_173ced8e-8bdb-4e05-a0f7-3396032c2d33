// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseSaleLog(db *gorm.DB, opts ...gen.DOOption) insWarehouseSaleLog {
	_insWarehouseSaleLog := insWarehouseSaleLog{}

	_insWarehouseSaleLog.insWarehouseSaleLogDo.UseDB(db, opts...)
	_insWarehouseSaleLog.insWarehouseSaleLogDo.UseModel(&insbuy.InsWarehouseSaleLog{})

	tableName := _insWarehouseSaleLog.insWarehouseSaleLogDo.TableName()
	_insWarehouseSaleLog.ALL = field.NewAsterisk(tableName)
	_insWarehouseSaleLog.ID = field.NewUint(tableName, "id")
	_insWarehouseSaleLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseSaleLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseSaleLog.StoreId = field.NewUint(tableName, "store_id")
	_insWarehouseSaleLog.OrderType = field.NewInt(tableName, "order_type")
	_insWarehouseSaleLog.IsPackage = field.NewInt(tableName, "is_package")
	_insWarehouseSaleLog.IsSub = field.NewInt(tableName, "is_sub")
	_insWarehouseSaleLog.ProductId = field.NewUint(tableName, "product_id")
	_insWarehouseSaleLog.PackageId = field.NewUint(tableName, "package_id")
	_insWarehouseSaleLog.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insWarehouseSaleLog.MaterialId = field.NewUint(tableName, "material_id")
	_insWarehouseSaleLog.LastNum = field.NewUint(tableName, "num")
	_insWarehouseSaleLog.RealTotalPrice = field.NewFloat64(tableName, "real_total_price")
	_insWarehouseSaleLog.CostPrice = field.NewFloat64(tableName, "cost_price")
	_insWarehouseSaleLog.CostTotalPrice = field.NewFloat64(tableName, "cost_total_price")
	_insWarehouseSaleLog.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insWarehouseSaleLog.Ext = field.NewField(tableName, "ext")
	_insWarehouseSaleLog.BusinessDay = field.NewTime(tableName, "business_day")

	_insWarehouseSaleLog.fillFieldMap()

	return _insWarehouseSaleLog
}

type insWarehouseSaleLog struct {
	insWarehouseSaleLogDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	StoreId        field.Uint
	OrderType      field.Int
	IsPackage      field.Int
	IsSub          field.Int
	ProductId      field.Uint
	PackageId      field.Uint
	WarehouseId    field.Uint
	MaterialId     field.Uint
	LastNum        field.Uint
	RealTotalPrice field.Float64
	CostPrice      field.Float64
	CostTotalPrice field.Float64
	OrderDetailsId field.Uint64
	Ext            field.Field
	BusinessDay    field.Time

	fieldMap map[string]field.Expr
}

func (i insWarehouseSaleLog) Table(newTableName string) *insWarehouseSaleLog {
	i.insWarehouseSaleLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseSaleLog) As(alias string) *insWarehouseSaleLog {
	i.insWarehouseSaleLogDo.DO = *(i.insWarehouseSaleLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseSaleLog) updateTableName(table string) *insWarehouseSaleLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.OrderType = field.NewInt(table, "order_type")
	i.IsPackage = field.NewInt(table, "is_package")
	i.IsSub = field.NewInt(table, "is_sub")
	i.ProductId = field.NewUint(table, "product_id")
	i.PackageId = field.NewUint(table, "package_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.MaterialId = field.NewUint(table, "material_id")
	i.LastNum = field.NewUint(table, "num")
	i.RealTotalPrice = field.NewFloat64(table, "real_total_price")
	i.CostPrice = field.NewFloat64(table, "cost_price")
	i.CostTotalPrice = field.NewFloat64(table, "cost_total_price")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.Ext = field.NewField(table, "ext")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseSaleLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseSaleLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 18)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["order_type"] = i.OrderType
	i.fieldMap["is_package"] = i.IsPackage
	i.fieldMap["is_sub"] = i.IsSub
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["num"] = i.LastNum
	i.fieldMap["real_total_price"] = i.RealTotalPrice
	i.fieldMap["cost_price"] = i.CostPrice
	i.fieldMap["cost_total_price"] = i.CostTotalPrice
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insWarehouseSaleLog) clone(db *gorm.DB) insWarehouseSaleLog {
	i.insWarehouseSaleLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseSaleLog) replaceDB(db *gorm.DB) insWarehouseSaleLog {
	i.insWarehouseSaleLogDo.ReplaceDB(db)
	return i
}

type insWarehouseSaleLogDo struct{ gen.DO }

func (i insWarehouseSaleLogDo) Debug() *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseSaleLogDo) WithContext(ctx context.Context) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseSaleLogDo) ReadDB() *insWarehouseSaleLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseSaleLogDo) WriteDB() *insWarehouseSaleLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseSaleLogDo) Session(config *gorm.Session) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseSaleLogDo) Clauses(conds ...clause.Expression) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseSaleLogDo) Returning(value interface{}, columns ...string) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseSaleLogDo) Not(conds ...gen.Condition) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseSaleLogDo) Or(conds ...gen.Condition) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseSaleLogDo) Select(conds ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseSaleLogDo) Where(conds ...gen.Condition) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseSaleLogDo) Order(conds ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseSaleLogDo) Distinct(cols ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseSaleLogDo) Omit(cols ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseSaleLogDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseSaleLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseSaleLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseSaleLogDo) Group(cols ...field.Expr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseSaleLogDo) Having(conds ...gen.Condition) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseSaleLogDo) Limit(limit int) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseSaleLogDo) Offset(offset int) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseSaleLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseSaleLogDo) Unscoped() *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseSaleLogDo) Create(values ...*insbuy.InsWarehouseSaleLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseSaleLogDo) CreateInBatches(values []*insbuy.InsWarehouseSaleLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseSaleLogDo) Save(values ...*insbuy.InsWarehouseSaleLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseSaleLogDo) First() (*insbuy.InsWarehouseSaleLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseSaleLog), nil
	}
}

func (i insWarehouseSaleLogDo) Take() (*insbuy.InsWarehouseSaleLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseSaleLog), nil
	}
}

func (i insWarehouseSaleLogDo) Last() (*insbuy.InsWarehouseSaleLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseSaleLog), nil
	}
}

func (i insWarehouseSaleLogDo) Find() ([]*insbuy.InsWarehouseSaleLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseSaleLog), err
}

func (i insWarehouseSaleLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseSaleLog, err error) {
	buf := make([]*insbuy.InsWarehouseSaleLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseSaleLogDo) FindInBatches(result *[]*insbuy.InsWarehouseSaleLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseSaleLogDo) Attrs(attrs ...field.AssignExpr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseSaleLogDo) Assign(attrs ...field.AssignExpr) *insWarehouseSaleLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseSaleLogDo) Joins(fields ...field.RelationField) *insWarehouseSaleLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseSaleLogDo) Preload(fields ...field.RelationField) *insWarehouseSaleLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseSaleLogDo) FirstOrInit() (*insbuy.InsWarehouseSaleLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseSaleLog), nil
	}
}

func (i insWarehouseSaleLogDo) FirstOrCreate() (*insbuy.InsWarehouseSaleLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseSaleLog), nil
	}
}

func (i insWarehouseSaleLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseSaleLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseSaleLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseSaleLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseSaleLogDo) Delete(models ...*insbuy.InsWarehouseSaleLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseSaleLogDo) withDO(do gen.Dao) *insWarehouseSaleLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
