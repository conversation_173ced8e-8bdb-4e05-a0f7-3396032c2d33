// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventoryBatchHistory(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventoryBatchHistory {
	_insWarehouseInventoryBatchHistory := insWarehouseInventoryBatchHistory{}

	_insWarehouseInventoryBatchHistory.insWarehouseInventoryBatchHistoryDo.UseDB(db, opts...)
	_insWarehouseInventoryBatchHistory.insWarehouseInventoryBatchHistoryDo.UseModel(&insbuy.InsWarehouseInventoryBatchHistory{})

	tableName := _insWarehouseInventoryBatchHistory.insWarehouseInventoryBatchHistoryDo.TableName()
	_insWarehouseInventoryBatchHistory.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventoryBatchHistory.ID = field.NewUint(tableName, "id")
	_insWarehouseInventoryBatchHistory.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventoryBatchHistory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventoryBatchHistory.BatchId = field.NewUint(tableName, "batch_id")
	_insWarehouseInventoryBatchHistory.ChangeType = field.NewInt(tableName, "change_type")
	_insWarehouseInventoryBatchHistory.MaterialId = field.NewUint(tableName, "material_id")
	_insWarehouseInventoryBatchHistory.Num = field.NewInt(tableName, "num")
	_insWarehouseInventoryBatchHistory.AfterNum = field.NewInt(tableName, "after_num")
	_insWarehouseInventoryBatchHistory.OperatorId = field.NewUint(tableName, "operator_id")

	_insWarehouseInventoryBatchHistory.fillFieldMap()

	return _insWarehouseInventoryBatchHistory
}

type insWarehouseInventoryBatchHistory struct {
	insWarehouseInventoryBatchHistoryDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	BatchId    field.Uint
	ChangeType field.Int
	MaterialId field.Uint
	Num        field.Int
	AfterNum   field.Int
	OperatorId field.Uint

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventoryBatchHistory) Table(newTableName string) *insWarehouseInventoryBatchHistory {
	i.insWarehouseInventoryBatchHistoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventoryBatchHistory) As(alias string) *insWarehouseInventoryBatchHistory {
	i.insWarehouseInventoryBatchHistoryDo.DO = *(i.insWarehouseInventoryBatchHistoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventoryBatchHistory) updateTableName(table string) *insWarehouseInventoryBatchHistory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.BatchId = field.NewUint(table, "batch_id")
	i.ChangeType = field.NewInt(table, "change_type")
	i.MaterialId = field.NewUint(table, "material_id")
	i.Num = field.NewInt(table, "num")
	i.AfterNum = field.NewInt(table, "after_num")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventoryBatchHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventoryBatchHistory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["batch_id"] = i.BatchId
	i.fieldMap["change_type"] = i.ChangeType
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["num"] = i.Num
	i.fieldMap["after_num"] = i.AfterNum
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insWarehouseInventoryBatchHistory) clone(db *gorm.DB) insWarehouseInventoryBatchHistory {
	i.insWarehouseInventoryBatchHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventoryBatchHistory) replaceDB(db *gorm.DB) insWarehouseInventoryBatchHistory {
	i.insWarehouseInventoryBatchHistoryDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryBatchHistoryDo struct{ gen.DO }

func (i insWarehouseInventoryBatchHistoryDo) Debug() *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryBatchHistoryDo) WithContext(ctx context.Context) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryBatchHistoryDo) ReadDB() *insWarehouseInventoryBatchHistoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryBatchHistoryDo) WriteDB() *insWarehouseInventoryBatchHistoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryBatchHistoryDo) Session(config *gorm.Session) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryBatchHistoryDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryBatchHistoryDo) Not(conds ...gen.Condition) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Or(conds ...gen.Condition) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Select(conds ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Where(conds ...gen.Condition) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Order(conds ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Distinct(cols ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryBatchHistoryDo) Omit(cols ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryBatchHistoryDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryBatchHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryBatchHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryBatchHistoryDo) Group(cols ...field.Expr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryBatchHistoryDo) Having(conds ...gen.Condition) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryBatchHistoryDo) Limit(limit int) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryBatchHistoryDo) Offset(offset int) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryBatchHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryBatchHistoryDo) Unscoped() *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryBatchHistoryDo) Create(values ...*insbuy.InsWarehouseInventoryBatchHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryBatchHistoryDo) CreateInBatches(values []*insbuy.InsWarehouseInventoryBatchHistory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryBatchHistoryDo) Save(values ...*insbuy.InsWarehouseInventoryBatchHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryBatchHistoryDo) First() (*insbuy.InsWarehouseInventoryBatchHistory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatchHistory), nil
	}
}

func (i insWarehouseInventoryBatchHistoryDo) Take() (*insbuy.InsWarehouseInventoryBatchHistory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatchHistory), nil
	}
}

func (i insWarehouseInventoryBatchHistoryDo) Last() (*insbuy.InsWarehouseInventoryBatchHistory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatchHistory), nil
	}
}

func (i insWarehouseInventoryBatchHistoryDo) Find() ([]*insbuy.InsWarehouseInventoryBatchHistory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventoryBatchHistory), err
}

func (i insWarehouseInventoryBatchHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventoryBatchHistory, err error) {
	buf := make([]*insbuy.InsWarehouseInventoryBatchHistory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryBatchHistoryDo) FindInBatches(result *[]*insbuy.InsWarehouseInventoryBatchHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryBatchHistoryDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryBatchHistoryDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryBatchHistoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryBatchHistoryDo) Joins(fields ...field.RelationField) *insWarehouseInventoryBatchHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryBatchHistoryDo) Preload(fields ...field.RelationField) *insWarehouseInventoryBatchHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryBatchHistoryDo) FirstOrInit() (*insbuy.InsWarehouseInventoryBatchHistory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatchHistory), nil
	}
}

func (i insWarehouseInventoryBatchHistoryDo) FirstOrCreate() (*insbuy.InsWarehouseInventoryBatchHistory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatchHistory), nil
	}
}

func (i insWarehouseInventoryBatchHistoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventoryBatchHistory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryBatchHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryBatchHistoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryBatchHistoryDo) Delete(models ...*insbuy.InsWarehouseInventoryBatchHistory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryBatchHistoryDo) withDO(do gen.Dao) *insWarehouseInventoryBatchHistoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
