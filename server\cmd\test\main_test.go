package test

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport/simple"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/ebus"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jkafka"
	"github.com/liushuochen/gotable"
	"go.uber.org/zap"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"strings"
	"testing"
)

func localSupperDir(filename string, maxLevel int) (string, string) {
	d1 := filepath.Dir(filename)
	n1 := filepath.Base(filename)
	for i := 0; i < maxLevel; i++ {
		p1 := filepath.Join(d1, n1)
		f1, _ := os.Lstat(p1)
		if f1 != nil {
			return d1, p1
		}
		d1 = filepath.Join(d1, "..")
	}
	return "", ""
}

func prepare() {
	if global.GVA_DB == nil {
		d1, p1 := localSupperDir("config.yaml", 4)
		fmt.Printf("d1=%s, p1=%s\n", d1, p1)
		if d1 != "" {
			os.Chdir(d1)
		}
		ebus.InitSys()
		global.GVA_VP = core.Viper() // 初始化Viper
		//initialize.OtherInit()
		global.GVA_LOG = core.Zap() // 初始化zap日志库
		zap.ReplaceGlobals(global.GVA_LOG)
		global.GVA_DB = initialize.Gorm() // gorm连接数据库
		//jgorm.DebugGorm()
		//initialize.Timer()
		initialize.DBList()
		if global.GVA_CONFIG.System.UseRedis {
			initialize.Redis()
		}
		initialize.BindDB()
		if err := jkafka.InitGlobalKafka(global.GVA_CONFIG.Kafka); err != nil {
			panic(err)
		}
		if err := service.Init(); err != nil {
			panic(err)
		}
		if global.GVA_DB != nil {
			global.GVA_DB = global.GVA_DB.Debug()
		}
	}
}

func dumpJSON(t *testing.T, v interface{}) {
	b1, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(b1))
}

// 输出表格
func dumpTable(header []string, rows interface{}) {
	rv := reflect.ValueOf(rows)
	if rv.IsNil() || !rv.IsValid() {
		fmt.Println("rows is nil or invalid")
		return
	}
	if rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	if rv.Kind() != reflect.Slice && rv.Kind() != reflect.Array {
		fmt.Println("rows must be slice or array")
		return
	}

	if len(header) == 0 {
		elTyp := rv.Type().Elem()
		if elTyp.Kind() == reflect.Slice {
			elTyp = elTyp.Elem()
		}
		if elTyp.Kind() == reflect.Map {
			header = make([]string, 0)
			for _, k := range rv.Index(0).MapKeys() {
				header = append(header, k.String())
		}
			sort.Strings(header)
		} else if elTyp.Kind() == reflect.Struct {
		header = make([]string, 0, elTyp.NumField())
		var fn1 func(reflect.Type)
		fn1 = func(e reflect.Type) {
			for i := 0; i < e.NumField(); i++ {
				field := e.Field(i)
				if field.Anonymous {
					fn1(field.Type)
					continue
				}
				vField := strings.SplitN(field.Tag.Get("json"), ",", 2)[0]
				if vField == "" {
					vField = field.Name
				}
				if vField == "-" {
					continue
				}
				header = append(header, vField)
			}
		}
		fn1(elTyp)
		}
		if len(header) == 0 {
			return
		}
	}

	var filterHeader = make(map[string]struct{})
	for _, h := range header {
		filterHeader[h] = struct{}{}
	}
	table, err := gotable.Create(header...)
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	for i := 0; i < rv.Len(); i++ {
		// 获取元素
		e := rv.Index(i)
		// 转成map
		var tv = make(map[string]string)
		b1, e1 := json.Marshal(e.Interface())
		if e1 != nil {
			fmt.Println(e1.Error())
			continue
		}
		var tv1 map[string]interface{}
		e1 = json.Unmarshal(b1, &tv1)
		if e1 != nil {
			fmt.Println(e1.Error())
			continue
		}
		for k, v := range tv1 {
			if _, ok := filterHeader[k]; !ok {
				continue
			}
			if s, ok := v.(string); ok {
				tv[k] = s
				continue
			}
			b1, _ := json.Marshal(v)
			tv[k] = string(b1)
		}
		e1 = table.AddRow(tv)
		if e1 != nil {
			fmt.Println(e1.Error())
			continue
		}
	}
	fmt.Println(table.String())
}

func TestShowDb(t *testing.T) {
	prepare()
	if db := global.GVA_DB; db != nil {
		var tables []string
		if db1 := db.Raw("show tables").Scan(&tables); db1.Error != nil {
			t.Fatal(db1.Error)
		} else {
			t.Log(tables)
		}
	}
}

// 加载可用门店，
//
//	uid 用于加载指定用户被授权的门店，传 0 时将加载所有门店
//	storeFilter 为门店过滤条件，可以为 门店 id、name、或 insCode，如果为空则保留所有授权门店
func loadStoreEnv(ctx context.Context, logger *zap.Logger, q *query.Query, uid uint, storeFilter []string) (storeEnv *simple.StoreEnvInfo, authStoreIds []uint, err error) {
	storeEnv, err = simple.GetStoreEnv(ctx, logger, q, simple.NewStoreListEnvParams(uid, storeFilter).ForNormalMode())
	if err != nil {
		return
	}
	if len(storeEnv.StoreValid) == 0 {
		return nil, nil, fmt.Errorf("没有可用的门店")
	}
	authStoreIds = make([]uint, 0, len(storeEnv.StoreValid))
	for _, v := range storeEnv.StoreValid {
		authStoreIds = append(authStoreIds, v.StoreId)
	}
	return
}

func Test重建菜单和权限(t *testing.T) {
	var err error
	ctx := context.TODO()
	prepare()
	if global.GVA_DB != nil {
		initDBService := service.ServiceGroupApp.SystemServiceGroup.InitDBService
		//ctx, handler := initDBService.InitDbHandler(ctx, global.GVA_CONFIG.System.DbType)
		ctx = context.WithValue(ctx, "db", global.GVA_DB)

		var menus []system.SysBaseMenu
		if db1 := global.GVA_DB.Find(&menus); db1.Error != nil {
			t.Fatal(db1.Error)
		} else {
			ctx = context.WithValue(ctx, "sys_base_menus", menus)
		}
		var sys_authorities []system.SysAuthority
		if db1 := global.GVA_DB.Find(&sys_authorities); db1.Error != nil {
			t.Fatal(db1.Error)
		} else {
			ctx = context.WithValue(ctx, "sys_authorities", sys_authorities)
		}
		initializer := initDBService.FindSubInitializer("sys_menu_authorities")
		if initializer != nil {
			if suc := initializer.DataInserted(ctx); !suc {
				t.Error("数据插入失败")
			}
			ctx, err = initializer.InitializeData(ctx)
			if err != nil {
				t.Error(err)
			}
		}
	}
}
