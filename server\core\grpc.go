package core

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"net"
	"reflect"
	"strings"
)

func RunRpcServer(ctx context.Context) {
	if global.GVA_CONFIG.Grpc.Addr == "" {
		return
	}
	// 初始化grpc服务
	s := grpc.NewServer(
		grpc.ChainUnaryInterceptor(panicInterceptor, authInterceptor, logInterceptor),
		grpc.ChainStreamInterceptor(panicWithServerStreamInterceptor, authWithServerStreamInterceptor),
		//grpc.Creds(cred),
		grpc.MaxRecvMsgSize(1024*1024*1024),
	)
	service.ServiceGroupApp.InsBuyServiceGroup.RpcBookDesk.Register(s)
	service.ServiceGroupApp.InsBuyServiceGroup.RpcBookDeskAdmin.Register(s)

	l1, err := net.Listen("tcp", global.GVA_CONFIG.Grpc.Addr)
	if err != nil {
		panic(err)
	}
	go func() {
		select {
		case <-ctx.Done():
			if s != nil {
				s.Stop()
				s = nil
			}
			if l1 != nil {
				_ = l1.Close()
			}
		}
	}()
	go func() {
		if s != nil {
			if e1 := s.Serve(l1); e1 != nil {
				global.GVA_LOG.Error("grpc server stop", zap.Error(err))
			}
		}
	}()
	global.GVA_LOG.Info("start grpc server on: " + global.GVA_CONFIG.Grpc.Addr)
}

func panicInterceptor(c context.Context, req interface{}, _ *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("panic: %v\n", r)
		}
	}()
	return handler(c, req)
}
func panicWithServerStreamInterceptor(srv interface{}, ss grpc.ServerStream, _ *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("panic: %v\n", r)
		}
	}()
	return handler(srv, ss)
}

type logMessage struct {
	RequestUrl    string `json:"requestUrl"`
	RequestMethod string `json:"requestMethod"`
	UserAgent     string `json:"userAgent"`
	RequestBody   string `json:"requestBody"`
	RemoteAddress string `json:"remoteAddress"`
	Message       string `json:"message"`
}

// 打印访问日志
func logInterceptor(c context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	logger, logLevel := global.GVA_LOG, global.GVA_CONFIG.Grpc.LogLevel
	if logLevel == "" || logger == nil {
		return handler(c, req)
	}
	md, ok := metadata.FromIncomingContext(c)
	if !ok {
		return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	}
	remoteAddress := md.Get(":authority")[0] // address
	v := reflect.ValueOf(req)
	t := reflect.TypeOf(req)
	methodNames := []string{}
	r := map[string]interface{}{} // grpc request data
	for i := 0; i < t.NumMethod(); i++ {
		if strings.HasPrefix(t.Method(i).Name, "Get") {
			methodNames = append(methodNames, t.Method(i).Name) // get all grpc field functions
		}
	}
	for _, name := range methodNames {
		m := v.MethodByName(name) // get method
		p := make([]reflect.Value, m.Type().NumIn())
		result := m.Call(p)[0].Interface() // call function and get result
		r[strings.Replace(name, "Get", "", -1)] = result
	}
	rpcString, _ := json.Marshal(r)
	logMessage := logMessage{
		RequestUrl:    info.FullMethod,
		RequestMethod: "gRPC",
		UserAgent:     md.Get("user-agent")[0],
		RequestBody:   fmt.Sprintf("%s", rpcString),
		RemoteAddress: remoteAddress,
		Message:       "",
	}
	var userName string
	var au string
	if d, ok := md["authorization"]; ok {
		au = d[0]
		if name, _, ok := parseBasicAuth(au); ok {
			userName = name
		}
	}
	if v, err := handler(c, req); err != nil {
		logMessage.Message = strings.Replace(err.Error(), "'", `"`, -1)
		logger.Error("Error", zap.String("userName", userName), zap.Any("msg", logMessage))
		return v, err
	} else {
		// no errors
		if strings.ToLower(logLevel) == "info" {
			logger.Info("Info", zap.String("userName", userName), zap.Any("msg", logMessage))
		}
		return v, nil
	}
}
func parseBasicAuth(auth string) (username, password string, ok bool) {
	const prefix = "Basic "
	// Case insensitive prefix match. See Issue 22736.
	if len(auth) < len(prefix) || !strings.EqualFold(auth[:len(prefix)], prefix) {
		return
	}
	c, err := base64.StdEncoding.DecodeString(auth[len(prefix):])
	if err != nil {
		return
	}
	cs := string(c)
	s := strings.IndexByte(cs, ':')
	if s < 0 {
		return
	}
	return cs[:s], cs[s+1:], true
}
func authWithServerStreamInterceptor(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
	//if s.configs.Authorization {
	//	if !info.IsClientStream {
	//		return status.Errorf(codes.Unknown, "unknown service type")
	//	} else {
	//		if md, ok := metadata.FromIncomingContext(ss.Context()); !ok {
	//			return status.Errorf(codes.Unauthenticated, "invalid token")
	//		} else {
	//			var au string
	//			if d, ok := md["authorization"]; ok {
	//				au = d[0]
	//			} else {
	//				return status.Errorf(codes.Unauthenticated, "invalid token")
	//			}
	//			if userName, token, ok := parseBasicAuth(au); !ok {
	//				return status.Errorf(codes.Unauthenticated, "invalid token")
	//			} else {
	//				if r, err := s.gdb.query("select token from user_cfg where userName='" + userName + "'"); err != nil || len(r) == 0 {
	//					return status.Errorf(codes.Unauthenticated, "invalid token")
	//				} else {
	//					if token != r[0]["token"] {
	//						return status.Errorf(codes.Unauthenticated, "invalid token")
	//					} else {
	//						sub, obj, act := userName, "StreamWrite", "POST" // for stream gRPC, route permission is StreamWrite
	//						if ok, _ := s.gdb.e.Enforce(sub, obj, act); !ok {
	//							return status.Errorf(codes.Unauthenticated, "invalid token")
	//						}
	//						return handler(srv, ss)
	//					}
	//				}
	//			}
	//		}
	//	}
	//}
	return handler(srv, ss)
}
func authInterceptor(c context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (resp interface{}, err error) {
	//if s.configs.Authorization {
	//	methods := strings.Split(info.FullMethod, "/")
	//	if md, ok := metadata.FromIncomingContext(c); !ok {
	//		return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//	} else {
	//		if methods[len(methods)-1] == "UserLogin" {
	//			r := req.(*pb.AuthInfo)
	//			if result, err := s.gdb.userLogin(authInfo{
	//				UserName: r.GetUserName(),
	//				PassWord: r.GetPassWord(),
	//			}); err != nil {
	//				return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//			} else {
	//				return &pb.UserToken{Token: result.Token}, nil
	//			}
	//		} else {
	//			var au string
	//			if d, ok := md["authorization"]; ok {
	//				au = d[0]
	//			} else {
	//				return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//			}
	//			if userName, token, ok := parseBasicAuth(au); !ok {
	//				return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//			} else {
	//				if r, err := s.gdb.query("select token from user_cfg where userName='" + userName + "'"); err != nil || len(r) == 0 {
	//					return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//				} else {
	//					if token != r[0]["token"] {
	//						return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//					} else {
	//						sub, obj, act := userName, methods[len(methods)-1], "POST" // replace gRCP with POST
	//						if ok, _ := s.gdb.e.Enforce(sub, obj, act); !ok {
	//							return nil, status.Errorf(codes.Unauthenticated, "invalid token")
	//						}
	//						return handler(c, req)
	//					}
	//				}
	//			}
	//		}
	//	}
	//}
	return handler(c, req)
}
