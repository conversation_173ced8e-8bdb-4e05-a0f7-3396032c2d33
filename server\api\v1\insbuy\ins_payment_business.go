package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetBusinessList 获取分店支付方式列表
// @Tags InsPaymentBusiness
// @Summary 获取分店支付方式列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PaymentBusinessListReq true "获取分店支付方式列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/getBusinessList [get]
func (InsPaymentApi *InsPaymentApi) GetBusinessList(c *gin.Context) {
	var req insbuyReq.PaymentBusinessListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insPaymentService.GetBusinessList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// CreateBusiness 创建分店支付方式
// @Tags InsPaymentBusiness
// @Summary 创建分店支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PaymentBusinessReq true "创建分店支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insPayment/createBusiness [post]
func (InsPaymentApi *InsPaymentApi) CreateBusiness(c *gin.Context) {
	var req insbuyReq.PaymentBusinessReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.CreateBusiness(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateBusiness 更新分店支付方式
// @Tags InsPaymentBusiness
// @Summary 更新分店支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PaymentBusinessReq true "更新分店支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insPayment/updateBusiness [put]
func (InsPaymentApi *InsPaymentApi) UpdateBusiness(c *gin.Context) {
	var req insbuyReq.PaymentBusinessReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.UpdateBusiness(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteBusiness 删除分店支付方式
// @Tags InsPaymentBusiness
// @Summary 删除分店支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PaymentBusinessReq true "删除分店支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insPayment/deleteBusiness [delete]
func (InsPaymentApi *InsPaymentApi) DeleteBusiness(c *gin.Context) {
	var req request.GetById
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.DeleteBusiness(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// BatchAssignBusiness 批量分配支付方式
// @Tags InsPaymentBusiness
// @Summary 批量分配支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchAssignBusinessReq true "批量分配支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量分配成功"}"
// @Router /insPayment/batchAssignBusiness [post]
func (InsPaymentApi *InsPaymentApi) BatchAssignBusiness(c *gin.Context) {
	var req insbuyReq.BatchAssignBusinessReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.BatchAssignBusiness(req); err != nil {
		global.GVA_LOG.Error("批量分配失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("批量分配成功", c)
	}
}

// GetAssignBusinessByPayId 根据支付方式id获取分配的支付方式
// @Tags InsPaymentBusiness
// @Summary 根据支付方式id获取分店支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetBusinessByPayIdReq true "批量分配支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/getAssignBusinessByPayId [get]
func (InsPaymentApi *InsPaymentApi) GetAssignBusinessByPayId(c *gin.Context) {
	var req insbuyReq.GetBusinessByPayIdReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insPaymentService.GetAssignBusinessByPayId(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// OpenSystemPayment 开通系统支付方式
// @Tags InsPaymentBusiness
// @Summary 开通系统支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.OpenSystemPaymentReq true "开通系统支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"开启成功"}"
// @Router /insPayment/openSystemPayment [post]
func (InsPaymentApi *InsPaymentApi) OpenSystemPayment(c *gin.Context) {
	var req insbuyReq.OpenSystemPaymentReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.OpenSystemPayment(req); err != nil {
		global.GVA_LOG.Error("开启失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("开启成功", c)
	}
}
