package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsNotesApi struct{}

var (
	IdVerify = utils.Rules{
		"NotesId": {utils.NotEmpty()},
	}
	DetailIdVerify = utils.Rules{
		"NotesDetailId": {utils.NotEmpty()},
	}
)

// CreateInsNotesDetail
// @Tags      InsNotes 备注管理
// @Summary   创建备注
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.CreateInsNotesDetail     true  "备注"
// @Success   200   {object}  response.Response{msg=string}  "创建备注"
// @Router    /insNotesApi/createInsNotesDetail [post]
func (s *InsNotesApi) CreateInsNotesDetail(c *gin.Context) {
	var req insbuyReq.CreateInsNotesDetail
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	notesDetailId, err := insNotesService.CreateInsNotesDetail(req)
	if err != nil {
		global.GVA_LOG.Error("创建备注失败!", zap.Error(err))
		response.FailWithMessage("创建备注失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"notesDetailId": notesDetailId}, "创建成功", c)
}

// DeleteInsNotesDetail
// @Tags      InsNotes 备注管理
// @Summary   删除备注详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.InsNotesDetailIdReq     true  "备注ID"
// @Success   200   {object}  response.Response{msg=string}  "删除备注详情"
// @Router    /insNotesApi/deleteInsNotesDetail [delete]
func (s *InsNotesApi) DeleteInsNotesDetail(c *gin.Context) {
	var req insbuyReq.InsNotesDetailIdReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insNotesService.DeleteInsNotesDetail(req)
	if err != nil {
		global.GVA_LOG.Error("删除备注失败!", zap.Error(err))
		response.FailWithMessage("删除备注失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// UpdateInsNotesDetail
// @Tags      InsNotes 备注管理
// @Summary   更新备注详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.UpdateInsNotesDetail     true  "更新备注详情"
// @Success   200   {object}  response.Response{msg=string}  "更新备注详情"
// @Router    /insNotesApi/updateInsNotesDetail [put]
func (s *InsNotesApi) UpdateInsNotesDetail(c *gin.Context) {
	var req insbuyReq.UpdateInsNotesDetail
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insNotesService.UpdateInsNotesDetail(req)
	if err != nil {
		global.GVA_LOG.Error("更新备注失败!", zap.Error(err))
		response.FailWithMessage("更新备注失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindInsNotesDetail
// @Tags      InsNotes 备注管理
// @Summary   用id查询备注详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     insbuyReq.InsNotesDetailIdReq                                 true  "用id查询备注详情"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "用id查询备注详情"
// @Router    /insNotesApi/findInsNotesDetail [get]
func (s *InsNotesApi) FindInsNotesDetail(c *gin.Context) {
	var req insbuyReq.InsNotesDetailIdReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, DetailIdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	reInsNotesDetail, err := insNotesService.GetInsNotesDetail(req.NotesDetailId)
	if err != nil {
		global.GVA_LOG.Error("查询备注失败!", zap.Error(err))
		response.FailWithMessage("查询备注失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"reInsNotesDetail": reInsNotesDetail}, "查询成功", c)
}

// GetInsNotesDetailList
// @Tags      InsNotes 备注管理
// @Summary   获取备注详情列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     insbuyReq.GetInsNotesDetailList    true  "备注编码, 搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "备注详情列表"
// @Router    /insNotesApi/getInsNotesDetailList [get]
func (s *InsNotesApi) GetInsNotesDetailList(c *gin.Context) {
	var req insbuyReq.GetInsNotesDetailList
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insNotesService.GetInsNotesDetailInfoList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List: list,
	}, "获取成功", c)
}

// GetInsNotesList
// @Tags      InsNotes 备注管理
// @Summary   获取备注列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  query     insbuyReq.GetInsNotesList   true  "搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "获取备注列表"
// @Router    /insNotesApi/getInsNotesList [get]
func (s *InsNotesApi) GetInsNotesList(c *gin.Context) {
	var req insbuyReq.GetInsNotesList
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insNotesService.GetInsNotesList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List: list,
	}, "获取成功", c)
}

// UpdateInsNotes
// @Tags      InsNotes 备注管理
// @Summary   更新备注（开启/关闭）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.UpdateInsNotes     true  "备注ID"
// @Success   200   {object}  response.Response{msg=string}  "更新备注"
// @Router    /insNotesApi/updateInsNotes [put]
func (s *InsNotesApi) UpdateInsNotes(c *gin.Context) {
	var req insbuyReq.UpdateInsNotes
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(req, IdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = insNotesService.UpdateInsNotes(req)
	if err != nil {
		global.GVA_LOG.Error("更新备注失败!", zap.Error(err))
		response.FailWithMessage("更新备注失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}
