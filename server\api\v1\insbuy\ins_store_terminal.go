package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsStoreTerminalApi struct {
}

var insStoreTerminalService = service.ServiceGroupApp.InsBuyServiceGroup.InsStoreTerminalService

// CreateInsStoreTerminal 创建InsStoreTerminal
// @Tags InsStoreTerminal
// @Summary 创建InsStoreTerminal
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStoreTerminal true "创建InsStoreTerminal"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStoreTerminal/createInsStoreTerminal [post]
func (insStoreTerminalApi *InsStoreTerminalApi) CreateInsStoreTerminal(c *gin.Context) {
	var req insbuyReq.InsStoreTerminalReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreTerminalService.CreateInsStoreTerminal(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsStoreTerminal 删除InsStoreTerminal
// @Tags InsStoreTerminal
// @Summary 删除InsStoreTerminal
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStoreTerminal true "删除InsStoreTerminal"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insStoreTerminal/deleteInsStoreTerminal [delete]
func (insStoreTerminalApi *InsStoreTerminalApi) DeleteInsStoreTerminal(c *gin.Context) {
	var insStoreTerminal insbuy.InsStoreTerminal
	err := c.ShouldBindJSON(&insStoreTerminal)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreTerminalService.DeleteInsStoreTerminal(insStoreTerminal); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsStoreTerminalByIds 批量删除InsStoreTerminal
// @Tags InsStoreTerminal
// @Summary 批量删除InsStoreTerminal
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsStoreTerminal"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insStoreTerminal/deleteInsStoreTerminalByIds [delete]
func (insStoreTerminalApi *InsStoreTerminalApi) DeleteInsStoreTerminalByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreTerminalService.DeleteInsStoreTerminalByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsStoreTerminal 更新InsStoreTerminal
// @Tags InsStoreTerminal
// @Summary 更新InsStoreTerminal
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStoreTerminal true "更新InsStoreTerminal"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insStoreTerminal/updateInsStoreTerminal [put]
func (insStoreTerminalApi *InsStoreTerminalApi) UpdateInsStoreTerminal(c *gin.Context) {
	var insStoreTerminal insbuy.InsStoreTerminal
	err := c.ShouldBindJSON(&insStoreTerminal)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreTerminalService.UpdateInsStoreTerminal(insStoreTerminal); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsStoreTerminal 用id查询InsStoreTerminal
// @Tags InsStoreTerminal
// @Summary 用id查询InsStoreTerminal
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsStoreTerminal true "用id查询InsStoreTerminal"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insStoreTerminal/findInsStoreTerminal [get]
func (insStoreTerminalApi *InsStoreTerminalApi) FindInsStoreTerminal(c *gin.Context) {
	var insStoreTerminal insbuy.InsStoreTerminal
	err := c.ShouldBindQuery(&insStoreTerminal)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsStoreTerminal, err := insStoreTerminalService.GetInsStoreTerminal(insStoreTerminal.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsStoreTerminal": reinsStoreTerminal}, c)
	}
}

// GetInsStoreTerminalList 分页获取InsStoreTerminal列表
// @Tags InsStoreTerminal
// @Summary 分页获取InsStoreTerminal列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsStoreTerminalSearch true "分页获取InsStoreTerminal列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStoreTerminal/getInsStoreTerminalList [get]
func (insStoreTerminalApi *InsStoreTerminalApi) GetInsStoreTerminalList(c *gin.Context) {
	var pageInfo insbuyReq.InsStoreTerminalSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insStoreTerminalService.GetInsStoreTerminalInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
