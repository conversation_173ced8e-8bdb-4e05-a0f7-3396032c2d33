// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtKezeeOrderItem(db *gorm.DB, opts ...gen.DOOption) insExtKezeeOrderItem {
	_insExtKezeeOrderItem := insExtKezeeOrderItem{}

	_insExtKezeeOrderItem.insExtKezeeOrderItemDo.UseDB(db, opts...)
	_insExtKezeeOrderItem.insExtKezeeOrderItemDo.UseModel(&insbuy.InsExtKezeeOrderItem{})

	tableName := _insExtKezeeOrderItem.insExtKezeeOrderItemDo.TableName()
	_insExtKezeeOrderItem.ALL = field.NewAsterisk(tableName)
	_insExtKezeeOrderItem.Id = field.NewUint(tableName, "id")
	_insExtKezeeOrderItem.OrderId = field.NewUint(tableName, "order_id")
	_insExtKezeeOrderItem.KzId = field.NewString(tableName, "kz_id")
	_insExtKezeeOrderItem.ItemId = field.NewString(tableName, "item_id")
	_insExtKezeeOrderItem.ItemName = field.NewString(tableName, "item_name")
	_insExtKezeeOrderItem.UnitName = field.NewString(tableName, "unit_name")
	_insExtKezeeOrderItem.OrigSubtotal = field.NewFloat64(tableName, "orig_subtotal")
	_insExtKezeeOrderItem.LastQty = field.NewInt(tableName, "last_qty")
	_insExtKezeeOrderItem.DiscFlg = field.NewInt(tableName, "disc_flg")
	_insExtKezeeOrderItem.PkgFlg = field.NewInt(tableName, "pkg_flg")
	_insExtKezeeOrderItem.PkgScId = field.NewString(tableName, "pkg_sc_id")

	_insExtKezeeOrderItem.fillFieldMap()

	return _insExtKezeeOrderItem
}

type insExtKezeeOrderItem struct {
	insExtKezeeOrderItemDo

	ALL          field.Asterisk
	Id           field.Uint
	OrderId      field.Uint
	KzId         field.String
	ItemId       field.String
	ItemName     field.String
	UnitName     field.String
	OrigSubtotal field.Float64
	LastQty      field.Int
	DiscFlg      field.Int
	PkgFlg       field.Int
	PkgScId      field.String

	fieldMap map[string]field.Expr
}

func (i insExtKezeeOrderItem) Table(newTableName string) *insExtKezeeOrderItem {
	i.insExtKezeeOrderItemDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtKezeeOrderItem) As(alias string) *insExtKezeeOrderItem {
	i.insExtKezeeOrderItemDo.DO = *(i.insExtKezeeOrderItemDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtKezeeOrderItem) updateTableName(table string) *insExtKezeeOrderItem {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.OrderId = field.NewUint(table, "order_id")
	i.KzId = field.NewString(table, "kz_id")
	i.ItemId = field.NewString(table, "item_id")
	i.ItemName = field.NewString(table, "item_name")
	i.UnitName = field.NewString(table, "unit_name")
	i.OrigSubtotal = field.NewFloat64(table, "orig_subtotal")
	i.LastQty = field.NewInt(table, "last_qty")
	i.DiscFlg = field.NewInt(table, "disc_flg")
	i.PkgFlg = field.NewInt(table, "pkg_flg")
	i.PkgScId = field.NewString(table, "pkg_sc_id")

	i.fillFieldMap()

	return i
}

func (i *insExtKezeeOrderItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtKezeeOrderItem) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 11)
	i.fieldMap["id"] = i.Id
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["kz_id"] = i.KzId
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["item_name"] = i.ItemName
	i.fieldMap["unit_name"] = i.UnitName
	i.fieldMap["orig_subtotal"] = i.OrigSubtotal
	i.fieldMap["last_qty"] = i.LastQty
	i.fieldMap["disc_flg"] = i.DiscFlg
	i.fieldMap["pkg_flg"] = i.PkgFlg
	i.fieldMap["pkg_sc_id"] = i.PkgScId
}

func (i insExtKezeeOrderItem) clone(db *gorm.DB) insExtKezeeOrderItem {
	i.insExtKezeeOrderItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtKezeeOrderItem) replaceDB(db *gorm.DB) insExtKezeeOrderItem {
	i.insExtKezeeOrderItemDo.ReplaceDB(db)
	return i
}

type insExtKezeeOrderItemDo struct{ gen.DO }

func (i insExtKezeeOrderItemDo) Debug() *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtKezeeOrderItemDo) WithContext(ctx context.Context) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtKezeeOrderItemDo) ReadDB() *insExtKezeeOrderItemDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtKezeeOrderItemDo) WriteDB() *insExtKezeeOrderItemDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtKezeeOrderItemDo) Session(config *gorm.Session) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtKezeeOrderItemDo) Clauses(conds ...clause.Expression) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtKezeeOrderItemDo) Returning(value interface{}, columns ...string) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtKezeeOrderItemDo) Not(conds ...gen.Condition) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtKezeeOrderItemDo) Or(conds ...gen.Condition) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtKezeeOrderItemDo) Select(conds ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtKezeeOrderItemDo) Where(conds ...gen.Condition) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtKezeeOrderItemDo) Order(conds ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtKezeeOrderItemDo) Distinct(cols ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtKezeeOrderItemDo) Omit(cols ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtKezeeOrderItemDo) Join(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtKezeeOrderItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtKezeeOrderItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtKezeeOrderItemDo) Group(cols ...field.Expr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtKezeeOrderItemDo) Having(conds ...gen.Condition) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtKezeeOrderItemDo) Limit(limit int) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtKezeeOrderItemDo) Offset(offset int) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtKezeeOrderItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtKezeeOrderItemDo) Unscoped() *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtKezeeOrderItemDo) Create(values ...*insbuy.InsExtKezeeOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtKezeeOrderItemDo) CreateInBatches(values []*insbuy.InsExtKezeeOrderItem, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtKezeeOrderItemDo) Save(values ...*insbuy.InsExtKezeeOrderItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtKezeeOrderItemDo) First() (*insbuy.InsExtKezeeOrderItem, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderItem), nil
	}
}

func (i insExtKezeeOrderItemDo) Take() (*insbuy.InsExtKezeeOrderItem, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderItem), nil
	}
}

func (i insExtKezeeOrderItemDo) Last() (*insbuy.InsExtKezeeOrderItem, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderItem), nil
	}
}

func (i insExtKezeeOrderItemDo) Find() ([]*insbuy.InsExtKezeeOrderItem, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtKezeeOrderItem), err
}

func (i insExtKezeeOrderItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtKezeeOrderItem, err error) {
	buf := make([]*insbuy.InsExtKezeeOrderItem, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtKezeeOrderItemDo) FindInBatches(result *[]*insbuy.InsExtKezeeOrderItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtKezeeOrderItemDo) Attrs(attrs ...field.AssignExpr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtKezeeOrderItemDo) Assign(attrs ...field.AssignExpr) *insExtKezeeOrderItemDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtKezeeOrderItemDo) Joins(fields ...field.RelationField) *insExtKezeeOrderItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtKezeeOrderItemDo) Preload(fields ...field.RelationField) *insExtKezeeOrderItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtKezeeOrderItemDo) FirstOrInit() (*insbuy.InsExtKezeeOrderItem, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderItem), nil
	}
}

func (i insExtKezeeOrderItemDo) FirstOrCreate() (*insbuy.InsExtKezeeOrderItem, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderItem), nil
	}
}

func (i insExtKezeeOrderItemDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtKezeeOrderItem, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtKezeeOrderItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtKezeeOrderItemDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtKezeeOrderItemDo) Delete(models ...*insbuy.InsExtKezeeOrderItem) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtKezeeOrderItemDo) withDO(do gen.Dao) *insExtKezeeOrderItemDo {
	i.DO = *do.(*gen.DO)
	return i
}
