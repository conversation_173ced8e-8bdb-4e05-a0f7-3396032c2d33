# 手动编辑功能最佳实践

## 概述

新的手动编辑功能采用了接口化设计，提供了更好的代码复用性和可维护性。重新封装后的版本支持时间范围查询，提供更精确的数据过滤能力。

## 核心接口

```go
// ManualEditableItem 可手动编辑的报表项接口
type ManualEditableItem interface {
    GetManualEditKey() string                                   // 获取手动编辑的唯一标识
    ApplyManualEdit(manualId uint, fields []insstore.Field)    // 应用手动编辑数据，包含 ManualId
}

// ManualEditQueryParams 手动编辑查询参数
type ManualEditQueryParams struct {
    FinancialType int       `json:"financial_type"` // 财务报表类型
    StartDate     time.Time `json:"start_date"`     // 开始时间（可选）
    EndDate       time.Time `json:"end_date"`       // 结束时间（可选）
    StoreIds      []uint    `json:"store_ids"`      // 店铺ID列表（可选）
}
```

## 实现示例

### 1. SalesLadderItem 实现

```go
type SalesLadderItem struct {
    ManualId        uint          `json:"manual_id"`
    BKey            string        `json:"b_key"`       // 唯一标识
    SalesmanId      uint          `json:"salesman_id"`
    // ... 其他字段
    Attendance      jtypes.JPrice `json:"attendance" edittable:"true"`
    SocialSecurity  jtypes.JPrice `json:"social_security" edittable:"true"`
    Deduction       jtypes.JPrice `json:"deduction" edittable:"true"`
    AbsentTimes     int           `json:"absent_times" edittable:"true"`
}

// GetManualEditKey 实现 ManualEditableItem 接口
func (s *SalesLadderItem) GetManualEditKey() string {
    // 优先使用 BKey，如果为空则使用 SalesmanId
    if s.BKey != "" {
        return s.BKey
    }
    return fmt.Sprintf("%d", s.SalesmanId)
}

// ApplyManualEdit 实现 ManualEditableItem 接口
func (s *SalesLadderItem) ApplyManualEdit(manualId uint, extFields []insstore.Field) {
    // 设置 ManualId
    s.ManualId = manualId
    
    // 字段名称到结构体字段的映射
    fieldMapping := map[string]interface{}{
        "attendance":      &s.Attendance,
        "social_security": &s.SocialSecurity,
        "deduction":       &s.Deduction,
        "absent_times":    &s.AbsentTimes,
    }

    for _, field := range extFields {
        if fieldPtr, ok := fieldMapping[field.Key]; ok {
            switch ptr := fieldPtr.(type) {
            case *jtypes.JPrice:
                if val, err := strconv.ParseFloat(fmt.Sprintf("%s", field.Value), 64); err == nil {
                    *ptr = jtypes.JPrice(val)
                }
            case *int:
                if val, err := strconv.Atoi(fmt.Sprintf("%s", field.Value)); err == nil {
                    *ptr = val
                }
            }
        }
    }
}
```

### 2. 在报表函数中使用 - 新版本

```go
func SalesLadderList(ctx context.Context, q *query.Query, p FinancialWarehouseParams) (resp *SimpleStateReportResp, err error) {
    // ... 查询数据逻辑 ...
    
    // 4. 批量处理手动编辑数据 - 支持时间范围查询
    listPtrs := make([]*SalesLadderItem, len(list))
    for i := range list {
        listPtrs[i] = &list[i]
    }
    
    // 🎯 新的参数结构体方式，支持时间范围
    manualEditParams := ManualEditQueryParams{
        FinancialType: p.FinancialType,
        StartDate:     p.StartDate,     // 支持时间范围过滤
        EndDate:       p.EndDate,       // 支持时间范围过滤
        StoreIds:      p.StoreIds,      // 支持店铺过滤
    }
    
    if err := ProcessManualEditItems(ctx, q, manualEditParams, listPtrs); err != nil {
        global.GVA_LOG.Warn("处理手动编辑数据失败", zap.Error(err))
    }

    // 5. 重新计算相关金额
    for i := range list {
        list[i].CalculateActualAmount()
        list[i].CalculatePayable()
        list[i].CalculateFee()
    }
    
    // ... 返回响应 ...
}
```

### 3. 简化版本（向后兼容）

```go
// 如果不需要时间过滤，可以使用简化版本
if err := ProcessManualEditItemsSimple(ctx, q, financialType, listPtrs); err != nil {
    global.GVA_LOG.Warn("处理手动编辑数据失败", zap.Error(err))
}
```

## 新功能优势

### 1. **时间范围查询**
```go
// 只查询指定时间范围内的手动编辑数据
manualEditParams := ManualEditQueryParams{
    FinancialType: 11,
    StartDate:     time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
    EndDate:       time.Date(2024, 12, 31, 23, 59, 59, 0, time.UTC),
    StoreIds:      []uint{1, 2, 3},
}
```

### 2. **多维度过滤**
- ✅ **财务类型**: 不同报表类型的数据隔离
- ✅ **时间范围**: 精确到秒的时间过滤  
- ✅ **店铺范围**: 支持多店铺数据查询
- ✅ **唯一标识**: 灵活的 Key 生成策略

### 3. **智能 Key 策略**
```go
func (s *SalesLadderItem) GetManualEditKey() string {
    // 优先使用 BKey（业务主键），兜底使用 SalesmanId
    if s.BKey != "" {
        return s.BKey    // "20240315_001" 这样的业务标识
    }
    return fmt.Sprintf("%d", s.SalesmanId)  // "12345" 数字ID
}
```

### 4. **增强的日志记录**
```go
// 详细的调试日志
global.GVA_LOG.Debug("加载手动编辑数据完成", 
    zap.Int("总记录数", len(manualList)),
    zap.Int("有效记录数", len(p.manualEditData)),
    zap.Int("财务类型", p.params.FinancialType))
```

## 使用场景

### 场景 1: 按月度查询
```go
params := ManualEditQueryParams{
    FinancialType: 11,
    StartDate:     time.Date(2024, 3, 1, 0, 0, 0, 0, time.UTC),
    EndDate:       time.Date(2024, 3, 31, 23, 59, 59, 0, time.UTC),
}
```

### 场景 2: 多店铺联合查询
```go
params := ManualEditQueryParams{
    FinancialType: 11,
    StoreIds:      []uint{1, 2, 3, 4, 5},
}
```

### 场景 3: 实时数据（当日）
```go
today := time.Now()
startOfDay := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
endOfDay := startOfDay.Add(24 * time.Hour).Add(-time.Second)

params := ManualEditQueryParams{
    FinancialType: 11,
    StartDate:     startOfDay,
    EndDate:       endOfDay,
}
```

## ManualId 字段说明

### 作用
- **前端标识**: 前端通过 `ManualId > 0` 判断该行数据是否被手动编辑过
- **审计追踪**: 可以追踪到具体的手动编辑记录
- **权限控制**: 可以基于 `ManualId` 控制编辑权限

### 实现要点
```go
// 结构体中必须包含 ManualId 字段
type YourReportItem struct {
    ManualId uint `json:"manual_id"`  // 必须字段
    // ... 其他字段
}

// ApplyManualEdit 中必须设置
func (y *YourReportItem) ApplyManualEdit(manualId uint, fields []insstore.Field) {
    y.ManualId = manualId  // 🎯 关键步骤：设置手动编辑ID
    // ... 其他逻辑
}
```

### 前端使用示例
```javascript
// 前端判断是否为手动编辑数据
if (row.manual_id > 0) {
    // 显示"已手动编辑"标识
    showEditedFlag(true);
    // 可能需要不同的样式或权限
}
```

## 如何为现有报表添加手动编辑功能

### 步骤 1: 为结构体添加必要字段

```go
type YourReportItem struct {
    ManualId uint   `json:"manual_id"`  // 手动编辑ID
    BKey     string `json:"b_key"`      // 业务唯一标识（推荐）
    EditableField jtypes.JPrice `json:"editable_field" comment:"可编辑字段" edittable:"true"`
}
```

### 步骤 2: 实现 ManualEditableItem 接口

```go
func (y *YourReportItem) GetManualEditKey() string {
    if y.BKey != "" {
        return y.BKey  // 推荐使用业务标识
    }
    return fmt.Sprintf("%d", y.ID) // 兜底使用数字ID
}

func (y *YourReportItem) ApplyManualEdit(manualId uint, extFields []insstore.Field) {
    // 设置 ManualId (重要!)
    y.ManualId = manualId
    
    // 实现字段更新逻辑
    // ...
}
```

### 步骤 3: 在报表函数中集成

```go
// 新版本（推荐）- 支持时间范围
manualEditParams := ManualEditQueryParams{
    FinancialType: yourFinancialType,
    StartDate:     p.StartDate,
    EndDate:       p.EndDate,
    StoreIds:      p.StoreIds,
}
ProcessManualEditItems(ctx, q, manualEditParams, itemPtrs)

// 简化版本 - 向后兼容
ProcessManualEditItemsSimple(ctx, q, financialType, itemPtrs)
```

## 注意事项

1. 确保 `GetManualEditKey()` 返回的是唯一标识
2. **必须在 `ApplyManualEdit()` 中设置 `ManualId = manualId`**
3. 在 `ApplyManualEdit()` 中处理类型转换错误
4. 手动编辑后记得重新计算相关的计算字段
5. 使用指针类型的切片调用 `ProcessManualEditItems`
6. `ManualId` 用于前端识别哪些数据是手动编辑的
7. **新功能**: 合理使用时间范围参数，避免查询过多无关数据
8. **性能优化**: 店铺过滤可以显著提升查询性能 