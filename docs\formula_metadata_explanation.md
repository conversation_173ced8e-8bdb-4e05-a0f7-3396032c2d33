# 财务公式表达式系统 - 元数据字段详解

## FormulaRef结构字段说明

### 1. `code` 字段
**作用：** 分类编码，提供稳定的业务标识
```go
type FormulaRef struct {
    ID   uint   `json:"id"`     // 系统内部ID
    Code string `json:"code"`   // 业务编码，如 "REVENUE", "COST"
    Name string `json:"name"`   // 显示名称，如 "营业收入", "营业成本"
}
```

**重要性：**
- **业务标识**：即使分类名称变更，编码保持稳定
- **系统集成**：与其他系统对接时使用编码而非名称
- **数据迁移**：系统升级时通过编码匹配数据
- **审计追踪**：财务审计时编码是重要的追踪标识

### 2. `ref_type` 字段
**作用：** 引用类型，区分数据来源
```go
const (
    RefTypeDirect     = "direct"     // 直接数据分类
    RefTypeCalculated = "calculated" // 计算型分类
    RefTypeExternal   = "external"   // 外部系统数据
)
```

**应用场景：**
- **依赖分析**：计算型分类必须在直接分类之后计算
- **数据验证**：直接分类需要验证数据完整性
- **性能优化**：不同类型采用不同的缓存策略
- **错误诊断**：快速定位问题是数据问题还是计算问题

### 3. `is_valid` 字段
**作用：** 引用有效性标记
```go
type FormulaRef struct {
    IsValid bool `json:"is_valid"` // 引用是否有效
}
```

**维护机制：**
- **实时检查**：每次加载配置时验证引用有效性
- **自动修复**：发现无效引用时尝试自动修复
- **告警机制**：无效引用触发系统告警

## 元数据生成和维护机制

### 自动生成逻辑
```go
func (builder *FormulaJSONBuilder) generateFormulaRef(categoryId uint) FormulaRef {
    config := builder.configs[categoryId]
    
    return FormulaRef{
        ID:      config.Id,
        Name:    config.CategoryName,
        Code:    config.CategoryCode,
        RefType: determineRefType(config),
        IsValid: true,
    }
}

func determineRefType(config CostTypeConfigItem) string {
    if config.IsCalculated {
        return "calculated"
    }
    return "direct"
}
```

### 维护策略
1. **配置加载时**：自动同步最新的分类信息
2. **定期检查**：后台任务定期验证引用有效性
3. **变更触发**：分类配置变更时自动更新相关公式

## 2. 简化表达式语法设计

### 设计原则
- **自然语言化**：贴近人类理解方式
- **符号简洁**：减少复杂的函数调用
- **易于输入**：支持常见的数学符号

### 支持的表达式格式

#### 基础运算
```
[营业收入] - [营业成本]           # 减法
[销售费用] + [管理费用]           # 加法
[固定成本] * [数量]               # 乘法
[总成本] / [产量]                 # 除法
```

#### 百分比计算
```
[毛利润] / [营业收入] %           # 毛利率
[净利润] / [营业收入] %           # 净利率
[期间费用] / [营业收入] %         # 期间费用率
```

#### 复合表达式
```
([营业收入] - [营业成本]) / [营业收入] %    # 毛利率
[营业收入] - [营业成本] - [期间费用]       # 营业利润
```

#### 中文运算符支持
```
[营业收入] 减去 [营业成本]         # 等同于 [营业收入] - [营业成本]
[毛利润] 除以 [营业收入] %         # 等同于 [毛利润] / [营业收入] %
```

## 3. 实际业务场景示例

### 场景1：毛利润计算
**业务需求**：计算毛利润 = 营业收入 - 营业成本

**简化表达式**：
```
[营业收入] - [营业成本]
```

**JSON配置**：
```json
{
  "version": "1.0",
  "expression": "[营业收入] - [营业成本]",
  "storage_expr": "#1 - #2",
  "references": [
    {"id": 1, "name": "营业收入", "code": "REVENUE", "ref_type": "direct", "is_valid": true},
    {"id": 2, "name": "营业成本", "code": "COST", "ref_type": "direct", "is_valid": true}
  ],
  "metadata": {
    "description": "毛利润计算：营业收入减去营业成本",
    "business_rule": "反映企业主营业务的基本盈利能力"
  }
}
```

**预期计算结果**：
```
营业收入: 1000万  营业成本: 600万  → 毛利润: 400万
```

### 场景2：毛利率计算
**业务需求**：计算毛利率 = 毛利润 / 营业收入 × 100%

**简化表达式**：
```
[毛利润] / [营业收入] %
```

**JSON配置**：
```json
{
  "version": "1.0",
  "expression": "[毛利润] / [营业收入] %",
  "storage_expr": "#100 / #1 * 100",
  "references": [
    {"id": 100, "name": "毛利润", "code": "GROSS_PROFIT", "ref_type": "calculated", "is_valid": true},
    {"id": 1, "name": "营业收入", "code": "REVENUE", "ref_type": "direct", "is_valid": true}
  ],
  "metadata": {
    "description": "毛利率：毛利润占营业收入的百分比",
    "business_rule": "衡量产品或服务的盈利能力"
  }
}
```

**预期计算结果**：
```
毛利润: 400万  营业收入: 1000万  → 毛利率: 40%
```

### 场景3：期间费用合计
**业务需求**：计算期间费用合计 = 销售费用 + 管理费用 + 财务费用

**简化表达式**：
```
[销售费用] + [管理费用] + [财务费用]
```

**JSON配置**：
```json
{
  "version": "1.0",
  "expression": "[销售费用] + [管理费用] + [财务费用]",
  "storage_expr": "#3 + #4 + #5",
  "references": [
    {"id": 3, "name": "销售费用", "code": "SALES_EXPENSE", "ref_type": "direct", "is_valid": true},
    {"id": 4, "name": "管理费用", "code": "ADMIN_EXPENSE", "ref_type": "direct", "is_valid": true},
    {"id": 5, "name": "财务费用", "code": "FINANCE_EXPENSE", "ref_type": "direct", "is_valid": true}
  ],
  "metadata": {
    "description": "期间费用合计：三项费用的总和",
    "business_rule": "企业经营期间发生的各项费用总额"
  }
}
```

**预期计算结果**：
```
销售费用: 50万  管理费用: 30万  财务费用: 20万  → 期间费用合计: 100万
```

### 场景4：营业利润
**业务需求**：计算营业利润 = 毛利润 - 期间费用合计

**简化表达式**：
```
[毛利润] - [期间费用合计]
```

**JSON配置**：
```json
{
  "version": "1.0",
  "expression": "[毛利润] - [期间费用合计]",
  "storage_expr": "#100 - #101",
  "references": [
    {"id": 100, "name": "毛利润", "code": "GROSS_PROFIT", "ref_type": "calculated", "is_valid": true},
    {"id": 101, "name": "期间费用合计", "code": "PERIOD_EXPENSE_TOTAL", "ref_type": "calculated", "is_valid": true}
  ],
  "metadata": {
    "description": "营业利润：毛利润减去期间费用",
    "business_rule": "企业主营业务的最终盈利水平"
  }
}
```

**预期计算结果**：
```
毛利润: 400万  期间费用合计: 100万  → 营业利润: 300万
```

### 场景5：营业利润率
**业务需求**：计算营业利润率 = 营业利润 / 营业收入 × 100%

**简化表达式**：
```
[营业利润] / [营业收入] %
```

**JSON配置**：
```json
{
  "version": "1.0",
  "expression": "[营业利润] / [营业收入] %",
  "storage_expr": "#102 / #1 * 100",
  "references": [
    {"id": 102, "name": "营业利润", "code": "OPERATING_PROFIT", "ref_type": "calculated", "is_valid": true},
    {"id": 1, "name": "营业收入", "code": "REVENUE", "ref_type": "direct", "is_valid": true}
  ],
  "metadata": {
    "description": "营业利润率：营业利润占营业收入的百分比",
    "business_rule": "衡量企业经营效率和盈利能力的核心指标"
  }
}
```

**预期计算结果**：
```
营业利润: 300万  营业收入: 1000万  → 营业利润率: 30%
```
