// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSundriesMaterialHistory(db *gorm.DB, opts ...gen.DOOption) insSundriesMaterialHistory {
	_insSundriesMaterialHistory := insSundriesMaterialHistory{}

	_insSundriesMaterialHistory.insSundriesMaterialHistoryDo.UseDB(db, opts...)
	_insSundriesMaterialHistory.insSundriesMaterialHistoryDo.UseModel(&insbuy.InsSundriesMaterialHistory{})

	tableName := _insSundriesMaterialHistory.insSundriesMaterialHistoryDo.TableName()
	_insSundriesMaterialHistory.ALL = field.NewAsterisk(tableName)
	_insSundriesMaterialHistory.ID = field.NewUint(tableName, "id")
	_insSundriesMaterialHistory.CreatedAt = field.NewTime(tableName, "created_at")
	_insSundriesMaterialHistory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSundriesMaterialHistory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSundriesMaterialHistory.MaterialId = field.NewInt(tableName, "material_id")
	_insSundriesMaterialHistory.OldShopPrice = field.NewFloat64(tableName, "old_shop_price")
	_insSundriesMaterialHistory.OldMarketPrice = field.NewFloat64(tableName, "old_market_price")
	_insSundriesMaterialHistory.NewShopPrice = field.NewFloat64(tableName, "new_shop_price")
	_insSundriesMaterialHistory.NewMarketPrice = field.NewFloat64(tableName, "new_market_price")
	_insSundriesMaterialHistory.Remark = field.NewString(tableName, "remark")
	_insSundriesMaterialHistory.OperatorId = field.NewUint(tableName, "operator_id")

	_insSundriesMaterialHistory.fillFieldMap()

	return _insSundriesMaterialHistory
}

type insSundriesMaterialHistory struct {
	insSundriesMaterialHistoryDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	MaterialId     field.Int
	OldShopPrice   field.Float64
	OldMarketPrice field.Float64
	NewShopPrice   field.Float64
	NewMarketPrice field.Float64
	Remark         field.String
	OperatorId     field.Uint

	fieldMap map[string]field.Expr
}

func (i insSundriesMaterialHistory) Table(newTableName string) *insSundriesMaterialHistory {
	i.insSundriesMaterialHistoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSundriesMaterialHistory) As(alias string) *insSundriesMaterialHistory {
	i.insSundriesMaterialHistoryDo.DO = *(i.insSundriesMaterialHistoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSundriesMaterialHistory) updateTableName(table string) *insSundriesMaterialHistory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.MaterialId = field.NewInt(table, "material_id")
	i.OldShopPrice = field.NewFloat64(table, "old_shop_price")
	i.OldMarketPrice = field.NewFloat64(table, "old_market_price")
	i.NewShopPrice = field.NewFloat64(table, "new_shop_price")
	i.NewMarketPrice = field.NewFloat64(table, "new_market_price")
	i.Remark = field.NewString(table, "remark")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insSundriesMaterialHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSundriesMaterialHistory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 11)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["old_shop_price"] = i.OldShopPrice
	i.fieldMap["old_market_price"] = i.OldMarketPrice
	i.fieldMap["new_shop_price"] = i.NewShopPrice
	i.fieldMap["new_market_price"] = i.NewMarketPrice
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insSundriesMaterialHistory) clone(db *gorm.DB) insSundriesMaterialHistory {
	i.insSundriesMaterialHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSundriesMaterialHistory) replaceDB(db *gorm.DB) insSundriesMaterialHistory {
	i.insSundriesMaterialHistoryDo.ReplaceDB(db)
	return i
}

type insSundriesMaterialHistoryDo struct{ gen.DO }

func (i insSundriesMaterialHistoryDo) Debug() *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insSundriesMaterialHistoryDo) WithContext(ctx context.Context) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSundriesMaterialHistoryDo) ReadDB() *insSundriesMaterialHistoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSundriesMaterialHistoryDo) WriteDB() *insSundriesMaterialHistoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSundriesMaterialHistoryDo) Session(config *gorm.Session) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSundriesMaterialHistoryDo) Clauses(conds ...clause.Expression) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSundriesMaterialHistoryDo) Returning(value interface{}, columns ...string) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSundriesMaterialHistoryDo) Not(conds ...gen.Condition) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSundriesMaterialHistoryDo) Or(conds ...gen.Condition) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSundriesMaterialHistoryDo) Select(conds ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSundriesMaterialHistoryDo) Where(conds ...gen.Condition) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSundriesMaterialHistoryDo) Order(conds ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSundriesMaterialHistoryDo) Distinct(cols ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSundriesMaterialHistoryDo) Omit(cols ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSundriesMaterialHistoryDo) Join(table schema.Tabler, on ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSundriesMaterialHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSundriesMaterialHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSundriesMaterialHistoryDo) Group(cols ...field.Expr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSundriesMaterialHistoryDo) Having(conds ...gen.Condition) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSundriesMaterialHistoryDo) Limit(limit int) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSundriesMaterialHistoryDo) Offset(offset int) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSundriesMaterialHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSundriesMaterialHistoryDo) Unscoped() *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSundriesMaterialHistoryDo) Create(values ...*insbuy.InsSundriesMaterialHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSundriesMaterialHistoryDo) CreateInBatches(values []*insbuy.InsSundriesMaterialHistory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSundriesMaterialHistoryDo) Save(values ...*insbuy.InsSundriesMaterialHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSundriesMaterialHistoryDo) First() (*insbuy.InsSundriesMaterialHistory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialHistory), nil
	}
}

func (i insSundriesMaterialHistoryDo) Take() (*insbuy.InsSundriesMaterialHistory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialHistory), nil
	}
}

func (i insSundriesMaterialHistoryDo) Last() (*insbuy.InsSundriesMaterialHistory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialHistory), nil
	}
}

func (i insSundriesMaterialHistoryDo) Find() ([]*insbuy.InsSundriesMaterialHistory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSundriesMaterialHistory), err
}

func (i insSundriesMaterialHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSundriesMaterialHistory, err error) {
	buf := make([]*insbuy.InsSundriesMaterialHistory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSundriesMaterialHistoryDo) FindInBatches(result *[]*insbuy.InsSundriesMaterialHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSundriesMaterialHistoryDo) Attrs(attrs ...field.AssignExpr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSundriesMaterialHistoryDo) Assign(attrs ...field.AssignExpr) *insSundriesMaterialHistoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSundriesMaterialHistoryDo) Joins(fields ...field.RelationField) *insSundriesMaterialHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSundriesMaterialHistoryDo) Preload(fields ...field.RelationField) *insSundriesMaterialHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSundriesMaterialHistoryDo) FirstOrInit() (*insbuy.InsSundriesMaterialHistory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialHistory), nil
	}
}

func (i insSundriesMaterialHistoryDo) FirstOrCreate() (*insbuy.InsSundriesMaterialHistory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialHistory), nil
	}
}

func (i insSundriesMaterialHistoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsSundriesMaterialHistory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSundriesMaterialHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSundriesMaterialHistoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSundriesMaterialHistoryDo) Delete(models ...*insbuy.InsSundriesMaterialHistory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSundriesMaterialHistoryDo) withDO(do gen.Dao) *insSundriesMaterialHistoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
