// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseHaiChang(db *gorm.DB, opts ...gen.DOOption) insWarehouseHaiChang {
	_insWarehouseHaiChang := insWarehouseHaiChang{}

	_insWarehouseHaiChang.insWarehouseHaiChangDo.UseDB(db, opts...)
	_insWarehouseHaiChang.insWarehouseHaiChangDo.UseModel(&insbuy.InsWarehouseHaiChang{})

	tableName := _insWarehouseHaiChang.insWarehouseHaiChangDo.TableName()
	_insWarehouseHaiChang.ALL = field.NewAsterisk(tableName)
	_insWarehouseHaiChang.ID = field.NewUint(tableName, "id")
	_insWarehouseHaiChang.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseHaiChang.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseHaiChang.LogId = field.NewUint(tableName, "log_id")
	_insWarehouseHaiChang.Name = field.NewString(tableName, "name")
	_insWarehouseHaiChang.FileName = field.NewString(tableName, "file_name")
	_insWarehouseHaiChang.OrderSn = field.NewString(tableName, "order_sn")
	_insWarehouseHaiChang.Body = field.NewField(tableName, "body")

	_insWarehouseHaiChang.fillFieldMap()

	return _insWarehouseHaiChang
}

type insWarehouseHaiChang struct {
	insWarehouseHaiChangDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	LogId     field.Uint
	Name      field.String
	FileName  field.String
	OrderSn   field.String
	Body      field.Field

	fieldMap map[string]field.Expr
}

func (i insWarehouseHaiChang) Table(newTableName string) *insWarehouseHaiChang {
	i.insWarehouseHaiChangDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseHaiChang) As(alias string) *insWarehouseHaiChang {
	i.insWarehouseHaiChangDo.DO = *(i.insWarehouseHaiChangDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseHaiChang) updateTableName(table string) *insWarehouseHaiChang {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.LogId = field.NewUint(table, "log_id")
	i.Name = field.NewString(table, "name")
	i.FileName = field.NewString(table, "file_name")
	i.OrderSn = field.NewString(table, "order_sn")
	i.Body = field.NewField(table, "body")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseHaiChang) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseHaiChang) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["log_id"] = i.LogId
	i.fieldMap["name"] = i.Name
	i.fieldMap["file_name"] = i.FileName
	i.fieldMap["order_sn"] = i.OrderSn
	i.fieldMap["body"] = i.Body
}

func (i insWarehouseHaiChang) clone(db *gorm.DB) insWarehouseHaiChang {
	i.insWarehouseHaiChangDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseHaiChang) replaceDB(db *gorm.DB) insWarehouseHaiChang {
	i.insWarehouseHaiChangDo.ReplaceDB(db)
	return i
}

type insWarehouseHaiChangDo struct{ gen.DO }

func (i insWarehouseHaiChangDo) Debug() *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseHaiChangDo) WithContext(ctx context.Context) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseHaiChangDo) ReadDB() *insWarehouseHaiChangDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseHaiChangDo) WriteDB() *insWarehouseHaiChangDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseHaiChangDo) Session(config *gorm.Session) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseHaiChangDo) Clauses(conds ...clause.Expression) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseHaiChangDo) Returning(value interface{}, columns ...string) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseHaiChangDo) Not(conds ...gen.Condition) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseHaiChangDo) Or(conds ...gen.Condition) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseHaiChangDo) Select(conds ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseHaiChangDo) Where(conds ...gen.Condition) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseHaiChangDo) Order(conds ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseHaiChangDo) Distinct(cols ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseHaiChangDo) Omit(cols ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseHaiChangDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseHaiChangDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseHaiChangDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseHaiChangDo) Group(cols ...field.Expr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseHaiChangDo) Having(conds ...gen.Condition) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseHaiChangDo) Limit(limit int) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseHaiChangDo) Offset(offset int) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseHaiChangDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseHaiChangDo) Unscoped() *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseHaiChangDo) Create(values ...*insbuy.InsWarehouseHaiChang) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseHaiChangDo) CreateInBatches(values []*insbuy.InsWarehouseHaiChang, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseHaiChangDo) Save(values ...*insbuy.InsWarehouseHaiChang) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseHaiChangDo) First() (*insbuy.InsWarehouseHaiChang, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChang), nil
	}
}

func (i insWarehouseHaiChangDo) Take() (*insbuy.InsWarehouseHaiChang, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChang), nil
	}
}

func (i insWarehouseHaiChangDo) Last() (*insbuy.InsWarehouseHaiChang, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChang), nil
	}
}

func (i insWarehouseHaiChangDo) Find() ([]*insbuy.InsWarehouseHaiChang, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseHaiChang), err
}

func (i insWarehouseHaiChangDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseHaiChang, err error) {
	buf := make([]*insbuy.InsWarehouseHaiChang, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseHaiChangDo) FindInBatches(result *[]*insbuy.InsWarehouseHaiChang, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseHaiChangDo) Attrs(attrs ...field.AssignExpr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseHaiChangDo) Assign(attrs ...field.AssignExpr) *insWarehouseHaiChangDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseHaiChangDo) Joins(fields ...field.RelationField) *insWarehouseHaiChangDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseHaiChangDo) Preload(fields ...field.RelationField) *insWarehouseHaiChangDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseHaiChangDo) FirstOrInit() (*insbuy.InsWarehouseHaiChang, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChang), nil
	}
}

func (i insWarehouseHaiChangDo) FirstOrCreate() (*insbuy.InsWarehouseHaiChang, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChang), nil
	}
}

func (i insWarehouseHaiChangDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseHaiChang, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseHaiChangDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseHaiChangDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseHaiChangDo) Delete(models ...*insbuy.InsWarehouseHaiChang) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseHaiChangDo) withDO(do gen.Dao) *insWarehouseHaiChangDo {
	i.DO = *do.(*gen.DO)
	return i
}
