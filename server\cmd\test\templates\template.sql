SELECT
    {{range $index, $field := .Fields}}
    {{.FieldExpression}} AS {{.Alias}}{{if not (last $index (len $.Fields))}},{{end}}
    {{end}}
FROM
    {{.BaseTable}}{{if .}}{{end}}
    {{range .JoinConditions}}
    {{.JoinType}} {{.Table}} ON {{.On}}
    {{end}}
{{if .Filters}}
WHERE
   {{buildFilters .Filters}}
{{end}}
{{if .GroupBy}}
GROUP BY
    {{range $index, $group := .GroupBy}}
    {{$group}}{{if not (last $index (len $.GroupBy))}},{{end}}
    {{end}}
{{end}}
{{if .Sorts}}
ORDER BY
    {{range $index, $sort := .Sorts}}
    {{$sort}}{{if not (last $index (len $.Sorts))}},{{end}}
    {{end}}
{{end}};
