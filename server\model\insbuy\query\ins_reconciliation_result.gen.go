// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReconciliationResult(db *gorm.DB, opts ...gen.DOOption) insReconciliationResult {
	_insReconciliationResult := insReconciliationResult{}

	_insReconciliationResult.insReconciliationResultDo.UseDB(db, opts...)
	_insReconciliationResult.insReconciliationResultDo.UseModel(&insbuy.InsReconciliationResult{})

	tableName := _insReconciliationResult.insReconciliationResultDo.TableName()
	_insReconciliationResult.ALL = field.NewAsterisk(tableName)
	_insReconciliationResult.ID = field.NewUint(tableName, "id")
	_insReconciliationResult.CreatedAt = field.NewTime(tableName, "created_at")
	_insReconciliationResult.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReconciliationResult.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReconciliationResult.StoreId = field.NewUint(tableName, "store_id")
	_insReconciliationResult.TaskId = field.NewUint(tableName, "task_id")
	_insReconciliationResult.Dim = field.NewInt(tableName, "dim")
	_insReconciliationResult.Cycle = field.NewInt(tableName, "cycle")
	_insReconciliationResult.Channel = field.NewString(tableName, "channel")
	_insReconciliationResult.StartDate = field.NewTime(tableName, "start_date")
	_insReconciliationResult.EndDate = field.NewTime(tableName, "end_date")
	_insReconciliationResult.BusinessIncome = field.NewFloat64(tableName, "business_income")
	_insReconciliationResult.ActualReceipt = field.NewFloat64(tableName, "actual_receipt")
	_insReconciliationResult.PaymentFee = field.NewFloat64(tableName, "payment_fee")
	_insReconciliationResult.Refund = field.NewFloat64(tableName, "refund")
	_insReconciliationResult.BadDebt = field.NewFloat64(tableName, "bad_debt")
	_insReconciliationResult.Difference = field.NewFloat64(tableName, "difference")
	_insReconciliationResult.DifferenceRate = field.NewFloat64(tableName, "difference_rate")
	_insReconciliationResult.AgreementDiffRate = field.NewFloat64(tableName, "agreement_diff_rate")
	_insReconciliationResult.Adjustment = field.NewFloat64(tableName, "adjustment")
	_insReconciliationResult.AuditNo = field.NewString(tableName, "audit_no")
	_insReconciliationResult.ApplyStatus = field.NewInt(tableName, "apply_status")

	_insReconciliationResult.fillFieldMap()

	return _insReconciliationResult
}

type insReconciliationResult struct {
	insReconciliationResultDo

	ALL               field.Asterisk
	ID                field.Uint
	CreatedAt         field.Time
	UpdatedAt         field.Time
	DeletedAt         field.Field
	StoreId           field.Uint
	TaskId            field.Uint
	Dim               field.Int
	Cycle             field.Int
	Channel           field.String
	StartDate         field.Time
	EndDate           field.Time
	BusinessIncome    field.Float64
	ActualReceipt     field.Float64
	PaymentFee        field.Float64
	Refund            field.Float64
	BadDebt           field.Float64
	Difference        field.Float64
	DifferenceRate    field.Float64
	AgreementDiffRate field.Float64
	Adjustment        field.Float64
	AuditNo           field.String
	ApplyStatus       field.Int

	fieldMap map[string]field.Expr
}

func (i insReconciliationResult) Table(newTableName string) *insReconciliationResult {
	i.insReconciliationResultDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReconciliationResult) As(alias string) *insReconciliationResult {
	i.insReconciliationResultDo.DO = *(i.insReconciliationResultDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReconciliationResult) updateTableName(table string) *insReconciliationResult {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.TaskId = field.NewUint(table, "task_id")
	i.Dim = field.NewInt(table, "dim")
	i.Cycle = field.NewInt(table, "cycle")
	i.Channel = field.NewString(table, "channel")
	i.StartDate = field.NewTime(table, "start_date")
	i.EndDate = field.NewTime(table, "end_date")
	i.BusinessIncome = field.NewFloat64(table, "business_income")
	i.ActualReceipt = field.NewFloat64(table, "actual_receipt")
	i.PaymentFee = field.NewFloat64(table, "payment_fee")
	i.Refund = field.NewFloat64(table, "refund")
	i.BadDebt = field.NewFloat64(table, "bad_debt")
	i.Difference = field.NewFloat64(table, "difference")
	i.DifferenceRate = field.NewFloat64(table, "difference_rate")
	i.AgreementDiffRate = field.NewFloat64(table, "agreement_diff_rate")
	i.Adjustment = field.NewFloat64(table, "adjustment")
	i.AuditNo = field.NewString(table, "audit_no")
	i.ApplyStatus = field.NewInt(table, "apply_status")

	i.fillFieldMap()

	return i
}

func (i *insReconciliationResult) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReconciliationResult) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 22)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["task_id"] = i.TaskId
	i.fieldMap["dim"] = i.Dim
	i.fieldMap["cycle"] = i.Cycle
	i.fieldMap["channel"] = i.Channel
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["business_income"] = i.BusinessIncome
	i.fieldMap["actual_receipt"] = i.ActualReceipt
	i.fieldMap["payment_fee"] = i.PaymentFee
	i.fieldMap["refund"] = i.Refund
	i.fieldMap["bad_debt"] = i.BadDebt
	i.fieldMap["difference"] = i.Difference
	i.fieldMap["difference_rate"] = i.DifferenceRate
	i.fieldMap["agreement_diff_rate"] = i.AgreementDiffRate
	i.fieldMap["adjustment"] = i.Adjustment
	i.fieldMap["audit_no"] = i.AuditNo
	i.fieldMap["apply_status"] = i.ApplyStatus
}

func (i insReconciliationResult) clone(db *gorm.DB) insReconciliationResult {
	i.insReconciliationResultDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReconciliationResult) replaceDB(db *gorm.DB) insReconciliationResult {
	i.insReconciliationResultDo.ReplaceDB(db)
	return i
}

type insReconciliationResultDo struct{ gen.DO }

func (i insReconciliationResultDo) Debug() *insReconciliationResultDo {
	return i.withDO(i.DO.Debug())
}

func (i insReconciliationResultDo) WithContext(ctx context.Context) *insReconciliationResultDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReconciliationResultDo) ReadDB() *insReconciliationResultDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReconciliationResultDo) WriteDB() *insReconciliationResultDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReconciliationResultDo) Session(config *gorm.Session) *insReconciliationResultDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReconciliationResultDo) Clauses(conds ...clause.Expression) *insReconciliationResultDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReconciliationResultDo) Returning(value interface{}, columns ...string) *insReconciliationResultDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReconciliationResultDo) Not(conds ...gen.Condition) *insReconciliationResultDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReconciliationResultDo) Or(conds ...gen.Condition) *insReconciliationResultDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReconciliationResultDo) Select(conds ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReconciliationResultDo) Where(conds ...gen.Condition) *insReconciliationResultDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReconciliationResultDo) Order(conds ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReconciliationResultDo) Distinct(cols ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReconciliationResultDo) Omit(cols ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReconciliationResultDo) Join(table schema.Tabler, on ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReconciliationResultDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReconciliationResultDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReconciliationResultDo) Group(cols ...field.Expr) *insReconciliationResultDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReconciliationResultDo) Having(conds ...gen.Condition) *insReconciliationResultDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReconciliationResultDo) Limit(limit int) *insReconciliationResultDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReconciliationResultDo) Offset(offset int) *insReconciliationResultDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReconciliationResultDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReconciliationResultDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReconciliationResultDo) Unscoped() *insReconciliationResultDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReconciliationResultDo) Create(values ...*insbuy.InsReconciliationResult) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReconciliationResultDo) CreateInBatches(values []*insbuy.InsReconciliationResult, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReconciliationResultDo) Save(values ...*insbuy.InsReconciliationResult) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReconciliationResultDo) First() (*insbuy.InsReconciliationResult, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationResult), nil
	}
}

func (i insReconciliationResultDo) Take() (*insbuy.InsReconciliationResult, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationResult), nil
	}
}

func (i insReconciliationResultDo) Last() (*insbuy.InsReconciliationResult, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationResult), nil
	}
}

func (i insReconciliationResultDo) Find() ([]*insbuy.InsReconciliationResult, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReconciliationResult), err
}

func (i insReconciliationResultDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReconciliationResult, err error) {
	buf := make([]*insbuy.InsReconciliationResult, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReconciliationResultDo) FindInBatches(result *[]*insbuy.InsReconciliationResult, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReconciliationResultDo) Attrs(attrs ...field.AssignExpr) *insReconciliationResultDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReconciliationResultDo) Assign(attrs ...field.AssignExpr) *insReconciliationResultDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReconciliationResultDo) Joins(fields ...field.RelationField) *insReconciliationResultDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReconciliationResultDo) Preload(fields ...field.RelationField) *insReconciliationResultDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReconciliationResultDo) FirstOrInit() (*insbuy.InsReconciliationResult, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationResult), nil
	}
}

func (i insReconciliationResultDo) FirstOrCreate() (*insbuy.InsReconciliationResult, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationResult), nil
	}
}

func (i insReconciliationResultDo) FindByPage(offset int, limit int) (result []*insbuy.InsReconciliationResult, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReconciliationResultDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReconciliationResultDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReconciliationResultDo) Delete(models ...*insbuy.InsReconciliationResult) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReconciliationResultDo) withDO(do gen.Dao) *insReconciliationResultDo {
	i.DO = *do.(*gen.DO)
	return i
}
