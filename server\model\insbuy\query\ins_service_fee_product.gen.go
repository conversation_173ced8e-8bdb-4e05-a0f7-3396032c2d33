// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeProduct(db *gorm.DB, opts ...gen.DOOption) insServiceFeeProduct {
	_insServiceFeeProduct := insServiceFeeProduct{}

	_insServiceFeeProduct.insServiceFeeProductDo.UseDB(db, opts...)
	_insServiceFeeProduct.insServiceFeeProductDo.UseModel(&insbuy.InsServiceFeeProduct{})

	tableName := _insServiceFeeProduct.insServiceFeeProductDo.TableName()
	_insServiceFeeProduct.ALL = field.NewAsterisk(tableName)
	_insServiceFeeProduct.ID = field.NewUint(tableName, "id")
	_insServiceFeeProduct.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeProduct.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeProduct.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeProduct.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeProduct.ProductId = field.NewUint(tableName, "product_id")
	_insServiceFeeProduct.DiscountRate = field.NewFloat64(tableName, "discount_rate")

	_insServiceFeeProduct.fillFieldMap()

	return _insServiceFeeProduct
}

type insServiceFeeProduct struct {
	insServiceFeeProductDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ServiceFeeId field.Uint
	ProductId    field.Uint
	DiscountRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insServiceFeeProduct) Table(newTableName string) *insServiceFeeProduct {
	i.insServiceFeeProductDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeProduct) As(alias string) *insServiceFeeProduct {
	i.insServiceFeeProductDo.DO = *(i.insServiceFeeProductDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeProduct) updateTableName(table string) *insServiceFeeProduct {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeProduct) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["discount_rate"] = i.DiscountRate
}

func (i insServiceFeeProduct) clone(db *gorm.DB) insServiceFeeProduct {
	i.insServiceFeeProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeProduct) replaceDB(db *gorm.DB) insServiceFeeProduct {
	i.insServiceFeeProductDo.ReplaceDB(db)
	return i
}

type insServiceFeeProductDo struct{ gen.DO }

func (i insServiceFeeProductDo) Debug() *insServiceFeeProductDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeProductDo) WithContext(ctx context.Context) *insServiceFeeProductDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeProductDo) ReadDB() *insServiceFeeProductDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeProductDo) WriteDB() *insServiceFeeProductDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeProductDo) Session(config *gorm.Session) *insServiceFeeProductDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeProductDo) Clauses(conds ...clause.Expression) *insServiceFeeProductDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeProductDo) Returning(value interface{}, columns ...string) *insServiceFeeProductDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeProductDo) Not(conds ...gen.Condition) *insServiceFeeProductDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeProductDo) Or(conds ...gen.Condition) *insServiceFeeProductDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeProductDo) Select(conds ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeProductDo) Where(conds ...gen.Condition) *insServiceFeeProductDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeProductDo) Order(conds ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeProductDo) Distinct(cols ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeProductDo) Omit(cols ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeProductDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeProductDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeProductDo) Group(cols ...field.Expr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeProductDo) Having(conds ...gen.Condition) *insServiceFeeProductDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeProductDo) Limit(limit int) *insServiceFeeProductDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeProductDo) Offset(offset int) *insServiceFeeProductDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeProductDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeProductDo) Unscoped() *insServiceFeeProductDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeProductDo) Create(values ...*insbuy.InsServiceFeeProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeProductDo) CreateInBatches(values []*insbuy.InsServiceFeeProduct, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeProductDo) Save(values ...*insbuy.InsServiceFeeProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeProductDo) First() (*insbuy.InsServiceFeeProduct, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeProduct), nil
	}
}

func (i insServiceFeeProductDo) Take() (*insbuy.InsServiceFeeProduct, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeProduct), nil
	}
}

func (i insServiceFeeProductDo) Last() (*insbuy.InsServiceFeeProduct, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeProduct), nil
	}
}

func (i insServiceFeeProductDo) Find() ([]*insbuy.InsServiceFeeProduct, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeProduct), err
}

func (i insServiceFeeProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeProduct, err error) {
	buf := make([]*insbuy.InsServiceFeeProduct, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeProductDo) FindInBatches(result *[]*insbuy.InsServiceFeeProduct, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeProductDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeProductDo) Assign(attrs ...field.AssignExpr) *insServiceFeeProductDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeProductDo) Joins(fields ...field.RelationField) *insServiceFeeProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeProductDo) Preload(fields ...field.RelationField) *insServiceFeeProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeProductDo) FirstOrInit() (*insbuy.InsServiceFeeProduct, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeProduct), nil
	}
}

func (i insServiceFeeProductDo) FirstOrCreate() (*insbuy.InsServiceFeeProduct, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeProduct), nil
	}
}

func (i insServiceFeeProductDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeProduct, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeProductDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeProductDo) Delete(models ...*insbuy.InsServiceFeeProduct) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeProductDo) withDO(do gen.Dao) *insServiceFeeProductDo {
	i.DO = *do.(*gen.DO)
	return i
}
