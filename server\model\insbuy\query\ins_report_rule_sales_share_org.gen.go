// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareOrg(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareOrg {
	_insReportRuleSalesShareOrg := insReportRuleSalesShareOrg{}

	_insReportRuleSalesShareOrg.insReportRuleSalesShareOrgDo.UseDB(db, opts...)
	_insReportRuleSalesShareOrg.insReportRuleSalesShareOrgDo.UseModel(&insbuy.InsReportRuleSalesShareOrg{})

	tableName := _insReportRuleSalesShareOrg.insReportRuleSalesShareOrgDo.TableName()
	_insReportRuleSalesShareOrg.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareOrg.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareOrg.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareOrg.OrgId = field.NewUint(tableName, "org_id")

	_insReportRuleSalesShareOrg.fillFieldMap()

	return _insReportRuleSalesShareOrg
}

type insReportRuleSalesShareOrg struct {
	insReportRuleSalesShareOrgDo

	ALL    field.Asterisk
	ID     field.Uint
	RuleId field.Uint
	OrgId  field.Uint

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareOrg) Table(newTableName string) *insReportRuleSalesShareOrg {
	i.insReportRuleSalesShareOrgDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareOrg) As(alias string) *insReportRuleSalesShareOrg {
	i.insReportRuleSalesShareOrgDo.DO = *(i.insReportRuleSalesShareOrgDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareOrg) updateTableName(table string) *insReportRuleSalesShareOrg {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.OrgId = field.NewUint(table, "org_id")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareOrg) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareOrg) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 3)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["org_id"] = i.OrgId
}

func (i insReportRuleSalesShareOrg) clone(db *gorm.DB) insReportRuleSalesShareOrg {
	i.insReportRuleSalesShareOrgDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareOrg) replaceDB(db *gorm.DB) insReportRuleSalesShareOrg {
	i.insReportRuleSalesShareOrgDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareOrgDo struct{ gen.DO }

func (i insReportRuleSalesShareOrgDo) Debug() *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareOrgDo) WithContext(ctx context.Context) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareOrgDo) ReadDB() *insReportRuleSalesShareOrgDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareOrgDo) WriteDB() *insReportRuleSalesShareOrgDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareOrgDo) Session(config *gorm.Session) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareOrgDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareOrgDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareOrgDo) Not(conds ...gen.Condition) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareOrgDo) Or(conds ...gen.Condition) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareOrgDo) Select(conds ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareOrgDo) Where(conds ...gen.Condition) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareOrgDo) Order(conds ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareOrgDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareOrgDo) Omit(cols ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareOrgDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareOrgDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareOrgDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareOrgDo) Group(cols ...field.Expr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareOrgDo) Having(conds ...gen.Condition) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareOrgDo) Limit(limit int) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareOrgDo) Offset(offset int) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareOrgDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareOrgDo) Unscoped() *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareOrgDo) Create(values ...*insbuy.InsReportRuleSalesShareOrg) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareOrgDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareOrg, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareOrgDo) Save(values ...*insbuy.InsReportRuleSalesShareOrg) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareOrgDo) First() (*insbuy.InsReportRuleSalesShareOrg, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareOrg), nil
	}
}

func (i insReportRuleSalesShareOrgDo) Take() (*insbuy.InsReportRuleSalesShareOrg, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareOrg), nil
	}
}

func (i insReportRuleSalesShareOrgDo) Last() (*insbuy.InsReportRuleSalesShareOrg, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareOrg), nil
	}
}

func (i insReportRuleSalesShareOrgDo) Find() ([]*insbuy.InsReportRuleSalesShareOrg, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareOrg), err
}

func (i insReportRuleSalesShareOrgDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareOrg, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareOrg, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareOrgDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareOrg, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareOrgDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareOrgDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareOrgDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareOrgDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareOrgDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareOrgDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareOrgDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareOrgDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareOrg, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareOrg), nil
	}
}

func (i insReportRuleSalesShareOrgDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareOrg, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareOrg), nil
	}
}

func (i insReportRuleSalesShareOrgDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareOrg, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareOrgDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareOrgDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareOrgDo) Delete(models ...*insbuy.InsReportRuleSalesShareOrg) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareOrgDo) withDO(do gen.Dao) *insReportRuleSalesShareOrgDo {
	i.DO = *do.(*gen.DO)
	return i
}
