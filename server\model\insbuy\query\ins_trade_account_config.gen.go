// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeAccountConfig(db *gorm.DB, opts ...gen.DOOption) insTradeAccountConfig {
	_insTradeAccountConfig := insTradeAccountConfig{}

	_insTradeAccountConfig.insTradeAccountConfigDo.UseDB(db, opts...)
	_insTradeAccountConfig.insTradeAccountConfigDo.UseModel(&insbuy.InsTradeAccountConfig{})

	tableName := _insTradeAccountConfig.insTradeAccountConfigDo.TableName()
	_insTradeAccountConfig.ALL = field.NewAsterisk(tableName)
	_insTradeAccountConfig.ID = field.NewUint(tableName, "id")
	_insTradeAccountConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeAccountConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeAccountConfig.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTradeAccountConfig.Status = field.NewInt(tableName, "status")
	_insTradeAccountConfig.StoreId = field.NewUint(tableName, "store_id")
	_insTradeAccountConfig.Username = field.NewString(tableName, "username")
	_insTradeAccountConfig.NickName = field.NewString(tableName, "nick_name")
	_insTradeAccountConfig.Phone = field.NewString(tableName, "phone")
	_insTradeAccountConfig.Position = field.NewString(tableName, "position")
	_insTradeAccountConfig.AccountType = field.NewInt(tableName, "account_type")
	_insTradeAccountConfig.AccountAmount = field.NewFloat64(tableName, "account_amount")
	_insTradeAccountConfig.UseAccountAmount = field.NewFloat64(tableName, "use_account_amount")
	_insTradeAccountConfig.UserId = field.NewUint(tableName, "user_id")
	_insTradeAccountConfig.OperatorId = field.NewUint(tableName, "operator_id")

	_insTradeAccountConfig.fillFieldMap()

	return _insTradeAccountConfig
}

type insTradeAccountConfig struct {
	insTradeAccountConfigDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	Status           field.Int
	StoreId          field.Uint
	Username         field.String
	NickName         field.String
	Phone            field.String
	Position         field.String
	AccountType      field.Int
	AccountAmount    field.Float64
	UseAccountAmount field.Float64
	UserId           field.Uint
	OperatorId       field.Uint

	fieldMap map[string]field.Expr
}

func (i insTradeAccountConfig) Table(newTableName string) *insTradeAccountConfig {
	i.insTradeAccountConfigDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeAccountConfig) As(alias string) *insTradeAccountConfig {
	i.insTradeAccountConfigDo.DO = *(i.insTradeAccountConfigDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeAccountConfig) updateTableName(table string) *insTradeAccountConfig {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Status = field.NewInt(table, "status")
	i.StoreId = field.NewUint(table, "store_id")
	i.Username = field.NewString(table, "username")
	i.NickName = field.NewString(table, "nick_name")
	i.Phone = field.NewString(table, "phone")
	i.Position = field.NewString(table, "position")
	i.AccountType = field.NewInt(table, "account_type")
	i.AccountAmount = field.NewFloat64(table, "account_amount")
	i.UseAccountAmount = field.NewFloat64(table, "use_account_amount")
	i.UserId = field.NewUint(table, "user_id")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insTradeAccountConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeAccountConfig) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["status"] = i.Status
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["username"] = i.Username
	i.fieldMap["nick_name"] = i.NickName
	i.fieldMap["phone"] = i.Phone
	i.fieldMap["position"] = i.Position
	i.fieldMap["account_type"] = i.AccountType
	i.fieldMap["account_amount"] = i.AccountAmount
	i.fieldMap["use_account_amount"] = i.UseAccountAmount
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insTradeAccountConfig) clone(db *gorm.DB) insTradeAccountConfig {
	i.insTradeAccountConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeAccountConfig) replaceDB(db *gorm.DB) insTradeAccountConfig {
	i.insTradeAccountConfigDo.ReplaceDB(db)
	return i
}

type insTradeAccountConfigDo struct{ gen.DO }

func (i insTradeAccountConfigDo) Debug() *insTradeAccountConfigDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeAccountConfigDo) WithContext(ctx context.Context) *insTradeAccountConfigDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeAccountConfigDo) ReadDB() *insTradeAccountConfigDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeAccountConfigDo) WriteDB() *insTradeAccountConfigDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeAccountConfigDo) Session(config *gorm.Session) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeAccountConfigDo) Clauses(conds ...clause.Expression) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeAccountConfigDo) Returning(value interface{}, columns ...string) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeAccountConfigDo) Not(conds ...gen.Condition) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeAccountConfigDo) Or(conds ...gen.Condition) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeAccountConfigDo) Select(conds ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeAccountConfigDo) Where(conds ...gen.Condition) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeAccountConfigDo) Order(conds ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeAccountConfigDo) Distinct(cols ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeAccountConfigDo) Omit(cols ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeAccountConfigDo) Join(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeAccountConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeAccountConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeAccountConfigDo) Group(cols ...field.Expr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeAccountConfigDo) Having(conds ...gen.Condition) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeAccountConfigDo) Limit(limit int) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeAccountConfigDo) Offset(offset int) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeAccountConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeAccountConfigDo) Unscoped() *insTradeAccountConfigDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeAccountConfigDo) Create(values ...*insbuy.InsTradeAccountConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeAccountConfigDo) CreateInBatches(values []*insbuy.InsTradeAccountConfig, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeAccountConfigDo) Save(values ...*insbuy.InsTradeAccountConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeAccountConfigDo) First() (*insbuy.InsTradeAccountConfig, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfig), nil
	}
}

func (i insTradeAccountConfigDo) Take() (*insbuy.InsTradeAccountConfig, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfig), nil
	}
}

func (i insTradeAccountConfigDo) Last() (*insbuy.InsTradeAccountConfig, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfig), nil
	}
}

func (i insTradeAccountConfigDo) Find() ([]*insbuy.InsTradeAccountConfig, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeAccountConfig), err
}

func (i insTradeAccountConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeAccountConfig, err error) {
	buf := make([]*insbuy.InsTradeAccountConfig, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeAccountConfigDo) FindInBatches(result *[]*insbuy.InsTradeAccountConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeAccountConfigDo) Attrs(attrs ...field.AssignExpr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeAccountConfigDo) Assign(attrs ...field.AssignExpr) *insTradeAccountConfigDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeAccountConfigDo) Joins(fields ...field.RelationField) *insTradeAccountConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeAccountConfigDo) Preload(fields ...field.RelationField) *insTradeAccountConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeAccountConfigDo) FirstOrInit() (*insbuy.InsTradeAccountConfig, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfig), nil
	}
}

func (i insTradeAccountConfigDo) FirstOrCreate() (*insbuy.InsTradeAccountConfig, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfig), nil
	}
}

func (i insTradeAccountConfigDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeAccountConfig, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeAccountConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeAccountConfigDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeAccountConfigDo) Delete(models ...*insbuy.InsTradeAccountConfig) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeAccountConfigDo) withDO(do gen.Dao) *insTradeAccountConfigDo {
	i.DO = *do.(*gen.DO)
	return i
}
