// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseAdmin(db *gorm.DB, opts ...gen.DOOption) insWarehouseAdmin {
	_insWarehouseAdmin := insWarehouseAdmin{}

	_insWarehouseAdmin.insWarehouseAdminDo.UseDB(db, opts...)
	_insWarehouseAdmin.insWarehouseAdminDo.UseModel(&insbuy.InsWarehouseAdmin{})

	tableName := _insWarehouseAdmin.insWarehouseAdminDo.TableName()
	_insWarehouseAdmin.ALL = field.NewAsterisk(tableName)
	_insWarehouseAdmin.ID = field.NewUint(tableName, "id")
	_insWarehouseAdmin.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseAdmin.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseAdmin.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseAdmin.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insWarehouseAdmin.UserId = field.NewUint(tableName, "admin_id")

	_insWarehouseAdmin.fillFieldMap()

	return _insWarehouseAdmin
}

type insWarehouseAdmin struct {
	insWarehouseAdminDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	WarehouseId field.Uint
	UserId      field.Uint

	fieldMap map[string]field.Expr
}

func (i insWarehouseAdmin) Table(newTableName string) *insWarehouseAdmin {
	i.insWarehouseAdminDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseAdmin) As(alias string) *insWarehouseAdmin {
	i.insWarehouseAdminDo.DO = *(i.insWarehouseAdminDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseAdmin) updateTableName(table string) *insWarehouseAdmin {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.UserId = field.NewUint(table, "admin_id")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseAdmin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseAdmin) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["admin_id"] = i.UserId
}

func (i insWarehouseAdmin) clone(db *gorm.DB) insWarehouseAdmin {
	i.insWarehouseAdminDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseAdmin) replaceDB(db *gorm.DB) insWarehouseAdmin {
	i.insWarehouseAdminDo.ReplaceDB(db)
	return i
}

type insWarehouseAdminDo struct{ gen.DO }

func (i insWarehouseAdminDo) Debug() *insWarehouseAdminDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseAdminDo) WithContext(ctx context.Context) *insWarehouseAdminDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseAdminDo) ReadDB() *insWarehouseAdminDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseAdminDo) WriteDB() *insWarehouseAdminDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseAdminDo) Session(config *gorm.Session) *insWarehouseAdminDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseAdminDo) Clauses(conds ...clause.Expression) *insWarehouseAdminDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseAdminDo) Returning(value interface{}, columns ...string) *insWarehouseAdminDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseAdminDo) Not(conds ...gen.Condition) *insWarehouseAdminDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseAdminDo) Or(conds ...gen.Condition) *insWarehouseAdminDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseAdminDo) Select(conds ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseAdminDo) Where(conds ...gen.Condition) *insWarehouseAdminDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseAdminDo) Order(conds ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseAdminDo) Distinct(cols ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseAdminDo) Omit(cols ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseAdminDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseAdminDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseAdminDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseAdminDo) Group(cols ...field.Expr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseAdminDo) Having(conds ...gen.Condition) *insWarehouseAdminDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseAdminDo) Limit(limit int) *insWarehouseAdminDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseAdminDo) Offset(offset int) *insWarehouseAdminDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseAdminDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseAdminDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseAdminDo) Unscoped() *insWarehouseAdminDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseAdminDo) Create(values ...*insbuy.InsWarehouseAdmin) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseAdminDo) CreateInBatches(values []*insbuy.InsWarehouseAdmin, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseAdminDo) Save(values ...*insbuy.InsWarehouseAdmin) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseAdminDo) First() (*insbuy.InsWarehouseAdmin, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseAdmin), nil
	}
}

func (i insWarehouseAdminDo) Take() (*insbuy.InsWarehouseAdmin, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseAdmin), nil
	}
}

func (i insWarehouseAdminDo) Last() (*insbuy.InsWarehouseAdmin, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseAdmin), nil
	}
}

func (i insWarehouseAdminDo) Find() ([]*insbuy.InsWarehouseAdmin, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseAdmin), err
}

func (i insWarehouseAdminDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseAdmin, err error) {
	buf := make([]*insbuy.InsWarehouseAdmin, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseAdminDo) FindInBatches(result *[]*insbuy.InsWarehouseAdmin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseAdminDo) Attrs(attrs ...field.AssignExpr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseAdminDo) Assign(attrs ...field.AssignExpr) *insWarehouseAdminDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseAdminDo) Joins(fields ...field.RelationField) *insWarehouseAdminDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseAdminDo) Preload(fields ...field.RelationField) *insWarehouseAdminDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseAdminDo) FirstOrInit() (*insbuy.InsWarehouseAdmin, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseAdmin), nil
	}
}

func (i insWarehouseAdminDo) FirstOrCreate() (*insbuy.InsWarehouseAdmin, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseAdmin), nil
	}
}

func (i insWarehouseAdminDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseAdmin, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseAdminDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseAdminDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseAdminDo) Delete(models ...*insbuy.InsWarehouseAdmin) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseAdminDo) withDO(do gen.Dao) *insWarehouseAdminDo {
	i.DO = *do.(*gen.DO)
	return i
}
