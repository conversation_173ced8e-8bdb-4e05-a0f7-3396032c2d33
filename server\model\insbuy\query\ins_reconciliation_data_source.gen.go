// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReconciliationDataSource(db *gorm.DB, opts ...gen.DOOption) insReconciliationDataSource {
	_insReconciliationDataSource := insReconciliationDataSource{}

	_insReconciliationDataSource.insReconciliationDataSourceDo.UseDB(db, opts...)
	_insReconciliationDataSource.insReconciliationDataSourceDo.UseModel(&insbuy.InsReconciliationDataSource{})

	tableName := _insReconciliationDataSource.insReconciliationDataSourceDo.TableName()
	_insReconciliationDataSource.ALL = field.NewAsterisk(tableName)
	_insReconciliationDataSource.ID = field.NewUint64(tableName, "id")
	_insReconciliationDataSource.StoreId = field.NewUint(tableName, "store_id")
	_insReconciliationDataSource.DataSourceType = field.NewInt(tableName, "data_source_type")
	_insReconciliationDataSource.Data = field.NewField(tableName, "data")
	_insReconciliationDataSource.Source = field.NewInt(tableName, "source")
	_insReconciliationDataSource.TaskId = field.NewUint(tableName, "task_id")
	_insReconciliationDataSource.FilePath = field.NewString(tableName, "file_path")
	_insReconciliationDataSource.CreatedAt = field.NewTime(tableName, "created_at")
	_insReconciliationDataSource.UpdatedAt = field.NewTime(tableName, "updated_at")

	_insReconciliationDataSource.fillFieldMap()

	return _insReconciliationDataSource
}

type insReconciliationDataSource struct {
	insReconciliationDataSourceDo

	ALL            field.Asterisk
	ID             field.Uint64
	StoreId        field.Uint
	DataSourceType field.Int
	Data           field.Field
	Source         field.Int
	TaskId         field.Uint
	FilePath       field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time

	fieldMap map[string]field.Expr
}

func (i insReconciliationDataSource) Table(newTableName string) *insReconciliationDataSource {
	i.insReconciliationDataSourceDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReconciliationDataSource) As(alias string) *insReconciliationDataSource {
	i.insReconciliationDataSourceDo.DO = *(i.insReconciliationDataSourceDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReconciliationDataSource) updateTableName(table string) *insReconciliationDataSource {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.DataSourceType = field.NewInt(table, "data_source_type")
	i.Data = field.NewField(table, "data")
	i.Source = field.NewInt(table, "source")
	i.TaskId = field.NewUint(table, "task_id")
	i.FilePath = field.NewString(table, "file_path")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")

	i.fillFieldMap()

	return i
}

func (i *insReconciliationDataSource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReconciliationDataSource) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["data_source_type"] = i.DataSourceType
	i.fieldMap["data"] = i.Data
	i.fieldMap["source"] = i.Source
	i.fieldMap["task_id"] = i.TaskId
	i.fieldMap["file_path"] = i.FilePath
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
}

func (i insReconciliationDataSource) clone(db *gorm.DB) insReconciliationDataSource {
	i.insReconciliationDataSourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReconciliationDataSource) replaceDB(db *gorm.DB) insReconciliationDataSource {
	i.insReconciliationDataSourceDo.ReplaceDB(db)
	return i
}

type insReconciliationDataSourceDo struct{ gen.DO }

func (i insReconciliationDataSourceDo) Debug() *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Debug())
}

func (i insReconciliationDataSourceDo) WithContext(ctx context.Context) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReconciliationDataSourceDo) ReadDB() *insReconciliationDataSourceDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReconciliationDataSourceDo) WriteDB() *insReconciliationDataSourceDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReconciliationDataSourceDo) Session(config *gorm.Session) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReconciliationDataSourceDo) Clauses(conds ...clause.Expression) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReconciliationDataSourceDo) Returning(value interface{}, columns ...string) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReconciliationDataSourceDo) Not(conds ...gen.Condition) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReconciliationDataSourceDo) Or(conds ...gen.Condition) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReconciliationDataSourceDo) Select(conds ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReconciliationDataSourceDo) Where(conds ...gen.Condition) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReconciliationDataSourceDo) Order(conds ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReconciliationDataSourceDo) Distinct(cols ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReconciliationDataSourceDo) Omit(cols ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReconciliationDataSourceDo) Join(table schema.Tabler, on ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReconciliationDataSourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReconciliationDataSourceDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReconciliationDataSourceDo) Group(cols ...field.Expr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReconciliationDataSourceDo) Having(conds ...gen.Condition) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReconciliationDataSourceDo) Limit(limit int) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReconciliationDataSourceDo) Offset(offset int) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReconciliationDataSourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReconciliationDataSourceDo) Unscoped() *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReconciliationDataSourceDo) Create(values ...*insbuy.InsReconciliationDataSource) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReconciliationDataSourceDo) CreateInBatches(values []*insbuy.InsReconciliationDataSource, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReconciliationDataSourceDo) Save(values ...*insbuy.InsReconciliationDataSource) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReconciliationDataSourceDo) First() (*insbuy.InsReconciliationDataSource, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationDataSource), nil
	}
}

func (i insReconciliationDataSourceDo) Take() (*insbuy.InsReconciliationDataSource, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationDataSource), nil
	}
}

func (i insReconciliationDataSourceDo) Last() (*insbuy.InsReconciliationDataSource, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationDataSource), nil
	}
}

func (i insReconciliationDataSourceDo) Find() ([]*insbuy.InsReconciliationDataSource, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReconciliationDataSource), err
}

func (i insReconciliationDataSourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReconciliationDataSource, err error) {
	buf := make([]*insbuy.InsReconciliationDataSource, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReconciliationDataSourceDo) FindInBatches(result *[]*insbuy.InsReconciliationDataSource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReconciliationDataSourceDo) Attrs(attrs ...field.AssignExpr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReconciliationDataSourceDo) Assign(attrs ...field.AssignExpr) *insReconciliationDataSourceDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReconciliationDataSourceDo) Joins(fields ...field.RelationField) *insReconciliationDataSourceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReconciliationDataSourceDo) Preload(fields ...field.RelationField) *insReconciliationDataSourceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReconciliationDataSourceDo) FirstOrInit() (*insbuy.InsReconciliationDataSource, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationDataSource), nil
	}
}

func (i insReconciliationDataSourceDo) FirstOrCreate() (*insbuy.InsReconciliationDataSource, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationDataSource), nil
	}
}

func (i insReconciliationDataSourceDo) FindByPage(offset int, limit int) (result []*insbuy.InsReconciliationDataSource, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReconciliationDataSourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReconciliationDataSourceDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReconciliationDataSourceDo) Delete(models ...*insbuy.InsReconciliationDataSource) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReconciliationDataSourceDo) withDO(do gen.Dao) *insReconciliationDataSourceDo {
	i.DO = *do.(*gen.DO)
	return i
}
