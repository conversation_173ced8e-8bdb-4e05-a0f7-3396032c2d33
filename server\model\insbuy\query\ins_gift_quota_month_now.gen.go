// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftQuotaMonthNow(db *gorm.DB, opts ...gen.DOOption) insGiftQuotaMonthNow {
	_insGiftQuotaMonthNow := insGiftQuotaMonthNow{}

	_insGiftQuotaMonthNow.insGiftQuotaMonthNowDo.UseDB(db, opts...)
	_insGiftQuotaMonthNow.insGiftQuotaMonthNowDo.UseModel(&insbuy.InsGiftQuotaMonthNow{})

	tableName := _insGiftQuotaMonthNow.insGiftQuotaMonthNowDo.TableName()
	_insGiftQuotaMonthNow.ALL = field.NewAsterisk(tableName)
	_insGiftQuotaMonthNow.ID = field.NewUint(tableName, "id")
	_insGiftQuotaMonthNow.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftQuotaMonthNow.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftQuotaMonthNow.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftQuotaMonthNow.UserId = field.NewUint(tableName, "user_id")
	_insGiftQuotaMonthNow.RuleId = field.NewInt(tableName, "rule_id")
	_insGiftQuotaMonthNow.DateType = field.NewInt(tableName, "date_type")
	_insGiftQuotaMonthNow.Quota = field.NewFloat64(tableName, "quota")
	_insGiftQuotaMonthNow.LastQuota = field.NewFloat64(tableName, "last_quota")
	_insGiftQuotaMonthNow.StoreId = field.NewUint(tableName, "store_id")

	_insGiftQuotaMonthNow.fillFieldMap()

	return _insGiftQuotaMonthNow
}

type insGiftQuotaMonthNow struct {
	insGiftQuotaMonthNowDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserId    field.Uint
	RuleId    field.Int
	DateType  field.Int
	Quota     field.Float64
	LastQuota field.Float64
	StoreId   field.Uint

	fieldMap map[string]field.Expr
}

func (i insGiftQuotaMonthNow) Table(newTableName string) *insGiftQuotaMonthNow {
	i.insGiftQuotaMonthNowDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftQuotaMonthNow) As(alias string) *insGiftQuotaMonthNow {
	i.insGiftQuotaMonthNowDo.DO = *(i.insGiftQuotaMonthNowDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftQuotaMonthNow) updateTableName(table string) *insGiftQuotaMonthNow {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserId = field.NewUint(table, "user_id")
	i.RuleId = field.NewInt(table, "rule_id")
	i.DateType = field.NewInt(table, "date_type")
	i.Quota = field.NewFloat64(table, "quota")
	i.LastQuota = field.NewFloat64(table, "last_quota")
	i.StoreId = field.NewUint(table, "store_id")

	i.fillFieldMap()

	return i
}

func (i *insGiftQuotaMonthNow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftQuotaMonthNow) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["date_type"] = i.DateType
	i.fieldMap["quota"] = i.Quota
	i.fieldMap["last_quota"] = i.LastQuota
	i.fieldMap["store_id"] = i.StoreId
}

func (i insGiftQuotaMonthNow) clone(db *gorm.DB) insGiftQuotaMonthNow {
	i.insGiftQuotaMonthNowDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftQuotaMonthNow) replaceDB(db *gorm.DB) insGiftQuotaMonthNow {
	i.insGiftQuotaMonthNowDo.ReplaceDB(db)
	return i
}

type insGiftQuotaMonthNowDo struct{ gen.DO }

func (i insGiftQuotaMonthNowDo) Debug() *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftQuotaMonthNowDo) WithContext(ctx context.Context) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftQuotaMonthNowDo) ReadDB() *insGiftQuotaMonthNowDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftQuotaMonthNowDo) WriteDB() *insGiftQuotaMonthNowDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftQuotaMonthNowDo) Session(config *gorm.Session) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftQuotaMonthNowDo) Clauses(conds ...clause.Expression) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftQuotaMonthNowDo) Returning(value interface{}, columns ...string) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftQuotaMonthNowDo) Not(conds ...gen.Condition) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftQuotaMonthNowDo) Or(conds ...gen.Condition) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftQuotaMonthNowDo) Select(conds ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftQuotaMonthNowDo) Where(conds ...gen.Condition) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftQuotaMonthNowDo) Order(conds ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftQuotaMonthNowDo) Distinct(cols ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftQuotaMonthNowDo) Omit(cols ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftQuotaMonthNowDo) Join(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftQuotaMonthNowDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftQuotaMonthNowDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftQuotaMonthNowDo) Group(cols ...field.Expr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftQuotaMonthNowDo) Having(conds ...gen.Condition) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftQuotaMonthNowDo) Limit(limit int) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftQuotaMonthNowDo) Offset(offset int) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftQuotaMonthNowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftQuotaMonthNowDo) Unscoped() *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftQuotaMonthNowDo) Create(values ...*insbuy.InsGiftQuotaMonthNow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftQuotaMonthNowDo) CreateInBatches(values []*insbuy.InsGiftQuotaMonthNow, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftQuotaMonthNowDo) Save(values ...*insbuy.InsGiftQuotaMonthNow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftQuotaMonthNowDo) First() (*insbuy.InsGiftQuotaMonthNow, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonthNow), nil
	}
}

func (i insGiftQuotaMonthNowDo) Take() (*insbuy.InsGiftQuotaMonthNow, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonthNow), nil
	}
}

func (i insGiftQuotaMonthNowDo) Last() (*insbuy.InsGiftQuotaMonthNow, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonthNow), nil
	}
}

func (i insGiftQuotaMonthNowDo) Find() ([]*insbuy.InsGiftQuotaMonthNow, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftQuotaMonthNow), err
}

func (i insGiftQuotaMonthNowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftQuotaMonthNow, err error) {
	buf := make([]*insbuy.InsGiftQuotaMonthNow, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftQuotaMonthNowDo) FindInBatches(result *[]*insbuy.InsGiftQuotaMonthNow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftQuotaMonthNowDo) Attrs(attrs ...field.AssignExpr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftQuotaMonthNowDo) Assign(attrs ...field.AssignExpr) *insGiftQuotaMonthNowDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftQuotaMonthNowDo) Joins(fields ...field.RelationField) *insGiftQuotaMonthNowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftQuotaMonthNowDo) Preload(fields ...field.RelationField) *insGiftQuotaMonthNowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftQuotaMonthNowDo) FirstOrInit() (*insbuy.InsGiftQuotaMonthNow, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonthNow), nil
	}
}

func (i insGiftQuotaMonthNowDo) FirstOrCreate() (*insbuy.InsGiftQuotaMonthNow, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonthNow), nil
	}
}

func (i insGiftQuotaMonthNowDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftQuotaMonthNow, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftQuotaMonthNowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftQuotaMonthNowDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftQuotaMonthNowDo) Delete(models ...*insbuy.InsGiftQuotaMonthNow) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftQuotaMonthNowDo) withDO(do gen.Dao) *insGiftQuotaMonthNowDo {
	i.DO = *do.(*gen.DO)
	return i
}
