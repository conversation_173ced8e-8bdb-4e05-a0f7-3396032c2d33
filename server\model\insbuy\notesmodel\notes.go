package notesmodel

import (
	"encoding/json"
	"gorm.io/datatypes"
	"strings"
)

// Notes 备注
type Notes []NotesRemark

// NotesRemark 备注详情
type NotesRemark struct {
	NotesDetailId int    `json:"notesDetailId" form:"notesDetailId"`
	Label         string `json:"label" form:"label"`
}

// ToJson 转json
func (n *Notes) ToJson() datatypes.JSON {
	marshal, _ := json.Marshal(n)
	return marshal
}

// FromJson 从json转
func (n *Notes) FromJson(data datatypes.JSON) error {
	return json.Unmarshal(data, n)
}

// JoinString 转换为string
func (n *Notes) JoinString() string {
	r := make([]string, 0)
	for _, v := range *n {
		r = append(r, v.Label)
	}
	return strings.Join(r, ",")
}
