// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeDeskCurrent(db *gorm.DB, opts ...gen.DOOption) insServiceFeeDeskCurrent {
	_insServiceFeeDeskCurrent := insServiceFeeDeskCurrent{}

	_insServiceFeeDeskCurrent.insServiceFeeDeskCurrentDo.UseDB(db, opts...)
	_insServiceFeeDeskCurrent.insServiceFeeDeskCurrentDo.UseModel(&insbuy.InsServiceFeeDeskCurrent{})

	tableName := _insServiceFeeDeskCurrent.insServiceFeeDeskCurrentDo.TableName()
	_insServiceFeeDeskCurrent.ALL = field.NewAsterisk(tableName)
	_insServiceFeeDeskCurrent.ID = field.NewUint(tableName, "id")
	_insServiceFeeDeskCurrent.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeDeskCurrent.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeDeskCurrent.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeDeskCurrent.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insServiceFeeDeskCurrent.StoreId = field.NewUint(tableName, "store_id")
	_insServiceFeeDeskCurrent.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeDeskCurrent.ServiceFee = field.NewFloat64(tableName, "service_fee")
	_insServiceFeeDeskCurrent.ServiceFeeRate = field.NewFloat64(tableName, "service_fee_rate")

	_insServiceFeeDeskCurrent.fillFieldMap()

	return _insServiceFeeDeskCurrent
}

type insServiceFeeDeskCurrent struct {
	insServiceFeeDeskCurrentDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	OpenDeskId     field.Uint
	StoreId        field.Uint
	ServiceFeeId   field.Uint
	ServiceFee     field.Float64
	ServiceFeeRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insServiceFeeDeskCurrent) Table(newTableName string) *insServiceFeeDeskCurrent {
	i.insServiceFeeDeskCurrentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeDeskCurrent) As(alias string) *insServiceFeeDeskCurrent {
	i.insServiceFeeDeskCurrentDo.DO = *(i.insServiceFeeDeskCurrentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeDeskCurrent) updateTableName(table string) *insServiceFeeDeskCurrent {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.ServiceFee = field.NewFloat64(table, "service_fee")
	i.ServiceFeeRate = field.NewFloat64(table, "service_fee_rate")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeDeskCurrent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeDeskCurrent) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["service_fee"] = i.ServiceFee
	i.fieldMap["service_fee_rate"] = i.ServiceFeeRate
}

func (i insServiceFeeDeskCurrent) clone(db *gorm.DB) insServiceFeeDeskCurrent {
	i.insServiceFeeDeskCurrentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeDeskCurrent) replaceDB(db *gorm.DB) insServiceFeeDeskCurrent {
	i.insServiceFeeDeskCurrentDo.ReplaceDB(db)
	return i
}

type insServiceFeeDeskCurrentDo struct{ gen.DO }

func (i insServiceFeeDeskCurrentDo) Debug() *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeDeskCurrentDo) WithContext(ctx context.Context) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeDeskCurrentDo) ReadDB() *insServiceFeeDeskCurrentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeDeskCurrentDo) WriteDB() *insServiceFeeDeskCurrentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeDeskCurrentDo) Session(config *gorm.Session) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeDeskCurrentDo) Clauses(conds ...clause.Expression) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeDeskCurrentDo) Returning(value interface{}, columns ...string) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeDeskCurrentDo) Not(conds ...gen.Condition) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeDeskCurrentDo) Or(conds ...gen.Condition) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeDeskCurrentDo) Select(conds ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeDeskCurrentDo) Where(conds ...gen.Condition) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeDeskCurrentDo) Order(conds ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeDeskCurrentDo) Distinct(cols ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeDeskCurrentDo) Omit(cols ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeDeskCurrentDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeDeskCurrentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeDeskCurrentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeDeskCurrentDo) Group(cols ...field.Expr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeDeskCurrentDo) Having(conds ...gen.Condition) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeDeskCurrentDo) Limit(limit int) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeDeskCurrentDo) Offset(offset int) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeDeskCurrentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeDeskCurrentDo) Unscoped() *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeDeskCurrentDo) Create(values ...*insbuy.InsServiceFeeDeskCurrent) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeDeskCurrentDo) CreateInBatches(values []*insbuy.InsServiceFeeDeskCurrent, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeDeskCurrentDo) Save(values ...*insbuy.InsServiceFeeDeskCurrent) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeDeskCurrentDo) First() (*insbuy.InsServiceFeeDeskCurrent, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDeskCurrent), nil
	}
}

func (i insServiceFeeDeskCurrentDo) Take() (*insbuy.InsServiceFeeDeskCurrent, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDeskCurrent), nil
	}
}

func (i insServiceFeeDeskCurrentDo) Last() (*insbuy.InsServiceFeeDeskCurrent, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDeskCurrent), nil
	}
}

func (i insServiceFeeDeskCurrentDo) Find() ([]*insbuy.InsServiceFeeDeskCurrent, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeDeskCurrent), err
}

func (i insServiceFeeDeskCurrentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeDeskCurrent, err error) {
	buf := make([]*insbuy.InsServiceFeeDeskCurrent, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeDeskCurrentDo) FindInBatches(result *[]*insbuy.InsServiceFeeDeskCurrent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeDeskCurrentDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeDeskCurrentDo) Assign(attrs ...field.AssignExpr) *insServiceFeeDeskCurrentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeDeskCurrentDo) Joins(fields ...field.RelationField) *insServiceFeeDeskCurrentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeDeskCurrentDo) Preload(fields ...field.RelationField) *insServiceFeeDeskCurrentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeDeskCurrentDo) FirstOrInit() (*insbuy.InsServiceFeeDeskCurrent, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDeskCurrent), nil
	}
}

func (i insServiceFeeDeskCurrentDo) FirstOrCreate() (*insbuy.InsServiceFeeDeskCurrent, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDeskCurrent), nil
	}
}

func (i insServiceFeeDeskCurrentDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeDeskCurrent, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeDeskCurrentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeDeskCurrentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeDeskCurrentDo) Delete(models ...*insbuy.InsServiceFeeDeskCurrent) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeDeskCurrentDo) withDO(do gen.Dao) *insServiceFeeDeskCurrentDo {
	i.DO = *do.(*gen.DO)
	return i
}
