// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductDetails(db *gorm.DB, opts ...gen.DOOption) insProductDetails {
	_insProductDetails := insProductDetails{}

	_insProductDetails.insProductDetailsDo.UseDB(db, opts...)
	_insProductDetails.insProductDetailsDo.UseModel(&insbuy.InsProductDetails{})

	tableName := _insProductDetails.insProductDetailsDo.TableName()
	_insProductDetails.ALL = field.NewAsterisk(tableName)
	_insProductDetails.ProductId = field.NewUint(tableName, "product_id")
	_insProductDetails.Ext = field.NewField(tableName, "ext")

	_insProductDetails.fillFieldMap()

	return _insProductDetails
}

type insProductDetails struct {
	insProductDetailsDo

	ALL       field.Asterisk
	ProductId field.Uint
	Ext       field.Field

	fieldMap map[string]field.Expr
}

func (i insProductDetails) Table(newTableName string) *insProductDetails {
	i.insProductDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductDetails) As(alias string) *insProductDetails {
	i.insProductDetailsDo.DO = *(i.insProductDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductDetails) updateTableName(table string) *insProductDetails {
	i.ALL = field.NewAsterisk(table)
	i.ProductId = field.NewUint(table, "product_id")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insProductDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 2)
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["ext"] = i.Ext
}

func (i insProductDetails) clone(db *gorm.DB) insProductDetails {
	i.insProductDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductDetails) replaceDB(db *gorm.DB) insProductDetails {
	i.insProductDetailsDo.ReplaceDB(db)
	return i
}

type insProductDetailsDo struct{ gen.DO }

func (i insProductDetailsDo) Debug() *insProductDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductDetailsDo) WithContext(ctx context.Context) *insProductDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductDetailsDo) ReadDB() *insProductDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductDetailsDo) WriteDB() *insProductDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductDetailsDo) Session(config *gorm.Session) *insProductDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductDetailsDo) Clauses(conds ...clause.Expression) *insProductDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductDetailsDo) Returning(value interface{}, columns ...string) *insProductDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductDetailsDo) Not(conds ...gen.Condition) *insProductDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductDetailsDo) Or(conds ...gen.Condition) *insProductDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductDetailsDo) Select(conds ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductDetailsDo) Where(conds ...gen.Condition) *insProductDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductDetailsDo) Order(conds ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductDetailsDo) Distinct(cols ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductDetailsDo) Omit(cols ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductDetailsDo) Group(cols ...field.Expr) *insProductDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductDetailsDo) Having(conds ...gen.Condition) *insProductDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductDetailsDo) Limit(limit int) *insProductDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductDetailsDo) Offset(offset int) *insProductDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductDetailsDo) Unscoped() *insProductDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductDetailsDo) Create(values ...*insbuy.InsProductDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductDetailsDo) CreateInBatches(values []*insbuy.InsProductDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductDetailsDo) Save(values ...*insbuy.InsProductDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductDetailsDo) First() (*insbuy.InsProductDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductDetails), nil
	}
}

func (i insProductDetailsDo) Take() (*insbuy.InsProductDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductDetails), nil
	}
}

func (i insProductDetailsDo) Last() (*insbuy.InsProductDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductDetails), nil
	}
}

func (i insProductDetailsDo) Find() ([]*insbuy.InsProductDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductDetails), err
}

func (i insProductDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductDetails, err error) {
	buf := make([]*insbuy.InsProductDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductDetailsDo) FindInBatches(result *[]*insbuy.InsProductDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductDetailsDo) Attrs(attrs ...field.AssignExpr) *insProductDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductDetailsDo) Assign(attrs ...field.AssignExpr) *insProductDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductDetailsDo) Joins(fields ...field.RelationField) *insProductDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductDetailsDo) Preload(fields ...field.RelationField) *insProductDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductDetailsDo) FirstOrInit() (*insbuy.InsProductDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductDetails), nil
	}
}

func (i insProductDetailsDo) FirstOrCreate() (*insbuy.InsProductDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductDetails), nil
	}
}

func (i insProductDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductDetailsDo) Delete(models ...*insbuy.InsProductDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductDetailsDo) withDO(do gen.Dao) *insProductDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
