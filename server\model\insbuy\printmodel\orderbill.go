package printmodel

import (
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"sort"
)

// TplOrderBill 结账单
type TplOrderBill struct {
	TplBase
	IsNetPay      int           // 是否是网上支付 0 否 1 是
	OpenTime      string        // 开台时间
	FmtDatetime   string        //结账时间
	PeopleNum     int           // 就餐人数
	DeskArea      string        // 桌台区域
	FromDeskName  string        //来源桌台
	DeskName      string        // 桌台名称
	Seller        string        // 销售
	Waiter        string        //服务员
	OrderSn       string        // 开台号
	OrderNo       string        // 订单号
	GiftAmount    jtypes.JPrice // 赠送金额
	GiftBy        string        // 赠送人
	Cashier       string        // 收银员
	LastOrderTime string        //最后下单时间
	PlayerPrice   jtypes.JPrice // 头号玩家金额

	Items     []TplOrderBillItem    // 商品列表
	GiftItems []TplOrderBillItem    //赠送商品列表
	Payments  []TplOrderBillPayment // 付款方式
	BillVip   []TplOrderBillVip     //会员卡支付详情

	//----酒吧结账单在使用----
	TotalNum       int           // 合计数量
	TotalAmount    jtypes.JPrice // 合计金额
	DiscountAmount jtypes.JPrice // 优惠金额
	RealAmount     jtypes.JPrice // 实收金额
	//----酒吧结账单在使用----

	//----餐饮结账单在使用----
	OrderAndGiftAmount jtypes.JPrice // 下单商品金额+折扣金额
	OrderAndGiftTotal  jtypes.JPrice // 下单商品金额+折扣金额+服务费
	OrderTotal         jtypes.JPrice //包含服务费
	DiscountFee        jtypes.JPrice // 优惠金额
	ServiceFee         jtypes.JPrice //服务费
	WaitPayServiceFee  jtypes.JPrice //待付服务费
	ServiceFeeTotal    jtypes.JPrice //优惠后的最终服务费合计
	PayTotal           jtypes.JPrice //支付合计-废弃
	PayTotalNoGift     jtypes.JPrice //去除赠送的支付合计
	WaitPayAmount      jtypes.JPrice //待付金额
	//----餐饮结账单在使用----

}

type TplOrderBillPayment struct {
	Name    string  // 支付方式
	VipName string  // 会员名称，可选
	Account string  // 会员账号，可选
	Amount  float64 // 金额
}

type Stall struct {
	Id      uint
	DevCode string
}

type TplOrderBillItem struct {
	ProductStall []Stall       //出品档口-非必须参数
	WarehouseId  int           //出品仓库
	Name         string        // 商品名称
	Remark       string        // 备注（可选）
	Num          int           // 规格
	Unit         string        // 套
	Price        jtypes.JPrice // 单价
	Total        jtypes.JPrice // 售价
	RealPrice    jtypes.JPrice //实付金额
	IsSubItem    bool          // 是否是子项
	IsPackage    bool          // 是否是套餐
}

// TplOrderBillVip 会员消费详情
type TplOrderBillVip struct {
	Phone        string        //手机号
	VipName      string        //会员姓名
	GiftAmount   jtypes.JPrice //赠送金额
	ChangeAmount jtypes.JPrice //变动金额
	TotalAmount  jtypes.JPrice //总金额
	PayPrice     jtypes.JPrice //支付金额
}

// Sorter 排序器

type Sorter interface {
	Sort(items []TplOrderBillItem) []TplOrderBillItem
}

type ByNameSorter struct{}

func (s ByNameSorter) Sort(items []TplOrderBillItem) []TplOrderBillItem {
	sort.Slice(items, func(i, j int) bool {
		return items[i].Name < items[j].Name
	})
	return items
}

// ByNameDescSorter 名称倒叙
type ByNameDescSorter struct{}

func (s ByNameDescSorter) Sort(items []TplOrderBillItem) []TplOrderBillItem {
	sort.Slice(items, func(i, j int) bool {
		return items[i].Name > items[j].Name
	})
	return items
}

// ByPriceSorter 价格排序
type ByPriceSorter struct{}

func (s ByPriceSorter) Sort(items []TplOrderBillItem) []TplOrderBillItem {
	sort.Slice(items, func(i, j int) bool {
		return items[i].Price > items[j].Price // 降序
	})
	return items
}

// ByPriceDescSorter 价格倒叙
type ByPriceDescSorter struct{}

func (s ByPriceDescSorter) Sort(items []TplOrderBillItem) []TplOrderBillItem {
	sort.Slice(items, func(i, j int) bool {
		return items[i].Price < items[j].Price // 升序
	})
	return items
}
