// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsNotes(db *gorm.DB, opts ...gen.DOOption) insNotes {
	_insNotes := insNotes{}

	_insNotes.insNotesDo.UseDB(db, opts...)
	_insNotes.insNotesDo.UseModel(&insbuy.InsNotes{})

	tableName := _insNotes.insNotesDo.TableName()
	_insNotes.ALL = field.NewAsterisk(tableName)
	_insNotes.ID = field.NewUint(tableName, "id")
	_insNotes.CreatedAt = field.NewTime(tableName, "created_at")
	_insNotes.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insNotes.DeletedAt = field.NewField(tableName, "deleted_at")
	_insNotes.StoreId = field.NewInt(tableName, "store_id")
	_insNotes.Name = field.NewString(tableName, "name")
	_insNotes.Code = field.NewString(tableName, "code")
	_insNotes.Status = field.NewUint(tableName, "status")

	_insNotes.fillFieldMap()

	return _insNotes
}

type insNotes struct {
	insNotesDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	StoreId   field.Int
	Name      field.String
	Code      field.String
	Status    field.Uint

	fieldMap map[string]field.Expr
}

func (i insNotes) Table(newTableName string) *insNotes {
	i.insNotesDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insNotes) As(alias string) *insNotes {
	i.insNotesDo.DO = *(i.insNotesDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insNotes) updateTableName(table string) *insNotes {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewInt(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Code = field.NewString(table, "code")
	i.Status = field.NewUint(table, "status")

	i.fillFieldMap()

	return i
}

func (i *insNotes) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insNotes) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["code"] = i.Code
	i.fieldMap["status"] = i.Status
}

func (i insNotes) clone(db *gorm.DB) insNotes {
	i.insNotesDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insNotes) replaceDB(db *gorm.DB) insNotes {
	i.insNotesDo.ReplaceDB(db)
	return i
}

type insNotesDo struct{ gen.DO }

func (i insNotesDo) Debug() *insNotesDo {
	return i.withDO(i.DO.Debug())
}

func (i insNotesDo) WithContext(ctx context.Context) *insNotesDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insNotesDo) ReadDB() *insNotesDo {
	return i.Clauses(dbresolver.Read)
}

func (i insNotesDo) WriteDB() *insNotesDo {
	return i.Clauses(dbresolver.Write)
}

func (i insNotesDo) Session(config *gorm.Session) *insNotesDo {
	return i.withDO(i.DO.Session(config))
}

func (i insNotesDo) Clauses(conds ...clause.Expression) *insNotesDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insNotesDo) Returning(value interface{}, columns ...string) *insNotesDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insNotesDo) Not(conds ...gen.Condition) *insNotesDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insNotesDo) Or(conds ...gen.Condition) *insNotesDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insNotesDo) Select(conds ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insNotesDo) Where(conds ...gen.Condition) *insNotesDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insNotesDo) Order(conds ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insNotesDo) Distinct(cols ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insNotesDo) Omit(cols ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insNotesDo) Join(table schema.Tabler, on ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insNotesDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insNotesDo) RightJoin(table schema.Tabler, on ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insNotesDo) Group(cols ...field.Expr) *insNotesDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insNotesDo) Having(conds ...gen.Condition) *insNotesDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insNotesDo) Limit(limit int) *insNotesDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insNotesDo) Offset(offset int) *insNotesDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insNotesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insNotesDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insNotesDo) Unscoped() *insNotesDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insNotesDo) Create(values ...*insbuy.InsNotes) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insNotesDo) CreateInBatches(values []*insbuy.InsNotes, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insNotesDo) Save(values ...*insbuy.InsNotes) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insNotesDo) First() (*insbuy.InsNotes, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotes), nil
	}
}

func (i insNotesDo) Take() (*insbuy.InsNotes, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotes), nil
	}
}

func (i insNotesDo) Last() (*insbuy.InsNotes, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotes), nil
	}
}

func (i insNotesDo) Find() ([]*insbuy.InsNotes, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsNotes), err
}

func (i insNotesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsNotes, err error) {
	buf := make([]*insbuy.InsNotes, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insNotesDo) FindInBatches(result *[]*insbuy.InsNotes, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insNotesDo) Attrs(attrs ...field.AssignExpr) *insNotesDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insNotesDo) Assign(attrs ...field.AssignExpr) *insNotesDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insNotesDo) Joins(fields ...field.RelationField) *insNotesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insNotesDo) Preload(fields ...field.RelationField) *insNotesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insNotesDo) FirstOrInit() (*insbuy.InsNotes, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotes), nil
	}
}

func (i insNotesDo) FirstOrCreate() (*insbuy.InsNotes, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotes), nil
	}
}

func (i insNotesDo) FindByPage(offset int, limit int) (result []*insbuy.InsNotes, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insNotesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insNotesDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insNotesDo) Delete(models ...*insbuy.InsNotes) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insNotesDo) withDO(do gen.Dao) *insNotesDo {
	i.DO = *do.(*gen.DO)
	return i
}
