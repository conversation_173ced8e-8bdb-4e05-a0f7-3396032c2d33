// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductActivity(db *gorm.DB, opts ...gen.DOOption) insProductActivity {
	_insProductActivity := insProductActivity{}

	_insProductActivity.insProductActivityDo.UseDB(db, opts...)
	_insProductActivity.insProductActivityDo.UseModel(&insbuy.InsProductActivity{})

	tableName := _insProductActivity.insProductActivityDo.TableName()
	_insProductActivity.ALL = field.NewAsterisk(tableName)
	_insProductActivity.ID = field.NewUint(tableName, "id")
	_insProductActivity.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductActivity.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductActivity.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductActivity.StoreId = field.NewInt(tableName, "store_id")
	_insProductActivity.ActivityName = field.NewString(tableName, "activity_name")
	_insProductActivity.Level = field.NewInt(tableName, "level")
	_insProductActivity.Status = field.NewInt(tableName, "status")
	_insProductActivity.IsManual = field.NewInt(tableName, "is_manual")
	_insProductActivity.StartTime = field.NewTime(tableName, "start_time")
	_insProductActivity.EndTime = field.NewTime(tableName, "end_time")
	_insProductActivity.Remark = field.NewString(tableName, "remark")
	_insProductActivity.InExclude = field.NewInt(tableName, "in_exclude")
	_insProductActivity.OperatorId = field.NewInt(tableName, "operator_id")

	_insProductActivity.fillFieldMap()

	return _insProductActivity
}

type insProductActivity struct {
	insProductActivityDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	StoreId      field.Int
	ActivityName field.String
	Level        field.Int
	Status       field.Int
	IsManual     field.Int
	StartTime    field.Time
	EndTime      field.Time
	Remark       field.String
	InExclude    field.Int
	OperatorId   field.Int

	fieldMap map[string]field.Expr
}

func (i insProductActivity) Table(newTableName string) *insProductActivity {
	i.insProductActivityDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductActivity) As(alias string) *insProductActivity {
	i.insProductActivityDo.DO = *(i.insProductActivityDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductActivity) updateTableName(table string) *insProductActivity {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewInt(table, "store_id")
	i.ActivityName = field.NewString(table, "activity_name")
	i.Level = field.NewInt(table, "level")
	i.Status = field.NewInt(table, "status")
	i.IsManual = field.NewInt(table, "is_manual")
	i.StartTime = field.NewTime(table, "start_time")
	i.EndTime = field.NewTime(table, "end_time")
	i.Remark = field.NewString(table, "remark")
	i.InExclude = field.NewInt(table, "in_exclude")
	i.OperatorId = field.NewInt(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insProductActivity) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductActivity) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["activity_name"] = i.ActivityName
	i.fieldMap["level"] = i.Level
	i.fieldMap["status"] = i.Status
	i.fieldMap["is_manual"] = i.IsManual
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["in_exclude"] = i.InExclude
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insProductActivity) clone(db *gorm.DB) insProductActivity {
	i.insProductActivityDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductActivity) replaceDB(db *gorm.DB) insProductActivity {
	i.insProductActivityDo.ReplaceDB(db)
	return i
}

type insProductActivityDo struct{ gen.DO }

func (i insProductActivityDo) Debug() *insProductActivityDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductActivityDo) WithContext(ctx context.Context) *insProductActivityDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductActivityDo) ReadDB() *insProductActivityDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductActivityDo) WriteDB() *insProductActivityDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductActivityDo) Session(config *gorm.Session) *insProductActivityDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductActivityDo) Clauses(conds ...clause.Expression) *insProductActivityDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductActivityDo) Returning(value interface{}, columns ...string) *insProductActivityDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductActivityDo) Not(conds ...gen.Condition) *insProductActivityDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductActivityDo) Or(conds ...gen.Condition) *insProductActivityDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductActivityDo) Select(conds ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductActivityDo) Where(conds ...gen.Condition) *insProductActivityDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductActivityDo) Order(conds ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductActivityDo) Distinct(cols ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductActivityDo) Omit(cols ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductActivityDo) Join(table schema.Tabler, on ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductActivityDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductActivityDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductActivityDo) Group(cols ...field.Expr) *insProductActivityDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductActivityDo) Having(conds ...gen.Condition) *insProductActivityDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductActivityDo) Limit(limit int) *insProductActivityDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductActivityDo) Offset(offset int) *insProductActivityDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductActivityDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductActivityDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductActivityDo) Unscoped() *insProductActivityDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductActivityDo) Create(values ...*insbuy.InsProductActivity) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductActivityDo) CreateInBatches(values []*insbuy.InsProductActivity, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductActivityDo) Save(values ...*insbuy.InsProductActivity) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductActivityDo) First() (*insbuy.InsProductActivity, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivity), nil
	}
}

func (i insProductActivityDo) Take() (*insbuy.InsProductActivity, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivity), nil
	}
}

func (i insProductActivityDo) Last() (*insbuy.InsProductActivity, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivity), nil
	}
}

func (i insProductActivityDo) Find() ([]*insbuy.InsProductActivity, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductActivity), err
}

func (i insProductActivityDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductActivity, err error) {
	buf := make([]*insbuy.InsProductActivity, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductActivityDo) FindInBatches(result *[]*insbuy.InsProductActivity, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductActivityDo) Attrs(attrs ...field.AssignExpr) *insProductActivityDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductActivityDo) Assign(attrs ...field.AssignExpr) *insProductActivityDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductActivityDo) Joins(fields ...field.RelationField) *insProductActivityDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductActivityDo) Preload(fields ...field.RelationField) *insProductActivityDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductActivityDo) FirstOrInit() (*insbuy.InsProductActivity, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivity), nil
	}
}

func (i insProductActivityDo) FirstOrCreate() (*insbuy.InsProductActivity, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivity), nil
	}
}

func (i insProductActivityDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductActivity, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductActivityDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductActivityDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductActivityDo) Delete(models ...*insbuy.InsProductActivity) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductActivityDo) withDO(do gen.Dao) *insProductActivityDo {
	i.DO = *do.(*gen.DO)
	return i
}
