package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehousePurchaseApi struct {
}

// GetStartPurchaseList 获取发起采购列表
// @Tags InsWarehousePurchase
// @Summary 获取发起采购列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyListReq true "获取发起采购列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getStartPurchaseList [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) GetStartPurchaseList(c *gin.Context) {
	var req insbuyReq.GetStartPurchaseListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehousePurchaseService.GetStartPurchaseList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// CreatePurchaseByApply 根据申购单创建采购单
// @Tags InsWarehousePurchase
// @Summary 根据申购单创建采购单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreatePurchaseByApplyReq true "根据申购单创建采购单"
// @Success   200   {object}  response.Response{data=insbuyResp.CreatePurchaseByApplyResp,msg=string}  "查询成功"
// @Router /insWarehouse/createPurchaseByApply [post]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) CreatePurchaseByApply(c *gin.Context) {
	var req insbuyReq.CreatePurchaseByApplyReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp := insbuyResp.CreatePurchaseByApplyResp{}
	resp, err = insWarehousePurchaseService.CreatePurchaseByApply(req)
	response.ResultErr(resp, err, c)
}

// ApplyPurchaseList 获取审核列表|采购单(采购)
// @Tags InsWarehousePurchase
// @Summary 获取审核列表|采购单(采购)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyListReq true "获取审核列表|采购单(采购)"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/applyPurchaseList [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) ApplyPurchaseList(c *gin.Context) {
	var req insbuyReq.ApplyListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehousePurchaseService.ApplyPurchaseList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExportApplyPurchaseDetails 申购审核列表详情导出
// @Tags InsWarehousePurchase
// @Summary 申购审核列表详情导出
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyListReq true "申购审核列表详情导出"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/exportApplyPurchaseDetails [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) ExportApplyPurchaseDetails(c *gin.Context) {
	var req insbuyReq.ApplyListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehousePurchaseService.ExportApplyPurchaseDetails(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.IsExport == 1 {
			return
		}
		response.OkWithData(list, c)
	}
}

// ApplyPurchaseDetailsList 申请单明细
// @Tags InsWarehousePurchase
// @Summary 申请单明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyDetailsListReq true "申请单明细"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/applyPurchaseDetailsList [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) ApplyPurchaseDetailsList(c *gin.Context) {
	var req insbuyReq.ApplyDetailsListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehousePurchaseService.ApplyPurchaseDetailsList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// PurchaseReport 采购报表
// @Tags InsWarehousePurchase
// @Summary 采购报表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.PurchaseReportReq true "采购报表"
// @Success   200   {object}  response.Response{data=insbuyResp.PurchaseReportResp,msg=string}  "查询成功"
// @Router /insWarehouse/purchaseReport [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) PurchaseReport(c *gin.Context) {
	var req insbuyReq.PurchaseReportReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehousePurchaseService.PurchaseReport(req); err != nil {
		if req.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// PurchaseDetailsList 采购详情列表（全部列表展开）
// @Tags InsWarehousePurchase
// @Summary 采购详情列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInoutCommonSearch true "采购详情列表"
// @Success   200   {object}  response.Response{data=insbuyResp.PurchaseReportResp,msg=string}  "查询成功"
// @Router /insWarehouse/purchaseDetailsList [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) PurchaseDetailsList(c *gin.Context) {
	var req insbuyReq.InsWarehouseInoutCommonSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehousePurchaseService.PurchaseDetailsList(req); err != nil {
		if req.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// BatchWarehouseIn
// @Tags InsWarehousePurchase
// @Summary 批量入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchWarehouseInReq true "批量入库"
// @Success   200   {object}  response.Response{data=string}  "批量入库"
// @Router /insWarehouse/batchWarehouseIn [post]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) BatchWarehouseIn(c *gin.Context) {
	var req insbuyReq.BatchWarehouseInReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehousePurchaseService.BatchWarehouseIn(req)
	response.Err(err, c)
}

// RelatedList 关联单据
// @Tags InsWarehousePurchase
// @Summary 关联单据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RelatedListReq true "关联单据"
// @Success   200   {object}  response.Response{data=insbuyResp.RelatedListResp,msg=string}  "关联单据"
// @Router /insWarehouse/relatedList [get]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) RelatedList(c *gin.Context) {
	var req insbuyReq.RelatedListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	data, err := insWarehousePurchaseService.RelatedList(req)
	response.ResultErr(data, err, c)
}

// PurchaseOneKeyIn 采购一键入库
// @Tags InsWarehousePurchase
// @Summary 采购一键入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PurchaseOneKeyInReq true "采购一键入库"
// @Success   200   {object}  response.Response{data=string}  "采购一键入库"
// @Router /insWarehouse/purchaseOneKeyIn [post]
func (InsWarehousePurchaseApi *InsWarehousePurchaseApi) PurchaseOneKeyIn(c *gin.Context) {
	var req insbuyReq.PurchaseOneKeyInReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehousePurchaseService.PurchaseOneKeyIn(req)
	response.Err(err, c)
}
