[insbuy]2025/07/30 - 10:06:36.277	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:06:36.365	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "1abadd9987b788c54a1ae908a20edddf", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:06:36.406	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "1abadd9987b788c54a1ae908a20edddf", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:06:36.406	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ebe9d142d520b4016468c46e81028f9b", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:06:36.407	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "ebe9d142d520b4016468c46e81028f9b", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:06:36.409	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0125424}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0125424, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:06:36.421	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:06:36.421", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:06:36.422	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:06:36.434	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "1abadd9987b788c54a1ae908a20edddf", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:08:49.456	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:08:49.546	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "b8555f2e8af3d3fb9290c396dd5f0d8e", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:08:49.568	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "b8555f2e8af3d3fb9290c396dd5f0d8e", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:08:49.568	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "90579b769e79e14751f1fafeb04efd45", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:08:49.568	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "90579b769e79e14751f1fafeb04efd45", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:08:49.569	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0141049}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0141049, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:08:49.583", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:08:49.583	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:08:49.592	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "b8555f2e8af3d3fb9290c396dd5f0d8e", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:10:32.870	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:10:32.948	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "49024ec5202c485a0ae4ac26c8ee4a77", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:10:32.970	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "49024ec5202c485a0ae4ac26c8ee4a77", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:10:32.970	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "50aa2a572ed9b6bee8f296a396872d75", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:10:32.970	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "50aa2a572ed9b6bee8f296a396872d75", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:10:32.971	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:10:32.980	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.009291}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.009291, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:10:32.980", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:10:32.981	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:10:32.985	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "49024ec5202c485a0ae4ac26c8ee4a77", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:14:21.112	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:14:21.200	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "baf337694c92b815a7e6545283a7e1a7", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:14:21.222	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "baf337694c92b815a7e6545283a7e1a7", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:14:21.222	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "06cbc739167e8f3665e4525c0063d82d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:14:21.224	[34minfo[0m	insbuy/contract_mapping_config.go:72	映射规则文件加载完成	{"traceId": "06cbc739167e8f3665e4525c0063d82d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:14:21.225	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:14:21.241	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0163211}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0163211, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:14:21.241", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:14:21.242	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:14:21.245	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "baf337694c92b815a7e6545283a7e1a7", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:15:13.965	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:15:14.071	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "b6e16804d499293f1a1e5b058828b845", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:15:14.104	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "b6e16804d499293f1a1e5b058828b845", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:15:14.104	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c7d6c7377af7200bf6272bf88343c485", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:15:14.106	[34minfo[0m	insbuy/contract_mapping_config.go:72	映射规则文件加载完成	{"traceId": "c7d6c7377af7200bf6272bf88343c485", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:15:14.106	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:15:14.163	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0566538}
[insbuy]2025/07/30 - 10:15:14.163	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:15:14.163	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:15:14.163	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0566538, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:15:14.163	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:15:14.163", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:15:14.164	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:15:14.168	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "b6e16804d499293f1a1e5b058828b845", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:17:27.737	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:17:27.823	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "aacab22782bd23f005238e3b45906a58", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:17:27.845	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "aacab22782bd23f005238e3b45906a58", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:17:27.845	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "dc1824f61576647d454ea3c7c57bb317", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:17:27.846	[34minfo[0m	insbuy/contract_mapping_config.go:70	映射规则文件加载完成	{"traceId": "dc1824f61576647d454ea3c7c57bb317", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:17:27.847	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	insbuy/contract_transformer.go:259	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0088989}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0088989, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:17:27.856", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:17:27.856	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:17:27.862	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "aacab22782bd23f005238e3b45906a58", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:20:31.513	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:20:31.770	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "2b31e2329cf5170ab677f92c6d678583", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:20:31.874	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "2b31e2329cf5170ab677f92c6d678583", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:20:31.874	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "8fe0a90fddfdfbe0d674396d390f4f9a", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:20:31.875	[34minfo[0m	insbuy/contract_mapping_config.go:69	映射规则文件加载完成	{"traceId": "8fe0a90fddfdfbe0d674396d390f4f9a", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:20:31.876	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:20:31.888	[34minfo[0m	insbuy/contract_transformer.go:263	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0125238}
[insbuy]2025/07/30 - 10:20:31.888	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:20:31.888	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0125238, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:20:31.888", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:20:31.889	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:20:31.901	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "2b31e2329cf5170ab677f92c6d678583", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:22:56.254	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:22:56.339	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "79e5724126b79aff8889159ba6c9e1ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:22:56.362	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "79e5724126b79aff8889159ba6c9e1ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:22:56.362	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5d08e6016a11204fe5c4372c0de62ba4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:22:56.363	[34minfo[0m	insbuy/contract_mapping_config.go:69	映射规则文件加载完成	{"traceId": "5d08e6016a11204fe5c4372c0de62ba4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:22:56.364	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:22:56.373	[34minfo[0m	insbuy/contract_transformer.go:263	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0096233}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0096233, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:22:56.373", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:22:56.374	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:22:56.379	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "79e5724126b79aff8889159ba6c9e1ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:25:39.648	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:25:39.736	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "72da870f0c61cc93c92b370b9c96f85a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:25:39.758	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "72da870f0c61cc93c92b370b9c96f85a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:25:39.758	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "249dbd6b18d45145e549fcca6117d444", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:25:39.758	[34minfo[0m	insbuy/contract_mapping_config.go:69	映射规则文件加载完成	{"traceId": "249dbd6b18d45145e549fcca6117d444", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:25:39.759	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:25:39.760	[34minfo[0m	insbuy/contract_transformer.go:260	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0013931}
[insbuy]2025/07/30 - 10:25:39.760	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0013931, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:25:39.761	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:25:39.762	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:25:39.762	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:25:39.762	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:25:39.760", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:25:39.762	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:25:39.765	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "72da870f0c61cc93c92b370b9c96f85a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:28:00.110	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:28:00.195	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "ea20424ef0947ceda91c76b8993a714b", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:28:00.236	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "ea20424ef0947ceda91c76b8993a714b", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:28:00.236	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4eb31afa3dd74002016352f3a771f200", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:28:00.237	[34minfo[0m	insbuy/contract_mapping_config.go:69	映射规则文件加载完成	{"traceId": "4eb31afa3dd74002016352f3a771f200", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:28:00.238	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	insbuy/contract_transformer.go:260	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0010376}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0010376, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:28:00.239	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:28:00.240	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:28:00.240	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:28:00.239", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:28:00.240	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:28:00.244	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "ea20424ef0947ceda91c76b8993a714b", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:30:16.289	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:30:16.379	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "4a993ce346079ad9ad67d124ec420185", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:30:16.401	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "4a993ce346079ad9ad67d124ec420185", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:30:16.402	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "697e2f88f73ffcdfddbe6acfb6946c71", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:30:16.403	[34minfo[0m	insbuy/contract_mapping_config.go:69	映射规则文件加载完成	{"traceId": "697e2f88f73ffcdfddbe6acfb6946c71", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:30:16.404	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:30:16.404	[34minfo[0m	insbuy/contract_transformer.go:260	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0005325}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0005325, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:30:16.404", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:30:16.405	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:30:16.410	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "4a993ce346079ad9ad67d124ec420185", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:35:23.569	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:35:23.652	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "e6ff645be92a715bf86636ca35a099ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:35:23.674	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "e6ff645be92a715bf86636ca35a099ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:35:23.675	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "77140d24dc448978cb254664affc20a4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:35:23.676	[34minfo[0m	insbuy/contract_mapping_config.go:70	映射规则文件加载完成	{"traceId": "77140d24dc448978cb254664affc20a4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:35:23.677	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:35:23.678	[34minfo[0m	insbuy/contract_transformer.go:260	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0010886}
[insbuy]2025/07/30 - 10:35:23.678	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 10:35:23.678	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0010886, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 10:35:23.678", "data_version": "1.0"}
[insbuy]2025/07/30 - 10:35:23.679	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 10:35:23.683	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "e6ff645be92a715bf86636ca35a099ca", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:55:57.824	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:55:57.941	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "0e54e09f5627b685ea028043a27a7562", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:55:57.983	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "0e54e09f5627b685ea028043a27a7562", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:55:57.983	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f9e393ffa0287367935157cf8d58400c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:55:57.984	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "f9e393ffa0287367935157cf8d58400c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 10:55:57.988	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "f9e393ffa0287367935157cf8d58400c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 10:55:57.988	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "f9e393ffa0287367935157cf8d58400c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:55:57.989	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:56:32.317	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:56:32.396	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "c53879c39ef1675084c9b34c5c3ce8b8", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:56:32.423	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "c53879c39ef1675084c9b34c5c3ce8b8", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:56:32.423	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "2d387015f7b48be9e67cdc29d8c8aa89", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:56:32.424	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "2d387015f7b48be9e67cdc29d8c8aa89", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 10:56:32.428	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "2d387015f7b48be9e67cdc29d8c8aa89", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 10:56:32.428	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "2d387015f7b48be9e67cdc29d8c8aa89", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:56:32.429	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 10:58:00.977	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 10:58:01.137	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "c323e8b929029625c6e3bb479fdcebd1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 10:58:01.189	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "c323e8b929029625c6e3bb479fdcebd1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 10:58:01.189	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "e17cfa7a496ab4b79bdb9d1040ec61d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 10:58:01.190	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "e17cfa7a496ab4b79bdb9d1040ec61d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 10:58:01.192	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "e17cfa7a496ab4b79bdb9d1040ec61d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 10:58:01.193	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "e17cfa7a496ab4b79bdb9d1040ec61d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 10:58:01.193	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:02:22.101	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:02:22.461	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "66c046e0a811ab666a747e1e359647d0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:02:22.525	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "66c046e0a811ab666a747e1e359647d0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:02:22.526	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b2646cbe4597929b58006df29d985b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:02:22.539	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "b2646cbe4597929b58006df29d985b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 11:02:22.541	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "b2646cbe4597929b58006df29d985b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:02:22.542	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "b2646cbe4597929b58006df29d985b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:02:22.542	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:06:51.690	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:06:51.812	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "7893173a351d34177d83856c42c50a42", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:06:51.835	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "7893173a351d34177d83856c42c50a42", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:06:51.836	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "48912f9d701d400553fd8f2df2ab7ca9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:06:51.838	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "48912f9d701d400553fd8f2df2ab7ca9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 11:06:51.843	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "48912f9d701d400553fd8f2df2ab7ca9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:06:51.843	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "48912f9d701d400553fd8f2df2ab7ca9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:06:51.845	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:12:37.779	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:12:37.987	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "1d3b12fc6f9603ca835ff85864f04cdd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:12:38.012	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "1d3b12fc6f9603ca835ff85864f04cdd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:12:38.012	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4af4060b67daf33c44b21995f29ba28f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:12:38.013	[34minfo[0m	insbuy/contract_mapping_config.go:65	解析后的原始数据	{"traceId": "4af4060b67daf33c44b21995f29ba28f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "rules": {"PAYMENT_REQUEST_APPROVAL":{"approval_code":"PAYMENT_REQUEST_APPROVAL","approval_name":"付款申请审批","field_mappings":{"account_holder":{"source_path":"form.widget16657399953960001.value.widgetAccountName","target_field":"account_holder","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"收款方户名"},"account_number":{"source_path":"form.widget16657399953960001.value.widgetAccountNumber","target_field":"account_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户号码"},"account_type":{"source_path":"form.widget16657399953960001.value.widgetAccountType.text","target_field":"account_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"账户类型"},"application_number":{"source_path":"basic.instance_code","target_field":"application_number","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请编号"},"application_status":{"source_path":"basic.status","target_field":"application_status","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"申请状态"},"attachments":{"source_path":"form.widget15828099482720001.ext","target_field":"attachments","data_type":"array","default_value":null,"transform":"parseAttachments","required":false,"validation":"","description":"附件列表"},"bank_branch":{"source_path":"form.widget16657399953960001.value.widgetAccountBankBranch.text","target_field":"bank_branch","data_type":"string","default_value":null,"transform":"extractBankBranch","required":false,"validation":"","description":"银行支行"},"bank_name":{"source_path":"form.widget16657399953960001.value.widgetAccountBankName.text","target_field":"bank_name","data_type":"string","default_value":null,"transform":"extractBankName","required":false,"validation":"","description":"银行名称"},"bank_region":{"source_path":"form.widget16657399953960001.value.widgetAccountBankArea.text","target_field":"bank_region","data_type":"string","default_value":null,"transform":"extractBankRegion","required":false,"validation":"","description":"银行所在地区"},"business_type":{"source_path":"form.widget16701426685000001.value","target_field":"business_type","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"业务类型"},"complete_time":{"source_path":"basic.end_time","target_field":"complete_time","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"完成时间"},"contract_paid_amount":{"source_path":"form.widget16671961212400001.value","target_field":"contract_paid_amount","data_type":"float","default_value":0,"transform":"","required":false,"validation":"","description":"合同已付金额"},"contract_sign_amount":{"source_path":"form.widget16671961125560001.value","target_field":"contract_sign_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"合同签约金额"},"current_request_amount":{"source_path":"form.widget1.value","target_field":"current_request_amount","data_type":"float","default_value":null,"transform":"","required":true,"validation":"","description":"本次请款金额"},"expected_payment_date":{"source_path":"form.widget3.value","target_field":"expected_payment_date","data_type":"time","default_value":null,"transform":"","required":false,"validation":"","description":"期望付款日期"},"expense_department":{"source_path":"form.widget16709264180350001.value[0].name","target_field":"expense_department","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"费用所属部门"},"initiate_time":{"source_path":"basic.start_time","target_field":"initiate_time","data_type":"time","default_value":null,"transform":"","required":true,"validation":"","description":"发起时间"},"initiator_department_id":{"source_path":"basic.department_id","target_field":"initiator_department_id","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"发起人部门ID"},"initiator_user_id":{"source_path":"basic.user_id","target_field":"initiator_user_id","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"发起人User ID"},"payment_currency":{"source_path":"form.widget16799083691540001.value","target_field":"payment_currency","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款币种"},"payment_entity":{"source_path":"form.widget16423899380530001.value","target_field":"payment_entity","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款主体"},"payment_reason":{"source_path":"form.widget0.value","target_field":"payment_reason","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"付款事由"},"serial_number":{"source_path":"form.widget16856053045110001.value","target_field":"serial_number","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"流水号"},"title":{"source_path":"basic.approval_name","target_field":"title","data_type":"string","default_value":null,"transform":"","required":true,"validation":"","description":"标题"},"vat_invoice_type":{"source_path":"form.widget17466051580200001.value","target_field":"vat_invoice_type","data_type":"string","default_value":null,"transform":"","required":false,"validation":"","description":"增值税发票类型"}},"default_values":{"contract_paid_amount":0,"data_version":"1.0","payment_currency":"人民币"},"required_fields":["application_number","title","application_status","initiate_time","initiator_user_id","payment_reason","payment_entity","business_type","payment_currency","contract_sign_amount","current_request_amount"],"validation_rules":[{"field":"application_number","rule":"required","parameter":"","message":"申请编号不能为空"}]}}}
[insbuy]2025/07/30 - 11:12:38.016	[34minfo[0m	insbuy/contract_mapping_config.go:80	成功加载映射规则	{"traceId": "4af4060b67daf33c44b21995f29ba28f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:12:38.016	[34minfo[0m	insbuy/contract_mapping_config.go:90	映射规则文件加载完成	{"traceId": "4af4060b67daf33c44b21995f29ba28f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:12:38.016	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:17:34.514	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:17:34.598	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "ffe572522a615fbced6e8650ef425a57", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:17:34.619	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "ffe572522a615fbced6e8650ef425a57", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:17:34.619	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4b59c0deded5b8af3f2d835c2844306b", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:17:34.621	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4b59c0deded5b8af3f2d835c2844306b", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:17:34.622	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4b59c0deded5b8af3f2d835c2844306b", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:17:34.623	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:19:50.571	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:19:50.798	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "9decf2e3b8afd702b5647272871196cd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:19:50.842	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "9decf2e3b8afd702b5647272871196cd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:19:50.842	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "824a201651168ffe0deaa239791397a7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:19:50.844	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "824a201651168ffe0deaa239791397a7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:19:50.844	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "824a201651168ffe0deaa239791397a7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:19:50.844	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:25:23.654	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:25:23.746	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "88194a0e28f9aa73939db65f3217464a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:25:23.769	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "88194a0e28f9aa73939db65f3217464a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:25:23.769	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f2ab09904942d438a20c7f004492f2c6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:25:23.771	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f2ab09904942d438a20c7f004492f2c6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:25:23.771	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f2ab09904942d438a20c7f004492f2c6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:25:23.772	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:26:17.056	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:26:17.151	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "3cd1d5199f454ea480c27f5290807396", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:26:17.176	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "3cd1d5199f454ea480c27f5290807396", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:26:17.176	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "1cf990a1fc67ce03c26c2bbe0494dfe5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:26:17.178	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "1cf990a1fc67ce03c26c2bbe0494dfe5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:26:17.178	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "1cf990a1fc67ce03c26c2bbe0494dfe5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:26:17.180	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:27:15.778	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:27:15.876	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "5799831126e0e7f2fee4073b24cfe001", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:27:15.900	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "5799831126e0e7f2fee4073b24cfe001", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:27:15.900	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "91575ada5d0ed25fc3387bb3770a74cd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:27:15.903	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "91575ada5d0ed25fc3387bb3770a74cd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:27:15.903	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "91575ada5d0ed25fc3387bb3770a74cd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:27:15.904	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:28:55.923	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:28:56.007	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "8b9ad981537f2c908ca4c85c425b8a49", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:28:56.029	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "8b9ad981537f2c908ca4c85c425b8a49", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:28:56.029	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "07fb2762c775cf9efdebc93345e7e177", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:28:56.030	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "07fb2762c775cf9efdebc93345e7e177", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:28:56.030	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "07fb2762c775cf9efdebc93345e7e177", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:28:56.031	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:29:38.569	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:29:38.671	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "71c211046e84a738c585c46b216ccb31", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:29:38.709	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "71c211046e84a738c585c46b216ccb31", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:29:38.709	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "80647b80cd1ed15e49080ccfee2b43a8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:29:38.710	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "80647b80cd1ed15e49080ccfee2b43a8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:29:38.710	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "80647b80cd1ed15e49080ccfee2b43a8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:29:38.710	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:30:20.628	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:30:20.714	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "30b081df10ded9a12949ac28a3f74ef0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:30:20.735	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "30b081df10ded9a12949ac28a3f74ef0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:30:20.735	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f3edf148ff98386cc052bef914ed8b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:30:20.737	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f3edf148ff98386cc052bef914ed8b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:30:20.737	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f3edf148ff98386cc052bef914ed8b06", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:30:20.737	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:31:03.805	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:31:03.901	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "6bc78e231f47c846b814db85ab7af1c5", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:31:03.929	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "6bc78e231f47c846b814db85ab7af1c5", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:31:03.929	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4feca95ab9868db7e35b61b616532c5e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:31:03.930	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4feca95ab9868db7e35b61b616532c5e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:31:03.930	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4feca95ab9868db7e35b61b616532c5e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:31:03.930	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	insbuy/contract_transformer.go:260	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.000558}
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.000558, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 11:31:03.931	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "title": "费用报销", "application_status": "PENDING", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 11:31:03.932	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "6831f4gf", "initiator_department_id": "c83f1f5639f57893", "serial_number": ""}
[insbuy]2025/07/30 - 11:31:03.932	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "7.19-7.21两位泰国KOL内容推广服务产生的机票/酒店/餐食/交通等各种费用", "payment_entity": "新电英雄（上海）文化传播有限公司", "business_type": "", "expense_department": "品牌文化部", "payment_currency": ""}
[insbuy]2025/07/30 - 11:31:03.932	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 11:31:03.932	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 11:31:03.935	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "范月晨", "account_type": "1", "account_number": "6216690100012349659", "bank_name": "{\"bankCode\":\"BOC\",\"bankNameEn\":\"Bank of China\",\"bankNameZh\":\"中国银行\",\"iconURL\":\"https://sf16-scmcdn-va.ibytedtos.com/obj/static-us/ea/approval-static/bank-icon/36185557c0282f6e84ff43193e00a191.png\"}", "bank_branch": "{\"bkId\":0,\"name\":\"中国银行学清路支行\"}", "bank_region": "[{\"code\":\"11\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":2038349,\"asci_name\":\"Beijing Shi\",\"level\":\"Province\"},{\"code\":\"110000\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":1816670,\"asci_name\":\"Beijing\",\"level\":\"City\"}]"}
[insbuy]2025/07/30 - 11:31:03.935	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": ["【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "酒店发票.pdf", "Hush  三虫玩家（上海）文化传播有限公司_20250728185110.pdf", "kezee发票.pdf", "豁麻  新电英雄（上海）文化传播有限公司_20250728182847.pdf", "五花马发票.jpg", "接机.pdf", "sd卡发票.pdf", "充电宝.pdf", "存包费用.pdf", "滴滴出行行程报销单(1).pdf", "滴滴出行行程报销单(2).pdf", "滴滴电子发票(1)525.9.pdf", "滴滴电子发票(2).pdf", "买药.pdf", "隐形眼镜护理液发票.pdf"], "remarks": ""}
[insbuy]2025/07/30 - 11:31:03.935	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 11:31:03.931", "data_version": "1.0"}
[insbuy]2025/07/30 - 11:31:03.935	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 11:31:03.935	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "6bc78e231f47c846b814db85ab7af1c5", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:33:37.288	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:33:37.362	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "a9e288687b9af73232d8c362fc12b237", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:33:37.381	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "a9e288687b9af73232d8c362fc12b237", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:33:37.381	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "3c2baee296ca63eaf34f4cca7e89222c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:33:37.383	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "3c2baee296ca63eaf34f4cca7e89222c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:33:37.383	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "3c2baee296ca63eaf34f4cca7e89222c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:33:37.384	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0010871}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0010871, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "title": "费用报销", "application_status": "PENDING", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "6831f4gf", "initiator_department_id": "c83f1f5639f57893", "serial_number": ""}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "7.19-7.21两位泰国KOL内容推广服务产生的机票/酒店/餐食/交通等各种费用", "payment_entity": "新电英雄（上海）文化传播有限公司", "business_type": "", "expense_department": "品牌文化部", "payment_currency": ""}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "范月晨", "account_type": "1", "account_number": "6216690100012349659", "bank_name": "{\"bankCode\":\"BOC\",\"bankNameEn\":\"Bank of China\",\"bankNameZh\":\"中国银行\",\"iconURL\":\"https://sf16-scmcdn-va.ibytedtos.com/obj/static-us/ea/approval-static/bank-icon/36185557c0282f6e84ff43193e00a191.png\"}", "bank_branch": "{\"bkId\":0,\"name\":\"中国银行学清路支行\"}", "bank_region": "[{\"code\":\"11\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":2038349,\"asci_name\":\"Beijing Shi\",\"level\":\"Province\"},{\"code\":\"110000\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":1816670,\"asci_name\":\"Beijing\",\"level\":\"City\"}]"}
[insbuy]2025/07/30 - 11:33:37.385	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": ["【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "酒店发票.pdf", "Hush  三虫玩家（上海）文化传播有限公司_20250728185110.pdf", "kezee发票.pdf", "豁麻  新电英雄（上海）文化传播有限公司_20250728182847.pdf", "五花马发票.jpg", "接机.pdf", "sd卡发票.pdf", "充电宝.pdf", "存包费用.pdf", "滴滴出行行程报销单(1).pdf", "滴滴出行行程报销单(2).pdf", "滴滴电子发票(1)525.9.pdf", "滴滴电子发票(2).pdf", "买药.pdf", "隐形眼镜护理液发票.pdf"], "remarks": ""}
[insbuy]2025/07/30 - 11:33:37.397	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 11:33:37.385", "data_version": "1.0"}
[insbuy]2025/07/30 - 11:33:37.397	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 11:33:37.397	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "a9e288687b9af73232d8c362fc12b237", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:33:06.638	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 11:33:06.729	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "ab311fe8a62f04f7f47b2ce9284bd6e2", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 11:33:06.751	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "ab311fe8a62f04f7f47b2ce9284bd6e2", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 11:33:06.752	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "a33e228d23f66c898d520e95bcc8fe59", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 11:33:06.754	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "a33e228d23f66c898d520e95bcc8fe59", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 11:33:06.754	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "a33e228d23f66c898d520e95bcc8fe59", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 11:33:06.754	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 11:33:06.754	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "title": "费用报销", "application_status": "PENDING", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "6831f4gf", "initiator_department_id": "c83f1f5639f57893", "serial_number": ""}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "7.19-7.21两位泰国KOL内容推广服务产生的机票/酒店/餐食/交通等各种费用", "payment_entity": "新电英雄（上海）文化传播有限公司", "business_type": "", "expense_department": "品牌文化部", "payment_currency": ""}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "范月晨", "account_type": "1", "account_number": "6216690100012349659", "bank_name": "{\"bankCode\":\"BOC\",\"bankNameEn\":\"Bank of China\",\"bankNameZh\":\"中国银行\",\"iconURL\":\"https://sf16-scmcdn-va.ibytedtos.com/obj/static-us/ea/approval-static/bank-icon/36185557c0282f6e84ff43193e00a191.png\"}", "bank_branch": "{\"bkId\":0,\"name\":\"中国银行学清路支行\"}", "bank_region": "[{\"code\":\"11\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":2038349,\"asci_name\":\"Beijing Shi\",\"level\":\"Province\"},{\"code\":\"110000\",\"name\":\"北京市\",\"pinyin\":\"bei jing shi\",\"geoname_id\":1816670,\"asci_name\":\"Beijing\",\"level\":\"City\"}]"}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": ["【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf", "酒店发票.pdf", "Hush  三虫玩家（上海）文化传播有限公司_20250728185110.pdf", "kezee发票.pdf", "豁麻  新电英雄（上海）文化传播有限公司_20250728182847.pdf", "五花马发票.jpg", "接机.pdf", "sd卡发票.pdf", "充电宝.pdf", "存包费用.pdf", "滴滴出行行程报销单(1).pdf", "滴滴出行行程报销单(2).pdf", "滴滴电子发票(1)525.9.pdf", "滴滴电子发票(2).pdf", "买药.pdf", "隐形眼镜护理液发票.pdf"], "remarks": ""}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 11:33:06.754", "data_version": "1.0"}
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 11:33:06.755	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "ab311fe8a62f04f7f47b2ce9284bd6e2", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 13:11:06.680	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 13:11:06.761	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "c32c26784788cb503bb7146c11295310", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 13:11:06.781	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "c32c26784788cb503bb7146c11295310", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 13:11:06.781	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "aa0d0cf412eeb29bda6c4480b783105f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 13:11:06.784	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "aa0d0cf412eeb29bda6c4480b783105f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 13:11:06.784	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "aa0d0cf412eeb29bda6c4480b783105f", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 13:11:06.785	[34minfo[0m	insbuy/contract_transformer.go:203	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0016594}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0016594, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "title": "费用报销", "application_status": "PENDING", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "6831f4gf", "initiator_department_id": "c83f1f5639f57893", "serial_number": ""}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "7.19-7.21两位泰国KOL内容推广服务产生的机票/酒店/餐食/交通等各种费用", "payment_entity": "新电英雄（上海）文化传播有限公司", "business_type": "", "expense_department": "品牌文化部", "payment_currency": ""}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "范月晨", "account_type": "1", "account_number": "6216690100012349659", "bank_name": "中国银行", "bank_branch": "中国银行学清路支行", "bank_region": "北京市 北京市"}
[insbuy]2025/07/30 - 13:11:06.786	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": ["【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=M2E0M2RlODhjZjg5MzEyZTc3ZGYxMWIwYzM5YjY3NzlfN2UzOWI2NzlhMWJiMmE2MzAxNWFjZTUxM2NkZjg5N2VfSUQ6NzUzMjA4NjUxNTM2MzI5OTMzMl8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzVhY2U3OTQzMjI3YWQxMDFhODM3YTdjN2QyMzU5NzRfNWFmZmRlNDA3ZTdmZmRkZGM3MzJlZDU5YmNkMzU4Y2RfSUQ6NzUzMjA4NjUxMTA2NTgwODg5OV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "酒店发票.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=MGI4ZjIyNDIxMDU4OGEyMDdjNjNhNjc5ODllMWE0NThfNWVkNjMxMGE0ZDU5NmFjYzA3OTg1ZDYwMTllZDA0OGJfSUQ6NzUzMjA4ODkyMjM5NTk2NzQ5MF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "Hush  三虫玩家（上海）文化传播有限公司_20250728185110.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ZGRjMmEzYjdlYTJiMTQ2NzQ4YTM2ZmQyNzE4MDY0MzBfNGNjOGY4ZWI0ZmM4NzEwNDNjOTM3YThlNjJiMTI3ZmVfSUQ6NzUzMjA4OTAwMzg5MTI3NzgyN18xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "kezee发票.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=NzEwNjY1YzYyNTY3ZDMwZjc3ODM1ZjBjM2ZmNGJmYTBfODBjNTEzNWRhMTc0NzZlZDQ4OTgwYjFiMTdmZTRlOTlfSUQ6NzUzMjA4OTAwNjQ0MTI5OTk3MV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "豁麻  新电英雄（上海）文化传播有限公司_20250728182847.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Y2MzMDQyNjk4ZjA4OTE1YWRkZjUwYzk5MzY5NDM0YzBfNjRlMjJiZWI2ZDVjZDcxYjVmYWNhYmEyODQ2NGNkNGJfSUQ6NzUzMjA4OTAwNjA5NzcyNzQ5MF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "五花马发票.jpg|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YmZjYTdjMmU3MTFmNjE1Yjk0YzQwMWVkYTliNDk1NTNfOGJhM2I3ODFiODEwMTJlYTQ3ZTcwMWViMjUwODZjODJfSUQ6NzUzMjA4OTAwNjQ0MTMxNjM1NV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "接机.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=NDIxNzkxYjIzNWY2ZmE0ZmFmNTc0NDg0NjZkYzE2OTFfYjBhNDkxOGUxNDM4NDNjZGRhMzk2NTU2NDRiZjM0ZjVfSUQ6NzUzMjA4OTA3MjY1NTIwNDM1Nl8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "sd卡发票.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Mzc3Nzc1ODlkMmRiNWVhMzI3NDczMDRiMGNkYWJjZTJfMTE4NGMyMTNhYWMzMWRjNzM3ZTYyZjNjYjA1MzVjYzhfSUQ6NzUzMjA4OTE5MjkxMTg2MzgxMV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "充电宝.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OWE5ZDExMjZlMDE0MjM0MWNjZTE4OTlhZDc3YTU3MWVfYmQ1MjdhYjgwZjg3ZmQwNGNmZjAxZjEwYmVlZjI5YzdfSUQ6NzUzMjA4OTE5NTc4NDMwNjY5MV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "存包费用.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzIzMjZmZGYzNzBmNDQ1OWMzYTM5MDMyNjE3NjNiN2ZfZTdiZDI5NGM4OTYzMDU4NTIwNzY4NDYxZWJhMWMzNDNfSUQ6NzUzMjA4OTE5MzY3MDkxODE2M18xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "滴滴出行行程报销单(1).pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Zjc4YmEwYWY3NTIxNDJkYjUxMGY1ZjU3OWIwZmE0ZTlfZjlmNmIwOWY2MDY3YTJhOTFmOGJiNzA4NjEzZjljNjFfSUQ6NzUzMjA4OTE5MzM4NTYyMzU1NF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM", "滴滴出行行程报销单(2).pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OTYyYzU0MDAwN2JjYzJkNWViYTNiNWJhNDVlN2RhYWRfODYwNTY2NGRjMzRhM2I2ZTQ0MzU3N2YwZWRkZjBjMmVfSUQ6NzUzMjA4OTE5NTUxOTkzNDQ5Ml8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM", "滴滴电子发票(1)525.9.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OGMzMjgwOGRhMmNmZGQ3ZmNjZGU1NGEwNGI5YmQzYmJfZjFkMjY0NDczM2IwMTJjNjBmY2NlYjllOWVkZDExYWVfSUQ6NzUzMjA4OTIxMzYxNDU1NTEzOF8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM", "滴滴电子发票(2).pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ZWVjZmYwNDJkMTM2MzNjYjkwYTY5ODlmMjRiZjVkNGZfMjkwOGE0OGJiMGE5NDM0Mjk3NTJlNWY0OTVhMTgwZThfSUQ6NzUzMjA4OTIxNDg2MDM3ODExM18xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM", "买药.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OGE4ZmJkNmJjZWFmNDU4NWY0NGY3YWJlZGM5MTA2ZWZfYThjMmY5NWE3NDZlZmVhYjQ5MTVhMGRiN2IzMmY3NjJfSUQ6NzUzMjA4OTIxNzY0NTYyNTM0Nl8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM", "隐形眼镜护理液发票.pdf|https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Yzg0MjkyZmViNDBiOWJkZjViODVjMjFkNWIwYTQ5MzFfYTFiMGI3MzBhMjQ5YWI1NzU0NGJiMWM4ZjUyMDZiZTBfSUQ6NzUzMjA4OTIxOTIyMDI5MTYxMl8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"], "remarks": ""}
[insbuy]2025/07/30 - 13:11:06.795	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 13:11:06.786", "data_version": "1.0"}
[insbuy]2025/07/30 - 13:11:06.795	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 13:11:06.795	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "c32c26784788cb503bb7146c11295310", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/30 - 14:08:19.029	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:08:19.152	[34minfo[0m	test/contract_transformer_test.go:544	成功查询到合同数据	{"traceId": "5c771838dbb22bda43eef3e992387ecf", "task": "TestWithDatabaseDataAndExport", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 14:08:19.153	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "83f3b3b6efaac4cdf2bf84715198b209", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:08:19.154	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "83f3b3b6efaac4cdf2bf84715198b209", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:08:19.154	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "83f3b3b6efaac4cdf2bf84715198b209", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:08:19.155	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:08:19.156	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005336}
[insbuy]2025/07/30 - 14:09:41.697	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:09:41.844	[34minfo[0m	test/contract_transformer_test.go:544	成功查询到合同数据	{"traceId": "7ad22aaa159ef66b134f2f5633a66d03", "task": "TestWithDatabaseDataAndExport", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 14:09:41.844	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4eb98f38492d5375b48baaf824dfd6f9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:09:41.845	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4eb98f38492d5375b48baaf824dfd6f9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:09:41.846	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4eb98f38492d5375b48baaf824dfd6f9", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:09:41.846	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:09:41.847	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.000567}
[insbuy]2025/07/30 - 14:02:07.572	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:02:07.684	[34minfo[0m	test/contract_transformer_test.go:544	成功查询到合同数据	{"traceId": "b18e0f3aa2912c6edb46018c9d1c25ca", "task": "TestWithDatabaseDataAndExport", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/30 - 14:02:07.684	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ea6c20f421e45672897888011fbc0ed7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:02:07.686	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ea6c20f421e45672897888011fbc0ed7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:02:07.687	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ea6c20f421e45672897888011fbc0ed7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:02:07.687	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": true, "error_count": 0, "warning_count": 0, "processing_time": 0.000548}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:167	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:170	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:178	转换结果:	{"success": true, "processing_time": 0.000548, "error_count": 0, "warning_count": 0}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:212	标准化数据 - 基础信息:	{"application_number": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "title": "费用报销", "application_status": "PENDING", "initiate_time": "[insbuy]2025/07/30 - 13:58:35.000", "complete_time": "[insbuy]2025/07/30 - 13:58:37.000"}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 人员信息:	{"initiator_user_id": "6831f4gf", "initiator_department_id": "c83f1f5639f57893", "serial_number": ""}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:226	标准化数据 - 业务信息:	{"payment_reason": "7.19-7.21两位泰国KOL内容推广服务产生的机票/酒店/餐食/交通等各种费用", "payment_entity": "新电英雄（上海）文化传播有限公司", "business_type": "", "expense_department": "品牌文化部", "payment_currency": ""}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:240	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:245	标准化数据 - 银行信息:	{"account_holder": "范月晨", "account_type": "1", "account_number": "6216690100012349659", "bank_name": "中国银行", "bank_branch": "中国银行学清路支行", "bank_region": "北京市 北京市"}
[insbuy]2025/07/30 - 14:02:07.688	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=M2E0M2RlODhjZjg5MzEyZTc3ZGYxMWIwYzM5YjY3NzlfN2UzOWI2NzlhMWJiMmE2MzAxNWFjZTUxM2NkZjg5N2VfSUQ6NzUzMjA4NjUxNTM2MzI5OTMzMl8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"【飞猪】曼谷-上海 (往返) 订单*************-机票款凭证 报销凭证.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzVhY2U3OTQzMjI3YWQxMDFhODM3YTdjN2QyMzU5NzRfNWFmZmRlNDA3ZTdmZmRkZGM3MzJlZDU5YmNkMzU4Y2RfSUQ6NzUzMjA4NjUxMTA2NTgwODg5OV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"酒店发票.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=MGI4ZjIyNDIxMDU4OGEyMDdjNjNhNjc5ODllMWE0NThfNWVkNjMxMGE0ZDU5NmFjYzA3OTg1ZDYwMTllZDA0OGJfSUQ6NzUzMjA4ODkyMjM5NTk2NzQ5MF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"Hush  三虫玩家（上海）文化传播有限公司_20250728185110.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ZGRjMmEzYjdlYTJiMTQ2NzQ4YTM2ZmQyNzE4MDY0MzBfNGNjOGY4ZWI0ZmM4NzEwNDNjOTM3YThlNjJiMTI3ZmVfSUQ6NzUzMjA4OTAwMzg5MTI3NzgyN18xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"kezee发票.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=NzEwNjY1YzYyNTY3ZDMwZjc3ODM1ZjBjM2ZmNGJmYTBfODBjNTEzNWRhMTc0NzZlZDQ4OTgwYjFiMTdmZTRlOTlfSUQ6NzUzMjA4OTAwNjQ0MTI5OTk3MV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"豁麻  新电英雄（上海）文化传播有限公司_20250728182847.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Y2MzMDQyNjk4ZjA4OTE1YWRkZjUwYzk5MzY5NDM0YzBfNjRlMjJiZWI2ZDVjZDcxYjVmYWNhYmEyODQ2NGNkNGJfSUQ6NzUzMjA4OTAwNjA5NzcyNzQ5MF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"五花马发票.jpg","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YmZjYTdjMmU3MTFmNjE1Yjk0YzQwMWVkYTliNDk1NTNfOGJhM2I3ODFiODEwMTJlYTQ3ZTcwMWViMjUwODZjODJfSUQ6NzUzMjA4OTAwNjQ0MTMxNjM1NV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"接机.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=NDIxNzkxYjIzNWY2ZmE0ZmFmNTc0NDg0NjZkYzE2OTFfYjBhNDkxOGUxNDM4NDNjZGRhMzk2NTU2NDRiZjM0ZjVfSUQ6NzUzMjA4OTA3MjY1NTIwNDM1Nl8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"sd卡发票.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Mzc3Nzc1ODlkMmRiNWVhMzI3NDczMDRiMGNkYWJjZTJfMTE4NGMyMTNhYWMzMWRjNzM3ZTYyZjNjYjA1MzVjYzhfSUQ6NzUzMjA4OTE5MjkxMTg2MzgxMV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"充电宝.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OWE5ZDExMjZlMDE0MjM0MWNjZTE4OTlhZDc3YTU3MWVfYmQ1MjdhYjgwZjg3ZmQwNGNmZjAxZjEwYmVlZjI5YzdfSUQ6NzUzMjA4OTE5NTc4NDMwNjY5MV8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"存包费用.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzIzMjZmZGYzNzBmNDQ1OWMzYTM5MDMyNjE3NjNiN2ZfZTdiZDI5NGM4OTYzMDU4NTIwNzY4NDYxZWJhMWMzNDNfSUQ6NzUzMjA4OTE5MzY3MDkxODE2M18xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"滴滴出行行程报销单(1).pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Zjc4YmEwYWY3NTIxNDJkYjUxMGY1ZjU3OWIwZmE0ZTlfZjlmNmIwOWY2MDY3YTJhOTFmOGJiNzA4NjEzZjljNjFfSUQ6NzUzMjA4OTE5MzM4NTYyMzU1NF8xNzUzNzY5Nzg3OjE3NTM4NTYxODdfVjM"},{"file_name":"滴滴出行行程报销单(2).pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OTYyYzU0MDAwN2JjYzJkNWViYTNiNWJhNDVlN2RhYWRfODYwNTY2NGRjMzRhM2I2ZTQ0MzU3N2YwZWRkZjBjMmVfSUQ6NzUzMjA4OTE5NTUxOTkzNDQ5Ml8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"},{"file_name":"滴滴电子发票(1)525.9.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OGMzMjgwOGRhMmNmZGQ3ZmNjZGU1NGEwNGI5YmQzYmJfZjFkMjY0NDczM2IwMTJjNjBmY2NlYjllOWVkZDExYWVfSUQ6NzUzMjA4OTIxMzYxNDU1NTEzOF8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"},{"file_name":"滴滴电子发票(2).pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ZWVjZmYwNDJkMTM2MzNjYjkwYTY5ODlmMjRiZjVkNGZfMjkwOGE0OGJiMGE5NDM0Mjk3NTJlNWY0OTVhMTgwZThfSUQ6NzUzMjA4OTIxNDg2MDM3ODExM18xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"},{"file_name":"买药.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=OGE4ZmJkNmJjZWFmNDU4NWY0NGY3YWJlZGM5MTA2ZWZfYThjMmY5NWE3NDZlZmVhYjQ5MTVhMGRiN2IzMmY3NjJfSUQ6NzUzMjA4OTIxNzY0NTYyNTM0Nl8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"},{"file_name":"隐形眼镜护理液发票.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Yzg0MjkyZmViNDBiOWJkZjViODVjMjFkNWIwYTQ5MzFfYTFiMGI3MzBhMjQ5YWI1NzU0NGJiMWM4ZjUyMDZiZTBfSUQ6NzUzMjA4OTIxOTIyMDI5MTYxMl8xNzUzNzY5Nzg4OjE3NTM4NTYxODhfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:02:07.692	[34minfo[0m	test/contract_transformer_test.go:260	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:02:07.688", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:02:07.692	[34minfo[0m	test/contract_transformer_test.go:268	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:35:33.727	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:35:33.797	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "abf7d84431975c6b4c4aa58998f54aa9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:35:33.810	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "abf7d84431975c6b4c4aa58998f54aa9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:35:33.810	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "adb1f89dfec38095a97fb8ba30503000", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:35:33.811	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "61d445d0007e009b37723a22ac493645", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:35:33.811	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f0d9b95bdca53d85d23ef5f7f60e01c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:35:33.813	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f0d9b95bdca53d85d23ef5f7f60e01c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:35:33.813	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f0d9b95bdca53d85d23ef5f7f60e01c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:35:33.813	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "8ff795fca5bda3c0974aef6b86564db7", "task": "LoadMappingRulesFromFile", "filename": "purchase_contract.json"}
[insbuy]2025/07/30 - 14:35:33.814	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "8ff795fca5bda3c0974aef6b86564db7", "task": "LoadMappingRulesFromFile", "filename": "purchase_contract.json", "approval_code": "C789D012-3456-7890-1234-56789ABCDEF0", "approval_name": "采购合同审批", "field_mappings_count": 30}
[insbuy]2025/07/30 - 14:35:33.814	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "8ff795fca5bda3c0974aef6b86564db7", "task": "LoadMappingRulesFromFile", "filename": "purchase_contract.json", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:35:33.814	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "70ab5bc6f6899b235646bc0b85cd0a8f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:35:33.815	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "70ab5bc6f6899b235646bc0b85cd0a8f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:35:33.815	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "70ab5bc6f6899b235646bc0b85cd0a8f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:35:33.815	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5ecaeb0c3aa0cb82aeb60f660d04108d", "task": "LoadMappingRulesFromFile", "filename": "sales_contract.yaml"}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "5ecaeb0c3aa0cb82aeb60f660d04108d", "task": "LoadMappingRulesFromFile", "filename": "sales_contract.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "销售合同审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "5ecaeb0c3aa0cb82aeb60f660d04108d", "task": "LoadMappingRulesFromFile", "filename": "sales_contract.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "61d445d0007e009b37723a22ac493645", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 4, "total_rules": 3}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "adb1f89dfec38095a97fb8ba30503000", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "adb1f89dfec38095a97fb8ba30503000", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "adb1f89dfec38095a97fb8ba30503000", "task": "LoadAllMappingConfigs", "approval_code": "C789D012-3456-7890-1234-56789ABCDEF0", "approval_name": "采购合同审批", "field_mappings": 30}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "adb1f89dfec38095a97fb8ba30503000", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "销售合同审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "d6a0cd8613721c453d442979f3cf35f1", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "d6a0cd8613721c453d442979f3cf35f1", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "销售合同审批"}
[insbuy]2025/07/30 - 14:35:33.816	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "abf7d84431975c6b4c4aa58998f54aa9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "销售合同审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:35:33.817	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:35:33.817	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:35:33.817", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:35:33.861	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "abf7d84431975c6b4c4aa58998f54aa9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_143533.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/07/30 - 14:44:17.053	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:44:17.147	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "331ef5dcf264209fd4a83c8beb245cdc", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:44:17.167	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "331ef5dcf264209fd4a83c8beb245cdc", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:44:17.167	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "be50c589bd273c40ccb2a9929c9f5c6a", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:44:17.167	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "f9f803b29322b54e1d36b02255bf02b2", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:44:17.167	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "fbd6cb73a9f6ce23a5fd29ec63b3ed4d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:44:17.170	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "fbd6cb73a9f6ce23a5fd29ec63b3ed4d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:44:17.170	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "fbd6cb73a9f6ce23a5fd29ec63b3ed4d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:44:17.170	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c622ce239fd0f9a9e2eb2bd52d4b7c5f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c622ce239fd0f9a9e2eb2bd52d4b7c5f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c622ce239fd0f9a9e2eb2bd52d4b7c5f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "f9f803b29322b54e1d36b02255bf02b2", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 1}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "be50c589bd273c40ccb2a9929c9f5c6a", "task": "LoadAllMappingConfigs", "total_rules": 1}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "be50c589bd273c40ccb2a9929c9f5c6a", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:44:17.171	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "f1db1e2b63f4972dadfeb12d3e7c15b4", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:26:40.515	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:26:40.606	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "85da23c230c0a0e3e86258e904cd4c35", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:26:40.622	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "85da23c230c0a0e3e86258e904cd4c35", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:26:40.622	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "be33642a582961bab66fc2ffbf002413", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:26:40.622	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "849bfcf3fd75204b8383b9c16c082765", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:26:40.622	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f4150f0202bfdaf783a7ec0c4d4e907d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f4150f0202bfdaf783a7ec0c4d4e907d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f4150f0202bfdaf783a7ec0c4d4e907d", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "6740fbdb16e8eb743e91ea4649160759", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "6740fbdb16e8eb743e91ea4649160759", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "6740fbdb16e8eb743e91ea4649160759", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "849bfcf3fd75204b8383b9c16c082765", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 1}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "be33642a582961bab66fc2ffbf002413", "task": "LoadAllMappingConfigs", "total_rules": 1}
[insbuy]2025/07/30 - 14:26:40.623	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "be33642a582961bab66fc2ffbf002413", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:26:40.624	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "56f66731d91c762a4db8fbbb0f04137a", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:28:49.669	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:28:49.775	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "b97db24a6cdc2fd763cf59490cc09465", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:28:49.802	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "b97db24a6cdc2fd763cf59490cc09465", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:28:49.802	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "cca05cd597a60a30789c58587f746237", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:28:49.802	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "511e15be45701c34435da65694d3c98b", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:28:49.802	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5a472bb569ccde620eb2e161ecb3d503", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:28:49.804	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "5a472bb569ccde620eb2e161ecb3d503", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:28:49.804	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "5a472bb569ccde620eb2e161ecb3d503", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:28:49.804	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "71256cf97322ab1846bdb2443185d15b", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "71256cf97322ab1846bdb2443185d15b", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "71256cf97322ab1846bdb2443185d15b", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "511e15be45701c34435da65694d3c98b", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "cca05cd597a60a30789c58587f746237", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "cca05cd597a60a30789c58587f746237", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "cca05cd597a60a30789c58587f746237", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "51ecbe7f73e09dbf8e09641efb0fb03b", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "51ecbe7f73e09dbf8e09641efb0fb03b", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "b97db24a6cdc2fd763cf59490cc09465", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:28:49.805	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 14:28:49.856	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:28:49.856	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:28:49.856	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:28:49.805", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:28:49.857	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "b97db24a6cdc2fd763cf59490cc09465", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_142849.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/07/30 - 14:29:46.972	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:29:47.051	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "122cfc2c98b20e3eda91acbd4269f446", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:29:47.065	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "122cfc2c98b20e3eda91acbd4269f446", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:29:47.066	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "b385711422319228c73a2935c681e711", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:29:47.066	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "b2d2f461baf1c4df9c8d690d801be4e6", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:29:47.066	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5625758dbe4f76b938dbbe5281d2e2cf", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:29:47.067	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "5625758dbe4f76b938dbbe5281d2e2cf", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:29:47.067	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "5625758dbe4f76b938dbbe5281d2e2cf", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:29:47.067	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "39e3ec0b0f4ea8ee7e6b6249ef3e4c89", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "39e3ec0b0f4ea8ee7e6b6249ef3e4c89", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "39e3ec0b0f4ea8ee7e6b6249ef3e4c89", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "b2d2f461baf1c4df9c8d690d801be4e6", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "b385711422319228c73a2935c681e711", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "b385711422319228c73a2935c681e711", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "b385711422319228c73a2935c681e711", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "0695c0fc6b77aac8d293fdf3f257a8cc", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "0695c0fc6b77aac8d293fdf3f257a8cc", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:29:47.068	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "122cfc2c98b20e3eda91acbd4269f446", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:29:47.069	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:29:47.069	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.000113}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.000113, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:29:47.069", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:29:47.110	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "122cfc2c98b20e3eda91acbd4269f446", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_142947.xlsx", "success": false, "processing_time": 0.000113}
[insbuy]2025/07/30 - 14:33:35.671	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:33:35.746	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "949882b66809a208dcd0e3dcee4f5abb", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:33:35.760	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "949882b66809a208dcd0e3dcee4f5abb", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:33:35.760	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "c353118842b5935bbe17113c0566271b", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:33:35.760	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "6555ee7642972cdb4ac77881fd9dc05b", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:33:35.761	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "cc4f9cd8b363fb440e3865da79ea8fdd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:33:35.761	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "cc4f9cd8b363fb440e3865da79ea8fdd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:33:35.761	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "cc4f9cd8b363fb440e3865da79ea8fdd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:33:35.761	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "d6b49058dd283cc9b484ab897077cca7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "d6b49058dd283cc9b484ab897077cca7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "d6b49058dd283cc9b484ab897077cca7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "6555ee7642972cdb4ac77881fd9dc05b", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "c353118842b5935bbe17113c0566271b", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "c353118842b5935bbe17113c0566271b", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "c353118842b5935bbe17113c0566271b", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "7316149ad3586f22b870c9c175e144ea", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "7316149ad3586f22b870c9c175e144ea", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "949882b66809a208dcd0e3dcee4f5abb", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:33:35.762	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:33:35.763	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005139}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005139, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:33:35.800	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:33:35.801	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:33:35.763", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:33:35.801	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:33:35.801	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "949882b66809a208dcd0e3dcee4f5abb", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_143335.xlsx", "success": false, "processing_time": 0.0005139}
[insbuy]2025/07/30 - 14:37:43.014	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:37:43.093	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "2b12ebfdecd1502d993fc03ee0e5da6e", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:37:43.109	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "2b12ebfdecd1502d993fc03ee0e5da6e", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:37:43.109	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "471779f3be516551832dd1fd63d7ed4c", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:37:43.109	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "0464b877dd0e58b6771c9f1c05a120ff", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:37:43.109	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "d3641fd93caa63848434525dded238e5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:37:43.110	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "d3641fd93caa63848434525dded238e5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:37:43.110	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "d3641fd93caa63848434525dded238e5", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:37:43.110	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "e2e64c8c5ab14c470e43a1bc20a5d5cb", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "e2e64c8c5ab14c470e43a1bc20a5d5cb", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "e2e64c8c5ab14c470e43a1bc20a5d5cb", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "0464b877dd0e58b6771c9f1c05a120ff", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "471779f3be516551832dd1fd63d7ed4c", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "471779f3be516551832dd1fd63d7ed4c", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "471779f3be516551832dd1fd63d7ed4c", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "97395fe96ce1ba80599b0f7d4d45051f", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "97395fe96ce1ba80599b0f7d4d45051f", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:37:43.111	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "2b12ebfdecd1502d993fc03ee0e5da6e", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:37:43.112	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:37:43.113	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.00143}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.00143, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:37:43.113", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:37:43.152	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "2b12ebfdecd1502d993fc03ee0e5da6e", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_143743.xlsx", "success": false, "processing_time": 0.00143}
[insbuy]2025/07/30 - 14:42:39.195	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:42:39.280	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "dd234add499007cc1b2e4235f9b8c4a9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:42:39.293	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "dd234add499007cc1b2e4235f9b8c4a9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:42:39.293	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "db18bbac7ab284c878feef6c1a619122", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:42:39.294	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "74f36dff221934964fe9c4f39c1fa7cd", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:42:39.294	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ed9ca07c7822f1b119bf98f89cc68292", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ed9ca07c7822f1b119bf98f89cc68292", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ed9ca07c7822f1b119bf98f89cc68292", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f22136165f4db12f5fa5dc2d7ce6bfa1", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f22136165f4db12f5fa5dc2d7ce6bfa1", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f22136165f4db12f5fa5dc2d7ce6bfa1", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "74f36dff221934964fe9c4f39c1fa7cd", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "db18bbac7ab284c878feef6c1a619122", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "db18bbac7ab284c878feef6c1a619122", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "db18bbac7ab284c878feef6c1a619122", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "d777f94e38395f25ec9968cc754e1611", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "d777f94e38395f25ec9968cc754e1611", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:42:39.295	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "dd234add499007cc1b2e4235f9b8c4a9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:42:39.296	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:42:39.296	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005158}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005158, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:42:39.296", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:42:39.334	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "dd234add499007cc1b2e4235f9b8c4a9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_144239.xlsx", "success": false, "processing_time": 0.0005158}
[insbuy]2025/07/30 - 14:44:42.662	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:44:42.818	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "3d8ed53ea76d35dd33645a9f0bb71daa", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:44:42.847	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "3d8ed53ea76d35dd33645a9f0bb71daa", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:44:42.847	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "05f426a3ac576306e8496e84eeae5cdd", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:44:42.847	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "a499ab1ad6a65339f61121601fff8a0d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:44:42.848	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "38fa5a4261911ac5a9fc27f9d19f70d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "38fa5a4261911ac5a9fc27f9d19f70d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "38fa5a4261911ac5a9fc27f9d19f70d1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "bfaeff3fff6d7a04aa0324ff2e841ac4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "bfaeff3fff6d7a04aa0324ff2e841ac4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "bfaeff3fff6d7a04aa0324ff2e841ac4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "a499ab1ad6a65339f61121601fff8a0d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "05f426a3ac576306e8496e84eeae5cdd", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "05f426a3ac576306e8496e84eeae5cdd", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "05f426a3ac576306e8496e84eeae5cdd", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "d8e59eae12f1796f9c9a80d25cae6884", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "d8e59eae12f1796f9c9a80d25cae6884", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:44:42.849	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "3d8ed53ea76d35dd33645a9f0bb71daa", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:44:42.850	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:44:42.850	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005732}
[insbuy]2025/07/30 - 14:44:42.891	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:44:42.891	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005732, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:44:42.850", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:44:42.892	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "3d8ed53ea76d35dd33645a9f0bb71daa", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_144442.xlsx", "success": false, "processing_time": 0.0005732}
[insbuy]2025/07/30 - 14:47:52.411	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:47:52.775	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "6fab9480845bdd2e94412f2205f86a42", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:47:52.798	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "6fab9480845bdd2e94412f2205f86a42", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:47:52.798	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "96e622f02350479168f54f9963f1ba83", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:47:52.798	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "9c4e345ab2a97c3c7245932174e70317", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:47:52.799	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "0beacafb53c7b70129db6e50cb453dac", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:47:52.799	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "0beacafb53c7b70129db6e50cb453dac", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:47:52.799	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "0beacafb53c7b70129db6e50cb453dac", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "57a9bc84aeb7b6daa87691fcbf45072a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "57a9bc84aeb7b6daa87691fcbf45072a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "57a9bc84aeb7b6daa87691fcbf45072a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "9c4e345ab2a97c3c7245932174e70317", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "96e622f02350479168f54f9963f1ba83", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "96e622f02350479168f54f9963f1ba83", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "96e622f02350479168f54f9963f1ba83", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "40b315b650c36bff4fa22266a9d1e686", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "40b315b650c36bff4fa22266a9d1e686", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:47:52.800	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "6fab9480845bdd2e94412f2205f86a42", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:47:52.801	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:47:52.801	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 14:47:52.842	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:47:52.801", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:47:52.843	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "6fab9480845bdd2e94412f2205f86a42", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_144752.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/07/30 - 14:53:03.536	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:53:03.617	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "088e555007ea3ba7f07064c77bc232e1", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:53:03.635	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "088e555007ea3ba7f07064c77bc232e1", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:53:03.635	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "c4cf660600eab0c69db25d1ed3223148", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:53:03.635	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "685ea8b6e1361ddf5326bcad7c4e465c", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:53:03.635	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "9f7637fd7c1668e4692eeddcd5665246", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:53:03.636	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "9f7637fd7c1668e4692eeddcd5665246", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "9f7637fd7c1668e4692eeddcd5665246", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4e149aa449768541eb30618a2f76425d", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4e149aa449768541eb30618a2f76425d", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4e149aa449768541eb30618a2f76425d", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "685ea8b6e1361ddf5326bcad7c4e465c", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "c4cf660600eab0c69db25d1ed3223148", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "c4cf660600eab0c69db25d1ed3223148", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "c4cf660600eab0c69db25d1ed3223148", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "c6767986bfdcb6889165954d16b5bec5", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "c6767986bfdcb6889165954d16b5bec5", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:53:03.637	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "088e555007ea3ba7f07064c77bc232e1", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:53:03.638	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:53:03.638	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 14:53:03.680	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:53:03.638", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:53:03.681	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "088e555007ea3ba7f07064c77bc232e1", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_145303.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/07/30 - 14:57:27.964	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 14:57:28.049	[34minfo[0m	test/contract_transformer_test.go:425	开始通用数据导出测试	{"traceId": "7b0686bfdcdaa2098c73920774b13455", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 14:57:28.086	[34minfo[0m	test/contract_transformer_test.go:438	成功查询到合同数据	{"traceId": "7b0686bfdcdaa2098c73920774b13455", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 14:57:28.086	[34minfo[0m	test/contract_transformer_test.go:515	开始加载所有映射配置文件	{"traceId": "40835a5e0915edd1e1df37c4cbbae64d", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 14:57:28.086	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "509b61e48aab173790d07bf32aff661e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 14:57:28.086	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "79dddb1ff9cf6c9220e05bf5e25020b6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "79dddb1ff9cf6c9220e05bf5e25020b6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "79dddb1ff9cf6c9220e05bf5e25020b6", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "1e7f5fd25b9579e19d0649812b716b6a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "1e7f5fd25b9579e19d0649812b716b6a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "1e7f5fd25b9579e19d0649812b716b6a", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "509b61e48aab173790d07bf32aff661e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	test/contract_transformer_test.go:526	成功加载映射配置	{"traceId": "40835a5e0915edd1e1df37c4cbbae64d", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "40835a5e0915edd1e1df37c4cbbae64d", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	test/contract_transformer_test.go:532	已加载映射规则	{"traceId": "40835a5e0915edd1e1df37c4cbbae64d", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	test/contract_transformer_test.go:549	开始选择映射规则	{"traceId": "51c4265fb092a5a3c9f4f5d7d5f3e2c3", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 14:57:28.087	[34minfo[0m	test/contract_transformer_test.go:554	找到精确匹配的映射规则	{"traceId": "51c4265fb092a5a3c9f4f5d7d5f3e2c3", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 14:57:28.088	[34minfo[0m	test/contract_transformer_test.go:458	成功选择映射规则	{"traceId": "7b0686bfdcdaa2098c73920774b13455", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 14:57:28.088	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 14:57:28.089	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005347}
[insbuy]2025/07/30 - 14:57:28.128	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 14:57:28.128	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 14:57:28.128	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005347, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 14:57:28.128	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 14:57:28.128	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 14:57:28.089", "data_version": "1.0"}
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 14:57:28.129	[34minfo[0m	test/contract_transformer_test.go:500	通用数据导出测试完成	{"traceId": "7b0686bfdcdaa2098c73920774b13455", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_145728.xlsx", "success": false, "processing_time": 0.0005347}
[insbuy]2025/07/30 - 15:02:49.645	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:02:49.733	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "86850fbf9bea67b575d3b7f878453129", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:02:49.734	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "86850fbf9bea67b575d3b7f878453129", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:02:49.734	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "86850fbf9bea67b575d3b7f878453129", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:03:53.232	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:03:53.315	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b6e1de6d29260aa77f90ac1445e85bbd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:03:53.316	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b6e1de6d29260aa77f90ac1445e85bbd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:03:53.316	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b6e1de6d29260aa77f90ac1445e85bbd", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:05:35.124	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:05:35.211	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "a6610ae528a3a6a9f87729092dc98c60", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:05:35.213	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "a6610ae528a3a6a9f87729092dc98c60", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:05:35.213	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "a6610ae528a3a6a9f87729092dc98c60", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:06:48.422	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:06:48.503	[34minfo[0m	test/contract_transformer_test.go:429	开始通用数据导出测试	{"traceId": "861d0958a6ea67c4493b74efd0554d9a", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 15:06:48.518	[34minfo[0m	test/contract_transformer_test.go:442	成功查询到合同数据	{"traceId": "861d0958a6ea67c4493b74efd0554d9a", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 15:06:48.518	[34minfo[0m	test/contract_transformer_test.go:519	开始加载所有映射配置文件	{"traceId": "baf73d7722335972669e3391918ca8ca", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:06:48.518	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "6c1647b1b862bdee53f479afe9a826af", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:06:48.519	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ce0ca8a813a3673e55ad9dfec7ed8f27", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:06:48.519	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ce0ca8a813a3673e55ad9dfec7ed8f27", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:06:48.519	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ce0ca8a813a3673e55ad9dfec7ed8f27", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:06:48.519	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "2a1a61801e3303202eca0b2d8f6f9184", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "2a1a61801e3303202eca0b2d8f6f9184", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "2a1a61801e3303202eca0b2d8f6f9184", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "6c1647b1b862bdee53f479afe9a826af", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:530	成功加载映射配置	{"traceId": "baf73d7722335972669e3391918ca8ca", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "baf73d7722335972669e3391918ca8ca", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "baf73d7722335972669e3391918ca8ca", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:553	开始选择映射规则	{"traceId": "26d5eb5f83722a8eac0bec8d7e348ed4", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:558	找到精确匹配的映射规则	{"traceId": "26d5eb5f83722a8eac0bec8d7e348ed4", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:06:48.520	[34minfo[0m	test/contract_transformer_test.go:462	成功选择映射规则	{"traceId": "861d0958a6ea67c4493b74efd0554d9a", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:06:48.521	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/30 - 15:06:48.521	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.00003}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.00003, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 0}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "招商银行", "bank_branch": "上海高安支行", "bank_region": "上海市 上海市"}
[insbuy]2025/07/30 - 15:06:48.558	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"复兴公园请款函--化粪池第二季度.pdf","file_url":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM"}], "remarks": ""}
[insbuy]2025/07/30 - 15:06:48.559	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/30 - 15:06:48.521", "data_version": "1.0"}
[insbuy]2025/07/30 - 15:06:48.559	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 15:06:48.559	[34minfo[0m	test/contract_transformer_test.go:504	通用数据导出测试完成	{"traceId": "861d0958a6ea67c4493b74efd0554d9a", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_150648.xlsx", "success": false, "processing_time": 0.00003}
[insbuy]2025/07/30 - 15:08:44.369	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:08:44.457	[34minfo[0m	test/contract_transformer_test.go:429	开始通用数据导出测试	{"traceId": "9817773999b01014f0e2113265383f02", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 15:08:44.474	[34minfo[0m	test/contract_transformer_test.go:442	成功查询到合同数据	{"traceId": "9817773999b01014f0e2113265383f02", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 15:08:44.474	[34minfo[0m	test/contract_transformer_test.go:519	开始加载所有映射配置文件	{"traceId": "8242753adb2c24b56c27b89f86d1c6e7", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:08:44.474	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "d346b9e1fb0738782f3cb74efbfb22f5", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:08:44.474	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "430b91078fc8c71dc4afbf4083162146", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:08:44.476	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "430b91078fc8c71dc4afbf4083162146", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:08:44.476	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "430b91078fc8c71dc4afbf4083162146", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:08:44.476	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "6de31262d58b5a6a04c4976aeff8260e", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "6de31262d58b5a6a04c4976aeff8260e", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "6de31262d58b5a6a04c4976aeff8260e", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "d346b9e1fb0738782f3cb74efbfb22f5", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:530	成功加载映射配置	{"traceId": "8242753adb2c24b56c27b89f86d1c6e7", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "8242753adb2c24b56c27b89f86d1c6e7", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "8242753adb2c24b56c27b89f86d1c6e7", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:553	开始选择映射规则	{"traceId": "24ad4c89eb65fb8448354e23fd12d73f", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:558	找到精确匹配的映射规则	{"traceId": "24ad4c89eb65fb8448354e23fd12d73f", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	test/contract_transformer_test.go:462	成功选择映射规则	{"traceId": "9817773999b01014f0e2113265383f02", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:08:44.477	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:11:00.930	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:11:01.050	[34minfo[0m	test/contract_transformer_test.go:429	开始通用数据导出测试	{"traceId": "c3be057405c3e9be8355924dcd2ec770", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 15:11:01.068	[34minfo[0m	test/contract_transformer_test.go:442	成功查询到合同数据	{"traceId": "c3be057405c3e9be8355924dcd2ec770", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 15:11:01.068	[34minfo[0m	test/contract_transformer_test.go:519	开始加载所有映射配置文件	{"traceId": "ccabe43e839679cce37393c989d04c2d", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:11:01.068	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "e34c3d00df9d90d5d4d552deb387157a", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:11:01.068	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "34b5fa82c48d0f4aa2321661b40eabb0", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:11:01.069	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "34b5fa82c48d0f4aa2321661b40eabb0", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:11:01.069	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "34b5fa82c48d0f4aa2321661b40eabb0", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:11:01.069	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "f9073ad5fd35d986e89a88876b117b11", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "f9073ad5fd35d986e89a88876b117b11", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "f9073ad5fd35d986e89a88876b117b11", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "e34c3d00df9d90d5d4d552deb387157a", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:530	成功加载映射配置	{"traceId": "ccabe43e839679cce37393c989d04c2d", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "ccabe43e839679cce37393c989d04c2d", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "ccabe43e839679cce37393c989d04c2d", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:553	开始选择映射规则	{"traceId": "9a313149063c12d37f69c53d12a06a39", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:558	找到精确匹配的映射规则	{"traceId": "9a313149063c12d37f69c53d12a06a39", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	test/contract_transformer_test.go:462	成功选择映射规则	{"traceId": "c3be057405c3e9be8355924dcd2ec770", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:11:01.070	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:12:58.644	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:12:58.729	[34minfo[0m	test/contract_transformer_test.go:429	开始通用数据导出测试	{"traceId": "37fab3cbcd6d401d53c406d728891b67", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 15:12:58.746	[34minfo[0m	test/contract_transformer_test.go:442	成功查询到合同数据	{"traceId": "37fab3cbcd6d401d53c406d728891b67", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 15:12:58.747	[34minfo[0m	test/contract_transformer_test.go:519	开始加载所有映射配置文件	{"traceId": "1e3f08a5cad3733151596da3e5e9554b", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:12:58.747	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "f8f6cb5fea630dffa2283b47a982cef1", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:12:58.747	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b247f10b48d81fb81a751e5e20bcfb11", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:12:58.748	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b247f10b48d81fb81a751e5e20bcfb11", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b247f10b48d81fb81a751e5e20bcfb11", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "66d7c4e1a0e3b1acb5f4189c6ae25835", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "66d7c4e1a0e3b1acb5f4189c6ae25835", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "66d7c4e1a0e3b1acb5f4189c6ae25835", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "f8f6cb5fea630dffa2283b47a982cef1", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:530	成功加载映射配置	{"traceId": "1e3f08a5cad3733151596da3e5e9554b", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "1e3f08a5cad3733151596da3e5e9554b", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:536	已加载映射规则	{"traceId": "1e3f08a5cad3733151596da3e5e9554b", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:553	开始选择映射规则	{"traceId": "1bfc3ec4a6e9cd1fffd84f7bfed03995", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:558	找到精确匹配的映射规则	{"traceId": "1bfc3ec4a6e9cd1fffd84f7bfed03995", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	test/contract_transformer_test.go:462	成功选择映射规则	{"traceId": "37fab3cbcd6d401d53c406d728891b67", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:12:58.749	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:12:58.750	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005226}
[insbuy]2025/07/30 - 15:12:58.785	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 15:12:58.785	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005226, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 1}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "CMB", "bank_branch": "114661", "bank_region": "1796231,1796236"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM","file_url":""}], "remarks": "温馨提示：\n1、提交付款时，请一并上传相关付款资料扫描件。如:双方盖章的合同、发票、设计方案、施工方案、结案材料、报告等。\n2、为不影响审批，纸质单据请务必第一时间交至财务处(纸质单据审核人)。\n3、Swift代码、联行号查询地址：https://www.icvio.cn/swift\n4、流程结束即视为款项已完成支付，如需【银行付款回单】请向[出纳]索要。"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "transform_time": "[insbuy]2025/07/30 - 15:12:58.750", "data_version": "1.0"}
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 15:12:58.786	[34minfo[0m	test/contract_transformer_test.go:504	通用数据导出测试完成	{"traceId": "37fab3cbcd6d401d53c406d728891b67", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_151258.xlsx", "success": false, "processing_time": 0.0005226}
[insbuy]2025/07/30 - 15:17:35.116	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:17:35.211	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "46f6667ff10acaba0074063e8dfadaac", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9"}
[insbuy]2025/07/30 - 15:17:35.228	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "46f6667ff10acaba0074063e8dfadaac", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "CANCELED"}
[insbuy]2025/07/30 - 15:17:35.228	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "c6b53a2f523e6405111dc8a8e73f8d05", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:17:35.228	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "b45dbaa8ab3d96b446c5ae583cf8d56e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:17:35.229	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "77036e8ec2f1f99aa7f63d08437d21c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "77036e8ec2f1f99aa7f63d08437d21c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "77036e8ec2f1f99aa7f63d08437d21c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "9677a329dba0e3325b4d0b231120d4d9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "9677a329dba0e3325b4d0b231120d4d9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "9677a329dba0e3325b4d0b231120d4d9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "b45dbaa8ab3d96b446c5ae583cf8d56e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "c6b53a2f523e6405111dc8a8e73f8d05", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "c6b53a2f523e6405111dc8a8e73f8d05", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "c6b53a2f523e6405111dc8a8e73f8d05", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "1e7d9bba915ec598c29b23d2ab8d7205", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "1e7d9bba915ec598c29b23d2ab8d7205", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:17:35.230	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "46f6667ff10acaba0074063e8dfadaac", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:17:35.231	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:17:35.231	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_name": "付款申请", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:01:47.339"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "FA4BF10C-6B76-465F-8D52-389417004EE9", "title": "付款申请", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "b16cebe7", "initiator_department_id": "e353e63f8abdf33c", "serial_number": "20250704003335"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "大楼二季度化粪池清掏费用，费用共计75000元，请领导审批!", "payment_entity": "上海函倍物业管理有限公司", "business_type": "物业类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 300000, "contract_paid_amount": 75000, "current_request_amount": 75000}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 1}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "上海君达管道工程有限公司", "account_type": "2", "account_number": "***************", "bank_name": "CMB", "bank_branch": "114661", "bank_region": "1796231,1796236"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/09 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=ODQzZjYwODcwODczMWYwMGMxZjg1OTliYzQ2N2NmMWVfOGIxMzdhOWQyMzAxNmI5MDA3NjRiOGYzMDUyNGFiY2NfSUQ6NzUyMzA4NDI0Nzg2NDMxMTgxMl8xNzUzNjk2ODM0OjE3NTM3ODMyMzRfVjM","file_url":""}], "remarks": "温馨提示：\n1、提交付款时，请一并上传相关付款资料扫描件。如:双方盖章的合同、发票、设计方案、施工方案、结案材料、报告等。\n2、为不影响审批，纸质单据请务必第一时间交至财务处(纸质单据审核人)。\n3、Swift代码、联行号查询地址：https://www.icvio.cn/swift\n4、流程结束即视为款项已完成支付，如需【银行付款回单】请向[出纳]索要。"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "transform_time": "[insbuy]2025/07/30 - 15:17:35.231", "data_version": "1.0"}
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 15:17:35.272	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "46f6667ff10acaba0074063e8dfadaac", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "output_file": "approval_export_FA4BF10C-6B76-465F-8D52-389417004EE9_20250730_151735.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/07/30 - 15:26:38.745	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/30 - 15:26:38.856	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "b6c55999b75bfb279e50317dfa79a6dd", "task": "TestUniversalDataExport", "instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F"}
[insbuy]2025/07/30 - 15:26:38.869	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "b6c55999b75bfb279e50317dfa79a6dd", "task": "TestUniversalDataExport", "instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请", "status": "APPROVED"}
[insbuy]2025/07/30 - 15:26:38.869	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "271ddf26963f888fc953296c7c1657a0", "task": "LoadAllMappingConfigs"}
[insbuy]2025/07/30 - 15:26:38.869	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "177dc02d4505ac0c348f7ab6263579c3", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/07/30 - 15:26:38.870	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "bb6c3df71e8f95574b9503f4fd68f430", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/30 - 15:26:38.872	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "bb6c3df71e8f95574b9503f4fd68f430", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:26:38.872	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "bb6c3df71e8f95574b9503f4fd68f430", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/30 - 15:26:38.872	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "23a8a745245c268ea5cc0a62364d0e77", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "23a8a745245c268ea5cc0a62364d0e77", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "23a8a745245c268ea5cc0a62364d0e77", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "177dc02d4505ac0c348f7ab6263579c3", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "271ddf26963f888fc953296c7c1657a0", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "271ddf26963f888fc953296c7c1657a0", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "271ddf26963f888fc953296c7c1657a0", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "c892c7c0e9b0f1b1675c4ec73b57c879", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "c892c7c0e9b0f1b1675c4ec73b57c879", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_name": "付款申请审批"}
[insbuy]2025/07/30 - 15:26:38.873	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "b6c55999b75bfb279e50317dfa79a6dd", "task": "TestUniversalDataExport", "instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "rule_approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "rule_approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/07/30 - 15:26:38.874	[34minfo[0m	insbuy/contract_transformer.go:209	开始转换合同数据	{"instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/30 - 15:26:38.874	[34minfo[0m	insbuy/contract_transformer.go:264	合同数据转换完成	{"instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "success": false, "error_count": 2, "warning_count": 0, "processing_time": 0.0005107}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "approval_name": "付款申请", "status": "APPROVED", "created_at": "[insbuy]2025/07/28 - 18:06:40.737"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005107, "error_count": 2, "warning_count": 0}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 2, "field": "tax_rate", "message": "税率应在0-1之间", "value": "6.0000"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "title": "付款申请", "application_status": "APPROVED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "758a28c3", "initiator_department_id": "4ef54ga4613fe2e5", "serial_number": "20250724003750"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "9月13日Lafin演出DJ Franchise付款，合同双方已盖章，发票已开具", "payment_entity": "喝清喝理（上海）餐饮管理有限公司", "business_type": "艺人类", "expense_department": "", "payment_currency": "人民币"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 79931, "contract_paid_amount": 0, "current_request_amount": 79931}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "专用发票", "tax_rate": 6}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "满雀（上海）文化传媒有限公司", "account_type": "2", "account_number": "310066917013002187777", "bank_name": "BOCOM", "bank_branch": "0", "bank_region": "1796231,1796236"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]2025/07/28 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=Yjg1NmE1M2RlZDBhY2UxZDI1ZTk1YzViYzBlNmM0NzhfMTc1MmY0NGIyMWM4ZmU4ZTBkZjgxYjQ4NjIzZDJkZGRfSUQ6NzUzMDYzOTEzMTQ0NDI5NzcyOV8xNzUzNjk3MTY4OjE3NTM3ODM1NjhfVjM","file_url":""}], "remarks": "温馨提示：\n1、提交付款时，请一并上传相关付款资料扫描件。如:双方盖章的合同、发票、设计方案、施工方案、结案材料、报告等。\n2、为不影响审批，纸质单据请务必第一时间交至财务处(纸质单据审核人)。\n3、Swift代码、联行号查询地址：https://www.icvio.cn/swift\n4、流程结束即视为款项已完成支付，如需【银行付款回单】请向[出纳]索要。"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "transform_time": "[insbuy]2025/07/30 - 15:26:38.874", "data_version": "1.0"}
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/07/30 - 15:26:38.909	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "b6c55999b75bfb279e50317dfa79a6dd", "task": "TestUniversalDataExport", "instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "output_file": "approval_export_3A26A9D4-87B6-4E7F-B7FC-353205194E9F_20250730_152638.xlsx", "success": false, "processing_time": 0.0005107}
