# GORM Gen 最佳实践指南

## 概述

本文档总结了在飞书合同管理系统中使用GORM Gen的最佳实践，基于官方文档 https://gorm.io/zh_CN/gen/ 的指导原则。

## 基本使用模式

### 1. 导入和初始化

```go
import (
    "gorm.io/gen"
    "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
)

// 获取生成的查询对象
db := query.InsContract
q := db.WithContext(context.Background())
```

### 2. 基本查询操作

#### 单条记录查询
```go
// 正确的方式 - 返回指针和错误
contract, err := q.Where(db.ID.Eq(id)).First()
if err != nil {
    if err == gorm.ErrRecordNotFound {
        // 处理记录不存在
    }
    return err
}

// 使用返回的指针
resp.InsContract = *contract
```

#### 多条记录查询
```go
// 返回指针切片
contracts, err := q.Where(db.Status.Eq("approved")).Find()
if err != nil {
    return err
}

// 转换为值切片（如果需要）
result := make([]insbuy.InsContract, len(contracts))
for i, contract := range contracts {
    result[i] = *contract
}
```

#### 分页查询
```go
// 先获取总数
total, err := q.Count()
if err != nil {
    return err
}

// 再获取分页数据
contracts, err := q.Offset(offset).Limit(pageSize).Find()
if err != nil {
    return err
}
```

### 3. 条件构建

#### 基本条件
```go
conditions := make([]gen.Condition, 0)

if approvalCode != "" {
    conditions = append(conditions, db.ApprovalCode.Eq(approvalCode))
}
if status != "" {
    conditions = append(conditions, db.Status.Eq(status))
}

// 应用条件
for _, condition := range conditions {
    q = q.Where(condition)
}
```

#### 范围查询
```go
// 时间范围
if startTime != nil {
    conditions = append(conditions, db.StartTime.Gte(*startTime))
}
if endTime != nil {
    conditions = append(conditions, db.EndTime.Lte(*endTime))
}

// 数值范围
if minAmount > 0 {
    conditions = append(conditions, db.ContractAmount.Gte(minAmount))
}
if maxAmount > 0 {
    conditions = append(conditions, db.ContractAmount.Lte(maxAmount))
}
```

#### IN查询
```go
// 单个IN查询
if len(statusList) > 0 {
    conditions = append(conditions, db.Status.In(statusList...))
}

// 多个IN查询
if len(approvalCodes) > 0 {
    conditions = append(conditions, db.ApprovalCode.In(approvalCodes...))
}
```

#### 模糊查询
```go
if keyword != "" {
    conditions = append(conditions, db.ContractTitle.Like("%" + keyword + "%"))
}
```

### 4. 排序

```go
// 动态排序
switch orderBy {
case "created_at":
    if orderType == "asc" {
        q = q.Order(db.CreatedAt)
    } else {
        q = q.Order(db.CreatedAt.Desc())
    }
case "contract_amount":
    if orderType == "asc" {
        q = q.Order(db.ContractAmount)
    } else {
        q = q.Order(db.ContractAmount.Desc())
    }
default:
    q = q.Order(db.CreatedAt.Desc())
}
```

### 5. 聚合查询

#### 基本聚合
```go
// 计数
count, err := q.Count()

// 求和
var totalAmount float64
err = q.Select(db.ContractAmount.Sum()).Scan(&totalAmount)

// 平均值
var avgAmount float64
err = q.Select(db.ContractAmount.Avg()).Scan(&avgAmount)

// 最大值和最小值
var stats struct {
    MaxAmount float64 `json:"max_amount"`
    MinAmount float64 `json:"min_amount"`
}
err = q.Select(
    db.ContractAmount.Max().As("max_amount"),
    db.ContractAmount.Min().As("min_amount"),
).Scan(&stats)
```

#### 分组聚合
```go
// 按状态分组统计
var statusStats []struct {
    Status string `json:"status"`
    Count  int64  `json:"count"`
}

err = q.Select(db.Status, db.Status.Count().As("count")).
    Group(db.Status).
    Scan(&statusStats)
```

#### 复杂聚合
```go
// 多字段分组和聚合
var deptStats []struct {
    DepartmentId string  `json:"department_id"`
    Count        int64   `json:"count"`
    TotalAmount  float64 `json:"total_amount"`
    AvgAmount    float64 `json:"avg_amount"`
}

err = q.Select(
    db.DepartmentId,
    db.DepartmentId.Count().As("count"),
    db.ContractAmount.Sum().As("total_amount"),
    db.ContractAmount.Avg().As("avg_amount"),
).Group(db.DepartmentId).
    Having(db.ContractAmount.Sum().Gt(10000)). // HAVING条件
    Scan(&deptStats)
```

### 6. 关联查询

#### LEFT JOIN
```go
commentDB := query.InsContractComment
taskDB := query.InsContractTask

q := db.WithContext(context.Background()).
    LeftJoin(commentDB, db.ID.EqCol(commentDB.ContractId)).
    LeftJoin(taskDB, db.ID.EqCol(taskDB.ContractId))

// 选择字段和聚合
var results []struct {
    insbuy.InsContract
    CommentCount int64 `json:"comment_count"`
    TaskCount    int64 `json:"task_count"`
}

err = q.Select(
    db.ALL,
    commentDB.ID.Count().As("comment_count"),
    taskDB.ID.Count().As("task_count"),
).Group(db.ID).
    Scan(&results)
```

### 7. 数据操作

#### 创建记录
```go
contract := &insbuy.InsContract{
    ApprovalCode: "xxx",
    ApprovalName: "xxx",
    // ... 其他字段
}

err = db.WithContext(ctx).Create(contract)
if err != nil {
    return fmt.Errorf("创建失败: %w", err)
}

// contract.ID 现在包含自动生成的ID
```

#### 更新记录
```go
// 按条件更新
result, err := db.WithContext(ctx).
    Where(db.ID.Eq(contractId)).
    Updates(map[string]interface{}{
        "status":     "approved",
        "updated_at": time.Now(),
    })

if err != nil {
    return fmt.Errorf("更新失败: %w", err)
}

// 检查影响的行数
if result.RowsAffected == 0 {
    return fmt.Errorf("没有记录被更新")
}
```

#### 批量更新
```go
// 批量更新多条记录
result, err := db.WithContext(ctx).
    Where(db.InstanceCode.In(instanceCodes...)).
    Updates(map[string]interface{}{
        "status":     newStatus,
        "updated_at": time.Now(),
    })

if err != nil {
    return fmt.Errorf("批量更新失败: %w", err)
}

log.Printf("更新了 %d 条记录", result.RowsAffected)
```

#### 删除记录
```go
// 软删除
result, err := db.WithContext(ctx).
    Where(db.ID.Eq(contractId)).
    Delete()

// 硬删除
result, err := db.WithContext(ctx).
    Unscoped().
    Where(db.ID.Eq(contractId)).
    Delete()
```

## 错误处理最佳实践

### 1. 记录不存在处理
```go
contract, err := q.Where(db.ID.Eq(id)).First()
if err != nil {
    if err == gorm.ErrRecordNotFound {
        return fmt.Errorf("合同不存在")
    }
    return fmt.Errorf("查询失败: %w", err)
}
```

### 2. 统一错误处理
```go
func (s *Service) handleQueryError(err error, operation string) error {
    if err == nil {
        return nil
    }
    
    if err == gorm.ErrRecordNotFound {
        return fmt.Errorf("记录不存在")
    }
    
    global.GVA_LOG.Error(operation+"失败", zap.Error(err))
    return fmt.Errorf("%s失败: %w", operation, err)
}
```

## 性能优化建议

### 1. 索引使用
```go
// 确保查询字段有索引
conditions = append(conditions, db.ApprovalCode.Eq(code)) // approval_code应该有索引
conditions = append(conditions, db.Status.Eq(status))     // status应该有索引
```

### 2. 分页查询优化
```go
// 先查总数，再查数据
total, err := q.Count()
if err != nil {
    return err
}

if total == 0 {
    return emptyResult, nil // 避免不必要的数据查询
}

contracts, err := q.Offset(offset).Limit(pageSize).Find()
```

### 3. 选择必要字段
```go
// 只选择需要的字段
contracts, err := q.Select(
    db.ID, 
    db.ApprovalCode, 
    db.Status, 
    db.ContractAmount,
).Find()
```

### 4. 批量操作
```go
// 批量查询而不是循环单个查询
contracts, err := q.Where(db.ID.In(ids...)).Find()

// 批量更新而不是循环单个更新
result, err := db.Where(db.Status.Eq("pending")).
    Updates(map[string]interface{}{"status": "approved"})
```

## 常见陷阱和注意事项

### 1. 指针处理
```go
// ❌ 错误：First()返回指针，不能直接赋值给值类型
var contract insbuy.InsContract
err = q.First(&contract) // 这是错误的用法

// ✅ 正确：使用指针接收
contract, err := q.First()
if err != nil {
    return err
}
resp.Contract = *contract // 解引用赋值
```

### 2. 条件构建
```go
// ❌ 错误：重复使用同一个查询对象
q := db.WithContext(ctx)
q.Where(db.Status.Eq("approved"))
q.Where(db.Amount.Gt(1000)) // 这会覆盖前面的条件

// ✅ 正确：链式调用或条件数组
q = q.Where(db.Status.Eq("approved")).Where(db.Amount.Gt(1000))

// 或者使用条件数组
conditions := []gen.Condition{
    db.Status.Eq("approved"),
    db.Amount.Gt(1000),
}
for _, cond := range conditions {
    q = q.Where(cond)
}
```

### 3. 事务处理
```go
// 在事务中使用GORM Gen
err = global.GVA_DB.Transaction(func(tx *gorm.DB) error {
    // 使用事务创建新的查询对象
    txDB := query.Use(tx).InsContract
    
    contract, err := txDB.Where(txDB.ID.Eq(id)).First()
    if err != nil {
        return err
    }
    
    // 更新操作
    _, err = txDB.Where(txDB.ID.Eq(id)).Updates(updates)
    return err
})
```

## 总结

GORM Gen提供了类型安全的数据库操作方式，通过以上最佳实践可以：

1. **提高代码质量**：类型安全，编译时检查
2. **提升开发效率**：智能提示，减少SQL错误
3. **增强可维护性**：结构化查询，易于理解和修改
4. **优化性能**：合理使用索引和批量操作

在实际项目中，建议：
- 统一错误处理模式
- 合理使用索引
- 避免N+1查询问题
- 适当使用事务
- 记录关键操作日志
