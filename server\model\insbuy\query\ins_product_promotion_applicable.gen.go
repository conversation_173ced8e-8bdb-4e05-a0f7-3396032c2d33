// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotionApplicable(db *gorm.DB, opts ...gen.DOOption) insProductPromotionApplicable {
	_insProductPromotionApplicable := insProductPromotionApplicable{}

	_insProductPromotionApplicable.insProductPromotionApplicableDo.UseDB(db, opts...)
	_insProductPromotionApplicable.insProductPromotionApplicableDo.UseModel(&insbuy.InsProductPromotionApplicable{})

	tableName := _insProductPromotionApplicable.insProductPromotionApplicableDo.TableName()
	_insProductPromotionApplicable.ALL = field.NewAsterisk(tableName)
	_insProductPromotionApplicable.ID = field.NewUint(tableName, "id")
	_insProductPromotionApplicable.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotionApplicable.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotionApplicable.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotionApplicable.PromotionId = field.NewUint(tableName, "promotion_id")
	_insProductPromotionApplicable.Applicable = field.NewInt(tableName, "applicable")

	_insProductPromotionApplicable.fillFieldMap()

	return _insProductPromotionApplicable
}

type insProductPromotionApplicable struct {
	insProductPromotionApplicableDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	PromotionId field.Uint
	Applicable  field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPromotionApplicable) Table(newTableName string) *insProductPromotionApplicable {
	i.insProductPromotionApplicableDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotionApplicable) As(alias string) *insProductPromotionApplicable {
	i.insProductPromotionApplicableDo.DO = *(i.insProductPromotionApplicableDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotionApplicable) updateTableName(table string) *insProductPromotionApplicable {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PromotionId = field.NewUint(table, "promotion_id")
	i.Applicable = field.NewInt(table, "applicable")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotionApplicable) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotionApplicable) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["promotion_id"] = i.PromotionId
	i.fieldMap["applicable"] = i.Applicable
}

func (i insProductPromotionApplicable) clone(db *gorm.DB) insProductPromotionApplicable {
	i.insProductPromotionApplicableDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotionApplicable) replaceDB(db *gorm.DB) insProductPromotionApplicable {
	i.insProductPromotionApplicableDo.ReplaceDB(db)
	return i
}

type insProductPromotionApplicableDo struct{ gen.DO }

func (i insProductPromotionApplicableDo) Debug() *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionApplicableDo) WithContext(ctx context.Context) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionApplicableDo) ReadDB() *insProductPromotionApplicableDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionApplicableDo) WriteDB() *insProductPromotionApplicableDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionApplicableDo) Session(config *gorm.Session) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionApplicableDo) Clauses(conds ...clause.Expression) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionApplicableDo) Returning(value interface{}, columns ...string) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionApplicableDo) Not(conds ...gen.Condition) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionApplicableDo) Or(conds ...gen.Condition) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionApplicableDo) Select(conds ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionApplicableDo) Where(conds ...gen.Condition) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionApplicableDo) Order(conds ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionApplicableDo) Distinct(cols ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionApplicableDo) Omit(cols ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionApplicableDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionApplicableDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionApplicableDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionApplicableDo) Group(cols ...field.Expr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionApplicableDo) Having(conds ...gen.Condition) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionApplicableDo) Limit(limit int) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionApplicableDo) Offset(offset int) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionApplicableDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionApplicableDo) Unscoped() *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionApplicableDo) Create(values ...*insbuy.InsProductPromotionApplicable) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionApplicableDo) CreateInBatches(values []*insbuy.InsProductPromotionApplicable, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionApplicableDo) Save(values ...*insbuy.InsProductPromotionApplicable) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionApplicableDo) First() (*insbuy.InsProductPromotionApplicable, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionApplicable), nil
	}
}

func (i insProductPromotionApplicableDo) Take() (*insbuy.InsProductPromotionApplicable, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionApplicable), nil
	}
}

func (i insProductPromotionApplicableDo) Last() (*insbuy.InsProductPromotionApplicable, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionApplicable), nil
	}
}

func (i insProductPromotionApplicableDo) Find() ([]*insbuy.InsProductPromotionApplicable, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotionApplicable), err
}

func (i insProductPromotionApplicableDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotionApplicable, err error) {
	buf := make([]*insbuy.InsProductPromotionApplicable, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionApplicableDo) FindInBatches(result *[]*insbuy.InsProductPromotionApplicable, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionApplicableDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionApplicableDo) Assign(attrs ...field.AssignExpr) *insProductPromotionApplicableDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionApplicableDo) Joins(fields ...field.RelationField) *insProductPromotionApplicableDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionApplicableDo) Preload(fields ...field.RelationField) *insProductPromotionApplicableDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionApplicableDo) FirstOrInit() (*insbuy.InsProductPromotionApplicable, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionApplicable), nil
	}
}

func (i insProductPromotionApplicableDo) FirstOrCreate() (*insbuy.InsProductPromotionApplicable, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionApplicable), nil
	}
}

func (i insProductPromotionApplicableDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotionApplicable, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionApplicableDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionApplicableDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionApplicableDo) Delete(models ...*insbuy.InsProductPromotionApplicable) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionApplicableDo) withDO(do gen.Dao) *insProductPromotionApplicableDo {
	i.DO = *do.(*gen.DO)
	return i
}
