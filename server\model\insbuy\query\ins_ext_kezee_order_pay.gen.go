// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtKezeeOrderPay(db *gorm.DB, opts ...gen.DOOption) insExtKezeeOrderPay {
	_insExtKezeeOrderPay := insExtKezeeOrderPay{}

	_insExtKezeeOrderPay.insExtKezeeOrderPayDo.UseDB(db, opts...)
	_insExtKezeeOrderPay.insExtKezeeOrderPayDo.UseModel(&insbuy.InsExtKezeeOrderPay{})

	tableName := _insExtKezeeOrderPay.insExtKezeeOrderPayDo.TableName()
	_insExtKezeeOrderPay.ALL = field.NewAsterisk(tableName)
	_insExtKezeeOrderPay.Id = field.NewUint(tableName, "id")
	_insExtKezeeOrderPay.OrderId = field.NewUint(tableName, "order_id")
	_insExtKezeeOrderPay.PaywayId = field.NewString(tableName, "payway_id")
	_insExtKezeeOrderPay.PayName = field.NewString(tableName, "pay_name")
	_insExtKezeeOrderPay.PayMoney = field.NewFloat64(tableName, "pay_money")
	_insExtKezeeOrderPay.IncomeMoney = field.NewFloat64(tableName, "income_money")

	_insExtKezeeOrderPay.fillFieldMap()

	return _insExtKezeeOrderPay
}

type insExtKezeeOrderPay struct {
	insExtKezeeOrderPayDo

	ALL         field.Asterisk
	Id          field.Uint
	OrderId     field.Uint
	PaywayId    field.String
	PayName     field.String
	PayMoney    field.Float64
	IncomeMoney field.Float64

	fieldMap map[string]field.Expr
}

func (i insExtKezeeOrderPay) Table(newTableName string) *insExtKezeeOrderPay {
	i.insExtKezeeOrderPayDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtKezeeOrderPay) As(alias string) *insExtKezeeOrderPay {
	i.insExtKezeeOrderPayDo.DO = *(i.insExtKezeeOrderPayDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtKezeeOrderPay) updateTableName(table string) *insExtKezeeOrderPay {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.OrderId = field.NewUint(table, "order_id")
	i.PaywayId = field.NewString(table, "payway_id")
	i.PayName = field.NewString(table, "pay_name")
	i.PayMoney = field.NewFloat64(table, "pay_money")
	i.IncomeMoney = field.NewFloat64(table, "income_money")

	i.fillFieldMap()

	return i
}

func (i *insExtKezeeOrderPay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtKezeeOrderPay) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.Id
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["payway_id"] = i.PaywayId
	i.fieldMap["pay_name"] = i.PayName
	i.fieldMap["pay_money"] = i.PayMoney
	i.fieldMap["income_money"] = i.IncomeMoney
}

func (i insExtKezeeOrderPay) clone(db *gorm.DB) insExtKezeeOrderPay {
	i.insExtKezeeOrderPayDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtKezeeOrderPay) replaceDB(db *gorm.DB) insExtKezeeOrderPay {
	i.insExtKezeeOrderPayDo.ReplaceDB(db)
	return i
}

type insExtKezeeOrderPayDo struct{ gen.DO }

func (i insExtKezeeOrderPayDo) Debug() *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtKezeeOrderPayDo) WithContext(ctx context.Context) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtKezeeOrderPayDo) ReadDB() *insExtKezeeOrderPayDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtKezeeOrderPayDo) WriteDB() *insExtKezeeOrderPayDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtKezeeOrderPayDo) Session(config *gorm.Session) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtKezeeOrderPayDo) Clauses(conds ...clause.Expression) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtKezeeOrderPayDo) Returning(value interface{}, columns ...string) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtKezeeOrderPayDo) Not(conds ...gen.Condition) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtKezeeOrderPayDo) Or(conds ...gen.Condition) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtKezeeOrderPayDo) Select(conds ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtKezeeOrderPayDo) Where(conds ...gen.Condition) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtKezeeOrderPayDo) Order(conds ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtKezeeOrderPayDo) Distinct(cols ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtKezeeOrderPayDo) Omit(cols ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtKezeeOrderPayDo) Join(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtKezeeOrderPayDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtKezeeOrderPayDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtKezeeOrderPayDo) Group(cols ...field.Expr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtKezeeOrderPayDo) Having(conds ...gen.Condition) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtKezeeOrderPayDo) Limit(limit int) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtKezeeOrderPayDo) Offset(offset int) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtKezeeOrderPayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtKezeeOrderPayDo) Unscoped() *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtKezeeOrderPayDo) Create(values ...*insbuy.InsExtKezeeOrderPay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtKezeeOrderPayDo) CreateInBatches(values []*insbuy.InsExtKezeeOrderPay, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtKezeeOrderPayDo) Save(values ...*insbuy.InsExtKezeeOrderPay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtKezeeOrderPayDo) First() (*insbuy.InsExtKezeeOrderPay, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderPay), nil
	}
}

func (i insExtKezeeOrderPayDo) Take() (*insbuy.InsExtKezeeOrderPay, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderPay), nil
	}
}

func (i insExtKezeeOrderPayDo) Last() (*insbuy.InsExtKezeeOrderPay, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderPay), nil
	}
}

func (i insExtKezeeOrderPayDo) Find() ([]*insbuy.InsExtKezeeOrderPay, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtKezeeOrderPay), err
}

func (i insExtKezeeOrderPayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtKezeeOrderPay, err error) {
	buf := make([]*insbuy.InsExtKezeeOrderPay, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtKezeeOrderPayDo) FindInBatches(result *[]*insbuy.InsExtKezeeOrderPay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtKezeeOrderPayDo) Attrs(attrs ...field.AssignExpr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtKezeeOrderPayDo) Assign(attrs ...field.AssignExpr) *insExtKezeeOrderPayDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtKezeeOrderPayDo) Joins(fields ...field.RelationField) *insExtKezeeOrderPayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtKezeeOrderPayDo) Preload(fields ...field.RelationField) *insExtKezeeOrderPayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtKezeeOrderPayDo) FirstOrInit() (*insbuy.InsExtKezeeOrderPay, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderPay), nil
	}
}

func (i insExtKezeeOrderPayDo) FirstOrCreate() (*insbuy.InsExtKezeeOrderPay, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrderPay), nil
	}
}

func (i insExtKezeeOrderPayDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtKezeeOrderPay, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtKezeeOrderPayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtKezeeOrderPayDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtKezeeOrderPayDo) Delete(models ...*insbuy.InsExtKezeeOrderPay) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtKezeeOrderPayDo) withDO(do gen.Dao) *insExtKezeeOrderPayDo {
	i.DO = *do.(*gen.DO)
	return i
}
