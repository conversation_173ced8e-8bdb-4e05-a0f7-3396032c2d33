// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeApplicable(db *gorm.DB, opts ...gen.DOOption) insServiceFeeApplicable {
	_insServiceFeeApplicable := insServiceFeeApplicable{}

	_insServiceFeeApplicable.insServiceFeeApplicableDo.UseDB(db, opts...)
	_insServiceFeeApplicable.insServiceFeeApplicableDo.UseModel(&insbuy.InsServiceFeeApplicable{})

	tableName := _insServiceFeeApplicable.insServiceFeeApplicableDo.TableName()
	_insServiceFeeApplicable.ALL = field.NewAsterisk(tableName)
	_insServiceFeeApplicable.ID = field.NewUint(tableName, "id")
	_insServiceFeeApplicable.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeApplicable.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeApplicable.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeApplicable.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeApplicable.Applicable = field.NewInt(tableName, "applicable")

	_insServiceFeeApplicable.fillFieldMap()

	return _insServiceFeeApplicable
}

type insServiceFeeApplicable struct {
	insServiceFeeApplicableDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ServiceFeeId field.Uint
	Applicable   field.Int

	fieldMap map[string]field.Expr
}

func (i insServiceFeeApplicable) Table(newTableName string) *insServiceFeeApplicable {
	i.insServiceFeeApplicableDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeApplicable) As(alias string) *insServiceFeeApplicable {
	i.insServiceFeeApplicableDo.DO = *(i.insServiceFeeApplicableDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeApplicable) updateTableName(table string) *insServiceFeeApplicable {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.Applicable = field.NewInt(table, "applicable")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeApplicable) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeApplicable) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["applicable"] = i.Applicable
}

func (i insServiceFeeApplicable) clone(db *gorm.DB) insServiceFeeApplicable {
	i.insServiceFeeApplicableDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeApplicable) replaceDB(db *gorm.DB) insServiceFeeApplicable {
	i.insServiceFeeApplicableDo.ReplaceDB(db)
	return i
}

type insServiceFeeApplicableDo struct{ gen.DO }

func (i insServiceFeeApplicableDo) Debug() *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeApplicableDo) WithContext(ctx context.Context) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeApplicableDo) ReadDB() *insServiceFeeApplicableDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeApplicableDo) WriteDB() *insServiceFeeApplicableDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeApplicableDo) Session(config *gorm.Session) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeApplicableDo) Clauses(conds ...clause.Expression) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeApplicableDo) Returning(value interface{}, columns ...string) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeApplicableDo) Not(conds ...gen.Condition) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeApplicableDo) Or(conds ...gen.Condition) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeApplicableDo) Select(conds ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeApplicableDo) Where(conds ...gen.Condition) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeApplicableDo) Order(conds ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeApplicableDo) Distinct(cols ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeApplicableDo) Omit(cols ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeApplicableDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeApplicableDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeApplicableDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeApplicableDo) Group(cols ...field.Expr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeApplicableDo) Having(conds ...gen.Condition) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeApplicableDo) Limit(limit int) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeApplicableDo) Offset(offset int) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeApplicableDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeApplicableDo) Unscoped() *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeApplicableDo) Create(values ...*insbuy.InsServiceFeeApplicable) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeApplicableDo) CreateInBatches(values []*insbuy.InsServiceFeeApplicable, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeApplicableDo) Save(values ...*insbuy.InsServiceFeeApplicable) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeApplicableDo) First() (*insbuy.InsServiceFeeApplicable, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeApplicable), nil
	}
}

func (i insServiceFeeApplicableDo) Take() (*insbuy.InsServiceFeeApplicable, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeApplicable), nil
	}
}

func (i insServiceFeeApplicableDo) Last() (*insbuy.InsServiceFeeApplicable, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeApplicable), nil
	}
}

func (i insServiceFeeApplicableDo) Find() ([]*insbuy.InsServiceFeeApplicable, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeApplicable), err
}

func (i insServiceFeeApplicableDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeApplicable, err error) {
	buf := make([]*insbuy.InsServiceFeeApplicable, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeApplicableDo) FindInBatches(result *[]*insbuy.InsServiceFeeApplicable, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeApplicableDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeApplicableDo) Assign(attrs ...field.AssignExpr) *insServiceFeeApplicableDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeApplicableDo) Joins(fields ...field.RelationField) *insServiceFeeApplicableDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeApplicableDo) Preload(fields ...field.RelationField) *insServiceFeeApplicableDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeApplicableDo) FirstOrInit() (*insbuy.InsServiceFeeApplicable, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeApplicable), nil
	}
}

func (i insServiceFeeApplicableDo) FirstOrCreate() (*insbuy.InsServiceFeeApplicable, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeApplicable), nil
	}
}

func (i insServiceFeeApplicableDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeApplicable, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeApplicableDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeApplicableDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeApplicableDo) Delete(models ...*insbuy.InsServiceFeeApplicable) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeApplicableDo) withDO(do gen.Dao) *insServiceFeeApplicableDo {
	i.DO = *do.(*gen.DO)
	return i
}
