package printmodel

// TplShiftListBill 班结表
type TplShiftListBill struct {
	TplBase
	//收银员名称
	CashierName string
	//营业日期
	BusinessDate string
	OpenNum      int    //开台数
	OpenTime     string //开班时间
	CloseTime    string //结班时间
	StatRange    string //统计范围

	OrderNum            int                   //开台数
	DeskNum             int                   //开桌数-纯桌台
	PeopleNum           int                   //人数
	TotalAmount         float64               //今日营收=商品金额合计
	GiftAmount          float64               //赠送金额
	DiscountFee         float64               // 优惠金额
	PlayerPrice         float64               // 头号玩家金额
	PaidAmount          float64               //已付金额
	UnpaidAmount        float64               //未付金额
	BalancePayAmount    float64               // 余额支付金额
	BalanceGiveAmount   float64               // 余额赠送金额
	TicketAmount        float64               //门票金额
	OfflineTicketAmount float64               //线下门票金额
	ServiceFee          float64               // 服务费
	Payments            []TplOrderBillPayment // 付款方式
	RoomBill            []TplRoomBill         // 包厢金额统计
	TotalPayAmounts     float64               //支付合计金额

	//餐厅清单使用-----
	RealAmount float64 //实收金额 =已付–折扣金额
}

// TplRoomBill 包厢金额统计
type TplRoomBill struct {
	RoomType string  // 包厢类型
	OpenNum  int     // 开房数
	Amount   float64 // 实收金额
	Discount float64 // 优惠金额（免单+ 折扣－调整）
}
