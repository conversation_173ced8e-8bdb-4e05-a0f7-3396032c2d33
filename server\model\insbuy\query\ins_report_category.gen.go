// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportCategory(db *gorm.DB, opts ...gen.DOOption) insReportCategory {
	_insReportCategory := insReportCategory{}

	_insReportCategory.insReportCategoryDo.UseDB(db, opts...)
	_insReportCategory.insReportCategoryDo.UseModel(&insbuy.InsReportCategory{})

	tableName := _insReportCategory.insReportCategoryDo.TableName()
	_insReportCategory.ALL = field.NewAsterisk(tableName)
	_insReportCategory.ID = field.NewUint(tableName, "id")
	_insReportCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportCategory.StoreId = field.NewUint(tableName, "store_id")
	_insReportCategory.Name = field.NewString(tableName, "name")
	_insReportCategory.Type = field.NewInt(tableName, "type")
	_insReportCategory.ParentID = field.NewUint(tableName, "parent_id")
	_insReportCategory.Sort = field.NewInt(tableName, "sort")

	_insReportCategory.fillFieldMap()

	return _insReportCategory
}

type insReportCategory struct {
	insReportCategoryDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	StoreId   field.Uint
	Name      field.String
	Type      field.Int
	ParentID  field.Uint
	Sort      field.Int

	fieldMap map[string]field.Expr
}

func (i insReportCategory) Table(newTableName string) *insReportCategory {
	i.insReportCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportCategory) As(alias string) *insReportCategory {
	i.insReportCategoryDo.DO = *(i.insReportCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportCategory) updateTableName(table string) *insReportCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Type = field.NewInt(table, "type")
	i.ParentID = field.NewUint(table, "parent_id")
	i.Sort = field.NewInt(table, "sort")

	i.fillFieldMap()

	return i
}

func (i *insReportCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["type"] = i.Type
	i.fieldMap["parent_id"] = i.ParentID
	i.fieldMap["sort"] = i.Sort
}

func (i insReportCategory) clone(db *gorm.DB) insReportCategory {
	i.insReportCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportCategory) replaceDB(db *gorm.DB) insReportCategory {
	i.insReportCategoryDo.ReplaceDB(db)
	return i
}

type insReportCategoryDo struct{ gen.DO }

func (i insReportCategoryDo) Debug() *insReportCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportCategoryDo) WithContext(ctx context.Context) *insReportCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportCategoryDo) ReadDB() *insReportCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportCategoryDo) WriteDB() *insReportCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportCategoryDo) Session(config *gorm.Session) *insReportCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportCategoryDo) Clauses(conds ...clause.Expression) *insReportCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportCategoryDo) Returning(value interface{}, columns ...string) *insReportCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportCategoryDo) Not(conds ...gen.Condition) *insReportCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportCategoryDo) Or(conds ...gen.Condition) *insReportCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportCategoryDo) Select(conds ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportCategoryDo) Where(conds ...gen.Condition) *insReportCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportCategoryDo) Order(conds ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportCategoryDo) Distinct(cols ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportCategoryDo) Omit(cols ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportCategoryDo) Group(cols ...field.Expr) *insReportCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportCategoryDo) Having(conds ...gen.Condition) *insReportCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportCategoryDo) Limit(limit int) *insReportCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportCategoryDo) Offset(offset int) *insReportCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportCategoryDo) Unscoped() *insReportCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportCategoryDo) Create(values ...*insbuy.InsReportCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportCategoryDo) CreateInBatches(values []*insbuy.InsReportCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportCategoryDo) Save(values ...*insbuy.InsReportCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportCategoryDo) First() (*insbuy.InsReportCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportCategory), nil
	}
}

func (i insReportCategoryDo) Take() (*insbuy.InsReportCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportCategory), nil
	}
}

func (i insReportCategoryDo) Last() (*insbuy.InsReportCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportCategory), nil
	}
}

func (i insReportCategoryDo) Find() ([]*insbuy.InsReportCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportCategory), err
}

func (i insReportCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportCategory, err error) {
	buf := make([]*insbuy.InsReportCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportCategoryDo) FindInBatches(result *[]*insbuy.InsReportCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportCategoryDo) Attrs(attrs ...field.AssignExpr) *insReportCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportCategoryDo) Assign(attrs ...field.AssignExpr) *insReportCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportCategoryDo) Joins(fields ...field.RelationField) *insReportCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportCategoryDo) Preload(fields ...field.RelationField) *insReportCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportCategoryDo) FirstOrInit() (*insbuy.InsReportCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportCategory), nil
	}
}

func (i insReportCategoryDo) FirstOrCreate() (*insbuy.InsReportCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportCategory), nil
	}
}

func (i insReportCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportCategoryDo) Delete(models ...*insbuy.InsReportCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportCategoryDo) withDO(do gen.Dao) *insReportCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
