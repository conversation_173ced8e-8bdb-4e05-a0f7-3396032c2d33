package test

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/inswarehouse"
	"github.com/xtulnx/jkit-go/jtime"
	"testing"
)

func TestFixDayData(t *testing.T) {
	prepare()
	d := jtime.Str2Time("2024-09-24")
	err := inswarehouse.FixDayData(context.Background(), query.Q, []uint{847}, 80, d)
	if err != nil {
		return
	}
}

func TestGetMaterialData(t *testing.T) {
	prepare()
	service := insbuy.InsOrderInfoService{}
	res, err2 := service.GetMaterialData(query.Q, []uint{1945})
	if err2 != nil {
		t.Fatalf("err%v", err2)
		return
	}
	t.Logf("res%v\n", res)
}

func TestWriteInventoryCostPrice(t *testing.T) {
	prepare()
	service := insbuy.InsWarehouseService{}
	err := service.WriteInventoryCostPrice(context.Background(), query.Q, insbuyReq.StatisticalCostPriceParams{
		StoreIds:  []uint{6},
		StartDate: jtime.Str2Time("2024-10-01"),
		EndDate:   jtime.Str2Time("2024-10-22"),
	})
	if err != nil {
		t.Fatalf("err%v", err)
		return
	}
	t.Logf("测试统计原料写入店铺成本价")
}

func TestBusinessDaySaleLog(t *testing.T) {
	prepare()
	service := insbuy.InsWarehouseService{}
	err := service.BusinessDaySaleLog(context.Background(), query.Q, insbuyReq.BusinessDaySaleLogParams{
		StoreIds:  []uint{6},
		StartDate: jtime.Str2Time("2024-10-22"),
		EndDate:   jtime.Str2Time("2024-10-22"),
	})
	if err != nil {
		t.Fatalf("err%v", err)
		return
	}
	t.Logf("测试销售记录,写入店铺成本价")
}

func TestDepositExpireSms(t *testing.T) {
	prepare()
	service := insbuy.InsDepositService{}
	err := service.DepositExpireSms()
	if err != nil {
		t.Fatalf("err%v", err)
		return
	}
}

func TestAutoInventoryCheck(t *testing.T) {
	prepare()
	service := insbuy.InsWarehouseService{}
	err := service.ProcessStoreAutoCheck(6, jtime.Str2Date("2025-07-16"))
	if err != nil {
		t.Fatalf("err%v", err)
		return
	}
	t.Logf("测试自动盘点")
}
