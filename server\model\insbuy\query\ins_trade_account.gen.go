// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeAccount(db *gorm.DB, opts ...gen.DOOption) insTradeAccount {
	_insTradeAccount := insTradeAccount{}

	_insTradeAccount.insTradeAccountDo.UseDB(db, opts...)
	_insTradeAccount.insTradeAccountDo.UseModel(&insbuy.InsTradeAccount{})

	tableName := _insTradeAccount.insTradeAccountDo.TableName()
	_insTradeAccount.ALL = field.NewAsterisk(tableName)
	_insTradeAccount.ID = field.NewUint(tableName, "id")
	_insTradeAccount.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeAccount.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeAccount.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTradeAccount.AcctType = field.NewInt(tableName, "acct_type")
	_insTradeAccount.StoreId = field.NewUint(tableName, "store_id")
	_insTradeAccount.AccountSn = field.NewString(tableName, "account_sn")
	_insTradeAccount.Amount = field.NewFloat64(tableName, "amount")
	_insTradeAccount.RepayAmount = field.NewFloat64(tableName, "repay_amount")
	_insTradeAccount.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insTradeAccount.AccountantId = field.NewUint(tableName, "accountant_id")
	_insTradeAccount.Version = field.NewUint(tableName, "version")
	_insTradeAccount.DeskName = field.NewString(tableName, "desk_name")
	_insTradeAccount.DeskId = field.NewUint(tableName, "desk_id")
	_insTradeAccount.BusinessDay = field.NewTime(tableName, "business_day")
	_insTradeAccount.TradeId = field.NewUint64(tableName, "trade_id")

	_insTradeAccount.fillFieldMap()

	return _insTradeAccount
}

type insTradeAccount struct {
	insTradeAccountDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	AcctType     field.Int
	StoreId      field.Uint
	AccountSn    field.String
	Amount       field.Float64
	RepayAmount  field.Float64
	OpenDeskId   field.Uint
	AccountantId field.Uint
	Version      field.Uint
	DeskName     field.String
	DeskId       field.Uint
	BusinessDay  field.Time
	TradeId      field.Uint64

	fieldMap map[string]field.Expr
}

func (i insTradeAccount) Table(newTableName string) *insTradeAccount {
	i.insTradeAccountDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeAccount) As(alias string) *insTradeAccount {
	i.insTradeAccountDo.DO = *(i.insTradeAccountDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeAccount) updateTableName(table string) *insTradeAccount {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.AcctType = field.NewInt(table, "acct_type")
	i.StoreId = field.NewUint(table, "store_id")
	i.AccountSn = field.NewString(table, "account_sn")
	i.Amount = field.NewFloat64(table, "amount")
	i.RepayAmount = field.NewFloat64(table, "repay_amount")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.AccountantId = field.NewUint(table, "accountant_id")
	i.Version = field.NewUint(table, "version")
	i.DeskName = field.NewString(table, "desk_name")
	i.DeskId = field.NewUint(table, "desk_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.TradeId = field.NewUint64(table, "trade_id")

	i.fillFieldMap()

	return i
}

func (i *insTradeAccount) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeAccount) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["acct_type"] = i.AcctType
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["account_sn"] = i.AccountSn
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["repay_amount"] = i.RepayAmount
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["accountant_id"] = i.AccountantId
	i.fieldMap["version"] = i.Version
	i.fieldMap["desk_name"] = i.DeskName
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["trade_id"] = i.TradeId
}

func (i insTradeAccount) clone(db *gorm.DB) insTradeAccount {
	i.insTradeAccountDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeAccount) replaceDB(db *gorm.DB) insTradeAccount {
	i.insTradeAccountDo.ReplaceDB(db)
	return i
}

type insTradeAccountDo struct{ gen.DO }

func (i insTradeAccountDo) Debug() *insTradeAccountDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeAccountDo) WithContext(ctx context.Context) *insTradeAccountDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeAccountDo) ReadDB() *insTradeAccountDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeAccountDo) WriteDB() *insTradeAccountDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeAccountDo) Session(config *gorm.Session) *insTradeAccountDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeAccountDo) Clauses(conds ...clause.Expression) *insTradeAccountDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeAccountDo) Returning(value interface{}, columns ...string) *insTradeAccountDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeAccountDo) Not(conds ...gen.Condition) *insTradeAccountDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeAccountDo) Or(conds ...gen.Condition) *insTradeAccountDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeAccountDo) Select(conds ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeAccountDo) Where(conds ...gen.Condition) *insTradeAccountDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeAccountDo) Order(conds ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeAccountDo) Distinct(cols ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeAccountDo) Omit(cols ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeAccountDo) Join(table schema.Tabler, on ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeAccountDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeAccountDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeAccountDo) Group(cols ...field.Expr) *insTradeAccountDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeAccountDo) Having(conds ...gen.Condition) *insTradeAccountDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeAccountDo) Limit(limit int) *insTradeAccountDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeAccountDo) Offset(offset int) *insTradeAccountDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeAccountDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeAccountDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeAccountDo) Unscoped() *insTradeAccountDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeAccountDo) Create(values ...*insbuy.InsTradeAccount) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeAccountDo) CreateInBatches(values []*insbuy.InsTradeAccount, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeAccountDo) Save(values ...*insbuy.InsTradeAccount) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeAccountDo) First() (*insbuy.InsTradeAccount, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccount), nil
	}
}

func (i insTradeAccountDo) Take() (*insbuy.InsTradeAccount, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccount), nil
	}
}

func (i insTradeAccountDo) Last() (*insbuy.InsTradeAccount, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccount), nil
	}
}

func (i insTradeAccountDo) Find() ([]*insbuy.InsTradeAccount, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeAccount), err
}

func (i insTradeAccountDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeAccount, err error) {
	buf := make([]*insbuy.InsTradeAccount, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeAccountDo) FindInBatches(result *[]*insbuy.InsTradeAccount, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeAccountDo) Attrs(attrs ...field.AssignExpr) *insTradeAccountDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeAccountDo) Assign(attrs ...field.AssignExpr) *insTradeAccountDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeAccountDo) Joins(fields ...field.RelationField) *insTradeAccountDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeAccountDo) Preload(fields ...field.RelationField) *insTradeAccountDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeAccountDo) FirstOrInit() (*insbuy.InsTradeAccount, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccount), nil
	}
}

func (i insTradeAccountDo) FirstOrCreate() (*insbuy.InsTradeAccount, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccount), nil
	}
}

func (i insTradeAccountDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeAccount, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeAccountDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeAccountDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeAccountDo) Delete(models ...*insbuy.InsTradeAccount) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeAccountDo) withDO(do gen.Dao) *insTradeAccountDo {
	i.DO = *do.(*gen.DO)
	return i
}
