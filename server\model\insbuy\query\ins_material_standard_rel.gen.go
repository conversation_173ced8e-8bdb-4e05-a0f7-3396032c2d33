// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterialStandardRel(db *gorm.DB, opts ...gen.DOOption) insMaterialStandardRel {
	_insMaterialStandardRel := insMaterialStandardRel{}

	_insMaterialStandardRel.insMaterialStandardRelDo.UseDB(db, opts...)
	_insMaterialStandardRel.insMaterialStandardRelDo.UseModel(&insbuy.InsMaterialStandardRel{})

	tableName := _insMaterialStandardRel.insMaterialStandardRelDo.TableName()
	_insMaterialStandardRel.ALL = field.NewAsterisk(tableName)
	_insMaterialStandardRel.ID = field.NewUint(tableName, "id")
	_insMaterialStandardRel.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterialStandardRel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterialStandardRel.DeletedAt = field.NewField(tableName, "deleted_at")
	_insMaterialStandardRel.StandardId = field.NewUint(tableName, "standard_id")
	_insMaterialStandardRel.MaterialId = field.NewUint(tableName, "material_id")

	_insMaterialStandardRel.fillFieldMap()

	return _insMaterialStandardRel
}

type insMaterialStandardRel struct {
	insMaterialStandardRelDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StandardId field.Uint
	MaterialId field.Uint

	fieldMap map[string]field.Expr
}

func (i insMaterialStandardRel) Table(newTableName string) *insMaterialStandardRel {
	i.insMaterialStandardRelDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterialStandardRel) As(alias string) *insMaterialStandardRel {
	i.insMaterialStandardRelDo.DO = *(i.insMaterialStandardRelDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterialStandardRel) updateTableName(table string) *insMaterialStandardRel {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StandardId = field.NewUint(table, "standard_id")
	i.MaterialId = field.NewUint(table, "material_id")

	i.fillFieldMap()

	return i
}

func (i *insMaterialStandardRel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterialStandardRel) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["standard_id"] = i.StandardId
	i.fieldMap["material_id"] = i.MaterialId
}

func (i insMaterialStandardRel) clone(db *gorm.DB) insMaterialStandardRel {
	i.insMaterialStandardRelDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterialStandardRel) replaceDB(db *gorm.DB) insMaterialStandardRel {
	i.insMaterialStandardRelDo.ReplaceDB(db)
	return i
}

type insMaterialStandardRelDo struct{ gen.DO }

func (i insMaterialStandardRelDo) Debug() *insMaterialStandardRelDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialStandardRelDo) WithContext(ctx context.Context) *insMaterialStandardRelDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialStandardRelDo) ReadDB() *insMaterialStandardRelDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialStandardRelDo) WriteDB() *insMaterialStandardRelDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialStandardRelDo) Session(config *gorm.Session) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialStandardRelDo) Clauses(conds ...clause.Expression) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialStandardRelDo) Returning(value interface{}, columns ...string) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialStandardRelDo) Not(conds ...gen.Condition) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialStandardRelDo) Or(conds ...gen.Condition) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialStandardRelDo) Select(conds ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialStandardRelDo) Where(conds ...gen.Condition) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialStandardRelDo) Order(conds ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialStandardRelDo) Distinct(cols ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialStandardRelDo) Omit(cols ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialStandardRelDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialStandardRelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialStandardRelDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialStandardRelDo) Group(cols ...field.Expr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialStandardRelDo) Having(conds ...gen.Condition) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialStandardRelDo) Limit(limit int) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialStandardRelDo) Offset(offset int) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialStandardRelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialStandardRelDo) Unscoped() *insMaterialStandardRelDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialStandardRelDo) Create(values ...*insbuy.InsMaterialStandardRel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialStandardRelDo) CreateInBatches(values []*insbuy.InsMaterialStandardRel, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialStandardRelDo) Save(values ...*insbuy.InsMaterialStandardRel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialStandardRelDo) First() (*insbuy.InsMaterialStandardRel, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandardRel), nil
	}
}

func (i insMaterialStandardRelDo) Take() (*insbuy.InsMaterialStandardRel, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandardRel), nil
	}
}

func (i insMaterialStandardRelDo) Last() (*insbuy.InsMaterialStandardRel, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandardRel), nil
	}
}

func (i insMaterialStandardRelDo) Find() ([]*insbuy.InsMaterialStandardRel, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterialStandardRel), err
}

func (i insMaterialStandardRelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterialStandardRel, err error) {
	buf := make([]*insbuy.InsMaterialStandardRel, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialStandardRelDo) FindInBatches(result *[]*insbuy.InsMaterialStandardRel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialStandardRelDo) Attrs(attrs ...field.AssignExpr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialStandardRelDo) Assign(attrs ...field.AssignExpr) *insMaterialStandardRelDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialStandardRelDo) Joins(fields ...field.RelationField) *insMaterialStandardRelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialStandardRelDo) Preload(fields ...field.RelationField) *insMaterialStandardRelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialStandardRelDo) FirstOrInit() (*insbuy.InsMaterialStandardRel, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandardRel), nil
	}
}

func (i insMaterialStandardRelDo) FirstOrCreate() (*insbuy.InsMaterialStandardRel, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandardRel), nil
	}
}

func (i insMaterialStandardRelDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterialStandardRel, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialStandardRelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialStandardRelDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialStandardRelDo) Delete(models ...*insbuy.InsMaterialStandardRel) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialStandardRelDo) withDO(do gen.Dao) *insMaterialStandardRelDo {
	i.DO = *do.(*gen.DO)
	return i
}
