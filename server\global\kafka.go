package global

const (
	KAFKAEVENT_BookDesk_ConfigActive        = "cfg.desk.act"     // 卡座预约活动配置激活
	KAFAKEVENT_BookDesk_ConfigCancel        = "cfg.desk.cancel"  // 卡座预约活动配置取消
	KAFKAEVENT_BookDesk_ConfigPackageUpdate = "cfg.desk.package" // 设置桌台的套餐，或修改套餐

	KAFKAEVENT_BookDesk_ActionBookSuccess = "act.desk.booksucc"    // 预约成功
	KAFKAEVENT_BookDesk_ActionBookFail    = "act.desk.bookfail"    // 预约失败：如目标日期该卡座已经被占用、或订单无效等
	KAFKAEVENT_BookDesk_ActionBookCancel  = "act.desk.bookcancel"  // 取消预约
	KAFKAEVENT_BookDesk_ActionBookRefund  = "act.desk.bookrefund"  // 退款
	KAFKAEVENT_BookDesk_ActionBookConsume = "act.desk.bookconsume" // 核销
	KAFKAEVENT_BookDesk_ActionDeskOpen    = "act.desk.open"        // 开台：该桌台已经开始提供服务，今天不可预约（不影响其它日期）；
	KAFKAEVENT_BookDesk_ActionDeskChange  = "act.desk.change"      // 换台：该桌已经换了新的卡座，今天不可预约（不影响其它日期）；
	KAFKAEVENT_BookDesk_ActionDeskReset   = "act.desk.reset"       // 清台：该桌已经可以重新提供服务，今天可以继续预约；

	KAFKAEVENT_Desk_Add  = "sync.desk.add"  //同步当前新增的桌台信息
	KAFKAEVENT_Desk_Edit = "sync.desk.edit" //同步当前修改的桌台信息
	KAFKAEVENT_Desk_Del  = "sync.desk.del"  //同步当前删除的桌台信息
	KAFKAEVENT_Desk_All  = "sync.desk.all"  //同步当店所有桌台列表信息
)
