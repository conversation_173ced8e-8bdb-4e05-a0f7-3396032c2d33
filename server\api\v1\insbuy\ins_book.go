package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insBookReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insBookRes "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Validation definition
var (
	insBookLoginVerify = utils.Rules{
		"Phone": {utils.NotEmpty()},
	}
	insSendCodeVerify = utils.Rules{
		"Phone": {utils.NotEmpty(), utils.IsMobile()},
	}
	insBookListVipVerify   = utils.Rules{}
	insBookListStoreVerify = utils.Rules{}
	insBookListDeskVerify  = utils.Rules{
		"StoreId": {utils.NotEmpty()},
	}
	insBookCreateVerify = utils.Rules{
		"StoreId":     {utils.NotEmpty()},
		"DeskId":      {utils.NotEmpty()},
		"ArrivalTime": {utils.NotEmpty()},
	}
	insBookDetailVerify = utils.Rules{
		"Id": {utils.NotEmpty()},
	}
	insBookListVerify = utils.Rules{}
)

type InsBookApi struct{}

// Login 咨客、销售人员 登录（待定）
// @Tags      InsBook
// @Summary   咨客、销售人员 登录
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookLogin          true  "登录信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookLogin,msg=string}  "登录成功"
// @Router    /insBook/login [post]
func (b *InsBookApi) Login(c *gin.Context) {
	var r insBookReq.InsBookLogin
	err := GinMustBind(c, &r)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err = utils.Verify(r, insBookLoginVerify)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookLogin
	res, err = insBookService.Login(r, authGenJwtToken)
	response.ResultErr(res, err, c)
}

// SmsLogin 手机验证码登录
// @Tags      InsBook
// @Summary   手机验证码登录
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookSmsLoginReq          true  "手机验证码登录"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookLogin,msg=string}  "手机验证码登录"
// @Router    /insBook/smsLogin [post]
func (b *InsBookApi) SmsLogin(c *gin.Context) {
	var r insBookReq.InsBookSmsLoginReq
	err := GinMustBind(c, &r)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err = utils.Verify(r, insSendCodeVerify)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookLogin
	res, err = insBookService.SmsLogin(r, authGenJwtToken)
	response.ResultErr(res, err, c)
}

// SendCode 发送验证码
// @Tags      InsBook
// @Summary   发送验证码
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookSendCodeReq          true  "发送验证码"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookLogin,msg=string}  "发送验证码"
// @Router    /insBook/sendCode [post]
func (b *InsBookApi) SendCode(c *gin.Context) {
	var r insBookReq.InsBookSendCodeReq
	err := GinMustBind(c, &r)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err = utils.Verify(r, insSendCodeVerify)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err = insBookService.SendCode(r)
	response.ResultErr(nil, err, c)
}

// ListVip 会员搜索（名称、手机号、会员号）, 姓名和手机号要脱敏
// @Tags      InsBook
// @Summary   会员搜索（名称、手机号、会员号）, 姓名和手机号要脱敏
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookListVip  true  "会员搜索信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookListVip}  "会员搜索成功"
// @Router    /insBook/listVip [get]
func (b *InsBookApi) ListVip(c *gin.Context) {
	var err error
	var r insBookReq.InsBookListVip
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListVipVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	r.FixPageSize(1, 10)
	var res *insBookRes.InsBookListVip
	res, err = insBookService.ListVip(r)
	response.ResultErr(res, err, c)
}

// Create 新增预约（直接新增会员信息、有会员信息时直接引用）
// @Tags      InsBook
// @Summary   新增预约（直接新增会员信息、有会员信息时直接引用）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookCreate          true  "预约信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookCreate}  "预约成功"
// @Router    /insBook/create [post]
func (b *InsBookApi) Create(c *gin.Context) {
	var err error
	var r insBookReq.InsBookCreate
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookCreateVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookCreate
	res, err = insBookService.Create(r)
	response.ResultErr(res, err, c)
}

// List 预约记录列表（仅显示和自己有关的预约记录）
// @Tags      InsBook
// @Summary   预约记录列表（仅显示和自己有关的预约记录）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookList          true  "预约记录列表信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookList}  "成功"
// @Router    /insBook/list [get]
func (b *InsBookApi) List(c *gin.Context) {
	var err error
	var r insBookReq.InsBookList
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	r.FixPageSize(1, 10)
	var res *insBookRes.InsBookList
	res, err = insBookService.List(r)
	response.ResultErr(res, err, c)
}

// ListStore 门店列表
// @Tags      InsBook
// @Summary   门店列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body      insBookReq.InsBookListStore  true  "门店列表信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookListStore}  "预约成功"
// @Router    /insBook/listStore [get]
func (b *InsBookApi) ListStore(c *gin.Context) {
	var err error
	var r insBookReq.InsBookListStore
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListStoreVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookListStore
	res, err = insBookService.ListStore(r)
	response.ResultErr(res, err, c)
}

// CurrentUserListStore 当前用户门店列表
// @Tags      InsBook
// @Summary   当前用户门店列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body      insBookReq.InsBookListStore  true  "当前用户门店列表信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookListStore}  "当前用户门店列表"
// @Router    /insBook/currentUserListStore [get]
func (b *InsBookApi) CurrentUserListStore(c *gin.Context) {
	var err error
	var r insBookReq.InsBookListStore
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListStoreVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookListStore
	res, err = insBookService.CurrentUserListStore(r)
	response.ResultErr(res, err, c)
}

// ListDesk 桌台列表
// @Tags      InsBook
// @Summary   桌台列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param    data  body      insBookReq.InsBookListDesk true  "桌台列表信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookListDesk} "成功"
// @Router    /insBook/listDesk [get]
func (b *InsBookApi) ListDesk(c *gin.Context) {
	var err error
	var r insBookReq.InsBookListDesk
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListDeskVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	r.FixPageSize(1, 10)
	var res *insBookRes.InsBookListDesk
	res, err = insBookService.ListDesk(r)
	response.ResultErr(res, err, c)
}

// Detail 预约详情
// @Tags      InsBook
// @Summary   预约详情
// @Security  ApiKeyAuth
// @accept    x-www-form-urlencoded
// @Produce   application/json
// @Param     id    path      int  true  "预约记录id"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookDetail,msg=string}  "the detailed booking record"
// @Router    /insBook/detail/{id} [get]
func (b *InsBookApi) Detail(c *gin.Context) {
	var err error
	var r insBookReq.InsBookDetail
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookDetailVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.InsBookDetail
	res, err = insBookService.Detail(r)
	response.ResultErr(res, err, c)
}

// Manage 预约管理 (所有的预约记录)
// @Tags      InsBook
// @Summary   预约管理（所有的预约记录）
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.InsBookList          true  "预约记录列表信息"
// @Success   200   {object}  response.Response{data=insBookRes.InsBookManage}  "成功"
// @Router    /insBook/manage [get]
func (b *InsBookApi) Manage(c *gin.Context) {
	var err error
	var r insBookReq.InsBookList
	if err = GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(r, insBookListVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	r.FixPageSize(1, 10)
	var res *insBookRes.InsBookManage
	res, err = insBookService.Manage(r)
	response.ResultErr(res, err, c)
}

// Check 预约审核
// @Tags      InsBook
// @Summary   预约审核
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param data body insBookReq.InsBookCheck true "更新预约记录信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router    /insBook/check [post]
func (b *InsBookApi) Check(c *gin.Context) {
	var insBook insBookReq.InsBookCheck
	err := GinMustBind(c, &insBook)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	verify := utils.Rules{
		"ID":     {utils.NotEmpty()},
		"Status": {utils.NotEmpty()},
	}
	if err := utils.Verify(insBook, verify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insBookService.Check(insBook); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}
