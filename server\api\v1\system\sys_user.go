package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	organization "github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/model"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// Login
// @Tags     Base
// @Summary  用户登录
// @Produce   application/json
// @Param    data  body      systemReq.Login                                             true  "用户名, 密码, 验证码"
// @Success  200   {object}  response.Response{data=systemRes.LoginResponse,msg=string}  "返回包括用户信息,token,过期时间"
// @Router   /base/login [post]
func (b *BaseApi) Login(c *gin.Context) {
	var l systemReq.Login
	err := c.ShouldBindJSON(&l)
	key := c.ClientIP()

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(l, utils.LoginVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 判断验证码是否开启
	openCaptcha := global.GVA_CONFIG.Captcha.OpenCaptcha               // 是否开启防爆次数
	openCaptchaTimeOut := global.GVA_CONFIG.Captcha.OpenCaptchaTimeOut // 缓存超时时间
	v, ok := global.BlackCache.Get(key)
	if !ok {
		global.BlackCache.Set(key, 1, time.Second*time.Duration(openCaptchaTimeOut))
	}

	var oc bool = openCaptcha == 0 || openCaptcha < interfaceToInt(v)

	var pass = false
	if !oc {
		pass = true
	} else {
		if superCaptcha := global.GVA_CONFIG.Captcha.SuperCaptcha; len(superCaptcha) > 0 {
			for _, _c := range superCaptcha {
				if _c == l.Captcha {
					pass = true
					break
				}
			}
		}
		if !pass {
			pass = store.Verify(l.CaptchaId, l.Captcha, true)
		}
	}

	//if !oc || store.Verify(l.CaptchaId, l.Captcha, true) {
	if pass {
		u := &system.SysUser{Username: l.Username, Password: l.Password}
		user, err := userService.Login(u)
		if err != nil {
			phone, p, e1 := insUserRegisterService.Login2(l.Username, l.Password)
			if e1 != nil {
				global.GVA_LOG.Error("登陆失败! 用户名不存在或者密码错误!", zap.Error(e1))
				// 验证码次数+1
				global.BlackCache.Increment(key, 1)
				response.FailWithMessage("用户名不存在或者密码错误", c)
				return
			}
			user, e1 = userService.Login(&system.SysUser{
				Username: phone,
				Password: p,
			})
			if e1 != nil {
				global.GVA_LOG.Error("登陆失败! 用户名不存在或者密码错误!", zap.Error(e1))
				// 验证码次数+1
				global.BlackCache.Increment(key, 1)
				response.FailWithMessage("用户名不存在或者密码错误", c)
				return
			}
		}
		if user.Enable != 1 {
			global.GVA_LOG.Error("登陆失败! 用户被禁止登录!")
			// 验证码次数+1
			global.BlackCache.Increment(key, 1)
			response.FailWithMessage("用户被禁止登录", c)
			return
		}
		b.TokenNext(c, *user)
		return
	}
	// 验证码次数+1
	global.BlackCache.Increment(key, 1)
	response.FailWithMessage("验证码错误", c)
}

// TokenNext 登录以后签发jwt
func (b *BaseApi) TokenNext(c *gin.Context, user system.SysUser) {
	j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)} // 唯一签名
	claims := j.CreateClaims(systemReq.BaseClaims{
		UUID:        user.UUID,
		ID:          user.ID,
		NickName:    user.NickName,
		Username:    user.Username,
		AuthorityId: user.AuthorityId,
	})
	token, err := j.CreateToken(claims)
	if err != nil {
		global.GVA_LOG.Error("获取token失败!", zap.Error(err))
		response.FailWithMessage("获取token失败", c)
		return
	}
	if !global.GVA_CONFIG.System.UseMultipoint {
		response.OkWithDetailed(systemRes.LoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
		return
	}

	if jwtStr, err := jwtService.GetRedisJWT(user.Username); err == redis.Nil {
		if err := jwtService.SetRedisJWT(token, user.Username); err != nil {
			global.GVA_LOG.Error("设置登录状态失败!", zap.Error(err))
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		response.OkWithDetailed(systemRes.LoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
	} else if err != nil {
		global.GVA_LOG.Error("设置登录状态失败!", zap.Error(err))
		response.FailWithMessage("设置登录状态失败", c)
	} else {
		var blackJWT system.JwtBlacklist
		blackJWT.Jwt = jwtStr
		if err := jwtService.JsonInBlacklist(blackJWT); err != nil {
			response.FailWithMessage("jwt作废失败", c)
			return
		}
		if err := jwtService.SetRedisJWT(token, user.Username); err != nil {
			response.FailWithMessage("设置登录状态失败", c)
			return
		}
		response.OkWithDetailed(systemRes.LoginResponse{
			User:      user,
			Token:     token,
			ExpiresAt: claims.RegisteredClaims.ExpiresAt.Unix() * 1000,
		}, "登录成功", c)
	}
}

// Register
// @Tags     SysUser
// @Summary  用户注册账号
// @Produce   application/json
// @Param    data  body      systemReq.Register                                            true  "用户名, 昵称, 密码, 角色ID"
// @Success  200   {object}  response.Response{data=systemRes.SysUserResponse,msg=string}  "用户注册账号,返回包括用户信息"
// @Router   /user/admin_register [post]
func (b *BaseApi) Register(c *gin.Context) {
	var r systemReq.Register
	err := c.ShouldBindJSON(&r)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(r, utils.RegisterVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if authorityService.CheckAuthority(utils.GetUserID(c), 0, authorityService.GetRoles(r.AuthorityIds)) {
		response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
		return
	}
	if r.BlocSale == 2 && len(r.StoreIds) > 1 {
		response.FailWithMessage("单店销售不可分配多个店铺", c)
		return
	}
	if !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) {
		uints := make([]uint, 0, len(r.StoreIds))
		for _, v := range r.StoreIds {
			uints = append(uints, v.StoreId)
		}
		diff, e1 := insUserService.GetStoreIdPermissionsDiff(utils.GetUserID(c), uints)
		if e1 != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(e1))
			err = e1
			return
		}
		if len(diff) > 0 {
			storeInfos, _ := insStoreService.GetInsStoreByIds(diff)
			i := make([]string, len(storeInfos))
			for idx, v := range storeInfos {
				i[idx] = v.Name
			}
			message := strings.Join(i, ",")
			response.FailWithMessage("设置失败-无店铺权限:"+message, c)
			return
		}
	}
	var authorities []system.SysAuthority
	isSaler := false
	for _, v := range r.AuthorityIds {
		if v == insbuy.SalesSAuthorityID {
			isSaler = true
		}
		authorities = append(authorities, system.SysAuthority{
			AuthorityId: v,
		})
	}
	user := &system.SysUser{
		Username:    r.Username,
		NickName:    r.NickName,
		Password:    r.Password,
		HeaderImg:   r.HeaderImg,
		AuthorityId: r.AuthorityId,
		Authorities: authorities,
		Enable:      r.Enable,
		Phone:       r.Phone,
		Email:       r.Email,
		BlocSale:    r.BlocSale,
		Contact:     r.ContactStr(),
	}
	userReturn, err := userService.Register(*user)
	if err != nil {
		global.GVA_LOG.Error("注册失败!", zap.Error(err))
		response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
		return
	}
	//分店
	if len(r.StoreIds) > 0 {
		for _, v := range r.StoreIds {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v.OrganizationID,
				SysUserIDS:     []uint{userReturn.ID},
			})
			if err != nil {
				response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
				return
			}
			if isSaler {
				err = insSalerStoreService.CreateInsSalerStore(insbuyReq.CreateInsSalerStoreReq{
					UserId:  userReturn.ID,
					StoreId: &v.StoreId,
				})
				if err != nil {
					response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
					return
				}
			}
		}
	}
	//组织
	if len(r.Organization) > 0 {
		for _, v := range r.Organization {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v,
				SysUserIDS:     []uint{userReturn.ID},
				IsVisual:       true,
			})
			if err != nil {
				response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
				return
			}
		}
	}
	//销售组
	if len(r.SalerOrg) > 0 {
		for _, v := range r.SalerOrg {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v,
				SysUserIDS:     []uint{userReturn.ID},
				IsVisual:       true,
			})
			if err != nil {
				response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
				return
			}
		}
	}
	if len(r.Authority2Ids) > 0 {
		_, err = authority2Service.SetUserAuthorities(userReturn.ID, r.Authority2Ids)
		if err != nil {
			response.FailWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册失败", c)
			return
		}
	}
	if len(r.ReportRule) > 0 {
		err = insReportService.SetSqlRuleByUser(userReturn.ID, r.ReportRule)
		if err != nil {
			response.FailWithMessage("设置报表规则失败", c)
			return
		}
	}
	response.OkWithDetailed(systemRes.SysUserResponse{User: userReturn}, "注册成功", c)
}

// ChangePassword
// @Tags      SysUser
// @Summary   用户修改密码
// @Security  ApiKeyAuth
// @Produce  application/json
// @Param     data  body      systemReq.ChangePasswordReq    true  "用户名, 原密码, 新密码"
// @Success   200   {object}  response.Response{msg=string}  "用户修改密码"
// @Router    /user/changePassword [post]
func (b *BaseApi) ChangePassword(c *gin.Context) {
	var req systemReq.ChangePasswordReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.ChangePasswordVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	uid := utils.GetUserID(c)
	u := &system.SysUser{GVA_MODEL: global.GVA_MODEL{ID: uid}, Password: req.Password}
	_, err = userService.ChangePassword(u, req.NewPassword)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败，原密码与当前账户不符", c)
		return
	}
	response.OkWithMessage("修改成功", c)
}

// GetUserList
// @Tags      SysUser
// @Summary   分页获取用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.PageInfo                                        true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "分页获取用户列表,返回包括列表,总数,页码,每页数量"
// @Router    /user/getUserList [post]
func (b *BaseApi) GetUserList(c *gin.Context) {
	var req systemReq.GetUserListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if !req.Export() {
		err = utils.Verify(req, utils.PageInfoVerify)
		if err != nil {
			response.FailWithMessage(err.Error(), c)
			return
		}
	}
	list, total, err := userService.GetUserInfoList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	uid := make([]uint, 0)
	for _, item := range list {
		uid = append(uid, item.ID)
	}
	storeInfo, err := insUserService.GetUserStoreInfoMap(uid)
	if err != nil {
		global.GVA_LOG.Error("获取店铺失败!", zap.Error(err))
		response.FailWithMessage("获取店铺失败", c)
		return
	}
	salerOrgMap, err := insUserService.GetUserSalerStoreInfoMap(uid)
	if err != nil {
		global.GVA_LOG.Error("获取店铺失败!", zap.Error(err))
		response.FailWithMessage("获取店铺失败", c)
		return
	}
	userOrg, err := insUserService.GetUserOrgInfoMap(uid)
	if err != nil {
		global.GVA_LOG.Error("获取店铺失败!", zap.Error(err))
		response.FailWithMessage("获取店铺失败", c)
		return
	}
	salerCodeMap, err := insSalerCodeService.IsGiveSalerCodeMap(uid)
	if err != nil {
		return
	}
	for i, item := range list {
		//分店
		list[i].StoreIds = make([]systemRes.Organization, 0)
		for _, v := range storeInfo[item.ID] {
			list[i].StoreIds = append(list[i].StoreIds, systemRes.Organization{
				Id:        v.Id,
				SysUserID: v.SysUserID,
				StoreId:   v.StoreId,
				Name:      v.Name,
			})
		}
		//销售部门
		list[i].SalerOrg = make([]systemRes.Organization, 0)
		for _, v := range salerOrgMap[item.ID] {
			list[i].SalerOrg = append(list[i].SalerOrg, systemRes.Organization{
				Id:        v.Id,
				SysUserID: v.SysUserID,
				StoreId:   v.StoreId,
				Name:      v.Name,
			})
		}
		//所属部门
		list[i].Organization = make([]systemRes.Organization, 0)
		for _, v := range userOrg[item.ID] {
			list[i].Organization = append(list[i].Organization, systemRes.Organization{
				Id:        v.Id,
				SysUserID: v.SysUserID,
				StoreId:   v.StoreId,
				Name:      v.Name,
			})
		}
		//是否赠送
		if _, ok := salerCodeMap[item.ID]; ok {
			list[i].IsGive = 1
		}
		list[i].Contact2 = make([]string, 0)
		if list[i].Contact != "" {
			split := strings.Split(list[i].Contact, ",")
			list[i].Contact2 = split
		}
	}

	if m1, e1 := authority2Service.QueryUsersRuleListInner(uid); e1 == nil {
		for i := range list {
			list[i].Authorities2 = m1[list[i].ID]
		}
	}
	//获取规则列表
	if rule, e1 := insReportService.GetSqlRuleListByUser(uid); e1 == nil {
		for i := range list {
			for _, v := range rule[list[i].ID] {
				list[i].ReportRule = append(list[i].ReportRule, systemRes.ReportRule{
					Id:       v.Id,
					RuleName: v.RuleName,
				})
			}

		}
	}
	if req.Export() {
		_, err = insImportService.ExcelCommonList(c, insbuy.ETUserList.ToInt(), list)
		if err != nil {
			response.FailWithMessage("导出失败", c)
			return
		}
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// SetUserAuthority
// @Tags      SysUser
// @Summary   更改用户权限
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserAuth          true  "用户UUID, 角色ID"
// @Success   200   {object}  response.Response{msg=string}  "设置用户权限"
// @Router    /user/setUserAuthority [post]
func (b *BaseApi) SetUserAuthority(c *gin.Context) {
	var sua systemReq.SetUserAuth
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if UserVerifyErr := utils.Verify(sua, utils.SetUserAuthorityVerify); UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	userID := utils.GetUserID(c)
	err = userService.SetUserAuthority(userID, sua.AuthorityId)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	claims := utils.GetUserInfo(c)
	j := &utils.JWT{SigningKey: []byte(global.GVA_CONFIG.JWT.SigningKey)} // 唯一签名
	claims.AuthorityId = sua.AuthorityId
	if token, err := j.CreateToken(*claims); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		c.Header("new-token", token)
		c.Header("new-expires-at", strconv.FormatInt(claims.ExpiresAt.Unix(), 10))
		response.OkWithMessage("修改成功", c)
	}
}

// SetUserAuthorities
// @Tags      SysUser
// @Summary   设置用户权限
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserAuthorities   true  "用户UUID, 角色ID"
// @Success   200   {object}  response.Response{msg=string}  "设置用户权限"
// @Router    /user/setUserAuthorities [post]
func (b *BaseApi) SetUserAuthorities(c *gin.Context) {
	var sua systemReq.SetUserAuthorities
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = userService.SetUserAuthorities(sua.ID, sua.AuthorityIds)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
		return
	}
	response.OkWithMessage("修改成功", c)
}

// DeleteUser
// @Tags      SysUser
// @Summary   删除用户
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.GetById                true  "用户ID"
// @Success   200   {object}  response.Response{msg=string}  "删除用户"
// @Router    /user/deleteUser [delete]
func (b *BaseApi) DeleteUser(c *gin.Context) {
	var reqId request.GetById
	err := c.ShouldBindJSON(&reqId)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(reqId, utils.IdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	jwtId := utils.GetUserID(c)
	if jwtId == uint(reqId.ID) {
		response.FailWithMessage("删除失败, 自杀失败", c)
		return
	}
	if authorityService.CheckAuthority(utils.GetUserID(c), uint(reqId.ID), nil) {
		response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
		return
	}
	if e1 := insUserService.CheckAuthorityByShopId(utils.GetUserID(c), uint(reqId.ID)); !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) && e1 != nil {
		response.FailWithMessage(e1.Error(), c)
		return
	}
	err = userService.DeleteUser(reqId.ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// SetUserInfo
// @Tags      SysUser
// @Summary   设置用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      system.SysUser                                             true  "ID, 用户名, 昵称, 头像链接"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "设置用户信息"
// @Router    /user/setUserInfo [put]
func (b *BaseApi) SetUserInfo(c *gin.Context) {
	var user systemReq.ChangeUserInfo
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(user, utils.IdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if authorityService.CheckAuthority(utils.GetUserID(c), user.ID, authorityService.GetRoles(user.AuthorityIds)) {
		response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
		return
	}
	if len(user.AuthorityIds) != 0 {
		err = userService.SetUserAuthorities(user.ID, user.AuthorityIds)
		if err != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(err))
			response.FailWithMessage("设置失败", c)
			return
		}
	}
	if len(user.Authority2Ids) != 0 {
		_, _, err = authority2Service.ResetUserAuthority(user.ID, user.Authority2Ids)
		if err != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(err))
			response.FailWithMessage("设置失败", c)
			return
		}
	}
	if user.BlocSale == 2 && len(user.StoreIds) > 1 {
		response.FailWithMessage("单店销售不可分配多个店铺", c)
		return
	}
	if !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) {
		uints := make([]uint, 0, len(user.StoreIds))
		for _, v := range user.StoreIds {
			uints = append(uints, v.StoreId)
		}
		storeIds, e1 := insUserService.GetCurrentUserStoreIds(user.ID)
		if e1 != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(e1))
			err = e1
			return
		}
		added, removed := utils.Diff(storeIds, uints)
		diffIds := make([]uint, 0, len(uints))
		diffIds = append(diffIds, added...)
		diffIds = append(diffIds, removed...)
		diff, e1 := insUserService.GetStoreIdPermissionsDiff(utils.GetUserID(c), diffIds)
		if e1 != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(e1))
			err = e1
			return
		}
		if len(diff) > 0 {
			storeInfos, _ := insStoreService.GetInsStoreByIds(diff)
			i := make([]string, len(storeInfos))
			for idx, v := range storeInfos {
				i[idx] = v.Name
			}
			message := strings.Join(i, ",")
			response.FailWithMessage("设置失败-无店铺权限:"+message, c)
			return
		}
	}
	//分店
	err = insSalerStoreService.DeleteInsSalerStoreByUserId(user.ID)
	if err != nil {
		response.FailWithMessage("设置失败", c)
		return
	}
	err = organizationService.DeleteOrgUsers([]uint{user.ID}, user.OldOrgIds)
	if err != nil {
		response.FailWithMessage("设置失败", c)
		return
	}
	if len(user.StoreIds) > 0 {
		for _, v := range user.StoreIds {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v.OrganizationID,
				SysUserIDS:     []uint{user.ID},
			})
			if err != nil {
				response.FailWithMessage("设置失败", c)
				return
			}
			err = insSalerStoreService.CreateInsSalerStore(insbuyReq.CreateInsSalerStoreReq{
				UserId:  user.ID,
				StoreId: &v.StoreId,
			})
			if err != nil {
				response.FailWithMessage("设置失败", c)
				return
			}
		}
	}
	//部门
	if len(user.Organization) > 0 {
		for _, v := range user.Organization {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v,
				SysUserIDS:     []uint{user.ID},
				IsVisual:       true,
			})
			if err != nil {
				response.FailWithMessage("设置失败", c)
				return
			}
		}
	}
	//销售组
	if len(user.SalerOrg) > 0 {
		for _, v := range user.SalerOrg {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: v,
				SysUserIDS:     []uint{user.ID},
				IsVisual:       true,
			})
			if err != nil {
				response.FailWithMessage("设置失败", c)
				return
			}
		}
	}
	if len(user.ReportRule) > 0 {
		err = insReportService.SetSqlRuleByUser(user.ID, user.ReportRule)
		if err != nil {
			response.FailWithMessage("设置报表规则失败", c)
			return
		}
	}
	err = userService.SetUserInfo(system.SysUser{
		GVA_MODEL: global.GVA_MODEL{
			ID: user.ID,
		},
		NickName:  user.NickName,
		HeaderImg: user.HeaderImg,
		Phone:     user.Phone,
		Email:     user.Email,
		SideMode:  user.SideMode,
		Enable:    user.Enable,
		BlocSale:  user.BlocSale,
		Contact:   user.ContactStr(),
	})
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}
	response.OkWithMessage("设置成功", c)
}

// SetSelfInfo
// @Tags      SysUser
// @Summary   设置用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      system.SysUser                                             true  "ID, 用户名, 昵称, 头像链接"
// @Success   200   {object}  response.Response{data=map[string]interface{},msg=string}  "设置用户信息"
// @Router    /user/SetSelfInfo [put]
func (b *BaseApi) SetSelfInfo(c *gin.Context) {
	var user systemReq.ChangeUserInfo
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	user.ID = utils.GetUserID(c)
	err = userService.SetSelfInfo(system.SysUser{
		GVA_MODEL: global.GVA_MODEL{
			ID: user.ID,
		},
		NickName:  user.NickName,
		HeaderImg: user.HeaderImg,
		Phone:     user.Phone,
		Email:     user.Email,
		SideMode:  user.SideMode,
		Enable:    user.Enable,
	})
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}
	response.OkWithMessage("设置成功", c)
}

type TUserInfoVo struct {
	system.SysUser
	Authority2 []systemRes.Authority2RuleItem `json:"authority2"`
}

// GetUserInfo
// @Tags      SysUser
// @Summary   获取用户信息
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Success   200  {object}  response.Response{data=map[string]interface{},msg=string}  "获取用户信息"
// @Router    /user/getUserInfo [get]
func (b *BaseApi) GetUserInfo(c *gin.Context) {
	uid := utils.GetUserID(c)
	ReqUser, err := userService.GetUserInfo(uid)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	items, err := authority2Service.GetAllRuleItemByUserId(ReqUser.ID)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"userInfo": TUserInfoVo{SysUser: ReqUser, Authority2: items}}, "获取成功", c)
}

// ResetPassword
// @Tags      SysUser
// @Summary   重置用户密码
// @Security  ApiKeyAuth
// @Produce  application/json
// @Param     data  body      system.SysUser                 true  "ID"
// @Success   200   {object}  response.Response{msg=string}  "重置用户密码"
// @Router    /user/resetPassword [post]
func (b *BaseApi) ResetPassword(c *gin.Context) {
	var user system.SysUser
	err := c.ShouldBindJSON(&user)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) && authorityService.CheckAuthority(utils.GetUserID(c), user.ID, nil) {
		response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
		return
	}
	if e1 := insUserService.CheckAuthorityByShopId(utils.GetUserID(c), user.ID); !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) && e1 != nil {
		response.FailWithMessage(e1.Error(), c)
		return
	}
	err = userService.ResetPassword(user.ID)
	if err != nil {
		global.GVA_LOG.Error("重置失败!", zap.Error(err))
		response.FailWithMessage("重置失败"+err.Error(), c)
		return
	}
	response.OkWithMessage("重置成功", c)
}

// GetSalesList
// @Tags      SysUser
// @Summary   分页获取用户列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      request.PageInfo                                        true  "页码, 每页大小"
// @Success   200   {object}  response.Response{data=response.PageResult,msg=string}  "分页获取用户列表,返回包括列表,总数,页码,每页数量"
// @Router    /user/getSalesList [post]
func (b *BaseApi) GetSalesList(c *gin.Context) {
	var req systemReq.GetUserListReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := userService.GetSalesList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// SetUserDefaultAuthority
// @Tags      SysUser
// @Summary   更改用户默认权限
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserDefaultAuth          true  "用户id, 角色ID"
// @Success   200   {object}  response.Response{msg=string}  "设置用户默认权限"
// @Router    /user/setUserDefaultAuthority [post]
func (b *BaseApi) SetUserDefaultAuthority(c *gin.Context) {
	var sua systemReq.SetUserDefaultAuth
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if UserVerifyErr := utils.Verify(sua, utils.SetUserAuthorityVerify); UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	err = userService.SetUserAuthority(sua.UserId, sua.AuthorityId)
	if err != nil {
		global.GVA_LOG.Error("修改默认角色失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("修改默认角色成功，提醒用户重新登录", c)
}

// SetUserAuthorityStatus
// 批量启用禁用账号
// @Tags      SysUser
// @Summary   批量启用禁用账号
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.SetUserAuthorityStatusReq                                  true  "批量启用禁用账号"
// @Success   200   {object}  response.Response{msg=string}  "批量启用禁用账号"
// @Router    /user/setUserAuthorityStatus [post]
func (b *BaseApi) SetUserAuthorityStatus(c *gin.Context) {
	var sua systemReq.SetUserAuthorityStatusReq
	err := c.ShouldBindJSON(&sua)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if UserVerifyErr := utils.Verify(sua, utils.SetUserStatusVerify); UserVerifyErr != nil {
		response.FailWithMessage(UserVerifyErr.Error(), c)
		return
	}
	for _, v := range sua.UserIds {
		if authorityService.CheckAuthority(utils.GetUserID(c), v, nil) {
			response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
			return
		}
	}
	if !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) {
		for _, v := range sua.UserIds {
			e1 := insUserService.CheckAuthorityByShopId(utils.GetUserID(c), v)
			if e1 != nil {
				err = e1
				response.FailWithMessage(err.Error(), c)
				return
			}
		}
	}
	err = userService.SetUserAuthorityStatus(sua)
	if err != nil {
		global.GVA_LOG.Error("修改用户状态失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("修改用户状态成功", c)
}

// BatchJoinStore
// 批量加入店铺
// @Tags      SysUser
// @Summary   批量加入店铺
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.BatchAddStoreReq                                  true  "批量加入店铺"
// @Success   200   {object}  response.Response{msg=string}  "批量加入店铺"
// @Router    /user/batchJoinStore [post]
func (b *BaseApi) BatchJoinStore(c *gin.Context) {
	var req systemReq.BatchAddStoreReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) {
		uints := []uint{req.StoreId}
		diff, e1 := insUserService.GetStoreIdPermissionsDiff(utils.GetUserID(c), uints)
		if e1 != nil {
			global.GVA_LOG.Error("设置失败!", zap.Error(e1))
			err = e1
			return
		}
		if len(diff) > 0 {
			storeInfos, _ := insStoreService.GetInsStoreByIds(diff)
			i := make([]string, len(storeInfos))
			for idx, v := range storeInfos {
				i[idx] = v.Name
			}
			message := strings.Join(i, ",")
			response.FailWithMessage("设置失败-无店铺权限:"+message, c)
			return
		}
	}
	if len(req.UserIds) > 0 {
		//查询有销售角色
		salesList, e1 := insSalerStoreService.GetSalesList(insbuyReq.GetSalesListReq{
			Uids: req.UserIds,
		})
		if e1 != nil {
			err = e1
			return
		}
		salesListMap := make(map[uint]insbuyResp.GetSalesListResp, 0)
		for _, v := range salesList {
			salesListMap[v.SalesId] = v
		}
		org, e1 := organizationService.GetOrganizationByStoreId(req.StoreId)
		if e1 != nil {
			return
		}
		//查询店铺对应的
		storeSalerMap, e1 := insSalerStoreService.GetInsSalerStoreMapByStoreId(req.StoreId)
		if e1 != nil {
			return
		}
		for _, v := range req.UserIds {
			err = organizationService.CreateOrgUser(organization.OrgUserReq{
				OrganizationID: org.ID,
				SysUserIDS:     []uint{v},
			})
			if err != nil {
				return
			}
			if _, ok := salesListMap[v]; ok {
				if _, ok2 := storeSalerMap[v]; !ok2 {
					err = insSalerStoreService.CreateInsSalerStore(insbuyReq.CreateInsSalerStoreReq{
						UserId:  v,
						StoreId: &req.StoreId,
					})
					if err != nil {
						return
					}
				}
			}
		}
	}
	response.OkWithMessage("批量加入店铺成功", c)
}
