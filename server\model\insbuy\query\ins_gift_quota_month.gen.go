// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftQuotaMonth(db *gorm.DB, opts ...gen.DOOption) insGiftQuotaMonth {
	_insGiftQuotaMonth := insGiftQuotaMonth{}

	_insGiftQuotaMonth.insGiftQuotaMonthDo.UseDB(db, opts...)
	_insGiftQuotaMonth.insGiftQuotaMonthDo.UseModel(&insbuy.InsGiftQuotaMonth{})

	tableName := _insGiftQuotaMonth.insGiftQuotaMonthDo.TableName()
	_insGiftQuotaMonth.ALL = field.NewAsterisk(tableName)
	_insGiftQuotaMonth.ID = field.NewUint(tableName, "id")
	_insGiftQuotaMonth.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftQuotaMonth.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftQuotaMonth.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftQuotaMonth.UserId = field.NewUint(tableName, "user_id")
	_insGiftQuotaMonth.DateType = field.NewInt(tableName, "date_type")
	_insGiftQuotaMonth.Quota = field.NewFloat64(tableName, "quota")
	_insGiftQuotaMonth.LastQuota = field.NewFloat64(tableName, "last_quota")
	_insGiftQuotaMonth.LastSurplusQuota = field.NewFloat64(tableName, "last_surplus_quota")
	_insGiftQuotaMonth.HalfMonthQuota = field.NewFloat64(tableName, "half_month_quota")
	_insGiftQuotaMonth.StoreId = field.NewUint(tableName, "store_id")
	_insGiftQuotaMonth.BusinessDay = field.NewTime(tableName, "business_day")

	_insGiftQuotaMonth.fillFieldMap()

	return _insGiftQuotaMonth
}

type insGiftQuotaMonth struct {
	insGiftQuotaMonthDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	UserId           field.Uint
	DateType         field.Int
	Quota            field.Float64
	LastQuota        field.Float64
	LastSurplusQuota field.Float64
	HalfMonthQuota   field.Float64
	StoreId          field.Uint
	BusinessDay      field.Time

	fieldMap map[string]field.Expr
}

func (i insGiftQuotaMonth) Table(newTableName string) *insGiftQuotaMonth {
	i.insGiftQuotaMonthDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftQuotaMonth) As(alias string) *insGiftQuotaMonth {
	i.insGiftQuotaMonthDo.DO = *(i.insGiftQuotaMonthDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftQuotaMonth) updateTableName(table string) *insGiftQuotaMonth {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserId = field.NewUint(table, "user_id")
	i.DateType = field.NewInt(table, "date_type")
	i.Quota = field.NewFloat64(table, "quota")
	i.LastQuota = field.NewFloat64(table, "last_quota")
	i.LastSurplusQuota = field.NewFloat64(table, "last_surplus_quota")
	i.HalfMonthQuota = field.NewFloat64(table, "half_month_quota")
	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insGiftQuotaMonth) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftQuotaMonth) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["date_type"] = i.DateType
	i.fieldMap["quota"] = i.Quota
	i.fieldMap["last_quota"] = i.LastQuota
	i.fieldMap["last_surplus_quota"] = i.LastSurplusQuota
	i.fieldMap["half_month_quota"] = i.HalfMonthQuota
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insGiftQuotaMonth) clone(db *gorm.DB) insGiftQuotaMonth {
	i.insGiftQuotaMonthDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftQuotaMonth) replaceDB(db *gorm.DB) insGiftQuotaMonth {
	i.insGiftQuotaMonthDo.ReplaceDB(db)
	return i
}

type insGiftQuotaMonthDo struct{ gen.DO }

func (i insGiftQuotaMonthDo) Debug() *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftQuotaMonthDo) WithContext(ctx context.Context) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftQuotaMonthDo) ReadDB() *insGiftQuotaMonthDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftQuotaMonthDo) WriteDB() *insGiftQuotaMonthDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftQuotaMonthDo) Session(config *gorm.Session) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftQuotaMonthDo) Clauses(conds ...clause.Expression) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftQuotaMonthDo) Returning(value interface{}, columns ...string) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftQuotaMonthDo) Not(conds ...gen.Condition) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftQuotaMonthDo) Or(conds ...gen.Condition) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftQuotaMonthDo) Select(conds ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftQuotaMonthDo) Where(conds ...gen.Condition) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftQuotaMonthDo) Order(conds ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftQuotaMonthDo) Distinct(cols ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftQuotaMonthDo) Omit(cols ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftQuotaMonthDo) Join(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftQuotaMonthDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftQuotaMonthDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftQuotaMonthDo) Group(cols ...field.Expr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftQuotaMonthDo) Having(conds ...gen.Condition) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftQuotaMonthDo) Limit(limit int) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftQuotaMonthDo) Offset(offset int) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftQuotaMonthDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftQuotaMonthDo) Unscoped() *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftQuotaMonthDo) Create(values ...*insbuy.InsGiftQuotaMonth) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftQuotaMonthDo) CreateInBatches(values []*insbuy.InsGiftQuotaMonth, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftQuotaMonthDo) Save(values ...*insbuy.InsGiftQuotaMonth) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftQuotaMonthDo) First() (*insbuy.InsGiftQuotaMonth, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonth), nil
	}
}

func (i insGiftQuotaMonthDo) Take() (*insbuy.InsGiftQuotaMonth, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonth), nil
	}
}

func (i insGiftQuotaMonthDo) Last() (*insbuy.InsGiftQuotaMonth, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonth), nil
	}
}

func (i insGiftQuotaMonthDo) Find() ([]*insbuy.InsGiftQuotaMonth, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftQuotaMonth), err
}

func (i insGiftQuotaMonthDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftQuotaMonth, err error) {
	buf := make([]*insbuy.InsGiftQuotaMonth, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftQuotaMonthDo) FindInBatches(result *[]*insbuy.InsGiftQuotaMonth, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftQuotaMonthDo) Attrs(attrs ...field.AssignExpr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftQuotaMonthDo) Assign(attrs ...field.AssignExpr) *insGiftQuotaMonthDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftQuotaMonthDo) Joins(fields ...field.RelationField) *insGiftQuotaMonthDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftQuotaMonthDo) Preload(fields ...field.RelationField) *insGiftQuotaMonthDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftQuotaMonthDo) FirstOrInit() (*insbuy.InsGiftQuotaMonth, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonth), nil
	}
}

func (i insGiftQuotaMonthDo) FirstOrCreate() (*insbuy.InsGiftQuotaMonth, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaMonth), nil
	}
}

func (i insGiftQuotaMonthDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftQuotaMonth, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftQuotaMonthDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftQuotaMonthDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftQuotaMonthDo) Delete(models ...*insbuy.InsGiftQuotaMonth) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftQuotaMonthDo) withDO(do gen.Dao) *insGiftQuotaMonthDo {
	i.DO = *do.(*gen.DO)
	return i
}
