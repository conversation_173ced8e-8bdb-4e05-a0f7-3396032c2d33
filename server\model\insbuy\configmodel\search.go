package configmodel

type RegionSearch struct {
	ProductName string `json:"productName" form:"productName"`
	RegionName  string `json:"regionName" form:"regionName"`
	RegionId    uint   `json:"regionId" form:"regionId"`
}

type DateSearch struct {
	StartDay string `json:"startDay" form:"startDay"`
	EndDay   string `json:"endDay" form:"endDay"`
}

// 场地费配置搜索参数
type SiteCostSearch struct {
	SaleName string `json:"saleName" form:"saleName"` // 销售员姓名搜索
	SaleOrg  string `json:"saleOrg" form:"saleOrg"`   // 销售组搜索
}

type GroupCostSearch struct {
	GroupName string `json:"groupName" form:"groupName"` //分类名称
	ParentId  uint   `json:"parentId" form:"parentId"`
}
