// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftRuleProduct(db *gorm.DB, opts ...gen.DOOption) insGiftRuleProduct {
	_insGiftRuleProduct := insGiftRuleProduct{}

	_insGiftRuleProduct.insGiftRuleProductDo.UseDB(db, opts...)
	_insGiftRuleProduct.insGiftRuleProductDo.UseModel(&insbuy.InsGiftRuleProduct{})

	tableName := _insGiftRuleProduct.insGiftRuleProductDo.TableName()
	_insGiftRuleProduct.ALL = field.NewAsterisk(tableName)
	_insGiftRuleProduct.ID = field.NewUint(tableName, "id")
	_insGiftRuleProduct.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftRuleProduct.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftRuleProduct.RuleId = field.NewInt(tableName, "rule_id")
	_insGiftRuleProduct.IsExclude = field.NewInt(tableName, "is_exclude")
	_insGiftRuleProduct.ProductId = field.NewInt(tableName, "product_id")
	_insGiftRuleProduct.PriceType = field.NewInt(tableName, "price_type")
	_insGiftRuleProduct.Value = field.NewFloat64(tableName, "value")

	_insGiftRuleProduct.fillFieldMap()

	return _insGiftRuleProduct
}

type insGiftRuleProduct struct {
	insGiftRuleProductDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	RuleId    field.Int
	IsExclude field.Int
	ProductId field.Int
	PriceType field.Int
	Value     field.Float64

	fieldMap map[string]field.Expr
}

func (i insGiftRuleProduct) Table(newTableName string) *insGiftRuleProduct {
	i.insGiftRuleProductDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftRuleProduct) As(alias string) *insGiftRuleProduct {
	i.insGiftRuleProductDo.DO = *(i.insGiftRuleProductDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftRuleProduct) updateTableName(table string) *insGiftRuleProduct {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.RuleId = field.NewInt(table, "rule_id")
	i.IsExclude = field.NewInt(table, "is_exclude")
	i.ProductId = field.NewInt(table, "product_id")
	i.PriceType = field.NewInt(table, "price_type")
	i.Value = field.NewFloat64(table, "value")

	i.fillFieldMap()

	return i
}

func (i *insGiftRuleProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftRuleProduct) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["is_exclude"] = i.IsExclude
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["price_type"] = i.PriceType
	i.fieldMap["value"] = i.Value
}

func (i insGiftRuleProduct) clone(db *gorm.DB) insGiftRuleProduct {
	i.insGiftRuleProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftRuleProduct) replaceDB(db *gorm.DB) insGiftRuleProduct {
	i.insGiftRuleProductDo.ReplaceDB(db)
	return i
}

type insGiftRuleProductDo struct{ gen.DO }

func (i insGiftRuleProductDo) Debug() *insGiftRuleProductDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftRuleProductDo) WithContext(ctx context.Context) *insGiftRuleProductDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftRuleProductDo) ReadDB() *insGiftRuleProductDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftRuleProductDo) WriteDB() *insGiftRuleProductDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftRuleProductDo) Session(config *gorm.Session) *insGiftRuleProductDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftRuleProductDo) Clauses(conds ...clause.Expression) *insGiftRuleProductDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftRuleProductDo) Returning(value interface{}, columns ...string) *insGiftRuleProductDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftRuleProductDo) Not(conds ...gen.Condition) *insGiftRuleProductDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftRuleProductDo) Or(conds ...gen.Condition) *insGiftRuleProductDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftRuleProductDo) Select(conds ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftRuleProductDo) Where(conds ...gen.Condition) *insGiftRuleProductDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftRuleProductDo) Order(conds ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftRuleProductDo) Distinct(cols ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftRuleProductDo) Omit(cols ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftRuleProductDo) Join(table schema.Tabler, on ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftRuleProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftRuleProductDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftRuleProductDo) Group(cols ...field.Expr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftRuleProductDo) Having(conds ...gen.Condition) *insGiftRuleProductDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftRuleProductDo) Limit(limit int) *insGiftRuleProductDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftRuleProductDo) Offset(offset int) *insGiftRuleProductDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftRuleProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftRuleProductDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftRuleProductDo) Unscoped() *insGiftRuleProductDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftRuleProductDo) Create(values ...*insbuy.InsGiftRuleProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftRuleProductDo) CreateInBatches(values []*insbuy.InsGiftRuleProduct, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftRuleProductDo) Save(values ...*insbuy.InsGiftRuleProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftRuleProductDo) First() (*insbuy.InsGiftRuleProduct, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleProduct), nil
	}
}

func (i insGiftRuleProductDo) Take() (*insbuy.InsGiftRuleProduct, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleProduct), nil
	}
}

func (i insGiftRuleProductDo) Last() (*insbuy.InsGiftRuleProduct, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleProduct), nil
	}
}

func (i insGiftRuleProductDo) Find() ([]*insbuy.InsGiftRuleProduct, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftRuleProduct), err
}

func (i insGiftRuleProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftRuleProduct, err error) {
	buf := make([]*insbuy.InsGiftRuleProduct, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftRuleProductDo) FindInBatches(result *[]*insbuy.InsGiftRuleProduct, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftRuleProductDo) Attrs(attrs ...field.AssignExpr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftRuleProductDo) Assign(attrs ...field.AssignExpr) *insGiftRuleProductDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftRuleProductDo) Joins(fields ...field.RelationField) *insGiftRuleProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftRuleProductDo) Preload(fields ...field.RelationField) *insGiftRuleProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftRuleProductDo) FirstOrInit() (*insbuy.InsGiftRuleProduct, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleProduct), nil
	}
}

func (i insGiftRuleProductDo) FirstOrCreate() (*insbuy.InsGiftRuleProduct, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleProduct), nil
	}
}

func (i insGiftRuleProductDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftRuleProduct, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftRuleProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftRuleProductDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftRuleProductDo) Delete(models ...*insbuy.InsGiftRuleProduct) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftRuleProductDo) withDO(do gen.Dao) *insGiftRuleProductDo {
	i.DO = *do.(*gen.DO)
	return i
}
