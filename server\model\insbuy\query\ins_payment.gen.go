// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsPayment(db *gorm.DB, opts ...gen.DOOption) insPayment {
	_insPayment := insPayment{}

	_insPayment.insPaymentDo.UseDB(db, opts...)
	_insPayment.insPaymentDo.UseModel(&insbuy.InsPayment{})

	tableName := _insPayment.insPaymentDo.TableName()
	_insPayment.ALL = field.NewAsterisk(tableName)
	_insPayment.PayId = field.NewUint(tableName, "pay_id")
	_insPayment.PayCode = field.NewString(tableName, "pay_code")
	_insPayment.PayName = field.NewString(tableName, "pay_name")
	_insPayment.PayFullName = field.NewString(tableName, "pay_full_name")
	_insPayment.PayFee = field.NewString(tableName, "pay_fee")
	_insPayment.PayDesc = field.NewString(tableName, "pay_desc")
	_insPayment.PayOrder = field.NewUint(tableName, "pay_order")
	_insPayment.Enabled = field.NewUint(tableName, "enabled")
	_insPayment.IsCod = field.NewUint(tableName, "is_cod")
	_insPayment.IsOnline = field.NewUint(tableName, "is_online")
	_insPayment.Type = field.NewUint(tableName, "type")
	_insPayment.Icon = field.NewString(tableName, "icon")
	_insPayment.Priority = field.NewInt(tableName, "priority")
	_insPayment.IsThird = field.NewInt(tableName, "is_third")
	_insPayment.IsReverse = field.NewInt(tableName, "is_reverse")
	_insPayment.IsMix = field.NewInt(tableName, "is_mix")
	_insPayment.CreditingType = field.NewInt(tableName, "crediting_type")

	_insPayment.fillFieldMap()

	return _insPayment
}

type insPayment struct {
	insPaymentDo

	ALL           field.Asterisk
	PayId         field.Uint
	PayCode       field.String
	PayName       field.String
	PayFullName   field.String
	PayFee        field.String
	PayDesc       field.String
	PayOrder      field.Uint
	Enabled       field.Uint
	IsCod         field.Uint
	IsOnline      field.Uint
	Type          field.Uint
	Icon          field.String
	Priority      field.Int
	IsThird       field.Int
	IsReverse     field.Int
	IsMix         field.Int
	CreditingType field.Int

	fieldMap map[string]field.Expr
}

func (i insPayment) Table(newTableName string) *insPayment {
	i.insPaymentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insPayment) As(alias string) *insPayment {
	i.insPaymentDo.DO = *(i.insPaymentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insPayment) updateTableName(table string) *insPayment {
	i.ALL = field.NewAsterisk(table)
	i.PayId = field.NewUint(table, "pay_id")
	i.PayCode = field.NewString(table, "pay_code")
	i.PayName = field.NewString(table, "pay_name")
	i.PayFullName = field.NewString(table, "pay_full_name")
	i.PayFee = field.NewString(table, "pay_fee")
	i.PayDesc = field.NewString(table, "pay_desc")
	i.PayOrder = field.NewUint(table, "pay_order")
	i.Enabled = field.NewUint(table, "enabled")
	i.IsCod = field.NewUint(table, "is_cod")
	i.IsOnline = field.NewUint(table, "is_online")
	i.Type = field.NewUint(table, "type")
	i.Icon = field.NewString(table, "icon")
	i.Priority = field.NewInt(table, "priority")
	i.IsThird = field.NewInt(table, "is_third")
	i.IsReverse = field.NewInt(table, "is_reverse")
	i.IsMix = field.NewInt(table, "is_mix")
	i.CreditingType = field.NewInt(table, "crediting_type")

	i.fillFieldMap()

	return i
}

func (i *insPayment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insPayment) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["pay_id"] = i.PayId
	i.fieldMap["pay_code"] = i.PayCode
	i.fieldMap["pay_name"] = i.PayName
	i.fieldMap["pay_full_name"] = i.PayFullName
	i.fieldMap["pay_fee"] = i.PayFee
	i.fieldMap["pay_desc"] = i.PayDesc
	i.fieldMap["pay_order"] = i.PayOrder
	i.fieldMap["enabled"] = i.Enabled
	i.fieldMap["is_cod"] = i.IsCod
	i.fieldMap["is_online"] = i.IsOnline
	i.fieldMap["type"] = i.Type
	i.fieldMap["icon"] = i.Icon
	i.fieldMap["priority"] = i.Priority
	i.fieldMap["is_third"] = i.IsThird
	i.fieldMap["is_reverse"] = i.IsReverse
	i.fieldMap["is_mix"] = i.IsMix
	i.fieldMap["crediting_type"] = i.CreditingType
}

func (i insPayment) clone(db *gorm.DB) insPayment {
	i.insPaymentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insPayment) replaceDB(db *gorm.DB) insPayment {
	i.insPaymentDo.ReplaceDB(db)
	return i
}

type insPaymentDo struct{ gen.DO }

func (i insPaymentDo) Debug() *insPaymentDo {
	return i.withDO(i.DO.Debug())
}

func (i insPaymentDo) WithContext(ctx context.Context) *insPaymentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insPaymentDo) ReadDB() *insPaymentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insPaymentDo) WriteDB() *insPaymentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insPaymentDo) Session(config *gorm.Session) *insPaymentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insPaymentDo) Clauses(conds ...clause.Expression) *insPaymentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insPaymentDo) Returning(value interface{}, columns ...string) *insPaymentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insPaymentDo) Not(conds ...gen.Condition) *insPaymentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insPaymentDo) Or(conds ...gen.Condition) *insPaymentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insPaymentDo) Select(conds ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insPaymentDo) Where(conds ...gen.Condition) *insPaymentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insPaymentDo) Order(conds ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insPaymentDo) Distinct(cols ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insPaymentDo) Omit(cols ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insPaymentDo) Join(table schema.Tabler, on ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insPaymentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insPaymentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insPaymentDo) Group(cols ...field.Expr) *insPaymentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insPaymentDo) Having(conds ...gen.Condition) *insPaymentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insPaymentDo) Limit(limit int) *insPaymentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insPaymentDo) Offset(offset int) *insPaymentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insPaymentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insPaymentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insPaymentDo) Unscoped() *insPaymentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insPaymentDo) Create(values ...*insbuy.InsPayment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insPaymentDo) CreateInBatches(values []*insbuy.InsPayment, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insPaymentDo) Save(values ...*insbuy.InsPayment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insPaymentDo) First() (*insbuy.InsPayment, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayment), nil
	}
}

func (i insPaymentDo) Take() (*insbuy.InsPayment, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayment), nil
	}
}

func (i insPaymentDo) Last() (*insbuy.InsPayment, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayment), nil
	}
}

func (i insPaymentDo) Find() ([]*insbuy.InsPayment, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsPayment), err
}

func (i insPaymentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsPayment, err error) {
	buf := make([]*insbuy.InsPayment, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insPaymentDo) FindInBatches(result *[]*insbuy.InsPayment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insPaymentDo) Attrs(attrs ...field.AssignExpr) *insPaymentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insPaymentDo) Assign(attrs ...field.AssignExpr) *insPaymentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insPaymentDo) Joins(fields ...field.RelationField) *insPaymentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insPaymentDo) Preload(fields ...field.RelationField) *insPaymentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insPaymentDo) FirstOrInit() (*insbuy.InsPayment, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayment), nil
	}
}

func (i insPaymentDo) FirstOrCreate() (*insbuy.InsPayment, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayment), nil
	}
}

func (i insPaymentDo) FindByPage(offset int, limit int) (result []*insbuy.InsPayment, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insPaymentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insPaymentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insPaymentDo) Delete(models ...*insbuy.InsPayment) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insPaymentDo) withDO(do gen.Dao) *insPaymentDo {
	i.DO = *do.(*gen.DO)
	return i
}
