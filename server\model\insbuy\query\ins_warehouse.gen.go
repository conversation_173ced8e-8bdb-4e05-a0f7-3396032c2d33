// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouse(db *gorm.DB, opts ...gen.DOOption) insWarehouse {
	_insWarehouse := insWarehouse{}

	_insWarehouse.insWarehouseDo.UseDB(db, opts...)
	_insWarehouse.insWarehouseDo.UseModel(&insbuy.InsWarehouse{})

	tableName := _insWarehouse.insWarehouseDo.TableName()
	_insWarehouse.ALL = field.NewAsterisk(tableName)
	_insWarehouse.ID = field.NewUint(tableName, "id")
	_insWarehouse.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouse.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouse.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouse.StoreId = field.NewUint(tableName, "store_id")
	_insWarehouse.Code = field.NewString(tableName, "code")
	_insWarehouse.Name = field.NewString(tableName, "name")
	_insWarehouse.Type = field.NewInt(tableName, "type")
	_insWarehouse.Contacts = field.NewString(tableName, "contacts")
	_insWarehouse.Phone = field.NewString(tableName, "phone")
	_insWarehouse.Address = field.NewString(tableName, "address")
	_insWarehouse.Capacity = field.NewInt(tableName, "capacity")
	_insWarehouse.Remark = field.NewString(tableName, "remark")
	_insWarehouse.IsDefault = field.NewInt(tableName, "is_default")
	_insWarehouse.Attribute = field.NewInt(tableName, "attribute")

	_insWarehouse.fillFieldMap()

	return _insWarehouse
}

type insWarehouse struct {
	insWarehouseDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	StoreId   field.Uint
	Code      field.String
	Name      field.String
	Type      field.Int
	Contacts  field.String
	Phone     field.String
	Address   field.String
	Capacity  field.Int
	Remark    field.String
	IsDefault field.Int
	Attribute field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouse) Table(newTableName string) *insWarehouse {
	i.insWarehouseDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouse) As(alias string) *insWarehouse {
	i.insWarehouseDo.DO = *(i.insWarehouseDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouse) updateTableName(table string) *insWarehouse {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Code = field.NewString(table, "code")
	i.Name = field.NewString(table, "name")
	i.Type = field.NewInt(table, "type")
	i.Contacts = field.NewString(table, "contacts")
	i.Phone = field.NewString(table, "phone")
	i.Address = field.NewString(table, "address")
	i.Capacity = field.NewInt(table, "capacity")
	i.Remark = field.NewString(table, "remark")
	i.IsDefault = field.NewInt(table, "is_default")
	i.Attribute = field.NewInt(table, "attribute")

	i.fillFieldMap()

	return i
}

func (i *insWarehouse) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouse) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["code"] = i.Code
	i.fieldMap["name"] = i.Name
	i.fieldMap["type"] = i.Type
	i.fieldMap["contacts"] = i.Contacts
	i.fieldMap["phone"] = i.Phone
	i.fieldMap["address"] = i.Address
	i.fieldMap["capacity"] = i.Capacity
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["is_default"] = i.IsDefault
	i.fieldMap["attribute"] = i.Attribute
}

func (i insWarehouse) clone(db *gorm.DB) insWarehouse {
	i.insWarehouseDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouse) replaceDB(db *gorm.DB) insWarehouse {
	i.insWarehouseDo.ReplaceDB(db)
	return i
}

type insWarehouseDo struct{ gen.DO }

func (i insWarehouseDo) Debug() *insWarehouseDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseDo) WithContext(ctx context.Context) *insWarehouseDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseDo) ReadDB() *insWarehouseDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseDo) WriteDB() *insWarehouseDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseDo) Session(config *gorm.Session) *insWarehouseDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseDo) Clauses(conds ...clause.Expression) *insWarehouseDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseDo) Returning(value interface{}, columns ...string) *insWarehouseDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseDo) Not(conds ...gen.Condition) *insWarehouseDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseDo) Or(conds ...gen.Condition) *insWarehouseDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseDo) Select(conds ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseDo) Where(conds ...gen.Condition) *insWarehouseDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseDo) Order(conds ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseDo) Distinct(cols ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseDo) Omit(cols ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseDo) Group(cols ...field.Expr) *insWarehouseDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseDo) Having(conds ...gen.Condition) *insWarehouseDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseDo) Limit(limit int) *insWarehouseDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseDo) Offset(offset int) *insWarehouseDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseDo) Unscoped() *insWarehouseDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseDo) Create(values ...*insbuy.InsWarehouse) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseDo) CreateInBatches(values []*insbuy.InsWarehouse, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseDo) Save(values ...*insbuy.InsWarehouse) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseDo) First() (*insbuy.InsWarehouse, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouse), nil
	}
}

func (i insWarehouseDo) Take() (*insbuy.InsWarehouse, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouse), nil
	}
}

func (i insWarehouseDo) Last() (*insbuy.InsWarehouse, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouse), nil
	}
}

func (i insWarehouseDo) Find() ([]*insbuy.InsWarehouse, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouse), err
}

func (i insWarehouseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouse, err error) {
	buf := make([]*insbuy.InsWarehouse, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseDo) FindInBatches(result *[]*insbuy.InsWarehouse, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseDo) Attrs(attrs ...field.AssignExpr) *insWarehouseDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseDo) Assign(attrs ...field.AssignExpr) *insWarehouseDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseDo) Joins(fields ...field.RelationField) *insWarehouseDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseDo) Preload(fields ...field.RelationField) *insWarehouseDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseDo) FirstOrInit() (*insbuy.InsWarehouse, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouse), nil
	}
}

func (i insWarehouseDo) FirstOrCreate() (*insbuy.InsWarehouse, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouse), nil
	}
}

func (i insWarehouseDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouse, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseDo) Delete(models ...*insbuy.InsWarehouse) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseDo) withDO(do gen.Dao) *insWarehouseDo {
	i.DO = *do.(*gen.DO)
	return i
}
