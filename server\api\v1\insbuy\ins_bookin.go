package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyRes "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// InsBookInApi 预约收银相关
type InsBookInApi struct{}

var (
	insBookInOpenDeskVerify = utils.Rules{
		"peopleNum":  {utils.NotEmpty()},
		"memberId":   {utils.NotEmpty()},
		"deskId":     {utils.NotEmpty()},
		"salesmanId": {utils.NotEmpty()},
		"minAmount":  {utils.NotEmpty()},
	}
	insChangeDeskVerify = utils.Rules{
		"newDeskId":  {utils.NotEmpty()},
		"openDeskId": {utils.NotEmpty()},
	}
	insChangeDeskStatusVerify = utils.Rules{
		"deskId":       {utils.NotEmpty()},
		"deskStatusId": {utils.NotEmpty()},
	}
	insBookInCloseDeskVerify = utils.Rules{
		"deskId": {utils.NotEmpty()},
	}
	insBookInOpenDeskDetailVerify = utils.Rules{
		"deskId": {utils.NotEmpty()},
	}
)

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

// GetAreaList 指定分店，列出店内所有区域
// @Tags InsBookIn（预约、收银台）
// @Summary 指定分店，列出店内所有区域
// @Security ApiKeyAuth
// @Accept x-www-form-urlencoded,application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInAreaList true "指定分店，列出店内所有区域"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInAreaList,msg=string}  "店内所有区域"
// @Router /insBookIn/getAreaList [get]
func (a *InsBookInApi) GetAreaList(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInAreaList
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insbuyRes.InsBookInAreaList
	res, err = insBookInService.GetAreaList(req)
	response.ResultErr(res, err, c)
}

// GetDeskStatusList 指定分店，列出店内所有「桌台可用状态」
// @Tags InsBookIn（预约、收银台）
// @Summary 指定分店，列出店内所有「桌台可用状态」
// @Security ApiKeyAuth
// @Accept x-www-form-urlencoded,application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInDeskStatusList true "指定分店，列出店内所有「桌台可用状态」"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInDeskStatusList,msg=string}  "所有「桌台可用状态」"
// @Router /insBookIn/getDeskStatusList [get]
func (a *InsBookInApi) GetDeskStatusList(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInDeskStatusList
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insbuyRes.InsBookInDeskStatusList
	res, err = insBookInService.GetDeskStatusList(req)
	response.ResultErr(res, err, c)
}

// GetDeskList 指定分店、区域（可选）、桌台状态（可选），列出所有桌台，包括桌台状态等信息
// @Tags InsBookIn（预约、收银台）
// @Summary 指定分店、区域（可选）、桌台状态（可选），列出所有桌台，包括桌台状态等信息
// @Security ApiKeyAuth
// @Accept x-www-form-urlencoded,application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInDeskList true "指定分店、区域（可选）、桌台状态（可选），列出所有桌台，包括桌台状态等信息"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInDeskList,msg=string}  "列出所有桌台，包括桌台状态等信息"
// @Router /insBookIn/getDeskList [get]
func (a *InsBookInApi) GetDeskList(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInDeskList
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.GetDeskList(req)
	// 数据过滤-普通销售不能看到公共桌台的营业数据
	onlySalesman := false
	if req.SalesmanId > 0 {
		onlySalesman, _ = authorityService.IsOnlySalesman(req.SalesmanId)
	}
	for i, val := range res.List {
		if req.SalesmanId > 0 && onlySalesman && val.GiveAttr == insbuy.DGAPublic.ToInt() {
			res.List[i].Consumed = 0
			res.List[i].PaidAmount = 0
			res.List[i].WaitAmount = 0
			res.List[i].GiveAmount = 0
		}
	}
	response.ResultErr(res, err, c)
}

// OpenDeskHistoryList 分页获取开台历史
// @Tags InsBookIn
// @Summary 分页获取开台历史
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.OpenDeskHistoryListReq true "分页获取开台历史"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInDeskList,msg=string}  "列出所有桌台，包括桌台状态等信息"
// @Router /insBookIn/openDeskHistoryList [get]
func (a *InsBookInApi) OpenDeskHistoryList(c *gin.Context) {
	var req insbuyReq.OpenDeskHistoryListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insBookInService.OpenDeskHistoryList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETPastOrderList.ToInt(), list)
			if e != nil {
				response.FailWithMessage("导出失败", c)
				return
			}
			c.Abort()
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// OpenDesk 开台
// @Tags InsBookIn（预约、收银台）
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInOpenDesk true "开台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInOpenDesk,msg=string}  "开台"
// @Router /insBookIn/openDesk [post]
func (a *InsBookInApi) OpenDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInOpenDesk
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insBookInOpenDeskVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.OpenDesk(req)
	response.ResultErr(res, err, c)
}

// BookOpenDesk 预约开台(已开台的台子状态切换)
// @Tags InsBookIn（预约、收银台）
// @Summary 预约开台(已开台的台子状态切换)
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.BookOpenDeskReq true "预约开台(已开台的台子状态切换)"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInOpenDesk,msg=string}
// @Router /insBookIn/bookOpenDesk [post]
func (a *InsBookInApi) BookOpenDesk(c *gin.Context) {
	var err error
	var req insbuyReq.BookOpenDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.BookOpenDesk(req)
	response.ResultErr(resp, err, c)
}

// CloseDesk 关台
// @Tags InsBookIn（预约、收银台）
// @Summary  关台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInCloseDeskReq true "关台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInCloseDesk,msg=string}  "关台"
// @Router /insBookIn/closeDesk [post]
func (a *InsBookInApi) CloseDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInCloseDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insBookInCloseDeskVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.CloseOpenDesk(req)
	response.ResultErr(res, err, c)
}

// CloseAllDesk 关闭所有桌台
// @Tags InsBookIn（预约、收银台）
// @Summary  关闭所有桌台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInCloseAllDeskResp true "关闭所有桌台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInCloseAllDesk,msg=string}  "关台"
// @Router /insBookIn/closeAllDesk [post]
func (a *InsBookInApi) CloseAllDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInCloseAllDeskResp
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.CloseAllDesk(req)
	response.ResultErr(resp, err, c)
}

// OpenDeskDetail 开台详情
// @Tags InsBookIn（开台详情）
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInOpenDeskDetail true "开台详情"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInOpenDeskDetail,msg=string}  "开台详情"
// @Router /insBookIn/openDeskDetail [get]
func (a *InsBookInApi) OpenDeskDetail(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInOpenDeskDetail
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insBookInOpenDeskDetailVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.OpenDeskDetail(req)
	response.ResultErr(res, err, c)
}

// UpdateOpenDesk 开台修改接口
// @Tags InsBookIn（预约、收银台）
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInOpenDesk true "修改开台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInUpdateOpenDesk,msg=string}  "开台"
// @Router /insBookIn/updateOpenDesk [put]
func (a *InsBookInApi) UpdateOpenDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInOpenDesk
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insBookInOpenDeskVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.UpdateOpenDesk(c, req)
	response.ResultErr(res, err, c)
}

// AddTempDesk 添加临时桌台
// @Tags InsBookIn（预约、收银台）
// @Summary 添加临时桌台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInAddTempDesk true "添加临时桌台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInAddTempDesk,msg=string}  "添加临时桌台"
// @Router /insBookIn/addTempDesk [post]
func (a *InsBookInApi) AddTempDesk(c *gin.Context) {
	var r insbuyReq.InsBookInAddTempDesk
	err := GinMustBind(c, &r)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	verify := utils.Rules{
		"DeskName": {utils.NotEmpty()},
	}
	err = utils.Verify(r, verify)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	i := insbuyReq.CreateDesk{
		DeskName:              r.DeskName,
		DeskAreaId:            0,
		DeskCategoryId:        0,
		DefaultMinConsumption: 0,
		SortOrder:             0,
		StoreId:               0,
	}
	if err := insDeskService.CreateInsDesk(i); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.AddTemp'!", zap.Error(err))
		response.FailWithMessage("添加临时桌台失败", c)
	} else {
		response.OkWithMessage("添加临时桌台成功", c)
	}
}

// ChangeDesk 换桌
// @Tags InsBookIn 换桌
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskChangeDeskReq true "换桌"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"换桌成功"}"
// @Router /insBookIn/changeDesk [put]
func (a *InsBookInApi) ChangeDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsDeskChangeDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insChangeDeskVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.ChangeDesk(req)
	response.ResultErr(resp, err, c)
}

// MergeDesk 合并桌台
// @Tags InsBookIn 合并桌台
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskMergeDeskReq true "合并桌台"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"合并桌台成功"}"
// @Router /insBookIn/mergeDesk [put]
func (a *InsBookInApi) MergeDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsDeskMergeDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.MergeDesk(req)
	response.ResultErr(resp, err, c)
}

// ChangeDeskStatus 切换桌台状态
// @Tags InsBookIn 切换桌台状态
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskChangeDeskStatusReq true "切换桌台状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"切换桌台状态"}"
// @Router /insBookIn/changeDeskStatus [put]
func (a *InsBookInApi) ChangeDeskStatus(c *gin.Context) {
	var err error
	var req insbuyReq.InsDeskChangeDeskStatusReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = utils.Verify(req, insChangeDeskStatusVerify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.ChangeDeskStatus(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.ChangeDeskStatus'!", zap.Error(err))
		response.FailWithMessage("切换桌台状态失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("切换桌台状态成功", c)
	}
}

// DeskBillPrint 账单打印接口
// @Tags InsBookIn 账单打印接口
// @Summary 开台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInDeskPrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"账单打印接口"}"
// @Router /insBookIn/deskBillPrint [post]
func (a *InsBookInApi) DeskBillPrint(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInDeskPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.DeskBillPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.DeskBillPrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// CurrentBillPrint 当前账单打印
// @Tags InsBookIn 当前账单打印
// @Summary 当前账单打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.CurrentBillPrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"账单打印接口"}"
// @Router /insBookIn/currentBillPrint [post]
func (a *InsBookInApi) CurrentBillPrint(c *gin.Context) {
	var err error
	var req insbuyReq.CurrentBillPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.CurrentBillPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.CurrentBillPrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败", c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// PartBillPrint 部分结账打印
// @Tags InsBookIn 部分结账打印
// @Summary 部分结账打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.PartBillPrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"账单打印接口"}"
// @Router /insBookIn/partBillPrint [post]
func (a *InsBookInApi) PartBillPrint(c *gin.Context) {
	var err error
	var req insbuyReq.PartBillPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.PartBillPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.PartBillPrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败", c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// ShipmentBillPrint 出品单
// @Tags InsBookIn 出品单
// @Summary 出品单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInDeskPrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"出品单"}"
// @Router /insBookIn/shipmentBillPrint [post]
func (a *InsBookInApi) ShipmentBillPrint(c *gin.Context) {
	var err error
	var req insbuyReq.ShipmentDeskPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.ShipmentDeskPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.ShipmentBillPrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败", c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// StoreWinePrint 存酒单打印
// @Tags InsBookIn 存酒单打印
// @Summary 存酒单打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.StoreWinePrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"出品单"}"
// @Router /insBookIn/storeBillPrint [post]
func (a *InsBookInApi) StoreWinePrint(c *gin.Context) {
	var err error
	var req insbuyReq.StoreWinePrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.StoreWinePrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.StoreWinePrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败", c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// TakeWinePrint 取酒单打印
// @Tags InsBookIn 取酒单打印
// @Summary 取酒单打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.StoreWinePrintReq true "账单打印接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"出品单"}"
// @Router /insBookIn/takeWinePrint [post]
func (a *InsBookInApi) TakeWinePrint(c *gin.Context) {
	var err error
	var req insbuyReq.TakeWinePrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.TakeWinePrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.TakeWinePrint'!", zap.Error(err))
		response.FailWithMessage("账单打印失败", c)
	} else {
		response.OkWithMessage("账单打印成功", c)
	}
}

// ShiftPrint 班结表打印
// @Tags InsBookIn 班结表打印
// @Summary 班结表打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOrderReportPreviewReq true "班结表打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"班结表打印"}"
// @Router /insBookIn/shiftPrint [post]
func (a *InsBookInApi) ShiftPrint(c *gin.Context) {
	var err error
	var req insbuyReq.GetOrderReportPreviewReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.ShiftPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.ShiftPrint'!", zap.Error(err))
		response.FailWithMessage("班结表打印失败", c)
	} else {
		response.OkWithMessage("班结表打印成功", c)
	}
}

// OnDutyPrint 当班统计打印
// @Tags InsBookIn 当班统计打印
// @Summary 当班统计打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.OnDutyPrintReq true "当班统计打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"当班统计打印"}"
// @Router /insBookIn/onDutyPrint [post]
func (a *InsBookInApi) OnDutyPrint(c *gin.Context) {
	var err error
	var req insbuyReq.OnDutyPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.OnDutyPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.OnDutyPrint'!", zap.Error(err))
		response.FailWithMessage("当班统计打印失败", c)
	} else {
		response.OkWithMessage("当班统计打印成功", c)
	}
}

// NetPayPrint 网络支付打印
// @Tags InsBookIn 网络支付打印
// @Summary 网络支付打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.NetPayPrintReq true "网络支付打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"网络支付打印"}"
// @Router /insBookIn/netPayPrint [post]
func (a *InsBookInApi) NetPayPrint(c *gin.Context) {
	var err error
	var req insbuyReq.NetPayPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.NetPayPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.NetPayPrint'!", zap.Error(err))
		response.FailWithMessage("网络支付打印失败", c)
	} else {
		response.OkWithMessage("网络支付打印成功", c)
	}
}

// RefundPrint 退款打印
// @Tags InsBookIn 退款打印
// @Summary 退款打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.RefundPrintReq true "退款打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"退款打印"}"
// @Router /insBookIn/refundPrint [post]
func (a *InsBookInApi) RefundPrint(c *gin.Context) {
	var err error
	var req insbuyReq.RefundPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.RefundPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.RefundPrint'!", zap.Error(err))
		response.FailWithMessage("退款打印失败", c)
	} else {
		response.OkWithMessage("退款打印成功", c)
	}
}

// AcctPrint 挂账打印
// @Tags InsBookIn 挂账打印
// @Summary 挂账打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.AcctPrintReq true "挂账打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"挂账打印"}"
// @Router /insBookIn/acctPrint [post]
func (a *InsBookInApi) AcctPrint(c *gin.Context) {
	var err error
	var req insbuyReq.AcctPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.AcctPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.AcctPrint'!", zap.Error(err))
		response.FailWithMessage("挂账打印失败", c)
	} else {
		response.OkWithMessage("挂账打印成功", c)
	}
}

// AcctRepayPrint 挂账还款打印
// @Tags InsBookIn 挂账还款打印
// @Summary 挂账还款打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.AcctRepayPrintReq true "挂账还款打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"挂账还款打印"}"
// @Router /insBookIn/acctRepayPrint [post]
func (a *InsBookInApi) AcctRepayPrint(c *gin.Context) {
	var err error
	var req insbuyReq.AcctRepayPrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.AcctRepayPrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.AcctRepayPrint'!", zap.Error(err))
		response.FailWithMessage("挂账还款打印失败", c)
	} else {
		response.OkWithMessage("挂账还款打印成功", c)
	}
}

// VipConsumePrint 会员消费打印
// @Tags InsBookIn 会员消费打印
// @Summary 会员消费打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.VipConsumePrintReq true "会员消费打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"会员消费打印"}"
// @Router /insBookIn/vipConsumePrint [post]
func (a *InsBookInApi) VipConsumePrint(c *gin.Context) {
	var err error
	var req insbuyReq.VipConsumePrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.VipConsumePrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.VipConsumePrint'!", zap.Error(err))
		response.FailWithMessage("会员消费打印失败", c)
	} else {
		response.OkWithMessage("会员消费打印成功", c)
	}
}

// VipRechargePrint 会员充值打印
// @Tags InsBookIn 会员充值打印
// @Summary 会员充值打印
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.VipRechargePrintReq true "会员充值打印"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"会员充值打印"}"
// @Router /insBookIn/vipRechargePrint [post]
func (a *InsBookInApi) VipRechargePrint(c *gin.Context) {
	var err error
	var req insbuyReq.VipRechargePrintReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}

	if err = insBookInService.VipRechargePrint(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.VipRechargePrint'!", zap.Error(err))
		response.FailWithMessage("会员充值打印失败", c)
	} else {
		response.OkWithMessage("会员充值打印成功", c)
	}
}

// GetOpenDeskList 获取桌台当天的开台列表数据
// @Tags InsBookIn 获取桌台当天的开台列表数据
// @Summary 获取桌台当天的开台列表数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOpenDeskListReq true "获取桌台当天的开台列表数据"
// @Success   200   {object}  response.Response{data=insbuyRes.GetOpenDeskListResp,msg=string}  "获取桌台当天的开台列表数据"
// @Router /insBookIn/getOpenDeskList [get]
func (a *InsBookInApi) GetOpenDeskList(c *gin.Context) {
	var err error
	var req insbuyReq.GetOpenDeskListReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	res, err := insBookInService.GetOpenDeskListByDeskId(req)
	response.ResultErr(res, err, c)
}

// ReOpenDesk 复台
// @Tags InsBookIn 复台
// @Summary 复台
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInReOpenDeskReq true "复台"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInReOpenDeskResp,msg=string}  "复台"
// @Router /insBookIn/reOpenDesk [put]
func (a *InsBookInApi) ReOpenDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInReOpenDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.ReOpenDesk(req)
	response.ResultErr(resp, err, c)
}

// ProductChangeDesk 商品转桌
// @Tags InsBookIn 商品转桌
// @Summary 商品转桌
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.ProductChangeDeskReq true "商品转桌"
// @Success   200   {object}  response.Response{data=insbuyRes.ProductChangeDeskResp,msg=string}  "复台"
// @Router /insBookIn/productChangeDesk [put]
func (a *InsBookInApi) ProductChangeDesk(c *gin.Context) {
	var err error
	var req insbuyReq.ProductChangeDeskReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.ProductChangeDesk(req)
	response.ResultErr(resp, err, c)
}

// GetDeskConsumed 获取整店消费情况
// @Tags InsBookIn 获取整店消费情况
// @Summary 获取整店消费情况
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetDeskConsumedReq true "获取整店消费情况"
// @Success   200   {object}  response.Response{data=consumedmodel.DeskConsumed,msg=string}
// @Router /insBookIn/getDeskConsumed [get]
func (a *InsBookInApi) GetDeskConsumed(c *gin.Context) {
	var err error
	var req insbuyReq.GetDeskConsumedReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.GetDeskConsumed(req)
	response.ResultErr(resp, err, c)
}

// GetOpenDeskConsumedRecord 销售获取桌台记录信息
// @Tags InsBookIn 销售获取桌台记录信息
// @Summary 销售获取桌台记录信息
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOpenDeskConsumedRecord true "销售获取桌台记录信息"
// @Success   200   {object}  response.Response{data=insbuyRes.GetOpenDeskConsumedRecordResp,msg=string}
// @Router /insBookIn/getOpenDeskConsumedRecord [get]
func (a *InsBookInApi) GetOpenDeskConsumedRecord(c *gin.Context) {
	var err error
	var req insbuyReq.GetOpenDeskConsumedRecord
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.GetOpenDeskConsumedRecord(req)
	response.ResultErr(resp, err, c)
}

// EndBusinessDay 结束营业日
// @Tags InsBookIn 结束营业日
// @Summary 结束营业日
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.EndBusinessDayReq true "结束营业日"
// @Success   200   {object}  response.Response{}
// @Router /insBookIn/endBusinessDay [post]
func (a *InsBookInApi) EndBusinessDay(c *gin.Context) {
	var err error
	var req insbuyReq.EndBusinessDayReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.EndBusinessDay(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.EndBusinessDay'!", zap.Error(err))
		response.FailWithMessage("结束营业日失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("结束营业日成功", c)
	}
}

// ManualStartBusinessDay 手动开始营业日
// @Tags InsBookIn 手动开始营业日
// @Summary 手动开始营业日
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartBusinessDayReq true "手动开始营业日"
// @Success   200   {object}  response.Response{}
// @Router /insBookIn/manualStartBusinessDay [post]
func (a *InsBookInApi) ManualStartBusinessDay(c *gin.Context) {
	var err error
	var req insbuyReq.StartBusinessDayReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.ManualStartBusinessDay(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.ManualStartBusinessDay'!", zap.Error(err))
		response.FailWithMessage("开始营业日失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("开始营业日成功", c)
	}
}

// RefreshBusinessData 刷新营业数据
// @Tags InsBookIn 刷新营业数据
// @Summary 刷新营业数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.EndBusinessDayReq true "刷新营业数据"
// @Success   200   {object}  response.Response{}
// @Router /insBookIn/refreshBusinessData [post]
func (a *InsBookInApi) RefreshBusinessData(c *gin.Context) {
	var err error
	var req insbuyReq.EndBusinessDayReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = insBookInService.RefreshBusinessData(req); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.RefreshBusinessData'!", zap.Error(err))
		response.FailWithMessage("刷新营业数据失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("刷新营业数据成功", c)
	}
}

// GetOpenDeskInfo 查询桌台信息（小程序）
// @Tags InsBookIn 查询桌台信息
// @Summary 查询桌台信息
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOpenDeskInfoReq true "查询桌台信息（小程序）"
// @Success   200   {object}  response.Response{data=insbuyRes.GetOpenDeskInfoResp,msg=string}
// @Router /insBookIn/getOpenDeskInfo [get]
func (a *InsBookInApi) GetOpenDeskInfo(c *gin.Context) {
	var err error
	var req insbuyReq.GetOpenDeskInfoReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.GetOpenDeskInfo(req)
	response.ResultErr(resp, err, c)
}

// ScanCodeOrder 扫码点单
// @Tags InsBookIn 扫码点单
// @Summary 扫码点单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.ScanCodeOrderReq true "扫码点单"
// @Success   200   {object}  response.Response{data=insbuyRes.ScanCodeOrderResp,msg=string}
// @Router /insBookIn/scanCodeOrder [post]
func (a *InsBookInApi) ScanCodeOrder(c *gin.Context) {
	var err error
	var req insbuyReq.ScanCodeOrderReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.ScanCodeOrder(req)
	response.ResultErr(resp, err, c)
}

// CreateScanOrderInfo 创建订单
// @Tags InsOrderInfo
// @Summary 创建订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AddOrderInfo true "创建订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insBookIn/createScanOrderInfo [post]
func (a *InsBookInApi) CreateScanOrderInfo(c *gin.Context) {
	var req insbuyReq.CreateOrderReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	req.Source = insbuy.OSourceScan
	if resp, err := insOrderInfoService.CreateOrderInfoV2(req); err != nil {
		global.GVA_LOG.Error("创建订单失败!", zap.Error(err))
		response.FailWithMessage("创建订单失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ScanOpenDesk 扫码开台接口
// @Tags InsBookIn 扫码开台接口
// @Summary 扫码开台接口
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsBookInOpenDesk true "扫码开台接口"
// @Success   200   {object}  response.Response{data=insbuyRes.InsBookInOpenDesk,msg=string}
// @Router /insBookIn/scanOpenDesk [post]
func (a *InsBookInApi) ScanOpenDesk(c *gin.Context) {
	var err error
	var req insbuyReq.InsBookInOpenDesk
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insBookInService.ScanOpenDesk(req)
	response.ResultErr(resp, err, c)
}

// OpenDeskSnapshot 开台数据快照接口
// @Tags InsBookIn
// @Summary 开台数据快照接口
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.OpenDeskSnapshotReq true "开台数据快照接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insBookIn/openDeskSnapshot [post]
func (a *InsBookInApi) OpenDeskSnapshot(c *gin.Context) {
	var err error
	var req insbuyReq.OpenDeskSnapshotReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	insBookInService.OpenDeskSnapshot(req)
	response.Ok(c)
}

// OpenDeskSnapshotJson 开台数据全量快照 json
// @Tags InsBookIn
// @Summary 开台数据全量快照 json
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.OpenDeskSnapshotReq true "开台数据全量快照 json"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insBookIn/openDeskSnapshotJson [post]
func (a *InsBookInApi) OpenDeskSnapshotJson(c *gin.Context) {
	var err error
	var req insbuyReq.OpenDeskSnapshotReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	insBookInService.OpenDeskSnapshotJson(req)
	response.Ok(c)
}
