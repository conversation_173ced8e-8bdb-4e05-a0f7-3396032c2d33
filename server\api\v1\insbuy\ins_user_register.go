package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsUserRegisterApi struct {
}

// CreateInsUserRegister 创建InsUserRegister
// @Tags InsUserRegister
// @Summary 创建InsUserRegister
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsUserRegister true "创建InsUserRegister"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insUserRegister/createInsUserRegister [post]
func (insUserRegisterApi *InsUserRegisterApi) CreateInsUserRegister(c *gin.Context) {
	var insUserRegister insbuy.InsUserRegister
	err := c.ShouldBindJSON(&insUserRegister)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//用户存在就拒绝
	err = insUserRegisterService.CheckPhone(insUserRegister.UserName)
	if err != nil {
		response.FailWithMessage("注册失败"+err.Error(), c)
		return
	}
	if err := insUserRegisterService.CreateInsUserRegister(&insUserRegister); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsUserRegister 删除InsUserRegister
// @Tags InsUserRegister
// @Summary 删除InsUserRegister
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsUserRegister true "删除InsUserRegister"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insUserRegister/deleteInsUserRegister [delete]
func (insUserRegisterApi *InsUserRegisterApi) DeleteInsUserRegister(c *gin.Context) {
	var insUserRegister insbuy.InsUserRegister
	err := c.ShouldBindJSON(&insUserRegister)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insUserRegisterService.DeleteInsUserRegister(insUserRegister); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsUserRegisterByIds 批量删除InsUserRegister
// @Tags InsUserRegister
// @Summary 批量删除InsUserRegister
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsUserRegister"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insUserRegister/deleteInsUserRegisterByIds [delete]
func (insUserRegisterApi *InsUserRegisterApi) DeleteInsUserRegisterByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insUserRegisterService.DeleteInsUserRegisterByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsUserRegister 更新InsUserRegister
// @Tags InsUserRegister
// @Summary 更新InsUserRegister
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsUserRegister true "更新InsUserRegister"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insUserRegister/updateInsUserRegister [put]
func (insUserRegisterApi *InsUserRegisterApi) UpdateInsUserRegister(c *gin.Context) {
	var insUserRegister insbuy.InsUserRegister
	err := c.ShouldBindJSON(&insUserRegister)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insUserRegisterService.UpdateInsUserRegister(insUserRegister); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsUserRegister 用id查询InsUserRegister
// @Tags InsUserRegister
// @Summary 用id查询InsUserRegister
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsUserRegister true "用id查询InsUserRegister"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insUserRegister/findInsUserRegister [get]
func (insUserRegisterApi *InsUserRegisterApi) FindInsUserRegister(c *gin.Context) {
	var insUserRegister insbuy.InsUserRegister
	err := c.ShouldBindQuery(&insUserRegister)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsUserRegister, err := insUserRegisterService.GetInsUserRegister(insUserRegister.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsUserRegister": reinsUserRegister}, c)
	}
}

// GetInsUserRegisterList 分页获取InsUserRegister列表
// @Tags InsUserRegister
// @Summary 分页获取InsUserRegister列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsUserRegisterSearch true "分页获取InsUserRegister列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insUserRegister/getInsUserRegisterList [get]
func (insUserRegisterApi *InsUserRegisterApi) GetInsUserRegisterList(c *gin.Context) {
	var pageInfo insbuyReq.InsUserRegisterSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insUserRegisterService.GetInsUserRegisterInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CloseAccount 注销账户
// @Tags InsUserRegister
// @Summary 注销账户
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CloseAccountReq true "注销账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"注销成功"}"
// @Router /insUserRegister/closeAccount [delete]
func (insUserRegisterApi *InsUserRegisterApi) CloseAccount(c *gin.Context) {
	var req insbuyReq.CloseAccountReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insUserRegisterService.CloseAccount(req.UserName, req.Password); err != nil {
		global.GVA_LOG.Error("注销失败!", zap.Error(err))
		response.FailWithMessage("注销失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("注销成功", c)
	}
}
