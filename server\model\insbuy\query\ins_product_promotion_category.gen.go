// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotionCategory(db *gorm.DB, opts ...gen.DOOption) insProductPromotionCategory {
	_insProductPromotionCategory := insProductPromotionCategory{}

	_insProductPromotionCategory.insProductPromotionCategoryDo.UseDB(db, opts...)
	_insProductPromotionCategory.insProductPromotionCategoryDo.UseModel(&insbuy.InsProductPromotionCategory{})

	tableName := _insProductPromotionCategory.insProductPromotionCategoryDo.TableName()
	_insProductPromotionCategory.ALL = field.NewAsterisk(tableName)
	_insProductPromotionCategory.ID = field.NewUint(tableName, "id")
	_insProductPromotionCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotionCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotionCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotionCategory.PromotionId = field.NewUint(tableName, "promotion_id")
	_insProductPromotionCategory.CategoryId = field.NewUint(tableName, "category_id")
	_insProductPromotionCategory.DiscountRate = field.NewFloat64(tableName, "discount_rate")

	_insProductPromotionCategory.fillFieldMap()

	return _insProductPromotionCategory
}

type insProductPromotionCategory struct {
	insProductPromotionCategoryDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	PromotionId  field.Uint
	CategoryId   field.Uint
	DiscountRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insProductPromotionCategory) Table(newTableName string) *insProductPromotionCategory {
	i.insProductPromotionCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotionCategory) As(alias string) *insProductPromotionCategory {
	i.insProductPromotionCategoryDo.DO = *(i.insProductPromotionCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotionCategory) updateTableName(table string) *insProductPromotionCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PromotionId = field.NewUint(table, "promotion_id")
	i.CategoryId = field.NewUint(table, "category_id")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotionCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotionCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["promotion_id"] = i.PromotionId
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["discount_rate"] = i.DiscountRate
}

func (i insProductPromotionCategory) clone(db *gorm.DB) insProductPromotionCategory {
	i.insProductPromotionCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotionCategory) replaceDB(db *gorm.DB) insProductPromotionCategory {
	i.insProductPromotionCategoryDo.ReplaceDB(db)
	return i
}

type insProductPromotionCategoryDo struct{ gen.DO }

func (i insProductPromotionCategoryDo) Debug() *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionCategoryDo) WithContext(ctx context.Context) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionCategoryDo) ReadDB() *insProductPromotionCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionCategoryDo) WriteDB() *insProductPromotionCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionCategoryDo) Session(config *gorm.Session) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionCategoryDo) Clauses(conds ...clause.Expression) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionCategoryDo) Returning(value interface{}, columns ...string) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionCategoryDo) Not(conds ...gen.Condition) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionCategoryDo) Or(conds ...gen.Condition) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionCategoryDo) Select(conds ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionCategoryDo) Where(conds ...gen.Condition) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionCategoryDo) Order(conds ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionCategoryDo) Distinct(cols ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionCategoryDo) Omit(cols ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionCategoryDo) Group(cols ...field.Expr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionCategoryDo) Having(conds ...gen.Condition) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionCategoryDo) Limit(limit int) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionCategoryDo) Offset(offset int) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionCategoryDo) Unscoped() *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionCategoryDo) Create(values ...*insbuy.InsProductPromotionCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionCategoryDo) CreateInBatches(values []*insbuy.InsProductPromotionCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionCategoryDo) Save(values ...*insbuy.InsProductPromotionCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionCategoryDo) First() (*insbuy.InsProductPromotionCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionCategory), nil
	}
}

func (i insProductPromotionCategoryDo) Take() (*insbuy.InsProductPromotionCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionCategory), nil
	}
}

func (i insProductPromotionCategoryDo) Last() (*insbuy.InsProductPromotionCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionCategory), nil
	}
}

func (i insProductPromotionCategoryDo) Find() ([]*insbuy.InsProductPromotionCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotionCategory), err
}

func (i insProductPromotionCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotionCategory, err error) {
	buf := make([]*insbuy.InsProductPromotionCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionCategoryDo) FindInBatches(result *[]*insbuy.InsProductPromotionCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionCategoryDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionCategoryDo) Assign(attrs ...field.AssignExpr) *insProductPromotionCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionCategoryDo) Joins(fields ...field.RelationField) *insProductPromotionCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionCategoryDo) Preload(fields ...field.RelationField) *insProductPromotionCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionCategoryDo) FirstOrInit() (*insbuy.InsProductPromotionCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionCategory), nil
	}
}

func (i insProductPromotionCategoryDo) FirstOrCreate() (*insbuy.InsProductPromotionCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionCategory), nil
	}
}

func (i insProductPromotionCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotionCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionCategoryDo) Delete(models ...*insbuy.InsProductPromotionCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionCategoryDo) withDO(do gen.Dao) *insProductPromotionCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
