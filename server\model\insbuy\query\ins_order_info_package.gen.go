// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderInfoPackage(db *gorm.DB, opts ...gen.DOOption) insOrderInfoPackage {
	_insOrderInfoPackage := insOrderInfoPackage{}

	_insOrderInfoPackage.insOrderInfoPackageDo.UseDB(db, opts...)
	_insOrderInfoPackage.insOrderInfoPackageDo.UseModel(&insbuy.InsOrderInfoPackage{})

	tableName := _insOrderInfoPackage.insOrderInfoPackageDo.TableName()
	_insOrderInfoPackage.ALL = field.NewAsterisk(tableName)
	_insOrderInfoPackage.ID = field.NewUint(tableName, "id")
	_insOrderInfoPackage.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderInfoPackage.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderInfoPackage.DeletedAt = field.NewField(tableName, "deleted_at")
	_insOrderInfoPackage.ProductName = field.NewString(tableName, "product_name")
	_insOrderInfoPackage.Nums = field.NewInt(tableName, "nums")
	_insOrderInfoPackage.ReturnNum = field.NewInt(tableName, "return_num")
	_insOrderInfoPackage.Unit = field.NewInt(tableName, "unit")
	_insOrderInfoPackage.ProductPrice = field.NewFloat64(tableName, "product_price")
	_insOrderInfoPackage.OriginalPrice = field.NewFloat64(tableName, "original_price")
	_insOrderInfoPackage.GivePrice = field.NewFloat64(tableName, "give_price")
	_insOrderInfoPackage.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderInfoPackage.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insOrderInfoPackage.ReplacedFromId = field.NewUint(tableName, "replaced_from_id")
	_insOrderInfoPackage.ProductId = field.NewUint(tableName, "product_id")
	_insOrderInfoPackage.PackageId = field.NewUint(tableName, "package_id")
	_insOrderInfoPackage.PackageItemId = field.NewInt(tableName, "package_item_id")
	_insOrderInfoPackage.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insOrderInfoPackage.IsGift = field.NewInt(tableName, "is_gift")
	_insOrderInfoPackage.BusinessDay = field.NewTime(tableName, "business_day")

	_insOrderInfoPackage.fillFieldMap()

	return _insOrderInfoPackage
}

type insOrderInfoPackage struct {
	insOrderInfoPackageDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	ProductName    field.String
	Nums           field.Int
	ReturnNum      field.Int
	Unit           field.Int
	ProductPrice   field.Float64
	OriginalPrice  field.Float64
	GivePrice      field.Float64
	OrderId        field.Uint64
	OrderDetailsId field.Uint64
	ReplacedFromId field.Uint
	ProductId      field.Uint
	PackageId      field.Uint
	PackageItemId  field.Int
	WarehouseId    field.Int
	IsGift         field.Int
	BusinessDay    field.Time

	fieldMap map[string]field.Expr
}

func (i insOrderInfoPackage) Table(newTableName string) *insOrderInfoPackage {
	i.insOrderInfoPackageDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderInfoPackage) As(alias string) *insOrderInfoPackage {
	i.insOrderInfoPackageDo.DO = *(i.insOrderInfoPackageDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderInfoPackage) updateTableName(table string) *insOrderInfoPackage {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ProductName = field.NewString(table, "product_name")
	i.Nums = field.NewInt(table, "nums")
	i.ReturnNum = field.NewInt(table, "return_num")
	i.Unit = field.NewInt(table, "unit")
	i.ProductPrice = field.NewFloat64(table, "product_price")
	i.OriginalPrice = field.NewFloat64(table, "original_price")
	i.GivePrice = field.NewFloat64(table, "give_price")
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.ReplacedFromId = field.NewUint(table, "replaced_from_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.PackageId = field.NewUint(table, "package_id")
	i.PackageItemId = field.NewInt(table, "package_item_id")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.IsGift = field.NewInt(table, "is_gift")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insOrderInfoPackage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderInfoPackage) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 20)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["nums"] = i.Nums
	i.fieldMap["return_num"] = i.ReturnNum
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["product_price"] = i.ProductPrice
	i.fieldMap["original_price"] = i.OriginalPrice
	i.fieldMap["give_price"] = i.GivePrice
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["replaced_from_id"] = i.ReplacedFromId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["package_item_id"] = i.PackageItemId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["is_gift"] = i.IsGift
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insOrderInfoPackage) clone(db *gorm.DB) insOrderInfoPackage {
	i.insOrderInfoPackageDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderInfoPackage) replaceDB(db *gorm.DB) insOrderInfoPackage {
	i.insOrderInfoPackageDo.ReplaceDB(db)
	return i
}

type insOrderInfoPackageDo struct{ gen.DO }

func (i insOrderInfoPackageDo) Debug() *insOrderInfoPackageDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderInfoPackageDo) WithContext(ctx context.Context) *insOrderInfoPackageDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderInfoPackageDo) ReadDB() *insOrderInfoPackageDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderInfoPackageDo) WriteDB() *insOrderInfoPackageDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderInfoPackageDo) Session(config *gorm.Session) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderInfoPackageDo) Clauses(conds ...clause.Expression) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderInfoPackageDo) Returning(value interface{}, columns ...string) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderInfoPackageDo) Not(conds ...gen.Condition) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderInfoPackageDo) Or(conds ...gen.Condition) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderInfoPackageDo) Select(conds ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderInfoPackageDo) Where(conds ...gen.Condition) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderInfoPackageDo) Order(conds ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderInfoPackageDo) Distinct(cols ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderInfoPackageDo) Omit(cols ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderInfoPackageDo) Join(table schema.Tabler, on ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderInfoPackageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderInfoPackageDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderInfoPackageDo) Group(cols ...field.Expr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderInfoPackageDo) Having(conds ...gen.Condition) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderInfoPackageDo) Limit(limit int) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderInfoPackageDo) Offset(offset int) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderInfoPackageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderInfoPackageDo) Unscoped() *insOrderInfoPackageDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderInfoPackageDo) Create(values ...*insbuy.InsOrderInfoPackage) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderInfoPackageDo) CreateInBatches(values []*insbuy.InsOrderInfoPackage, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderInfoPackageDo) Save(values ...*insbuy.InsOrderInfoPackage) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderInfoPackageDo) First() (*insbuy.InsOrderInfoPackage, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoPackage), nil
	}
}

func (i insOrderInfoPackageDo) Take() (*insbuy.InsOrderInfoPackage, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoPackage), nil
	}
}

func (i insOrderInfoPackageDo) Last() (*insbuy.InsOrderInfoPackage, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoPackage), nil
	}
}

func (i insOrderInfoPackageDo) Find() ([]*insbuy.InsOrderInfoPackage, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderInfoPackage), err
}

func (i insOrderInfoPackageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderInfoPackage, err error) {
	buf := make([]*insbuy.InsOrderInfoPackage, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderInfoPackageDo) FindInBatches(result *[]*insbuy.InsOrderInfoPackage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderInfoPackageDo) Attrs(attrs ...field.AssignExpr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderInfoPackageDo) Assign(attrs ...field.AssignExpr) *insOrderInfoPackageDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderInfoPackageDo) Joins(fields ...field.RelationField) *insOrderInfoPackageDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderInfoPackageDo) Preload(fields ...field.RelationField) *insOrderInfoPackageDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderInfoPackageDo) FirstOrInit() (*insbuy.InsOrderInfoPackage, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoPackage), nil
	}
}

func (i insOrderInfoPackageDo) FirstOrCreate() (*insbuy.InsOrderInfoPackage, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfoPackage), nil
	}
}

func (i insOrderInfoPackageDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderInfoPackage, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderInfoPackageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderInfoPackageDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderInfoPackageDo) Delete(models ...*insbuy.InsOrderInfoPackage) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderInfoPackageDo) withDO(do gen.Dao) *insOrderInfoPackageDo {
	i.DO = *do.(*gen.DO)
	return i
}
