// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutDetails(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutDetails {
	_insWarehouseInoutDetails := insWarehouseInoutDetails{}

	_insWarehouseInoutDetails.insWarehouseInoutDetailsDo.UseDB(db, opts...)
	_insWarehouseInoutDetails.insWarehouseInoutDetailsDo.UseModel(&insbuy.InsWarehouseInoutDetails{})

	tableName := _insWarehouseInoutDetails.insWarehouseInoutDetailsDo.TableName()
	_insWarehouseInoutDetails.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutDetails.Id = field.NewInt(tableName, "id")
	_insWarehouseInoutDetails.InoutId = field.NewInt(tableName, "inout_id")
	_insWarehouseInoutDetails.SupplierId = field.NewInt(tableName, "supplier_id")
	_insWarehouseInoutDetails.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseInoutDetails.PurchasePrice = field.NewFloat64(tableName, "purchase_price")
	_insWarehouseInoutDetails.PurchaseTotalPrice = field.NewFloat64(tableName, "purchase_total_price")
	_insWarehouseInoutDetails.SourceType = field.NewInt(tableName, "source_type")
	_insWarehouseInoutDetails.PurchaseNum = field.NewInt(tableName, "purchase_num")
	_insWarehouseInoutDetails.ApplyNum = field.NewInt(tableName, "apply_num")
	_insWarehouseInoutDetails.ActualNum = field.NewInt(tableName, "actual_num")
	_insWarehouseInoutDetails.WeekStockNum = field.NewInt(tableName, "week_stock_num")
	_insWarehouseInoutDetails.AddStep = field.NewInt(tableName, "add_step")
	_insWarehouseInoutDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInoutDetails.ActualOutNum = field.NewInt(tableName, "actual_out_num")
	_insWarehouseInoutDetails.OutStep = field.NewInt(tableName, "out_step")
	_insWarehouseInoutDetails.Remark = field.NewString(tableName, "remark")
	_insWarehouseInoutDetails.Num = field.NewFloat64(tableName, "num")

	_insWarehouseInoutDetails.fillFieldMap()

	return _insWarehouseInoutDetails
}

type insWarehouseInoutDetails struct {
	insWarehouseInoutDetailsDo

	ALL                field.Asterisk
	Id                 field.Int
	InoutId            field.Int
	SupplierId         field.Int
	MaterialId         field.Int
	PurchasePrice      field.Float64
	PurchaseTotalPrice field.Float64
	SourceType         field.Int
	PurchaseNum        field.Int
	ApplyNum           field.Int
	ActualNum          field.Int
	WeekStockNum       field.Int
	AddStep            field.Int
	CreatedAt          field.Time
	UpdatedAt          field.Time
	DeletedAt          field.Field
	ActualOutNum       field.Int
	OutStep            field.Int
	Remark             field.String
	Num                field.Float64

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutDetails) Table(newTableName string) *insWarehouseInoutDetails {
	i.insWarehouseInoutDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutDetails) As(alias string) *insWarehouseInoutDetails {
	i.insWarehouseInoutDetailsDo.DO = *(i.insWarehouseInoutDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutDetails) updateTableName(table string) *insWarehouseInoutDetails {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.InoutId = field.NewInt(table, "inout_id")
	i.SupplierId = field.NewInt(table, "supplier_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.PurchasePrice = field.NewFloat64(table, "purchase_price")
	i.PurchaseTotalPrice = field.NewFloat64(table, "purchase_total_price")
	i.SourceType = field.NewInt(table, "source_type")
	i.PurchaseNum = field.NewInt(table, "purchase_num")
	i.ApplyNum = field.NewInt(table, "apply_num")
	i.ActualNum = field.NewInt(table, "actual_num")
	i.WeekStockNum = field.NewInt(table, "week_stock_num")
	i.AddStep = field.NewInt(table, "add_step")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ActualOutNum = field.NewInt(table, "actual_out_num")
	i.OutStep = field.NewInt(table, "out_step")
	i.Remark = field.NewString(table, "remark")
	i.Num = field.NewFloat64(table, "num")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 19)
	i.fieldMap["id"] = i.Id
	i.fieldMap["inout_id"] = i.InoutId
	i.fieldMap["supplier_id"] = i.SupplierId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["purchase_price"] = i.PurchasePrice
	i.fieldMap["purchase_total_price"] = i.PurchaseTotalPrice
	i.fieldMap["source_type"] = i.SourceType
	i.fieldMap["purchase_num"] = i.PurchaseNum
	i.fieldMap["apply_num"] = i.ApplyNum
	i.fieldMap["actual_num"] = i.ActualNum
	i.fieldMap["week_stock_num"] = i.WeekStockNum
	i.fieldMap["add_step"] = i.AddStep
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["actual_out_num"] = i.ActualOutNum
	i.fieldMap["out_step"] = i.OutStep
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["num"] = i.Num
}

func (i insWarehouseInoutDetails) clone(db *gorm.DB) insWarehouseInoutDetails {
	i.insWarehouseInoutDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutDetails) replaceDB(db *gorm.DB) insWarehouseInoutDetails {
	i.insWarehouseInoutDetailsDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutDetailsDo struct{ gen.DO }

func (i insWarehouseInoutDetailsDo) Debug() *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutDetailsDo) WithContext(ctx context.Context) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutDetailsDo) ReadDB() *insWarehouseInoutDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutDetailsDo) WriteDB() *insWarehouseInoutDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutDetailsDo) Session(config *gorm.Session) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutDetailsDo) Clauses(conds ...clause.Expression) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutDetailsDo) Returning(value interface{}, columns ...string) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutDetailsDo) Not(conds ...gen.Condition) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutDetailsDo) Or(conds ...gen.Condition) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutDetailsDo) Select(conds ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutDetailsDo) Where(conds ...gen.Condition) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutDetailsDo) Order(conds ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutDetailsDo) Distinct(cols ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutDetailsDo) Omit(cols ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutDetailsDo) Group(cols ...field.Expr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutDetailsDo) Having(conds ...gen.Condition) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutDetailsDo) Limit(limit int) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutDetailsDo) Offset(offset int) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutDetailsDo) Unscoped() *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutDetailsDo) Create(values ...*insbuy.InsWarehouseInoutDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutDetailsDo) CreateInBatches(values []*insbuy.InsWarehouseInoutDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutDetailsDo) Save(values ...*insbuy.InsWarehouseInoutDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutDetailsDo) First() (*insbuy.InsWarehouseInoutDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetails), nil
	}
}

func (i insWarehouseInoutDetailsDo) Take() (*insbuy.InsWarehouseInoutDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetails), nil
	}
}

func (i insWarehouseInoutDetailsDo) Last() (*insbuy.InsWarehouseInoutDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetails), nil
	}
}

func (i insWarehouseInoutDetailsDo) Find() ([]*insbuy.InsWarehouseInoutDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutDetails), err
}

func (i insWarehouseInoutDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutDetails, err error) {
	buf := make([]*insbuy.InsWarehouseInoutDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutDetailsDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutDetailsDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutDetailsDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutDetailsDo) Joins(fields ...field.RelationField) *insWarehouseInoutDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutDetailsDo) Preload(fields ...field.RelationField) *insWarehouseInoutDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutDetailsDo) FirstOrInit() (*insbuy.InsWarehouseInoutDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetails), nil
	}
}

func (i insWarehouseInoutDetailsDo) FirstOrCreate() (*insbuy.InsWarehouseInoutDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetails), nil
	}
}

func (i insWarehouseInoutDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutDetailsDo) Delete(models ...*insbuy.InsWarehouseInoutDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutDetailsDo) withDO(do gen.Dao) *insWarehouseInoutDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
