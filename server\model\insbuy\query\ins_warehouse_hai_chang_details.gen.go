// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseHaiChangDetails(db *gorm.DB, opts ...gen.DOOption) insWarehouseHaiChangDetails {
	_insWarehouseHaiChangDetails := insWarehouseHaiChangDetails{}

	_insWarehouseHaiChangDetails.insWarehouseHaiChangDetailsDo.UseDB(db, opts...)
	_insWarehouseHaiChangDetails.insWarehouseHaiChangDetailsDo.UseModel(&insbuy.InsWarehouseHaiChangDetails{})

	tableName := _insWarehouseHaiChangDetails.insWarehouseHaiChangDetailsDo.TableName()
	_insWarehouseHaiChangDetails.ALL = field.NewAsterisk(tableName)
	_insWarehouseHaiChangDetails.ID = field.NewUint(tableName, "id")
	_insWarehouseHaiChangDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseHaiChangDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseHaiChangDetails.OrderSn = field.NewString(tableName, "order_sn")
	_insWarehouseHaiChangDetails.Body = field.NewField(tableName, "body")

	_insWarehouseHaiChangDetails.fillFieldMap()

	return _insWarehouseHaiChangDetails
}

type insWarehouseHaiChangDetails struct {
	insWarehouseHaiChangDetailsDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	OrderSn   field.String
	Body      field.Field

	fieldMap map[string]field.Expr
}

func (i insWarehouseHaiChangDetails) Table(newTableName string) *insWarehouseHaiChangDetails {
	i.insWarehouseHaiChangDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseHaiChangDetails) As(alias string) *insWarehouseHaiChangDetails {
	i.insWarehouseHaiChangDetailsDo.DO = *(i.insWarehouseHaiChangDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseHaiChangDetails) updateTableName(table string) *insWarehouseHaiChangDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OrderSn = field.NewString(table, "order_sn")
	i.Body = field.NewField(table, "body")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseHaiChangDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseHaiChangDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["order_sn"] = i.OrderSn
	i.fieldMap["body"] = i.Body
}

func (i insWarehouseHaiChangDetails) clone(db *gorm.DB) insWarehouseHaiChangDetails {
	i.insWarehouseHaiChangDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseHaiChangDetails) replaceDB(db *gorm.DB) insWarehouseHaiChangDetails {
	i.insWarehouseHaiChangDetailsDo.ReplaceDB(db)
	return i
}

type insWarehouseHaiChangDetailsDo struct{ gen.DO }

func (i insWarehouseHaiChangDetailsDo) Debug() *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseHaiChangDetailsDo) WithContext(ctx context.Context) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseHaiChangDetailsDo) ReadDB() *insWarehouseHaiChangDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseHaiChangDetailsDo) WriteDB() *insWarehouseHaiChangDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseHaiChangDetailsDo) Session(config *gorm.Session) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseHaiChangDetailsDo) Clauses(conds ...clause.Expression) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Returning(value interface{}, columns ...string) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseHaiChangDetailsDo) Not(conds ...gen.Condition) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Or(conds ...gen.Condition) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Select(conds ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Where(conds ...gen.Condition) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Order(conds ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Distinct(cols ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseHaiChangDetailsDo) Omit(cols ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseHaiChangDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseHaiChangDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseHaiChangDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseHaiChangDetailsDo) Group(cols ...field.Expr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseHaiChangDetailsDo) Having(conds ...gen.Condition) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseHaiChangDetailsDo) Limit(limit int) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseHaiChangDetailsDo) Offset(offset int) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseHaiChangDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseHaiChangDetailsDo) Unscoped() *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseHaiChangDetailsDo) Create(values ...*insbuy.InsWarehouseHaiChangDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseHaiChangDetailsDo) CreateInBatches(values []*insbuy.InsWarehouseHaiChangDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseHaiChangDetailsDo) Save(values ...*insbuy.InsWarehouseHaiChangDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseHaiChangDetailsDo) First() (*insbuy.InsWarehouseHaiChangDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangDetails), nil
	}
}

func (i insWarehouseHaiChangDetailsDo) Take() (*insbuy.InsWarehouseHaiChangDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangDetails), nil
	}
}

func (i insWarehouseHaiChangDetailsDo) Last() (*insbuy.InsWarehouseHaiChangDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangDetails), nil
	}
}

func (i insWarehouseHaiChangDetailsDo) Find() ([]*insbuy.InsWarehouseHaiChangDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseHaiChangDetails), err
}

func (i insWarehouseHaiChangDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseHaiChangDetails, err error) {
	buf := make([]*insbuy.InsWarehouseHaiChangDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseHaiChangDetailsDo) FindInBatches(result *[]*insbuy.InsWarehouseHaiChangDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseHaiChangDetailsDo) Attrs(attrs ...field.AssignExpr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseHaiChangDetailsDo) Assign(attrs ...field.AssignExpr) *insWarehouseHaiChangDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseHaiChangDetailsDo) Joins(fields ...field.RelationField) *insWarehouseHaiChangDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseHaiChangDetailsDo) Preload(fields ...field.RelationField) *insWarehouseHaiChangDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseHaiChangDetailsDo) FirstOrInit() (*insbuy.InsWarehouseHaiChangDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangDetails), nil
	}
}

func (i insWarehouseHaiChangDetailsDo) FirstOrCreate() (*insbuy.InsWarehouseHaiChangDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangDetails), nil
	}
}

func (i insWarehouseHaiChangDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseHaiChangDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseHaiChangDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseHaiChangDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseHaiChangDetailsDo) Delete(models ...*insbuy.InsWarehouseHaiChangDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseHaiChangDetailsDo) withDO(do gen.Dao) *insWarehouseHaiChangDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
