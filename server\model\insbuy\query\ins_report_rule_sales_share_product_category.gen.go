// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareProductCategory(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareProductCategory {
	_insReportRuleSalesShareProductCategory := insReportRuleSalesShareProductCategory{}

	_insReportRuleSalesShareProductCategory.insReportRuleSalesShareProductCategoryDo.UseDB(db, opts...)
	_insReportRuleSalesShareProductCategory.insReportRuleSalesShareProductCategoryDo.UseModel(&insbuy.InsReportRuleSalesShareProductCategory{})

	tableName := _insReportRuleSalesShareProductCategory.insReportRuleSalesShareProductCategoryDo.TableName()
	_insReportRuleSalesShareProductCategory.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareProductCategory.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareProductCategory.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareProductCategory.CategoryId = field.NewUint(tableName, "category_id")
	_insReportRuleSalesShareProductCategory.ShareModel = field.NewInt(tableName, "share_model")
	_insReportRuleSalesShareProductCategory.ShareParam = field.NewFloat64(tableName, "share_param")

	_insReportRuleSalesShareProductCategory.fillFieldMap()

	return _insReportRuleSalesShareProductCategory
}

type insReportRuleSalesShareProductCategory struct {
	insReportRuleSalesShareProductCategoryDo

	ALL        field.Asterisk
	ID         field.Uint
	RuleId     field.Uint
	CategoryId field.Uint
	ShareModel field.Int
	ShareParam field.Float64

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareProductCategory) Table(newTableName string) *insReportRuleSalesShareProductCategory {
	i.insReportRuleSalesShareProductCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareProductCategory) As(alias string) *insReportRuleSalesShareProductCategory {
	i.insReportRuleSalesShareProductCategoryDo.DO = *(i.insReportRuleSalesShareProductCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareProductCategory) updateTableName(table string) *insReportRuleSalesShareProductCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.CategoryId = field.NewUint(table, "category_id")
	i.ShareModel = field.NewInt(table, "share_model")
	i.ShareParam = field.NewFloat64(table, "share_param")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareProductCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareProductCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["share_model"] = i.ShareModel
	i.fieldMap["share_param"] = i.ShareParam
}

func (i insReportRuleSalesShareProductCategory) clone(db *gorm.DB) insReportRuleSalesShareProductCategory {
	i.insReportRuleSalesShareProductCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareProductCategory) replaceDB(db *gorm.DB) insReportRuleSalesShareProductCategory {
	i.insReportRuleSalesShareProductCategoryDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareProductCategoryDo struct{ gen.DO }

func (i insReportRuleSalesShareProductCategoryDo) Debug() *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareProductCategoryDo) WithContext(ctx context.Context) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareProductCategoryDo) ReadDB() *insReportRuleSalesShareProductCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareProductCategoryDo) WriteDB() *insReportRuleSalesShareProductCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareProductCategoryDo) Session(config *gorm.Session) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareProductCategoryDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareProductCategoryDo) Not(conds ...gen.Condition) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Or(conds ...gen.Condition) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Select(conds ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Where(conds ...gen.Condition) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Order(conds ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareProductCategoryDo) Omit(cols ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareProductCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareProductCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareProductCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareProductCategoryDo) Group(cols ...field.Expr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareProductCategoryDo) Having(conds ...gen.Condition) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareProductCategoryDo) Limit(limit int) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareProductCategoryDo) Offset(offset int) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareProductCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareProductCategoryDo) Unscoped() *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareProductCategoryDo) Create(values ...*insbuy.InsReportRuleSalesShareProductCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareProductCategoryDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareProductCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareProductCategoryDo) Save(values ...*insbuy.InsReportRuleSalesShareProductCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareProductCategoryDo) First() (*insbuy.InsReportRuleSalesShareProductCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProductCategory), nil
	}
}

func (i insReportRuleSalesShareProductCategoryDo) Take() (*insbuy.InsReportRuleSalesShareProductCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProductCategory), nil
	}
}

func (i insReportRuleSalesShareProductCategoryDo) Last() (*insbuy.InsReportRuleSalesShareProductCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProductCategory), nil
	}
}

func (i insReportRuleSalesShareProductCategoryDo) Find() ([]*insbuy.InsReportRuleSalesShareProductCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareProductCategory), err
}

func (i insReportRuleSalesShareProductCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareProductCategory, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareProductCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareProductCategoryDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareProductCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareProductCategoryDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareProductCategoryDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareProductCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareProductCategoryDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareProductCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareProductCategoryDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareProductCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareProductCategoryDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareProductCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProductCategory), nil
	}
}

func (i insReportRuleSalesShareProductCategoryDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareProductCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProductCategory), nil
	}
}

func (i insReportRuleSalesShareProductCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareProductCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareProductCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareProductCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareProductCategoryDo) Delete(models ...*insbuy.InsReportRuleSalesShareProductCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareProductCategoryDo) withDO(do gen.Dao) *insReportRuleSalesShareProductCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
