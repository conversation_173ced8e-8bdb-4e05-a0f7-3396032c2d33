// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterial(db *gorm.DB, opts ...gen.DOOption) insMaterial {
	_insMaterial := insMaterial{}

	_insMaterial.insMaterialDo.UseDB(db, opts...)
	_insMaterial.insMaterialDo.UseModel(&insbuy.InsMaterial{})

	tableName := _insMaterial.insMaterialDo.TableName()
	_insMaterial.ALL = field.NewAsterisk(tableName)
	_insMaterial.ID = field.NewUint(tableName, "id")
	_insMaterial.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterial.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterial.DeletedAt = field.NewField(tableName, "deleted_at")
	_insMaterial.MaterialSn = field.NewString(tableName, "material_sn")
	_insMaterial.CategoryId = field.NewInt(tableName, "category_id")
	_insMaterial.BrandId = field.NewInt(tableName, "brand_id")
	_insMaterial.MaterialName = field.NewString(tableName, "material_name")
	_insMaterial.ShopPrice = field.NewFloat64(tableName, "shop_price")
	_insMaterial.MarketPrice = field.NewFloat64(tableName, "market_price")
	_insMaterial.RebatePrice = field.NewFloat64(tableName, "rebate_price")
	_insMaterial.StandardPrice = field.NewFloat64(tableName, "standard_price")
	_insMaterial.MUnit = field.NewInt(tableName, "m_unit")
	_insMaterial.Spec = field.NewFloat64(tableName, "spec")
	_insMaterial.Unit = field.NewInt(tableName, "unit")
	_insMaterial.Stock = field.NewInt(tableName, "stock")
	_insMaterial.BarCode = field.NewString(tableName, "bar_code")
	_insMaterial.QrCode = field.NewString(tableName, "qr_code")
	_insMaterial.MaterialThumb = field.NewString(tableName, "material_thumb")
	_insMaterial.IsReal = field.NewInt(tableName, "is_real")
	_insMaterial.IsOnSale = field.NewInt(tableName, "is_on_sale")
	_insMaterial.IsDelete = field.NewInt(tableName, "is_delete")
	_insMaterial.SortOrder = field.NewInt(tableName, "sort_order")
	_insMaterial.IsUnique = field.NewInt(tableName, "is_unique")
	_insMaterial.ExpireDay = field.NewInt(tableName, "expire_day")
	_insMaterial.Remark = field.NewString(tableName, "remark")
	_insMaterial.AuditNo = field.NewString(tableName, "audit_no")
	_insMaterial.ApplyStatus = field.NewInt(tableName, "apply_status")
	_insMaterial.IsRecycle = field.NewInt(tableName, "is_recycle")
	_insMaterial.EmptiesType = field.NewInt(tableName, "empties_type")

	_insMaterial.fillFieldMap()

	return _insMaterial
}

type insMaterial struct {
	insMaterialDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	MaterialSn    field.String
	CategoryId    field.Int
	BrandId       field.Int
	MaterialName  field.String
	ShopPrice     field.Float64
	MarketPrice   field.Float64
	RebatePrice   field.Float64
	StandardPrice field.Float64
	MUnit         field.Int
	Spec          field.Float64
	Unit          field.Int
	Stock         field.Int
	BarCode       field.String
	QrCode        field.String
	MaterialThumb field.String
	IsReal        field.Int
	IsOnSale      field.Int
	IsDelete      field.Int
	SortOrder     field.Int
	IsUnique      field.Int
	ExpireDay     field.Int
	Remark        field.String
	AuditNo       field.String
	ApplyStatus   field.Int
	IsRecycle     field.Int
	EmptiesType   field.Int

	fieldMap map[string]field.Expr
}

func (i insMaterial) Table(newTableName string) *insMaterial {
	i.insMaterialDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterial) As(alias string) *insMaterial {
	i.insMaterialDo.DO = *(i.insMaterialDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterial) updateTableName(table string) *insMaterial {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.MaterialSn = field.NewString(table, "material_sn")
	i.CategoryId = field.NewInt(table, "category_id")
	i.BrandId = field.NewInt(table, "brand_id")
	i.MaterialName = field.NewString(table, "material_name")
	i.ShopPrice = field.NewFloat64(table, "shop_price")
	i.MarketPrice = field.NewFloat64(table, "market_price")
	i.RebatePrice = field.NewFloat64(table, "rebate_price")
	i.StandardPrice = field.NewFloat64(table, "standard_price")
	i.MUnit = field.NewInt(table, "m_unit")
	i.Spec = field.NewFloat64(table, "spec")
	i.Unit = field.NewInt(table, "unit")
	i.Stock = field.NewInt(table, "stock")
	i.BarCode = field.NewString(table, "bar_code")
	i.QrCode = field.NewString(table, "qr_code")
	i.MaterialThumb = field.NewString(table, "material_thumb")
	i.IsReal = field.NewInt(table, "is_real")
	i.IsOnSale = field.NewInt(table, "is_on_sale")
	i.IsDelete = field.NewInt(table, "is_delete")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.IsUnique = field.NewInt(table, "is_unique")
	i.ExpireDay = field.NewInt(table, "expire_day")
	i.Remark = field.NewString(table, "remark")
	i.AuditNo = field.NewString(table, "audit_no")
	i.ApplyStatus = field.NewInt(table, "apply_status")
	i.IsRecycle = field.NewInt(table, "is_recycle")
	i.EmptiesType = field.NewInt(table, "empties_type")

	i.fillFieldMap()

	return i
}

func (i *insMaterial) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterial) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 30)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["material_sn"] = i.MaterialSn
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["brand_id"] = i.BrandId
	i.fieldMap["material_name"] = i.MaterialName
	i.fieldMap["shop_price"] = i.ShopPrice
	i.fieldMap["market_price"] = i.MarketPrice
	i.fieldMap["rebate_price"] = i.RebatePrice
	i.fieldMap["standard_price"] = i.StandardPrice
	i.fieldMap["m_unit"] = i.MUnit
	i.fieldMap["spec"] = i.Spec
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["stock"] = i.Stock
	i.fieldMap["bar_code"] = i.BarCode
	i.fieldMap["qr_code"] = i.QrCode
	i.fieldMap["material_thumb"] = i.MaterialThumb
	i.fieldMap["is_real"] = i.IsReal
	i.fieldMap["is_on_sale"] = i.IsOnSale
	i.fieldMap["is_delete"] = i.IsDelete
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["is_unique"] = i.IsUnique
	i.fieldMap["expire_day"] = i.ExpireDay
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["audit_no"] = i.AuditNo
	i.fieldMap["apply_status"] = i.ApplyStatus
	i.fieldMap["is_recycle"] = i.IsRecycle
	i.fieldMap["empties_type"] = i.EmptiesType
}

func (i insMaterial) clone(db *gorm.DB) insMaterial {
	i.insMaterialDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterial) replaceDB(db *gorm.DB) insMaterial {
	i.insMaterialDo.ReplaceDB(db)
	return i
}

type insMaterialDo struct{ gen.DO }

func (i insMaterialDo) Debug() *insMaterialDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialDo) WithContext(ctx context.Context) *insMaterialDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialDo) ReadDB() *insMaterialDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialDo) WriteDB() *insMaterialDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialDo) Session(config *gorm.Session) *insMaterialDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialDo) Clauses(conds ...clause.Expression) *insMaterialDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialDo) Returning(value interface{}, columns ...string) *insMaterialDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialDo) Not(conds ...gen.Condition) *insMaterialDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialDo) Or(conds ...gen.Condition) *insMaterialDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialDo) Select(conds ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialDo) Where(conds ...gen.Condition) *insMaterialDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialDo) Order(conds ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialDo) Distinct(cols ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialDo) Omit(cols ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialDo) Group(cols ...field.Expr) *insMaterialDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialDo) Having(conds ...gen.Condition) *insMaterialDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialDo) Limit(limit int) *insMaterialDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialDo) Offset(offset int) *insMaterialDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialDo) Unscoped() *insMaterialDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialDo) Create(values ...*insbuy.InsMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialDo) CreateInBatches(values []*insbuy.InsMaterial, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialDo) Save(values ...*insbuy.InsMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialDo) First() (*insbuy.InsMaterial, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterial), nil
	}
}

func (i insMaterialDo) Take() (*insbuy.InsMaterial, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterial), nil
	}
}

func (i insMaterialDo) Last() (*insbuy.InsMaterial, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterial), nil
	}
}

func (i insMaterialDo) Find() ([]*insbuy.InsMaterial, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterial), err
}

func (i insMaterialDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterial, err error) {
	buf := make([]*insbuy.InsMaterial, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialDo) FindInBatches(result *[]*insbuy.InsMaterial, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialDo) Attrs(attrs ...field.AssignExpr) *insMaterialDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialDo) Assign(attrs ...field.AssignExpr) *insMaterialDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialDo) Joins(fields ...field.RelationField) *insMaterialDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialDo) Preload(fields ...field.RelationField) *insMaterialDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialDo) FirstOrInit() (*insbuy.InsMaterial, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterial), nil
	}
}

func (i insMaterialDo) FirstOrCreate() (*insbuy.InsMaterial, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterial), nil
	}
}

func (i insMaterialDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterial, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialDo) Delete(models ...*insbuy.InsMaterial) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialDo) withDO(do gen.Dao) *insMaterialDo {
	i.DO = *do.(*gen.DO)
	return i
}
