[insbuy]2025/07/30 - 14:35:33.817	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "abf7d84431975c6b4c4aa58998f54aa9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:28:49.805	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "b97db24a6cdc2fd763cf59490cc09465", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:29:47.069	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "122cfc2c98b20e3eda91acbd4269f446", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:33:35.763	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "949882b66809a208dcd0e3dcee4f5abb", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:37:43.113	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "2b12ebfdecd1502d993fc03ee0e5da6e", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:42:39.296	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "dd234add499007cc1b2e4235f9b8c4a9", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:44:42.850	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "3d8ed53ea76d35dd33645a9f0bb71daa", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:47:52.801	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "6fab9480845bdd2e94412f2205f86a42", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:53:03.638	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "088e555007ea3ba7f07064c77bc232e1", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 14:57:28.089	[33mwarn[0m	test/contract_transformer_test.go:479	转换存在警告或错误	{"traceId": "7b0686bfdcdaa2098c73920774b13455", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 15:06:48.521	[33mwarn[0m	test/contract_transformer_test.go:483	转换存在警告或错误	{"traceId": "861d0958a6ea67c4493b74efd0554d9a", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 15:12:58.750	[33mwarn[0m	test/contract_transformer_test.go:483	转换存在警告或错误	{"traceId": "37fab3cbcd6d401d53c406d728891b67", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 15:17:35.231	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "46f6667ff10acaba0074063e8dfadaac", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/07/30 - 15:26:38.874	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "b6c55999b75bfb279e50317dfa79a6dd", "task": "TestUniversalDataExport", "instance_code": "3A26A9D4-87B6-4E7F-B7FC-353205194E9F", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"},{"field":"tax_rate","message":"税率应在0-1之间","value":"6.0000"}]}
