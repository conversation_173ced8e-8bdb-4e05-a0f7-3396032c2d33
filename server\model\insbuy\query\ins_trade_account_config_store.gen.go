// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeAccountConfigStore(db *gorm.DB, opts ...gen.DOOption) insTradeAccountConfigStore {
	_insTradeAccountConfigStore := insTradeAccountConfigStore{}

	_insTradeAccountConfigStore.insTradeAccountConfigStoreDo.UseDB(db, opts...)
	_insTradeAccountConfigStore.insTradeAccountConfigStoreDo.UseModel(&insbuy.InsTradeAccountConfigStore{})

	tableName := _insTradeAccountConfigStore.insTradeAccountConfigStoreDo.TableName()
	_insTradeAccountConfigStore.ALL = field.NewAsterisk(tableName)
	_insTradeAccountConfigStore.ID = field.NewUint(tableName, "id")
	_insTradeAccountConfigStore.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeAccountConfigStore.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeAccountConfigStore.StoreId = field.NewUint(tableName, "store_id")
	_insTradeAccountConfigStore.ConfigId = field.NewUint(tableName, "config_id")

	_insTradeAccountConfigStore.fillFieldMap()

	return _insTradeAccountConfigStore
}

type insTradeAccountConfigStore struct {
	insTradeAccountConfigStoreDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	StoreId   field.Uint
	ConfigId  field.Uint

	fieldMap map[string]field.Expr
}

func (i insTradeAccountConfigStore) Table(newTableName string) *insTradeAccountConfigStore {
	i.insTradeAccountConfigStoreDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeAccountConfigStore) As(alias string) *insTradeAccountConfigStore {
	i.insTradeAccountConfigStoreDo.DO = *(i.insTradeAccountConfigStoreDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeAccountConfigStore) updateTableName(table string) *insTradeAccountConfigStore {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.ConfigId = field.NewUint(table, "config_id")

	i.fillFieldMap()

	return i
}

func (i *insTradeAccountConfigStore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeAccountConfigStore) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["config_id"] = i.ConfigId
}

func (i insTradeAccountConfigStore) clone(db *gorm.DB) insTradeAccountConfigStore {
	i.insTradeAccountConfigStoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeAccountConfigStore) replaceDB(db *gorm.DB) insTradeAccountConfigStore {
	i.insTradeAccountConfigStoreDo.ReplaceDB(db)
	return i
}

type insTradeAccountConfigStoreDo struct{ gen.DO }

func (i insTradeAccountConfigStoreDo) Debug() *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeAccountConfigStoreDo) WithContext(ctx context.Context) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeAccountConfigStoreDo) ReadDB() *insTradeAccountConfigStoreDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeAccountConfigStoreDo) WriteDB() *insTradeAccountConfigStoreDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeAccountConfigStoreDo) Session(config *gorm.Session) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeAccountConfigStoreDo) Clauses(conds ...clause.Expression) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeAccountConfigStoreDo) Returning(value interface{}, columns ...string) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeAccountConfigStoreDo) Not(conds ...gen.Condition) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeAccountConfigStoreDo) Or(conds ...gen.Condition) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeAccountConfigStoreDo) Select(conds ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeAccountConfigStoreDo) Where(conds ...gen.Condition) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeAccountConfigStoreDo) Order(conds ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeAccountConfigStoreDo) Distinct(cols ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeAccountConfigStoreDo) Omit(cols ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeAccountConfigStoreDo) Join(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeAccountConfigStoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeAccountConfigStoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeAccountConfigStoreDo) Group(cols ...field.Expr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeAccountConfigStoreDo) Having(conds ...gen.Condition) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeAccountConfigStoreDo) Limit(limit int) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeAccountConfigStoreDo) Offset(offset int) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeAccountConfigStoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeAccountConfigStoreDo) Unscoped() *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeAccountConfigStoreDo) Create(values ...*insbuy.InsTradeAccountConfigStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeAccountConfigStoreDo) CreateInBatches(values []*insbuy.InsTradeAccountConfigStore, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeAccountConfigStoreDo) Save(values ...*insbuy.InsTradeAccountConfigStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeAccountConfigStoreDo) First() (*insbuy.InsTradeAccountConfigStore, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfigStore), nil
	}
}

func (i insTradeAccountConfigStoreDo) Take() (*insbuy.InsTradeAccountConfigStore, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfigStore), nil
	}
}

func (i insTradeAccountConfigStoreDo) Last() (*insbuy.InsTradeAccountConfigStore, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfigStore), nil
	}
}

func (i insTradeAccountConfigStoreDo) Find() ([]*insbuy.InsTradeAccountConfigStore, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeAccountConfigStore), err
}

func (i insTradeAccountConfigStoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeAccountConfigStore, err error) {
	buf := make([]*insbuy.InsTradeAccountConfigStore, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeAccountConfigStoreDo) FindInBatches(result *[]*insbuy.InsTradeAccountConfigStore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeAccountConfigStoreDo) Attrs(attrs ...field.AssignExpr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeAccountConfigStoreDo) Assign(attrs ...field.AssignExpr) *insTradeAccountConfigStoreDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeAccountConfigStoreDo) Joins(fields ...field.RelationField) *insTradeAccountConfigStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeAccountConfigStoreDo) Preload(fields ...field.RelationField) *insTradeAccountConfigStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeAccountConfigStoreDo) FirstOrInit() (*insbuy.InsTradeAccountConfigStore, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfigStore), nil
	}
}

func (i insTradeAccountConfigStoreDo) FirstOrCreate() (*insbuy.InsTradeAccountConfigStore, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountConfigStore), nil
	}
}

func (i insTradeAccountConfigStoreDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeAccountConfigStore, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeAccountConfigStoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeAccountConfigStoreDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeAccountConfigStoreDo) Delete(models ...*insbuy.InsTradeAccountConfigStore) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeAccountConfigStoreDo) withDO(do gen.Dao) *insTradeAccountConfigStoreDo {
	i.DO = *do.(*gen.DO)
	return i
}
