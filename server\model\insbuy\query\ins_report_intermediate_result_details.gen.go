// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportIntermediateResultDetails(db *gorm.DB, opts ...gen.DOOption) insReportIntermediateResultDetails {
	_insReportIntermediateResultDetails := insReportIntermediateResultDetails{}

	_insReportIntermediateResultDetails.insReportIntermediateResultDetailsDo.UseDB(db, opts...)
	_insReportIntermediateResultDetails.insReportIntermediateResultDetailsDo.UseModel(&insbuy.InsReportIntermediateResultDetails{})

	tableName := _insReportIntermediateResultDetails.insReportIntermediateResultDetailsDo.TableName()
	_insReportIntermediateResultDetails.ALL = field.NewAsterisk(tableName)
	_insReportIntermediateResultDetails.ID = field.NewUint64(tableName, "id")
	_insReportIntermediateResultDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportIntermediateResultDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportIntermediateResultDetails.ResultId = field.NewUint64(tableName, "result_id")
	_insReportIntermediateResultDetails.StoreID = field.NewUint(tableName, "store_id")
	_insReportIntermediateResultDetails.BusinessDay = field.NewTime(tableName, "business_day")
	_insReportIntermediateResultDetails.DataKey = field.NewString(tableName, "data_key")
	_insReportIntermediateResultDetails.ResultData = field.NewField(tableName, "result_data")

	_insReportIntermediateResultDetails.fillFieldMap()

	return _insReportIntermediateResultDetails
}

type insReportIntermediateResultDetails struct {
	insReportIntermediateResultDetailsDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Time
	UpdatedAt   field.Time
	ResultId    field.Uint64
	StoreID     field.Uint
	BusinessDay field.Time
	DataKey     field.String
	ResultData  field.Field

	fieldMap map[string]field.Expr
}

func (i insReportIntermediateResultDetails) Table(newTableName string) *insReportIntermediateResultDetails {
	i.insReportIntermediateResultDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportIntermediateResultDetails) As(alias string) *insReportIntermediateResultDetails {
	i.insReportIntermediateResultDetailsDo.DO = *(i.insReportIntermediateResultDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportIntermediateResultDetails) updateTableName(table string) *insReportIntermediateResultDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ResultId = field.NewUint64(table, "result_id")
	i.StoreID = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.DataKey = field.NewString(table, "data_key")
	i.ResultData = field.NewField(table, "result_data")

	i.fillFieldMap()

	return i
}

func (i *insReportIntermediateResultDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportIntermediateResultDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["result_id"] = i.ResultId
	i.fieldMap["store_id"] = i.StoreID
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["data_key"] = i.DataKey
	i.fieldMap["result_data"] = i.ResultData
}

func (i insReportIntermediateResultDetails) clone(db *gorm.DB) insReportIntermediateResultDetails {
	i.insReportIntermediateResultDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportIntermediateResultDetails) replaceDB(db *gorm.DB) insReportIntermediateResultDetails {
	i.insReportIntermediateResultDetailsDo.ReplaceDB(db)
	return i
}

type insReportIntermediateResultDetailsDo struct{ gen.DO }

func (i insReportIntermediateResultDetailsDo) Debug() *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportIntermediateResultDetailsDo) WithContext(ctx context.Context) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportIntermediateResultDetailsDo) ReadDB() *insReportIntermediateResultDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportIntermediateResultDetailsDo) WriteDB() *insReportIntermediateResultDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportIntermediateResultDetailsDo) Session(config *gorm.Session) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportIntermediateResultDetailsDo) Clauses(conds ...clause.Expression) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportIntermediateResultDetailsDo) Returning(value interface{}, columns ...string) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportIntermediateResultDetailsDo) Not(conds ...gen.Condition) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportIntermediateResultDetailsDo) Or(conds ...gen.Condition) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportIntermediateResultDetailsDo) Select(conds ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportIntermediateResultDetailsDo) Where(conds ...gen.Condition) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportIntermediateResultDetailsDo) Order(conds ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportIntermediateResultDetailsDo) Distinct(cols ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportIntermediateResultDetailsDo) Omit(cols ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportIntermediateResultDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportIntermediateResultDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportIntermediateResultDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportIntermediateResultDetailsDo) Group(cols ...field.Expr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportIntermediateResultDetailsDo) Having(conds ...gen.Condition) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportIntermediateResultDetailsDo) Limit(limit int) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportIntermediateResultDetailsDo) Offset(offset int) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportIntermediateResultDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportIntermediateResultDetailsDo) Unscoped() *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportIntermediateResultDetailsDo) Create(values ...*insbuy.InsReportIntermediateResultDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportIntermediateResultDetailsDo) CreateInBatches(values []*insbuy.InsReportIntermediateResultDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportIntermediateResultDetailsDo) Save(values ...*insbuy.InsReportIntermediateResultDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportIntermediateResultDetailsDo) First() (*insbuy.InsReportIntermediateResultDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResultDetails), nil
	}
}

func (i insReportIntermediateResultDetailsDo) Take() (*insbuy.InsReportIntermediateResultDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResultDetails), nil
	}
}

func (i insReportIntermediateResultDetailsDo) Last() (*insbuy.InsReportIntermediateResultDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResultDetails), nil
	}
}

func (i insReportIntermediateResultDetailsDo) Find() ([]*insbuy.InsReportIntermediateResultDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportIntermediateResultDetails), err
}

func (i insReportIntermediateResultDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportIntermediateResultDetails, err error) {
	buf := make([]*insbuy.InsReportIntermediateResultDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportIntermediateResultDetailsDo) FindInBatches(result *[]*insbuy.InsReportIntermediateResultDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportIntermediateResultDetailsDo) Attrs(attrs ...field.AssignExpr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportIntermediateResultDetailsDo) Assign(attrs ...field.AssignExpr) *insReportIntermediateResultDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportIntermediateResultDetailsDo) Joins(fields ...field.RelationField) *insReportIntermediateResultDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportIntermediateResultDetailsDo) Preload(fields ...field.RelationField) *insReportIntermediateResultDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportIntermediateResultDetailsDo) FirstOrInit() (*insbuy.InsReportIntermediateResultDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResultDetails), nil
	}
}

func (i insReportIntermediateResultDetailsDo) FirstOrCreate() (*insbuy.InsReportIntermediateResultDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResultDetails), nil
	}
}

func (i insReportIntermediateResultDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportIntermediateResultDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportIntermediateResultDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportIntermediateResultDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportIntermediateResultDetailsDo) Delete(models ...*insbuy.InsReportIntermediateResultDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportIntermediateResultDetailsDo) withDO(do gen.Dao) *insReportIntermediateResultDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
