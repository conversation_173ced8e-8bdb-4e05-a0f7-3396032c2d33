package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insBookReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insBookRes "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"github.com/xtulnx/jkit-go/jtime"
)

type HyReportApi struct{}

// Summary 获取汇总数据
// @Tags      HyReport
// @Summary   获取汇总数据
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insBookReq.HyReportSummary          true  "汇总筛选条件"
// @Success   200   {object}  response.Response{data=insBookRes.HyReportSummary,msg=string}  "汇总数据"
// @Router /insReport/hy/summary [get]
func (b *HyReportApi) Summary(c *gin.Context) {
	var r insBookReq.HyReportSummary
	err := GinMustBind(c, &r)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insBookRes.HyReportSummary
	needTest := r.StartDate == "" && r.EndDate == "" && r.Mode == 1
	res, data, err := hyReportService.Summary(c.Request.Context(), r)
	var storeIds []string
	for _, v := range res.List {
		storeIds = append(storeIds, v.StoreId)
	}
	m := hyReportService.LastSummaryV2(c.Request.Context(), r, storeIds)
	for i, v := range res.List {
		if sumData, ok := m[v.StoreId]; ok {
			res.List[i].Summary.LastChargeTotal = sumData.ChargeTotal
			res.List[i].Summary.Progress = sumData.Progress
		}
	}
	if needTest && len(data) == 0 && res != nil && res.StartDate != "" && err == nil {
		startDate := jtime.Str2Date(res.StartDate)
		for i := 0; i < 10 && !startDate.IsZero(); i++ {
			r.EndDate = startDate.Format("2006-01-02")
			startDate = startDate.AddDate(0, 0, -1)
			r.StartDate = startDate.Format("2006-01-02")
			res, data, err = hyReportService.Summary(c.Request.Context(), r)
			if err != nil || len(data) > 0 {
				break
			}
		}
	}
	response.ResultErr(res, err, c)
}
