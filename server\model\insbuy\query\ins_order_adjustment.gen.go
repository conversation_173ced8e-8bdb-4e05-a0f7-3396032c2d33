// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderAdjustment(db *gorm.DB, opts ...gen.DOOption) insOrderAdjustment {
	_insOrderAdjustment := insOrderAdjustment{}

	_insOrderAdjustment.insOrderAdjustmentDo.UseDB(db, opts...)
	_insOrderAdjustment.insOrderAdjustmentDo.UseModel(&insbuy.InsOrderAdjustment{})

	tableName := _insOrderAdjustment.insOrderAdjustmentDo.TableName()
	_insOrderAdjustment.ALL = field.NewAsterisk(tableName)
	_insOrderAdjustment.ID = field.NewUint(tableName, "id")
	_insOrderAdjustment.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderAdjustment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderAdjustment.StoreId = field.NewUint(tableName, "store_id")
	_insOrderAdjustment.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insOrderAdjustment.AdjustmentType = field.NewInt(tableName, "adjustment_type")
	_insOrderAdjustment.Remark = field.NewString(tableName, "remark")
	_insOrderAdjustment.AttachmentUrl = field.NewString(tableName, "attachment_url")
	_insOrderAdjustment.OperatorId = field.NewUint(tableName, "operator_id")
	_insOrderAdjustment.Ext = field.NewField(tableName, "ext")

	_insOrderAdjustment.fillFieldMap()

	return _insOrderAdjustment
}

type insOrderAdjustment struct {
	insOrderAdjustmentDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	StoreId        field.Uint
	OpenDeskId     field.Uint
	AdjustmentType field.Int
	Remark         field.String
	AttachmentUrl  field.String
	OperatorId     field.Uint
	Ext            field.Field

	fieldMap map[string]field.Expr
}

func (i insOrderAdjustment) Table(newTableName string) *insOrderAdjustment {
	i.insOrderAdjustmentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderAdjustment) As(alias string) *insOrderAdjustment {
	i.insOrderAdjustmentDo.DO = *(i.insOrderAdjustmentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderAdjustment) updateTableName(table string) *insOrderAdjustment {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.AdjustmentType = field.NewInt(table, "adjustment_type")
	i.Remark = field.NewString(table, "remark")
	i.AttachmentUrl = field.NewString(table, "attachment_url")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insOrderAdjustment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderAdjustment) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["adjustment_type"] = i.AdjustmentType
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["attachment_url"] = i.AttachmentUrl
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["ext"] = i.Ext
}

func (i insOrderAdjustment) clone(db *gorm.DB) insOrderAdjustment {
	i.insOrderAdjustmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderAdjustment) replaceDB(db *gorm.DB) insOrderAdjustment {
	i.insOrderAdjustmentDo.ReplaceDB(db)
	return i
}

type insOrderAdjustmentDo struct{ gen.DO }

func (i insOrderAdjustmentDo) Debug() *insOrderAdjustmentDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderAdjustmentDo) WithContext(ctx context.Context) *insOrderAdjustmentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderAdjustmentDo) ReadDB() *insOrderAdjustmentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderAdjustmentDo) WriteDB() *insOrderAdjustmentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderAdjustmentDo) Session(config *gorm.Session) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderAdjustmentDo) Clauses(conds ...clause.Expression) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderAdjustmentDo) Returning(value interface{}, columns ...string) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderAdjustmentDo) Not(conds ...gen.Condition) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderAdjustmentDo) Or(conds ...gen.Condition) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderAdjustmentDo) Select(conds ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderAdjustmentDo) Where(conds ...gen.Condition) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderAdjustmentDo) Order(conds ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderAdjustmentDo) Distinct(cols ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderAdjustmentDo) Omit(cols ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderAdjustmentDo) Join(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderAdjustmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderAdjustmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderAdjustmentDo) Group(cols ...field.Expr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderAdjustmentDo) Having(conds ...gen.Condition) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderAdjustmentDo) Limit(limit int) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderAdjustmentDo) Offset(offset int) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderAdjustmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderAdjustmentDo) Unscoped() *insOrderAdjustmentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderAdjustmentDo) Create(values ...*insbuy.InsOrderAdjustment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderAdjustmentDo) CreateInBatches(values []*insbuy.InsOrderAdjustment, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderAdjustmentDo) Save(values ...*insbuy.InsOrderAdjustment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderAdjustmentDo) First() (*insbuy.InsOrderAdjustment, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustment), nil
	}
}

func (i insOrderAdjustmentDo) Take() (*insbuy.InsOrderAdjustment, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustment), nil
	}
}

func (i insOrderAdjustmentDo) Last() (*insbuy.InsOrderAdjustment, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustment), nil
	}
}

func (i insOrderAdjustmentDo) Find() ([]*insbuy.InsOrderAdjustment, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderAdjustment), err
}

func (i insOrderAdjustmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderAdjustment, err error) {
	buf := make([]*insbuy.InsOrderAdjustment, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderAdjustmentDo) FindInBatches(result *[]*insbuy.InsOrderAdjustment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderAdjustmentDo) Attrs(attrs ...field.AssignExpr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderAdjustmentDo) Assign(attrs ...field.AssignExpr) *insOrderAdjustmentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderAdjustmentDo) Joins(fields ...field.RelationField) *insOrderAdjustmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderAdjustmentDo) Preload(fields ...field.RelationField) *insOrderAdjustmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderAdjustmentDo) FirstOrInit() (*insbuy.InsOrderAdjustment, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustment), nil
	}
}

func (i insOrderAdjustmentDo) FirstOrCreate() (*insbuy.InsOrderAdjustment, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustment), nil
	}
}

func (i insOrderAdjustmentDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderAdjustment, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderAdjustmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderAdjustmentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderAdjustmentDo) Delete(models ...*insbuy.InsOrderAdjustment) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderAdjustmentDo) withDO(do gen.Dao) *insOrderAdjustmentDo {
	i.DO = *do.(*gen.DO)
	return i
}
