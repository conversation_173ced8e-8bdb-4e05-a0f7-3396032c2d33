// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSundriesMaterialSupplier(db *gorm.DB, opts ...gen.DOOption) insSundriesMaterialSupplier {
	_insSundriesMaterialSupplier := insSundriesMaterialSupplier{}

	_insSundriesMaterialSupplier.insSundriesMaterialSupplierDo.UseDB(db, opts...)
	_insSundriesMaterialSupplier.insSundriesMaterialSupplierDo.UseModel(&insbuy.InsSundriesMaterialSupplier{})

	tableName := _insSundriesMaterialSupplier.insSundriesMaterialSupplierDo.TableName()
	_insSundriesMaterialSupplier.ALL = field.NewAsterisk(tableName)
	_insSundriesMaterialSupplier.ID = field.NewUint(tableName, "id")
	_insSundriesMaterialSupplier.CreatedAt = field.NewTime(tableName, "created_at")
	_insSundriesMaterialSupplier.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSundriesMaterialSupplier.MaterialId = field.NewInt(tableName, "material_id")
	_insSundriesMaterialSupplier.SupplierId = field.NewInt(tableName, "supplier_id")
	_insSundriesMaterialSupplier.PurchasePrice = field.NewFloat64(tableName, "purchase_price")

	_insSundriesMaterialSupplier.fillFieldMap()

	return _insSundriesMaterialSupplier
}

type insSundriesMaterialSupplier struct {
	insSundriesMaterialSupplierDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	MaterialId    field.Int
	SupplierId    field.Int
	PurchasePrice field.Float64

	fieldMap map[string]field.Expr
}

func (i insSundriesMaterialSupplier) Table(newTableName string) *insSundriesMaterialSupplier {
	i.insSundriesMaterialSupplierDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSundriesMaterialSupplier) As(alias string) *insSundriesMaterialSupplier {
	i.insSundriesMaterialSupplierDo.DO = *(i.insSundriesMaterialSupplierDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSundriesMaterialSupplier) updateTableName(table string) *insSundriesMaterialSupplier {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.MaterialId = field.NewInt(table, "material_id")
	i.SupplierId = field.NewInt(table, "supplier_id")
	i.PurchasePrice = field.NewFloat64(table, "purchase_price")

	i.fillFieldMap()

	return i
}

func (i *insSundriesMaterialSupplier) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSundriesMaterialSupplier) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["supplier_id"] = i.SupplierId
	i.fieldMap["purchase_price"] = i.PurchasePrice
}

func (i insSundriesMaterialSupplier) clone(db *gorm.DB) insSundriesMaterialSupplier {
	i.insSundriesMaterialSupplierDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSundriesMaterialSupplier) replaceDB(db *gorm.DB) insSundriesMaterialSupplier {
	i.insSundriesMaterialSupplierDo.ReplaceDB(db)
	return i
}

type insSundriesMaterialSupplierDo struct{ gen.DO }

func (i insSundriesMaterialSupplierDo) Debug() *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Debug())
}

func (i insSundriesMaterialSupplierDo) WithContext(ctx context.Context) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSundriesMaterialSupplierDo) ReadDB() *insSundriesMaterialSupplierDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSundriesMaterialSupplierDo) WriteDB() *insSundriesMaterialSupplierDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSundriesMaterialSupplierDo) Session(config *gorm.Session) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSundriesMaterialSupplierDo) Clauses(conds ...clause.Expression) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSundriesMaterialSupplierDo) Returning(value interface{}, columns ...string) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSundriesMaterialSupplierDo) Not(conds ...gen.Condition) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSundriesMaterialSupplierDo) Or(conds ...gen.Condition) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSundriesMaterialSupplierDo) Select(conds ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSundriesMaterialSupplierDo) Where(conds ...gen.Condition) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSundriesMaterialSupplierDo) Order(conds ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSundriesMaterialSupplierDo) Distinct(cols ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSundriesMaterialSupplierDo) Omit(cols ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSundriesMaterialSupplierDo) Join(table schema.Tabler, on ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSundriesMaterialSupplierDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSundriesMaterialSupplierDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSundriesMaterialSupplierDo) Group(cols ...field.Expr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSundriesMaterialSupplierDo) Having(conds ...gen.Condition) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSundriesMaterialSupplierDo) Limit(limit int) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSundriesMaterialSupplierDo) Offset(offset int) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSundriesMaterialSupplierDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSundriesMaterialSupplierDo) Unscoped() *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSundriesMaterialSupplierDo) Create(values ...*insbuy.InsSundriesMaterialSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSundriesMaterialSupplierDo) CreateInBatches(values []*insbuy.InsSundriesMaterialSupplier, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSundriesMaterialSupplierDo) Save(values ...*insbuy.InsSundriesMaterialSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSundriesMaterialSupplierDo) First() (*insbuy.InsSundriesMaterialSupplier, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialSupplier), nil
	}
}

func (i insSundriesMaterialSupplierDo) Take() (*insbuy.InsSundriesMaterialSupplier, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialSupplier), nil
	}
}

func (i insSundriesMaterialSupplierDo) Last() (*insbuy.InsSundriesMaterialSupplier, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialSupplier), nil
	}
}

func (i insSundriesMaterialSupplierDo) Find() ([]*insbuy.InsSundriesMaterialSupplier, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSundriesMaterialSupplier), err
}

func (i insSundriesMaterialSupplierDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSundriesMaterialSupplier, err error) {
	buf := make([]*insbuy.InsSundriesMaterialSupplier, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSundriesMaterialSupplierDo) FindInBatches(result *[]*insbuy.InsSundriesMaterialSupplier, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSundriesMaterialSupplierDo) Attrs(attrs ...field.AssignExpr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSundriesMaterialSupplierDo) Assign(attrs ...field.AssignExpr) *insSundriesMaterialSupplierDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSundriesMaterialSupplierDo) Joins(fields ...field.RelationField) *insSundriesMaterialSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSundriesMaterialSupplierDo) Preload(fields ...field.RelationField) *insSundriesMaterialSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSundriesMaterialSupplierDo) FirstOrInit() (*insbuy.InsSundriesMaterialSupplier, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialSupplier), nil
	}
}

func (i insSundriesMaterialSupplierDo) FirstOrCreate() (*insbuy.InsSundriesMaterialSupplier, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterialSupplier), nil
	}
}

func (i insSundriesMaterialSupplierDo) FindByPage(offset int, limit int) (result []*insbuy.InsSundriesMaterialSupplier, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSundriesMaterialSupplierDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSundriesMaterialSupplierDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSundriesMaterialSupplierDo) Delete(models ...*insbuy.InsSundriesMaterialSupplier) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSundriesMaterialSupplierDo) withDO(do gen.Dao) *insSundriesMaterialSupplierDo {
	i.DO = *do.(*gen.DO)
	return i
}
