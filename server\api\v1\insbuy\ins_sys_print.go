package insbuy

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyModel "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyPrint "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/printmodel"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/printer"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/printer/escposkit"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/simplifiedchinese"
	"image"
	"image/color"
	"image/png"
	"net/http"
	"os"
)

var _ *insbuyPrint.TplBase = nil

// InsSysPrintApi 打印相关接口
//
//	[ ] 模板管理，包括预览
//	[ ] 打印渲染
//	[ ] 打印机管理
//	[ ] 提交打印任务
type InsSysPrintApi struct {
}

// TemplateRendTest 模板渲染测试
// @Tags InsSysPrint
// @Summary 模板渲染测试，输入模板内容和测试数据，输出渲染后的html 或 png
// @Description  如果什么都不传，并且是`GET`请求，那么就返回一个页面，让用户输入；
// @Description  可选传 模板内容、模板id、模板编码、测试数据；
// @Description  返回 html（默认）、json、png。
// @Description
// @Description  模板自定义函数:
// @Description
// @Description  * FmtFinancial: 格式化，按财务方式，千分位、两位小数
// @Description  * FmtDatetime: 日期格式化；参数 `dt[,format]`
// @Description  * FN.QRCode: 生成二维码；参数 `msg[,mod[,level[,size]]]`
// @Description  mod: **1**: 二维码URL, **2**: 二维码图片 base64(默认)
// @Description  level: **1**: L, **2**: M(默认), **3**: Q, **4**: H
// @Description  size: 长宽象素大小，默认128px；当尺寸不足表达二维码信息时，会自动扩大
// @Description  * FN.UrlJoin: 生成服务器访问地址，拼接访问域名。参数 action。
// @Description  * FN.UrlInvoiceApply: 生成开票地址
// @Description
// @Description  打印用的模板类型定义:
// @Description
// @Description  1. 出品单 TplOrderBill
// @Description  2. 结账单
// @Description  3. 存酒单
// @Description  4. 取酒单
// @Description  5. 商品退单
// @Description  6. 协议销账单
// @Description  7. 网络支付确认单
// @Description
// @Produce  application/json
// @Param data body insbuyReq.PrintTemplateRendTestReq false "模板内容"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Success 1 {object} insbuyPrint.TplOrderBill "模板对象：出品单"
// @Router /print/template/rend-test [post]
func (A *InsSysPrintApi) TemplateRendTest(c *gin.Context) {
	var req insbuyReq.PrintTemplateRendTestReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if c.Request.Method == http.MethodGet && req.Content == "" && req.TplTyp == 0 {
		insSysPrintService.DevSamplePage(c)
		return
	}

	h1, err := insSysPrintService.DevRender(req.PrintTemplateInfo)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	switch req.Img {
	case 0:
		response.OkWithData(gin.H{
			"content": req.Content,
			"data":    req.Data,
			"tplTyp":  req.TplTyp,
			"html":    h1,
		}, c)
	case 1:
		c.Status(http.StatusOK)
		c.Header("Content-Type", "text/html; charset=utf-8")
		_, _ = c.Writer.WriteString(h1)
	case 2, 3:
		b1, e1 := insSysPrintService.PrintContent2PngBuf(c.Request.Context(), h1, "", req.Img == 2)
		if e1 != nil {
			response.FailWithMessage(e1.Error(), c)
			return
		}
		c.Status(http.StatusOK)
		c.Header("Content-Type", "image/png")
		_, _ = c.Writer.Write(b1)
	default:
		response.FailWithMessage("无效的图片类型", c)
	}
}

// CreateInsSysPrintTemplate 创建InsSysPrintTemplate
// @Tags InsSysPrint
// @Summary 创建InsSysPrintTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyModel.InsSysPrintTemplate true "创建InsSysPrintTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /print/createInsSysPrintTemplate [post]
func (A *InsSysPrintApi) CreateInsSysPrintTemplate(c *gin.Context) {
	var insSysPrintTemplate insbuyModel.InsSysPrintTemplate
	if err := GinMustBind(c, &insSysPrintTemplate); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysPrintService.CreateInsSysPrintTemplate(&insSysPrintTemplate); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSysPrintTemplate 删除InsSysPrintTemplate
// @Tags InsSysPrint
// @Summary 删除InsSysPrintTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyModel.InsSysPrintTemplate true "删除InsSysPrintTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /print/deleteInsSysPrintTemplate [delete]
func (A *InsSysPrintApi) DeleteInsSysPrintTemplate(c *gin.Context) {
	var insSysPrintTemplate insbuyModel.InsSysPrintTemplate
	err := c.ShouldBindJSON(&insSysPrintTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysPrintService.DeleteInsSysPrintTemplate(insSysPrintTemplate); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSysPrintTemplateByIds 批量删除InsSysPrintTemplate
// @Tags InsSysPrint
// @Summary 批量删除InsSysPrintTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSysPrintTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /print/deleteInsSysPrintTemplateByIds [delete]
func (A *InsSysPrintApi) DeleteInsSysPrintTemplateByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysPrintService.DeleteInsSysPrintTemplateByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSysPrintTemplate 更新InsSysPrintTemplate
// @Tags InsSysPrint
// @Summary 更新InsSysPrintTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyModel.InsSysPrintTemplate true "更新InsSysPrintTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /print/updateInsSysPrintTemplate [put]
func (A *InsSysPrintApi) UpdateInsSysPrintTemplate(c *gin.Context) {
	var insSysPrintTemplate insbuyModel.InsSysPrintTemplate
	err := c.ShouldBindJSON(&insSysPrintTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysPrintService.UpdateInsSysPrintTemplate(insSysPrintTemplate); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSysPrintTemplate 用id查询InsSysPrintTemplate
// @Tags InsSysPrint
// @Summary 用id查询InsSysPrintTemplate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyModel.InsSysPrintTemplate true "用id查询InsSysPrintTemplate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /print/findInsSysPrintTemplate [get]
func (A *InsSysPrintApi) FindInsSysPrintTemplate(c *gin.Context) {
	var insSysPrintTemplate insbuyModel.InsSysPrintTemplate
	err := c.ShouldBindQuery(&insSysPrintTemplate)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsSysPrintTemplate, err := insSysPrintService.GetInsSysPrintTemplate(insSysPrintTemplate.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsSysPrintTemplate": reinsSysPrintTemplate}, c)
	}
}

// GetInsSysPrintTemplateList 分页获取InsSysPrintTemplate列表
// @Tags InsSysPrint
// @Summary 分页获取InsSysPrintTemplate列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSysPrintTemplateSearch true "分页获取InsSysPrintTemplate列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /print/getInsSysPrintTemplateList [get]
func (A *InsSysPrintApi) GetInsSysPrintTemplateList(c *gin.Context) {
	var pageInfo insbuyReq.InsSysPrintTemplateSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSysPrintService.GetInsSysPrintTemplateInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

// PrinterTest 打印机测试
// @Tags InsSysPrint
// @Summary 打印机测试，指定打印机打印，可以自定义内容（基于模板）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsSysPrintTestReq true "打印机测试"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /print/printerTest [post]
func (A *InsSysPrintApi) PrinterTest(c *gin.Context) {
	var req insbuyReq.InsSysPrintTestReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	p1, err := insSysDevPrinterService.FindPrinterByCode(req.PrinterCode)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	} else if p1.ID == 0 {
		response.ResultErr(nil, errno.QueryNotFound.WithMsg("打印机不存在"), c)
		return
	} else if !printer.IsSupport(printer.TPrinterType(p1.Type)) {
		response.ResultErr(nil, errno.QueryNotFound.WithMsg("打印机不支持"), c)
		return
	}

	html1, err := insSysPrintService.DevRender(req.PrintTemplateInfo)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	// TODO:

	switch printer.TPrinterType(p1.Type) {
	case printer.PrinterTypeThermalEscpos:
		conn, e1 := utils.DialTCPWithProxy(p1.Url, p1.Proxy)
		if e1 != nil {
			err = e1
		} else {
			defer conn.Close()
			if html1 != "" {
				err = insSysPrintService.PrintESCPOS(req.GetCtx(), html1, "", conn, true)
			} else {
				img1 := printer.SampleImageRuler(576, 64)
				w1, h1, data1 := escposkit.RasterImageFromGray(escposkit.ImageGray(img1))
				img2 := printer.SampleImageGradient(576, 64)
				w2, h2, data2 := escposkit.RasterImageFromGray(escposkit.ImageGray(img2))
				msg1, _ := simplifiedchinese.GB18030.NewEncoder().String(fmt.Sprintf("您的 IP: %s\n当前时间：%s\n打印机内部编号：%s", req.GetIP(), req.GetNow().Format("2006年01月02日 15时04分05秒"), p1.Code))
				qrTxt := fmt.Sprintf("@Jason.liao 说：\n  亲爱的「%s」，你好啊！你的账号是 %s。\n打印机的地址: %s，\n打印机的代理: %s", req.GetNickName(), req.GetUserName(), p1.Url, p1.Proxy)
				msg2, _ := escposkit.CMD.QRCode(qrTxt, false, 2, 1)
				err = escposkit.WriteMore(req.GetCtx(), conn,
					escposkit.CMD.Init(),
					escposkit.CMD.RasterBitmapHeader(escposkit.RasterModeNormal, w1, h1), data1,
					escposkit.CMD.Feed(1),
					escposkit.CMD.SetChineseOn(),
					escposkit.CMD.StyleAlign(escposkit.AlignCenter),
					msg1,
					msg2,
					escposkit.CMD.Feed(1),
					escposkit.CMD.RasterBitmapHeader(escposkit.RasterModeNormal, w2, h2), data2,
					escposkit.CMD.Cut(),
					escposkit.CMD.End(),
				)
			}
		}
	default:
	}
	if err != nil {
		response.ResultErr(nil, err, c)
	} else {
		response.Ok(c)
	}
}

// RenderBitmap 将位图数据渲染成图像
func RenderBitmap(data []byte, width, height int, filename string) {
	file, err := os.Create(filename)
	if err != nil {
		fmt.Println(err)
	}
	defer file.Close()

	// 创建灰度图像
	img := image.NewGray(image.Rect(0, 0, width, height))

	// 填充图像数据
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			idx := y*(width/8) + x/8
			bit := uint(x % 8)
			val := data[idx] & (0x80 >> bit)
			if val != 0 {
				img.SetGray(x, y, color.Gray{Y: 0}) // 黑色像素
			} else {
				img.SetGray(x, y, color.Gray{Y: 255}) // 白色像素
			}
		}
	}
	if err := png.Encode(file, img); err != nil {
		fmt.Println(err)
	}
	return
}

// PrinterSend 打印发送
func (A *InsSysPrintApi) PrinterSend(c *gin.Context) {
	/*img1 := printer.SampleImageRuler(576, 500)
	w2, h2, data2 := escposkit.RasterImageFromGray(escposkit.ImageGray(img1))
	RenderBitmap(data2, w2, h2, "testh.png")
	rows := h2 % 200
	data := splitImage(img1, rows, 1)
	fmt.Println("-------------")
	fmt.Println(data)
	fmt.Println(len(data))
	y := 0
	for _, item := range data {
		y += 1
		fmt.Println("-------------")
		fmt.Println(item)
		for _, v := range item {
			fmt.Println(v.Bounds().String())
			file, err := os.Create("test1" + fmt.Sprintf("%d", y) + ".png")
			if err != nil {
				fmt.Println(err)
			}
			defer file.Close()

			if err := png.Encode(file, v); err != nil {
				fmt.Println(err)
			}
		}
	}*/
	var req insbuyReq.PrinterSendReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	p1, err := insSysDevPrinterService.FindPrinterByCode(req.PrinterCode)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	} else if p1.ID == 0 {
		response.ResultErr(nil, errno.QueryNotFound.WithMsg("打印机不存在"), c)
		return
	} else if !printer.IsSupport(printer.TPrinterType(p1.Type)) {
		response.ResultErr(nil, errno.QueryNotFound.WithMsg("打印机不支持"), c)
		return
	}
	conn, e1 := utils.DialTCPWithProxy(p1.Url, p1.Proxy)
	if e1 != nil {
		err = e1
	} else {
		defer conn.Close()
		err = insSysPrintService.PrintESCPOSByBatch(req.GetCtx(), req.Html, "", conn, true)
		if err != nil {
			return
		}
	}
	if err != nil {
		response.ResultErr(nil, err, c)
	}
	response.Ok(c)
	return
}
