package insreport

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
)

// TestManualEditItem 测试用的报表项示例
type TestManualEditItem struct {
	ManualId    uint          `json:"manual_id"`
	ID          uint          `json:"id"`
	Name        string        `json:"name"`
	Amount      jtypes.JPrice `json:"amount" edittable:"true"`
	Count       int           `json:"count" edittable:"true"`
	Description string        `json:"description" edittable:"true"`
}

// GetManualEditKey 实现 ManualEditableItem 接口
func (t *TestManualEditItem) GetManualEditKey() string {
	return fmt.Sprintf("%d", t.ID)
}

// ApplyManualEdit 实现 ManualEditableItem 接口
func (t *TestManualEditItem) ApplyManualEdit(manualId uint, fields []insstore.Field) {
	// 🎯 第一步：设置 ManualId（关键步骤）
	t.ManualId = manualId

	// 字段映射
	fieldMapping := map[string]interface{}{
		"amount":      &t.Amount,
		"count":       &t.Count,
		"description": &t.Description,
	}

	// 应用手动编辑的字段
	for _, field := range fields {
		if fieldPtr, ok := fieldMapping[field.Key]; ok {
			strValue := convertToString(field.Value)
			if strValue == "" {
				continue
			}

			switch ptr := fieldPtr.(type) {
			case *jtypes.JPrice:
				if val, err := strconv.ParseFloat(strValue, 64); err == nil {
					*ptr = jtypes.JPrice(val)
				}
			case *int:
				if val, err := strconv.Atoi(strValue); err == nil {
					*ptr = val
				}
			case *string:
				*ptr = strings.TrimSpace(strValue)
			}
		}
	}
}

// ExampleUsage 使用示例
func ExampleUsage() {
	// 创建测试数据
	items := []*TestManualEditItem{
		{ID: 1, Name: "商品A", Amount: 100.0, Count: 5},
		{ID: 2, Name: "商品B", Amount: 200.0, Count: 3},
		{ID: 3, Name: "商品C", Amount: 150.0, Count: 8},
	}

	// 模拟手动编辑数据
	mockFields := []insstore.Field{
		{Key: "amount", Value: "123.45"},
		{Key: "count", Value: "10"},
		{Key: "description", Value: "手动修改的描述"},
	}

	// 应用手动编辑
	mockManualId := uint(12345)
	items[0].ApplyManualEdit(mockManualId, mockFields)

	// 验证结果
	fmt.Printf("手动编辑后的数据:\n")
	fmt.Printf("ManualId: %d (> 0 表示已手动编辑)\n", items[0].ManualId)
	fmt.Printf("Amount: %.2f (原值: 100.00)\n", float64(items[0].Amount))
	fmt.Printf("Count: %d (原值: 5)\n", items[0].Count)
	fmt.Printf("Description: %s\n", items[0].Description)

	// 前端使用示例
	for _, item := range items {
		if item.ManualId > 0 {
			fmt.Printf("商品 %s 已被手动编辑 (ManualId: %d)\n", item.Name, item.ManualId)
		} else {
			fmt.Printf("商品 %s 为系统计算值\n", item.Name)
		}
	}
}

// ValidateManualEditImplementation 验证实现是否正确
func ValidateManualEditImplementation(item *TestManualEditItem) []string {
	var issues []string

	// 检查是否实现了 ManualEditableItem 接口
	var _ ManualEditableItem = item

	// 检查是否有 ManualId 字段
	if item.ManualId == 0 {
		issues = append(issues, "ManualId 未设置，前端无法识别手动编辑状态")
	}

	// 检查 GetManualEditKey 是否返回有效值
	key := item.GetManualEditKey()
	if key == "" || key == "0" {
		issues = append(issues, "GetManualEditKey() 返回空值或无效值")
	}

	return issues
}
