package request

import (
	"context"
	"reflect"
	"strconv"
	"time"
)

// ReqWithNow 统一业务时间点
type ReqWithNow struct {
	now time.Time
}

func (R *ReqWithNow) SetNow(now time.Time) {
	R.now = now
}
func (R *ReqWithNow) GetNow() time.Time {
	if R.now.IsZero() {
		R.now = time.Now()
	}
	return R.now
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

// ReqWithIP 客户端 ip
type ReqWithIP struct {
	ip string
}

func (R *ReqWithIP) SetIP(ip string) {
	R.ip = ip
}
func (R *ReqWithIP) GetIP() string {
	return R.ip
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

// ReqWithCtx 上下文环境
type ReqWithCtx struct {
	ctx context.Context
}

func (R *ReqWithCtx) SetCtx(ctx context.Context) {
	R.ctx = ctx
}
func (R *ReqWithCtx) GetCtx() context.Context {
	return R.ctx
}
func (R *ReqWithCtx) SetCtxValue(k, v interface{}) {
	R.ctx = context.WithValue(R.ctx, k, v)
}
func (R *ReqWithCtx) GetCtxValue(k interface{}) interface{} {
	return R.ctx.Value(k)
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

// ReqWithStoreId 与店铺有关
type ReqWithStoreId struct {
	StoreId string `json:"storeId" form:"storeId" url:"storeId"` // 分店ID
}

func (R *ReqWithStoreId) GetStoreId() string {
	return R.StoreId
}
func (R *ReqWithStoreId) SetStoreId(storeId string) {
	if R.StoreId == "" {
		R.StoreId = storeId
	}
}
func (R *ReqWithStoreId) GetUintStoreId() uint {
	uStoreId, _ := strconv.ParseUint(R.StoreId, 10, 64)
	return uint(uStoreId)
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

type IUserInfoProvider interface {
	GetUserID() uint
	GetUserAuthorityId() uint
	GetUserName() string
	GetNickName() string
}

// ReqWithAuthUser 用户信息
type ReqWithAuthUser struct {
	user IUserInfoProvider
}

func (R *ReqWithAuthUser) IsValid() bool {
	//return R.user != nil
	if R.user == nil || reflect.ValueOf(R.user).IsNil() {
		return false
	}
	return true
}

func (R *ReqWithAuthUser) GetUserID() uint {
	if !R.IsValid() {
		return 0
	}
	return R.user.GetUserID()
}

func (R *ReqWithAuthUser) GetUserAuthorityId() uint {
	if !R.IsValid() {
		return 0
	}
	return R.user.GetUserAuthorityId()
}

func (R *ReqWithAuthUser) GetUserName() string {
	if !R.IsValid() {
		return ""
	}
	return R.user.GetUserName()
}

func (R *ReqWithAuthUser) GetNickName() string {
	if !R.IsValid() {
		return ""
	}
	return R.user.GetNickName()
}

func (R *ReqWithAuthUser) SetAuthUser(info IUserInfoProvider) {
	R.user = info
}
func (R *ReqWithAuthUser) GetAuthUser() IUserInfoProvider {
	return R.user
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

type ReqWithInsStoreCode struct {
	InsStoreCode string `json:"insStoreCode" form:"insStoreCode" url:"insStoreCode"`
}

func (R *ReqWithInsStoreCode) GetInsStoreCode() string {
	return R.InsStoreCode
}

func (R *ReqWithInsStoreCode) SetInsStoreCode(insStoreCode string) {
	if R.InsStoreCode == "" {
		R.InsStoreCode = insStoreCode
	}
}
