// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseCheckDayDetail(db *gorm.DB, opts ...gen.DOOption) insWarehouseCheckDayDetail {
	_insWarehouseCheckDayDetail := insWarehouseCheckDayDetail{}

	_insWarehouseCheckDayDetail.insWarehouseCheckDayDetailDo.UseDB(db, opts...)
	_insWarehouseCheckDayDetail.insWarehouseCheckDayDetailDo.UseModel(&insbuy.InsWarehouseCheckDayDetail{})

	tableName := _insWarehouseCheckDayDetail.insWarehouseCheckDayDetailDo.TableName()
	_insWarehouseCheckDayDetail.ALL = field.NewAsterisk(tableName)
	_insWarehouseCheckDayDetail.ID = field.NewUint(tableName, "id")
	_insWarehouseCheckDayDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseCheckDayDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseCheckDayDetail.CheckId = field.NewUint(tableName, "check_id")
	_insWarehouseCheckDayDetail.MaterialId = field.NewUint(tableName, "material_id")
	_insWarehouseCheckDayDetail.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insWarehouseCheckDayDetail.LastNum = field.NewFloat64(tableName, "last_num")
	_insWarehouseCheckDayDetail.NowNum = field.NewFloat64(tableName, "now_num")
	_insWarehouseCheckDayDetail.DiffNum = field.NewFloat64(tableName, "diff_num")
	_insWarehouseCheckDayDetail.Remark = field.NewString(tableName, "remark")

	_insWarehouseCheckDayDetail.fillFieldMap()

	return _insWarehouseCheckDayDetail
}

type insWarehouseCheckDayDetail struct {
	insWarehouseCheckDayDetailDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	CheckId     field.Uint
	MaterialId  field.Uint
	WarehouseId field.Uint
	LastNum     field.Float64
	NowNum      field.Float64
	DiffNum     field.Float64
	Remark      field.String

	fieldMap map[string]field.Expr
}

func (i insWarehouseCheckDayDetail) Table(newTableName string) *insWarehouseCheckDayDetail {
	i.insWarehouseCheckDayDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseCheckDayDetail) As(alias string) *insWarehouseCheckDayDetail {
	i.insWarehouseCheckDayDetailDo.DO = *(i.insWarehouseCheckDayDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseCheckDayDetail) updateTableName(table string) *insWarehouseCheckDayDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.CheckId = field.NewUint(table, "check_id")
	i.MaterialId = field.NewUint(table, "material_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.LastNum = field.NewFloat64(table, "last_num")
	i.NowNum = field.NewFloat64(table, "now_num")
	i.DiffNum = field.NewFloat64(table, "diff_num")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseCheckDayDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseCheckDayDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["check_id"] = i.CheckId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["last_num"] = i.LastNum
	i.fieldMap["now_num"] = i.NowNum
	i.fieldMap["diff_num"] = i.DiffNum
	i.fieldMap["remark"] = i.Remark
}

func (i insWarehouseCheckDayDetail) clone(db *gorm.DB) insWarehouseCheckDayDetail {
	i.insWarehouseCheckDayDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseCheckDayDetail) replaceDB(db *gorm.DB) insWarehouseCheckDayDetail {
	i.insWarehouseCheckDayDetailDo.ReplaceDB(db)
	return i
}

type insWarehouseCheckDayDetailDo struct{ gen.DO }

func (i insWarehouseCheckDayDetailDo) Debug() *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseCheckDayDetailDo) WithContext(ctx context.Context) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseCheckDayDetailDo) ReadDB() *insWarehouseCheckDayDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseCheckDayDetailDo) WriteDB() *insWarehouseCheckDayDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseCheckDayDetailDo) Session(config *gorm.Session) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseCheckDayDetailDo) Clauses(conds ...clause.Expression) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseCheckDayDetailDo) Returning(value interface{}, columns ...string) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseCheckDayDetailDo) Not(conds ...gen.Condition) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseCheckDayDetailDo) Or(conds ...gen.Condition) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseCheckDayDetailDo) Select(conds ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseCheckDayDetailDo) Where(conds ...gen.Condition) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseCheckDayDetailDo) Order(conds ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseCheckDayDetailDo) Distinct(cols ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseCheckDayDetailDo) Omit(cols ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseCheckDayDetailDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseCheckDayDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseCheckDayDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseCheckDayDetailDo) Group(cols ...field.Expr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseCheckDayDetailDo) Having(conds ...gen.Condition) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseCheckDayDetailDo) Limit(limit int) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseCheckDayDetailDo) Offset(offset int) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseCheckDayDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseCheckDayDetailDo) Unscoped() *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseCheckDayDetailDo) Create(values ...*insbuy.InsWarehouseCheckDayDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseCheckDayDetailDo) CreateInBatches(values []*insbuy.InsWarehouseCheckDayDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseCheckDayDetailDo) Save(values ...*insbuy.InsWarehouseCheckDayDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseCheckDayDetailDo) First() (*insbuy.InsWarehouseCheckDayDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDayDetail), nil
	}
}

func (i insWarehouseCheckDayDetailDo) Take() (*insbuy.InsWarehouseCheckDayDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDayDetail), nil
	}
}

func (i insWarehouseCheckDayDetailDo) Last() (*insbuy.InsWarehouseCheckDayDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDayDetail), nil
	}
}

func (i insWarehouseCheckDayDetailDo) Find() ([]*insbuy.InsWarehouseCheckDayDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseCheckDayDetail), err
}

func (i insWarehouseCheckDayDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseCheckDayDetail, err error) {
	buf := make([]*insbuy.InsWarehouseCheckDayDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseCheckDayDetailDo) FindInBatches(result *[]*insbuy.InsWarehouseCheckDayDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseCheckDayDetailDo) Attrs(attrs ...field.AssignExpr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseCheckDayDetailDo) Assign(attrs ...field.AssignExpr) *insWarehouseCheckDayDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseCheckDayDetailDo) Joins(fields ...field.RelationField) *insWarehouseCheckDayDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseCheckDayDetailDo) Preload(fields ...field.RelationField) *insWarehouseCheckDayDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseCheckDayDetailDo) FirstOrInit() (*insbuy.InsWarehouseCheckDayDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDayDetail), nil
	}
}

func (i insWarehouseCheckDayDetailDo) FirstOrCreate() (*insbuy.InsWarehouseCheckDayDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDayDetail), nil
	}
}

func (i insWarehouseCheckDayDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseCheckDayDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseCheckDayDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseCheckDayDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseCheckDayDetailDo) Delete(models ...*insbuy.InsWarehouseCheckDayDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseCheckDayDetailDo) withDO(do gen.Dao) *insWarehouseCheckDayDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
