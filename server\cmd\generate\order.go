package main

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

// Repository represents a repository with an auto-incrementing ID.
type Repository struct {
	ID   string
	Name string
	// Other repository attributes
}

// Counter represents a simple auto-incrementing counter.
type Counter struct {
	mu    sync.Mutex
	value int
}

func (c *Counter) next() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.value++
	return c.value
}

var idCounter = &Counter{}

func GenerateRepoID(name string) string {
	firstLetter := string(name[0])
	// Generate the auto-incrementing number
	number := idCounter.next()
	// Combine first letter and number
	repoID := fmt.Sprintf("%s%d", firstLetter, number)
	return repoID
}

func CreateRepository(name string) Repository {
	id := GenerateRepoID(name)
	repo := Repository{
		ID:   id,
		Name: name,
		// Set other repository attributes
	}
	return repo
}

func sup(i int64, n int) string {
	m := fmt.Sprintf("%d", i)
	for len(m) < n {
		m = fmt.Sprintf("0%s", m)
	}
	return m
}

func GetTimeTick64() int64 {
	return time.Now().UnixNano() / 1e6
}

func GetTimeTick32() int32 {
	return int32(time.Now().Unix())
}

func GetFormatTime(time time.Time) string {
	return time.Format("20060102")
}

// 基础做法 日期20191025时间戳1571987125435+3位随机数
func GenerateCode() {
	date := GetFormatTime(time.Now())
	r := rand.Intn(1000)
	code := fmt.Sprintf("%s%d%03d", date, GetTimeTick64(), r)
	fmt.Println(code, " rand ID generate successed!\n")
}

func main() {
	GenerateCode()

	t := time.Time{}
	m := t.UnixNano()/1e6 - t.UnixNano()/1e9*1e3
	fmt.Printf("m: %d\n", m)
	s := sup(1, 8)
	fmt.Printf("s: %s\n", s)

	repoName := "Sales"
	repo := CreateRepository(repoName)
	fmt.Printf("Repository ID: %s\n", repo.ID)
}
