// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsQueueChannel(db *gorm.DB, opts ...gen.DOOption) insQueueChannel {
	_insQueueChannel := insQueueChannel{}

	_insQueueChannel.insQueueChannelDo.UseDB(db, opts...)
	_insQueueChannel.insQueueChannelDo.UseModel(&insbuy.InsQueueChannel{})

	tableName := _insQueueChannel.insQueueChannelDo.TableName()
	_insQueueChannel.ALL = field.NewAsterisk(tableName)
	_insQueueChannel.ID = field.NewUint(tableName, "id")
	_insQueueChannel.CreatedAt = field.NewTime(tableName, "created_at")
	_insQueueChannel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insQueueChannel.DeletedAt = field.NewField(tableName, "deleted_at")
	_insQueueChannel.StoreId = field.NewUint(tableName, "store_id")
	_insQueueChannel.Name = field.NewString(tableName, "name")
	_insQueueChannel.Status = field.NewInt(tableName, "status")
	_insQueueChannel.MinPeople = field.NewInt(tableName, "min_people")
	_insQueueChannel.MaxPeople = field.NewInt(tableName, "max_people")
	_insQueueChannel.EstimatedWaitTime = field.NewInt(tableName, "estimated_wait_time")
	_insQueueChannel.Prefix = field.NewString(tableName, "prefix")
	_insQueueChannel.Store = insQueueChannelBelongsToStore{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Store", "insbuy.InsStore"),
	}

	_insQueueChannel.fillFieldMap()

	return _insQueueChannel
}

type insQueueChannel struct {
	insQueueChannelDo

	ALL               field.Asterisk
	ID                field.Uint
	CreatedAt         field.Time
	UpdatedAt         field.Time
	DeletedAt         field.Field
	StoreId           field.Uint
	Name              field.String
	Status            field.Int
	MinPeople         field.Int
	MaxPeople         field.Int
	EstimatedWaitTime field.Int
	Prefix            field.String
	Store             insQueueChannelBelongsToStore

	fieldMap map[string]field.Expr
}

func (i insQueueChannel) Table(newTableName string) *insQueueChannel {
	i.insQueueChannelDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insQueueChannel) As(alias string) *insQueueChannel {
	i.insQueueChannelDo.DO = *(i.insQueueChannelDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insQueueChannel) updateTableName(table string) *insQueueChannel {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Status = field.NewInt(table, "status")
	i.MinPeople = field.NewInt(table, "min_people")
	i.MaxPeople = field.NewInt(table, "max_people")
	i.EstimatedWaitTime = field.NewInt(table, "estimated_wait_time")
	i.Prefix = field.NewString(table, "prefix")

	i.fillFieldMap()

	return i
}

func (i *insQueueChannel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insQueueChannel) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["status"] = i.Status
	i.fieldMap["min_people"] = i.MinPeople
	i.fieldMap["max_people"] = i.MaxPeople
	i.fieldMap["estimated_wait_time"] = i.EstimatedWaitTime
	i.fieldMap["prefix"] = i.Prefix

}

func (i insQueueChannel) clone(db *gorm.DB) insQueueChannel {
	i.insQueueChannelDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insQueueChannel) replaceDB(db *gorm.DB) insQueueChannel {
	i.insQueueChannelDo.ReplaceDB(db)
	return i
}

type insQueueChannelBelongsToStore struct {
	db *gorm.DB

	field.RelationField
}

func (a insQueueChannelBelongsToStore) Where(conds ...field.Expr) *insQueueChannelBelongsToStore {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insQueueChannelBelongsToStore) WithContext(ctx context.Context) *insQueueChannelBelongsToStore {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insQueueChannelBelongsToStore) Session(session *gorm.Session) *insQueueChannelBelongsToStore {
	a.db = a.db.Session(session)
	return &a
}

func (a insQueueChannelBelongsToStore) Model(m *insbuy.InsQueueChannel) *insQueueChannelBelongsToStoreTx {
	return &insQueueChannelBelongsToStoreTx{a.db.Model(m).Association(a.Name())}
}

type insQueueChannelBelongsToStoreTx struct{ tx *gorm.Association }

func (a insQueueChannelBelongsToStoreTx) Find() (result *insbuy.InsStore, err error) {
	return result, a.tx.Find(&result)
}

func (a insQueueChannelBelongsToStoreTx) Append(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insQueueChannelBelongsToStoreTx) Replace(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insQueueChannelBelongsToStoreTx) Delete(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insQueueChannelBelongsToStoreTx) Clear() error {
	return a.tx.Clear()
}

func (a insQueueChannelBelongsToStoreTx) Count() int64 {
	return a.tx.Count()
}

type insQueueChannelDo struct{ gen.DO }

func (i insQueueChannelDo) Debug() *insQueueChannelDo {
	return i.withDO(i.DO.Debug())
}

func (i insQueueChannelDo) WithContext(ctx context.Context) *insQueueChannelDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insQueueChannelDo) ReadDB() *insQueueChannelDo {
	return i.Clauses(dbresolver.Read)
}

func (i insQueueChannelDo) WriteDB() *insQueueChannelDo {
	return i.Clauses(dbresolver.Write)
}

func (i insQueueChannelDo) Session(config *gorm.Session) *insQueueChannelDo {
	return i.withDO(i.DO.Session(config))
}

func (i insQueueChannelDo) Clauses(conds ...clause.Expression) *insQueueChannelDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insQueueChannelDo) Returning(value interface{}, columns ...string) *insQueueChannelDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insQueueChannelDo) Not(conds ...gen.Condition) *insQueueChannelDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insQueueChannelDo) Or(conds ...gen.Condition) *insQueueChannelDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insQueueChannelDo) Select(conds ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insQueueChannelDo) Where(conds ...gen.Condition) *insQueueChannelDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insQueueChannelDo) Order(conds ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insQueueChannelDo) Distinct(cols ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insQueueChannelDo) Omit(cols ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insQueueChannelDo) Join(table schema.Tabler, on ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insQueueChannelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insQueueChannelDo) RightJoin(table schema.Tabler, on ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insQueueChannelDo) Group(cols ...field.Expr) *insQueueChannelDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insQueueChannelDo) Having(conds ...gen.Condition) *insQueueChannelDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insQueueChannelDo) Limit(limit int) *insQueueChannelDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insQueueChannelDo) Offset(offset int) *insQueueChannelDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insQueueChannelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insQueueChannelDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insQueueChannelDo) Unscoped() *insQueueChannelDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insQueueChannelDo) Create(values ...*insbuy.InsQueueChannel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insQueueChannelDo) CreateInBatches(values []*insbuy.InsQueueChannel, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insQueueChannelDo) Save(values ...*insbuy.InsQueueChannel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insQueueChannelDo) First() (*insbuy.InsQueueChannel, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannel), nil
	}
}

func (i insQueueChannelDo) Take() (*insbuy.InsQueueChannel, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannel), nil
	}
}

func (i insQueueChannelDo) Last() (*insbuy.InsQueueChannel, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannel), nil
	}
}

func (i insQueueChannelDo) Find() ([]*insbuy.InsQueueChannel, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsQueueChannel), err
}

func (i insQueueChannelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsQueueChannel, err error) {
	buf := make([]*insbuy.InsQueueChannel, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insQueueChannelDo) FindInBatches(result *[]*insbuy.InsQueueChannel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insQueueChannelDo) Attrs(attrs ...field.AssignExpr) *insQueueChannelDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insQueueChannelDo) Assign(attrs ...field.AssignExpr) *insQueueChannelDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insQueueChannelDo) Joins(fields ...field.RelationField) *insQueueChannelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insQueueChannelDo) Preload(fields ...field.RelationField) *insQueueChannelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insQueueChannelDo) FirstOrInit() (*insbuy.InsQueueChannel, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannel), nil
	}
}

func (i insQueueChannelDo) FirstOrCreate() (*insbuy.InsQueueChannel, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannel), nil
	}
}

func (i insQueueChannelDo) FindByPage(offset int, limit int) (result []*insbuy.InsQueueChannel, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insQueueChannelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insQueueChannelDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insQueueChannelDo) Delete(models ...*insbuy.InsQueueChannel) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insQueueChannelDo) withDO(do gen.Dao) *insQueueChannelDo {
	i.DO = *do.(*gen.DO)
	return i
}
