package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
)

// 外部数据：订单数据
//
// 只读方式

type InsExternalPayApi struct {
}

// DailyReport 统计订单数量
// @Tags InsExternalPay
// @Summary 统计订单数量
// @Description 统计订单数量
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsExternalPayDailyReportReq false "统计订单数量"
// @Success 200 {string} string "{"success":true,"data":insbuyResp.InsExternalPayDailyReportResp,"msg":"获取成功"}"
// @Router /ext/pay/dailyReport [post]
func (InsExternalPayApi) DailyReport(c *gin.Context) {
	var req insbuyReq.InsExternalPayDailyReportReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var resp *insbuyResp.InsExternalPayDailyReportResp
	resp, err := extPayService.DailyReport(c, req)
	if req.Export() {
		_, err = insImportService.ExcelCommonList(c, insbuy.ETDailyReport.ToInt(), resp.List)
		if err != nil {
			return
		}
		return
	}
	response.ResultErr(resp, err, c)
}
