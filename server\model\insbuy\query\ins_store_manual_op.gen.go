// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreManualOp(db *gorm.DB, opts ...gen.DOOption) insStoreManualOp {
	_insStoreManualOp := insStoreManualOp{}

	_insStoreManualOp.insStoreManualOpDo.UseDB(db, opts...)
	_insStoreManualOp.insStoreManualOpDo.UseModel(&insbuy.InsStoreManualOp{})

	tableName := _insStoreManualOp.insStoreManualOpDo.TableName()
	_insStoreManualOp.ALL = field.NewAsterisk(tableName)
	_insStoreManualOp.ID = field.NewUint(tableName, "id")
	_insStoreManualOp.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreManualOp.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreManualOp.DeletedAt = field.NewField(tableName, "deleted_at")
	_insStoreManualOp.Key = field.NewString(tableName, "key")
	_insStoreManualOp.IndexKey = field.NewString(tableName, "index_key")
	_insStoreManualOp.StoreId = field.NewUint(tableName, "store_id")
	_insStoreManualOp.BusinessDay = field.NewTime(tableName, "business_day")
	_insStoreManualOp.BusinessType = field.NewInt(tableName, "business_type")
	_insStoreManualOp.BusinessCode = field.NewString(tableName, "business_code")
	_insStoreManualOp.Remark = field.NewString(tableName, "remark")
	_insStoreManualOp.Ext = field.NewField(tableName, "ext")
	_insStoreManualOp.OperatorId = field.NewUint(tableName, "operator_id")
	_insStoreManualOp.RelationSn = field.NewString(tableName, "relation_sn")

	_insStoreManualOp.fillFieldMap()

	return _insStoreManualOp
}

type insStoreManualOp struct {
	insStoreManualOpDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Key          field.String
	IndexKey     field.String
	StoreId      field.Uint
	BusinessDay  field.Time
	BusinessType field.Int
	BusinessCode field.String
	Remark       field.String
	Ext          field.Field
	OperatorId   field.Uint
	RelationSn   field.String

	fieldMap map[string]field.Expr
}

func (i insStoreManualOp) Table(newTableName string) *insStoreManualOp {
	i.insStoreManualOpDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreManualOp) As(alias string) *insStoreManualOp {
	i.insStoreManualOpDo.DO = *(i.insStoreManualOpDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreManualOp) updateTableName(table string) *insStoreManualOp {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Key = field.NewString(table, "key")
	i.IndexKey = field.NewString(table, "index_key")
	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.BusinessType = field.NewInt(table, "business_type")
	i.BusinessCode = field.NewString(table, "business_code")
	i.Remark = field.NewString(table, "remark")
	i.Ext = field.NewField(table, "ext")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.RelationSn = field.NewString(table, "relation_sn")

	i.fillFieldMap()

	return i
}

func (i *insStoreManualOp) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreManualOp) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["key"] = i.Key
	i.fieldMap["index_key"] = i.IndexKey
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["business_type"] = i.BusinessType
	i.fieldMap["business_code"] = i.BusinessCode
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["relation_sn"] = i.RelationSn
}

func (i insStoreManualOp) clone(db *gorm.DB) insStoreManualOp {
	i.insStoreManualOpDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreManualOp) replaceDB(db *gorm.DB) insStoreManualOp {
	i.insStoreManualOpDo.ReplaceDB(db)
	return i
}

type insStoreManualOpDo struct{ gen.DO }

func (i insStoreManualOpDo) Debug() *insStoreManualOpDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreManualOpDo) WithContext(ctx context.Context) *insStoreManualOpDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreManualOpDo) ReadDB() *insStoreManualOpDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreManualOpDo) WriteDB() *insStoreManualOpDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreManualOpDo) Session(config *gorm.Session) *insStoreManualOpDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreManualOpDo) Clauses(conds ...clause.Expression) *insStoreManualOpDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreManualOpDo) Returning(value interface{}, columns ...string) *insStoreManualOpDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreManualOpDo) Not(conds ...gen.Condition) *insStoreManualOpDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreManualOpDo) Or(conds ...gen.Condition) *insStoreManualOpDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreManualOpDo) Select(conds ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreManualOpDo) Where(conds ...gen.Condition) *insStoreManualOpDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreManualOpDo) Order(conds ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreManualOpDo) Distinct(cols ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreManualOpDo) Omit(cols ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreManualOpDo) Join(table schema.Tabler, on ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreManualOpDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreManualOpDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreManualOpDo) Group(cols ...field.Expr) *insStoreManualOpDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreManualOpDo) Having(conds ...gen.Condition) *insStoreManualOpDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreManualOpDo) Limit(limit int) *insStoreManualOpDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreManualOpDo) Offset(offset int) *insStoreManualOpDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreManualOpDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreManualOpDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreManualOpDo) Unscoped() *insStoreManualOpDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreManualOpDo) Create(values ...*insbuy.InsStoreManualOp) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreManualOpDo) CreateInBatches(values []*insbuy.InsStoreManualOp, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreManualOpDo) Save(values ...*insbuy.InsStoreManualOp) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreManualOpDo) First() (*insbuy.InsStoreManualOp, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreManualOp), nil
	}
}

func (i insStoreManualOpDo) Take() (*insbuy.InsStoreManualOp, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreManualOp), nil
	}
}

func (i insStoreManualOpDo) Last() (*insbuy.InsStoreManualOp, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreManualOp), nil
	}
}

func (i insStoreManualOpDo) Find() ([]*insbuy.InsStoreManualOp, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreManualOp), err
}

func (i insStoreManualOpDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreManualOp, err error) {
	buf := make([]*insbuy.InsStoreManualOp, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreManualOpDo) FindInBatches(result *[]*insbuy.InsStoreManualOp, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreManualOpDo) Attrs(attrs ...field.AssignExpr) *insStoreManualOpDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreManualOpDo) Assign(attrs ...field.AssignExpr) *insStoreManualOpDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreManualOpDo) Joins(fields ...field.RelationField) *insStoreManualOpDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreManualOpDo) Preload(fields ...field.RelationField) *insStoreManualOpDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreManualOpDo) FirstOrInit() (*insbuy.InsStoreManualOp, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreManualOp), nil
	}
}

func (i insStoreManualOpDo) FirstOrCreate() (*insbuy.InsStoreManualOp, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreManualOp), nil
	}
}

func (i insStoreManualOpDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreManualOp, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreManualOpDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreManualOpDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreManualOpDo) Delete(models ...*insbuy.InsStoreManualOp) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreManualOpDo) withDO(do gen.Dao) *insStoreManualOpDo {
	i.DO = *do.(*gen.DO)
	return i
}
