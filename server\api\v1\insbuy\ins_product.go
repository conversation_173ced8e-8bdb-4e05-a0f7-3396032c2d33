package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyRes "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsProductApi struct {
}

// CreateInsProductCategory 创建InsProductCategory
// @Tags InsProductCategory
// @Summary 创建InsProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductCategoryReq true "创建InsProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsProductCategory [post]
func (insProductApi *InsProductApi) CreateInsProductCategory(c *gin.Context) {
	var insProductCategory insbuyReq.InsProductCategoryReq
	err := GinMustBind(c, &insProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsProductCategory(insProductCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsProductCategory 删除InsProductCategory
// @Tags InsProductCategory
// @Summary 删除InsProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsProductCategory true "删除InsProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insProduct/deleteInsProductCategory [delete]
func (insProductApi *InsProductApi) DeleteInsProductCategory(c *gin.Context) {
	var insProductCategory insbuy.InsProductCategory
	err := c.ShouldBindJSON(&insProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductCategory(insProductCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsProductCategoryByIds 批量删除InsProductCategory
// @Tags InsProductCategory
// @Summary 批量删除InsProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insProduct/deleteInsProductCategoryByIds [delete]
func (insProductApi *InsProductApi) DeleteInsProductCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsProductCategory 更新InsProductCategory
// @Tags InsProductCategory
// @Summary 更新InsProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductCategoryReq true "更新InsProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductCategory [put]
func (insProductApi *InsProductApi) UpdateInsProductCategory(c *gin.Context) {
	var insProductCategory insbuyReq.InsProductCategoryReq
	err := GinMustBind(c, &insProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductCategory(insProductCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsProductCategory 用id查询InsProductCategory
// @Tags InsProductCategory
// @Summary 用id查询InsProductCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsProductCategory true "用id查询InsProductCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findInsProductCategory [get]
func (insProductApi *InsProductApi) FindInsProductCategory(c *gin.Context) {
	var insProductCategory insbuy.InsProductCategory
	err := c.ShouldBindQuery(&insProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsProductCategory, err := insProductService.GetInsProductCategory(insProductCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsProductCategory": reinsProductCategory}, c)
	}
}

// GetInsProductCategoryList 分页获取InsProductCategory列表
// @Tags InsProductCategory
// @Summary 分页获取InsProductCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductCategorySearch true "分页获取InsProductCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getInsProductCategoryList [get]
func (insProductApi *InsProductApi) GetInsProductCategoryList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductCategorySearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insProductService.GetInsProductCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsProduct 创建InsProduct
// @Tags InsProduct
// @Summary 创建InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductReq true "创建InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsProduct [post]
func (insProductApi *InsProductApi) CreateInsProduct(c *gin.Context) {
	var req insbuyReq.InsProductReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insProductService.CreateInsProduct(c, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// CreateInsProductBatch 批量创建InsProduct
// @Tags InsProduct
// @Summary 批量创建InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsProductBatchReq true "批量创建InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsProductBatch [post]
func (insProductApi *InsProductApi) CreateInsProductBatch(c *gin.Context) {
	var req insbuyReq.CreateInsProductBatchReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsProductBatch(c, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsProduct 删除InsProduct
// @Tags InsProduct
// @Summary 删除InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsProduct true "删除InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insProduct/deleteInsProduct [delete]
func (insProductApi *InsProductApi) DeleteInsProduct(c *gin.Context) {
	var insProduct insbuy.InsProduct
	err := c.ShouldBindJSON(&insProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProduct(c, insProduct); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsProductByIds 批量删除InsProduct
// @Tags InsProduct
// @Summary 批量删除InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insProduct/deleteInsProductByIds [delete]
func (insProductApi *InsProductApi) DeleteInsProductByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsProduct 更新InsProduct
// @Tags InsProduct
// @Summary 更新InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductReq true "更新InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProduct [put]
func (insProductApi *InsProductApi) UpdateInsProduct(c *gin.Context) {
	var req insbuyReq.InsProductReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insProductService.UpdateInsProduct(c, req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// UpdateInsProductBatch 批量更新InsProduct
// @Tags InsProduct
// @Summary 批量更新InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsProductBatchReq true "批量更新InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/UpdateInsProductBatch [put]
func (insProductApi *InsProductApi) UpdateInsProductBatch(c *gin.Context) {
	var req insbuyReq.UpdateInsProductBatchReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductBatch(req); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("批量更新成功", c)
	}
}

// BatchUpdateProductFields 批量修改商品指定字段
// @Tags InsProduct
// @Summary 批量修改商品指定字段
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchUpdateProductFieldsReq true "批量修改商品指定字段"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/batchUpdateProductFields [put]
func (insProductApi *InsProductApi) BatchUpdateProductFields(c *gin.Context) {
	var req insbuyReq.BatchUpdateProductFieldsReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.BatchUpdateProductFields(req); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("批量更新成功", c)
	}
}

// UpdateInsProductScanCode 批量修改商品是否可扫码点
// @Tags InsProduct
// @Summary 批量修改商品是否可扫码点
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateScanCodeProductBatchReq true "批量修改商品是否可扫码点"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/UpdateInsProductScanCode [put]
func (insProductApi *InsProductApi) UpdateInsProductScanCode(c *gin.Context) {
	var req insbuyReq.UpdateScanCodeProductBatchReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductScanCode(req); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("批量更新成功", c)
	}
}

// UpdateInsProductScanTips 批量修改商品是否需要到吧台点单提示
// @Tags InsProduct
// @Summary 批量修改商品是否需要到吧台点单提示
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateScanCodeProductBatchReq true "批量修改商品是否需要到吧台点单提示"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductScanTips [put]
func (insProductApi *InsProductApi) UpdateInsProductScanTips(c *gin.Context) {
	var req insbuyReq.UpdateScanCodeProductBatchReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductScanTips(req); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("批量更新成功", c)
	}
}

// UpdateInsProductStatus 更新InsProduct
// @Tags InsProduct
// @Summary 更新InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsProduct true "更新InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductStatus [put]
func (insProductApi *InsProductApi) UpdateInsProductStatus(c *gin.Context) {
	var insProduct insbuy.InsProduct
	err := c.ShouldBindJSON(&insProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductStatus(insProduct); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateInsProductStatusBatch 批量更新InsProduct上下架状态
// @Tags InsProduct
// @Summary 批量更新InsProduct上下架状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsProductStatusReq true "批量更新InsProduct上下架状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/UpdateInsProductStatusBatch [put]
func (insProductApi *InsProductApi) UpdateInsProductStatusBatch(c *gin.Context) {
	var req insbuyReq.UpdateInsProductStatusReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductStatusBatch(req); err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage("批量更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("批量更新成功", c)
	}
}

// FindInsProduct 用id查询InsProduct
// @Tags InsProduct
// @Summary 用id查询InsProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsProduct true "用id查询InsProduct"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findInsProduct [get]
func (insProductApi *InsProductApi) FindInsProduct(c *gin.Context) {
	var insProduct insbuy.InsProduct
	err := c.ShouldBindQuery(&insProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsProduct, err := insProductService.GetInsProduct(insProduct.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsProduct": reinsProduct}, c)
	}
}

// GetInsProductList 分页获取InsProduct列表
// @Tags InsProduct
// @Summary 分页获取InsProduct列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductSearch true "分页获取InsProduct列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getInsProductList [get]
func (insProductApi *InsProductApi) GetInsProductList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Export() {
		list, e1 := insProductService.ExportInsProduct(pageInfo)
		if e1 != nil {
			err = e1
			return
		}
		_, e := insImportService.ExcelCommonList(c, insbuy.ETProductList.ToInt(), list)
		if e != nil {
			return
		}
		return
	}
	if list, total, err := insProductService.GetInsProductInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetUserInsProductList 分页获取InsProduct列表
// @Tags InsProduct
// @Summary 分页获取InsProduct列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetUserInsProductList true "分页获取InsProduct列表"
// @Success   200   {object}  response.Response{data=insbuyRes.GetUserInsProductListResp,msg=string}  "商品列表"
// @Router /insProduct/getUserInsProductList [get]
func (insProductApi *InsProductApi) GetUserInsProductList(c *gin.Context) {
	var r insbuyReq.GetUserInsProductList
	if err := GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insbuyRes.GetUserInsProductListResp
	res, err := insProductService.GetUserInsProductList(r)
	response.ResultErr(res, err, c)
}

// GetGiveInsProductList 分页获取销售可赠送的商品列表
// @Tags InsProduct
// @Summary 分页获取销售可赠送的商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetGiveInsProductList true "分页获取销售可赠送的商品列表"
// @Success   200   {object}  response.Response{data=insbuyRes.GetUserInsProductListResp,msg=string}  "商品列表"
// @Router /insProduct/getGiveInsProductList [get]
func (insProductApi *InsProductApi) GetGiveInsProductList(c *gin.Context) {
	var r insbuyReq.GetGiveInsProductList
	if err := GinMustBind(c, &r); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var res *insbuyRes.GetUserInsProductListResp
	res, err := insProductService.GetGiveInsProductList(r)
	response.ResultErr(res, err, c)
}

// CreateInsBrand 创建InsBrand
// @Tags InsBrand
// @Summary 创建InsBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsBrand true "创建InsBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsBrand [post]
func (insBrandApi *InsProductApi) CreateInsBrand(c *gin.Context) {
	var insBrand insbuy.InsBrand
	err := c.ShouldBindJSON(&insBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsBrand(&insBrand); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsBrand 删除InsBrand
// @Tags InsBrand
// @Summary 删除InsBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsBrand true "删除InsBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insProduct/deleteInsBrand [delete]
func (insBrandApi *InsProductApi) DeleteInsBrand(c *gin.Context) {
	var insBrand insbuy.InsBrand
	err := c.ShouldBindJSON(&insBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsBrand(insBrand); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsBrandByIds 批量删除InsBrand
// @Tags InsBrand
// @Summary 批量删除InsBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insProduct/deleteInsBrandByIds [delete]
func (insBrandApi *InsProductApi) DeleteInsBrandByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsBrandByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsBrand 更新InsBrand
// @Tags InsBrand
// @Summary 更新InsBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsBrand true "更新InsBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsBrand [put]
func (insBrandApi *InsProductApi) UpdateInsBrand(c *gin.Context) {
	var insBrand insbuy.InsBrand
	err := c.ShouldBindJSON(&insBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsBrand(insBrand); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsBrand 用id查询InsBrand
// @Tags InsBrand
// @Summary 用id查询InsBrand
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsBrand true "用id查询InsBrand"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findInsBrand [get]
func (insBrandApi *InsProductApi) FindInsBrand(c *gin.Context) {
	var insBrand insbuy.InsBrand
	err := c.ShouldBindQuery(&insBrand)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsBrand, err := insProductService.GetInsBrand(insBrand.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsBrand": reinsBrand}, c)
	}
}

// GetInsBrandList 分页获取InsBrand列表
// @Tags InsBrand
// @Summary 分页获取InsBrand列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsBrandSearch true "分页获取InsBrand列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getInsBrandList [get]
func (insBrandApi *InsProductApi) GetInsBrandList(c *gin.Context) {
	var pageInfo insbuyReq.InsBrandSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insProductService.GetInsBrandInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsProductPackage 创建InsProductPackage
// @Tags InsProductPackage
// @Summary 创建InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductPackageReq true "创建InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsProductPackage [post]
func (insProductPackageApi *InsProductApi) CreateInsProductPackage(c *gin.Context) {
	var insProductPackage insbuyReq.InsProductPackageReq
	err := GinMustBind(c, &insProductPackage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsProductPackage(&insProductPackage); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsProductPackage 删除InsProductPackage
// @Tags InsProductPackage
// @Summary 删除InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsProductPackage true "删除InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insProduct/deleteInsProductPackage [delete]
func (insProductPackageApi *InsProductApi) DeleteInsProductPackage(c *gin.Context) {
	var insProductPackage insbuy.InsProductPackage
	err := c.ShouldBindJSON(&insProductPackage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductPackage(insProductPackage); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsProductPackageByIds 批量删除InsProductPackage
// @Tags InsProductPackage
// @Summary 批量删除InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insProduct/deleteInsProductPackageByIds [delete]
func (insProductPackageApi *InsProductApi) DeleteInsProductPackageByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.DeleteInsProductPackageByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsProductPackage 更新InsProductPackage
// @Tags InsProductPackage
// @Summary 更新InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductPackageReq true "更新InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductPackage [put]
func (insProductPackageApi *InsProductApi) UpdateInsProductPackage(c *gin.Context) {
	var insProductPackage insbuyReq.InsProductPackageReq
	err := GinMustBind(c, &insProductPackage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.UpdateInsProductPackage(insProductPackage); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsProductPackage 用id查询InsProductPackage
// @Tags InsProductPackage
// @Summary 用id查询InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsProductPackage true "用id查询InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findInsProductPackage [get]
func (insProductPackageApi *InsProductApi) FindInsProductPackage(c *gin.Context) {
	var insProductPackage insbuyReq.FindInsProductPackage
	err := c.ShouldBindQuery(&insProductPackage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsProductPackage, err := insProductService.GetInsProductPackage(insProductPackage.Id); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsProductPackage, c)
	}
}

// GetInsProductPackageList 分页获取InsProductPackage列表
// @Tags InsProductPackage
// @Summary 分页获取InsProductPackage列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductPackageSearch true "分页获取InsProductPackage列表"
// @Success   200   {object}  response.Response{data=insbuyRes.InsProductPackageItem,msg=string}  "开台"
// @Router /insProduct/getInsProductPackageList [get]
func (insProductPackageApi *InsProductApi) GetInsProductPackageList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductPackageSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Export() {
		list, e1 := insProductService.ExportPackage(pageInfo)
		if e1 != nil {
			err = e1
			return
		}
		_, e := insImportService.ExcelCommonList(c, insbuy.ETPackageList.ToInt(), list)
		if e != nil {
			return
		}
		return
	}
	if list, total, err := insProductService.GetInsProductPackageInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsProductActivity 创建商品活动
// @Tags InsProductActivity
// @Summary 创建商品活动
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductActivityCreate true "创建商品活动"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createInsProductActivity [post]
func (insProductPackageApi *InsProductApi) CreateInsProductActivity(c *gin.Context) {
	var insProductActivity insbuyReq.InsProductActivityCreate
	err := c.ShouldBindJSON(&insProductActivity)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateInsProductActivity(c, insProductActivity); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateInsProductActivity 更新商品活动
// @Tags InsProductActivity
// @Summary 更新商品活动
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductActivityItem true "更新商品活动"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductActivity [put]
func (insProductPackageApi *InsProductApi) UpdateInsProductActivity(c *gin.Context) {
	var insProductActivity insbuyReq.InsProductActivityItem
	err := c.ShouldBindJSON(&insProductActivity)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	verify := utils.Rules{
		"ActivityId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insProductActivity, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insProductService.UpdateInsProductActivity(c, insProductActivity); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateInsProductActivityStatus 更新商品活动状态
// @Tags InsProductActivity
// @Summary 更新商品活动状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsProductActivityStatusUpdate true "更新商品活动状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insProduct/updateInsProductActivityStatus [put]
func (insProductPackageApi *InsProductApi) UpdateInsProductActivityStatus(c *gin.Context) {
	var insProductActivity insbuyReq.InsProductActivityStatusUpdate
	err := c.ShouldBindJSON(&insProductActivity)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	verify := utils.Rules{
		"ActivityId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insProductActivity, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insProductService.UpdateInsProductActivityStatus(c, insProductActivity); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsProductActivity 用主键ID查询商品活动
// @Tags InsProductActivity
// @Summary 用id查询商品活动
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductActivitySearch true "用主键ID查询商品活动"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/findInsProductActivity [get]
func (insProductPackageApi *InsProductApi) FindInsProductActivity(c *gin.Context) {
	var insProductActivity insbuyReq.InsProductActivitySearch
	err := c.ShouldBindQuery(&insProductActivity)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"ActivityId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insProductActivity, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if reinsProductActivity, err := insProductService.GetInsProductActivity(insProductActivity.ActivityId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsProductActivity": reinsProductActivity}, c)
	}
}

// GetInsProductActivityList 分页获取商品活动列表
// @Tags InsProductActivity
// @Summary 分页获取商品活动列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductActivitySearch true "分页获取商品活动列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getInsProductActivityList [get]
func (insProductPackageApi *InsProductApi) GetInsProductActivityList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductActivitySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	verify := utils.Rules{
		"page":     {utils.NotEmpty()},
		"pageSize": {utils.NotEmpty()},
	}
	if err := utils.Verify(pageInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insProductService.GetInsProductActivityInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsProductActivityFormula 获取商品活动方式列表
// @Tags InsProductActivity
// @Summary 获取商品活动方式列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductActivityFormula true "获取商品活动方式列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getInsProductActivityFormula [get]
func (insProductPackageApi *InsProductApi) GetInsProductActivityFormula(c *gin.Context) {
	var pageInfo insbuyReq.InsProductActivityFormula
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insProductService.GetInsProductActivityFormula(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// ClientSideFindInsProductPackage 用id查询InsProductPackage
// @Tags InsProductPackage
// @Summary 用id查询InsProductPackage
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsProductPackage true "用id查询InsProductPackage"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insProduct/clientSideFindInsProductPackage [get]
func (insProductPackageApi *InsProductApi) ClientSideFindInsProductPackage(c *gin.Context) {
	var insProductPackage insbuyReq.ClientSideFindInsProductPackage
	err := c.ShouldBindQuery(&insProductPackage)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsProductPackage, err := insProductService.ClientSideFindInsProductPackage(insProductPackage.Id); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsProductPackage, c)
	}
}

// CreateClearing 创建沽清
// @Tags InsProduct
// @Summary 创建沽清
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateClearingReq true "创建沽清"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insProduct/createClearing [post]
func (insProductPackageApi *InsProductApi) CreateClearing(c *gin.Context) {
	var req insbuyReq.CreateClearingReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateClearing(req); err != nil {
		global.GVA_LOG.Error("沽清失败!", zap.Error(err))
		response.FailWithMessage("沽清失败", c)
	} else {
		response.OkWithMessage("沽清成功", c)
	}
}

// CancelClearing 取消沽清
// @Tags InsProduct
// @Summary 取消沽清
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CancelClearingReq true "取消沽清"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"取消成功"}"
// @Router /insProduct/cancelClearing [delete]
func (insProductPackageApi *InsProductApi) CancelClearing(c *gin.Context) {
	var req insbuyReq.CancelClearingReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CancelClearing(req); err != nil {
		global.GVA_LOG.Error("取消沽清失败!", zap.Error(err))
		response.FailWithMessage("取消沽清失败", c)
	} else {
		response.OkWithMessage("取消沽清成功", c)
	}
}

// ScanProductList 扫码商品接口
// @Tags InsProduct
// @Summary 扫码商品接口
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ScanProductListReq true "扫码商品接口"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"扫码成功"}"
// @Router /insProduct/scanProductList [get]
func (insProductPackageApi *InsProductApi) ScanProductList(c *gin.Context) {
	var req insbuyReq.ScanProductListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insProductService.ScanProductList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(res, c)
	}
}

// PackageMigrate 套餐迁移
// @Tags InsProduct
// @Summary 套餐迁移
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PackageMigrateReq true "套餐迁移"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"迁移成功"}"
// @Router /insProduct/packageMigrate [post]
func (insProductPackageApi *InsProductApi) PackageMigrate(c *gin.Context) {
	var req insbuyReq.PackageMigrateReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.PackageMigrate(req); err != nil {
		global.GVA_LOG.Error("迁移失败!", zap.Error(err))
		response.FailWithMessage("迁移失败", c)
	} else {
		response.OkWithMessage("迁移成功", c)
	}
}
