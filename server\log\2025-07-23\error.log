[insbuy]2025/07/23 - 13:29:43.868	[31<PERSON><PERSON>r[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 13:39:06.577	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 13:36:13.271	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 13:41:12.418	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 14:06:28.816	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 14:34:55.825	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 14:39:04.847	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 17:03:44.503	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 17:48:58.920	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 18:33:31.810	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/23 - 18:33:35.477	[31merror[0m	middleware/error.go:45	[Recovery from panic]	{"error": "runtime error: invalid memory address or nil pointer dereference", "request": "GET /insbuy-api/insReport/financial?page=1&pageSize=200&type=%E9%9B%86%E5%9B%A2%E7%AE%A1%E6%8A%A5&typ=1&startDate=2025-01-01&endDate=2025-07-31&report_entity2=%E4%B9%90%E5%9B%AD-%E9%80%9A%E7%A5%A8&report_entity2=%E4%B9%90%E5%9B%AD-%E7%9B%B4%E6%92%AD&financialType=35 HTTP/1.1\r\nHost: 127.0.0.1:8888\r\nAccept: */*\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUser-Agent: Apifox/1.0.0 (https://apifox.com)\r\nX-Store-Id: 6\r\nX-Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMDNmODRhNDQtNWIyYS00YmJiLWFhYTEtMTJiNDViYjEyMmNiIiwiSUQiOjIxLCJVc2VybmFtZSI6InNhcHBhbiIsIk5pY2tOYW1lIjoic2FwcGFuIiwiQXV0aG9yaXR5SWQiOjY2NiwiQnVmZmVyVGltZSI6ODY0MDAsImlzcyI6InFtUGx1cyIsImF1ZCI6WyJHVkEiXSwiZXhwIjoxNzUzNjkwMjc4LCJuYmYiOjE3NTMwODU0Nzh9.o1oTnl4GEzfewaGs9OEj7LHNWy0k81kdo67rpwK64CU\r\n\r\n", "stack": "goroutine 73 [running]:\nruntime/debug.Stack()\n\tD:/go/server/go1.20.1/src/runtime/debug/stack.go:24 +0x65\ngithub.com/flipped-aurora/gin-vue-admin/server/middleware.GinRecovery.func1.1()\n\tD:/go/code/insbuy-code/insbuy.srv/server/middleware/error.go:48 +0x2cc\npanic({0x4941f20, 0x4568750})\n\tD:/go/server/go1.20.1/src/runtime/panic.go:884 +0x213\ngithub.com/flipped-aurora/gin-vue-admin/server/service/insbuy.InsReportService.ReportFinancial({}, 0xc002f1b5c0)\n\tD:/go/code/insbuy-code/insbuy.srv/server/service/insbuy/ins_report_financial.go:60 +0x31d\nreflect.Value.call({0x4894120?, 0xc0022ba850?, 0xc002309140?}, {0x4e4020b, 0x4}, {0xc002060420, 0x1, 0x4c00bc0?})\n\tD:/go/server/go1.20.1/src/reflect/value.go:586 +0xb07\nreflect.Value.Call({0x4894120?, 0xc0022ba850?, 0xc002f1b5c0?}, {0xc002060420?, 0xc0003ef4d0?, 0xc00205ede0?})\n\tD:/go/server/go1.20.1/src/reflect/value.go:370 +0xbc\ngithub.com/flipped-aurora/gin-vue-admin/server/api/apikit.WrapperApi0.func1(0xc001fa1f00)\n\tD:/go/code/insbuy-code/insbuy.srv/server/api/apikit/utils.go:238 +0x6d2\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/flipped-aurora/gin-vue-admin/server/middleware.CasbinHandler.func1(0xc001fa1f00)\n\tD:/go/code/insbuy-code/insbuy.srv/server/middleware/casbin_rbac.go:36 +0x28d\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/flipped-aurora/gin-vue-admin/server/middleware.JWTAuth.func1(0xc001fa1f00)\n\tD:/go/code/insbuy-code/insbuy.srv/server/middleware/jwt.go:77 +0xb6b\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/flipped-aurora/gin-vue-admin/server/middleware.GinRecovery.func1(0xc001fa1f00)\n\tD:/go/code/insbuy-code/insbuy.srv/server/middleware/error.go:59 +0x76\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1(0xc001fa1f00)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/recovery.go:102 +0x82\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1(0xc001fa1f00)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/logger.go:240 +0xe7\ngithub.com/gin-gonic/gin.(*Context).Next(...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/context.go:174\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest(0xc0034ca680, 0xc001fa1f00)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/gin.go:620 +0x66b\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP(0xc0034ca680, {0x661e1c0?, 0xc00018aee0}, 0xc001fa1d00)\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.9.0/gin.go:576 +0x1dd\nnet/http.serverHandler.ServeHTTP({0xc0020e2480?}, {0x661e1c0, 0xc00018aee0}, 0xc001fa1d00)\n\tD:/go/server/go1.20.1/src/net/http/server.go:2936 +0x316\nnet/http.(*conn).serve(0xc0034a05a0, {0x661fac0, 0xc0022d13e0})\n\tD:/go/server/go1.20.1/src/net/http/server.go:1995 +0x612\ncreated by net/http.(*Server).Serve\n\tD:/go/server/go1.20.1/src/net/http/server.go:3089 +0x5ed\n"}
[insbuy]2025/07/23 - 18:38:11.761	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
