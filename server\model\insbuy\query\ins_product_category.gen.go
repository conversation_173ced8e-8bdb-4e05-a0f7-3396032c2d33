// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductCategory(db *gorm.DB, opts ...gen.DOOption) insProductCategory {
	_insProductCategory := insProductCategory{}

	_insProductCategory.insProductCategoryDo.UseDB(db, opts...)
	_insProductCategory.insProductCategoryDo.UseModel(&insbuy.InsProductCategory{})

	tableName := _insProductCategory.insProductCategoryDo.TableName()
	_insProductCategory.ALL = field.NewAsterisk(tableName)
	_insProductCategory.ID = field.NewUint(tableName, "id")
	_insProductCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductCategory.StoreId = field.NewUint(tableName, "store_id")
	_insProductCategory.CatName = field.NewString(tableName, "cat_name")
	_insProductCategory.Keywords = field.NewString(tableName, "keywords")
	_insProductCategory.CatDesc = field.NewString(tableName, "cat_desc")
	_insProductCategory.ParentId = field.NewInt(tableName, "parent_id")
	_insProductCategory.SortOrder = field.NewInt(tableName, "sort_order")
	_insProductCategory.ShowInNav = field.NewInt(tableName, "show_in_nav")
	_insProductCategory.Style = field.NewString(tableName, "style")
	_insProductCategory.IsShow = field.NewInt(tableName, "is_show")
	_insProductCategory.Grade = field.NewInt(tableName, "grade")
	_insProductCategory.CatType = field.NewInt(tableName, "cat_type")
	_insProductCategory.ExpireDay = field.NewInt(tableName, "expire_day")
	_insProductCategory.BlukExpireDay = field.NewInt(tableName, "bluk_expire_day")

	_insProductCategory.fillFieldMap()

	return _insProductCategory
}

type insProductCategory struct {
	insProductCategoryDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	StoreId       field.Uint
	CatName       field.String
	Keywords      field.String
	CatDesc       field.String
	ParentId      field.Int
	SortOrder     field.Int
	ShowInNav     field.Int
	Style         field.String
	IsShow        field.Int
	Grade         field.Int
	CatType       field.Int
	ExpireDay     field.Int
	BlukExpireDay field.Int

	fieldMap map[string]field.Expr
}

func (i insProductCategory) Table(newTableName string) *insProductCategory {
	i.insProductCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductCategory) As(alias string) *insProductCategory {
	i.insProductCategoryDo.DO = *(i.insProductCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductCategory) updateTableName(table string) *insProductCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.CatName = field.NewString(table, "cat_name")
	i.Keywords = field.NewString(table, "keywords")
	i.CatDesc = field.NewString(table, "cat_desc")
	i.ParentId = field.NewInt(table, "parent_id")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.ShowInNav = field.NewInt(table, "show_in_nav")
	i.Style = field.NewString(table, "style")
	i.IsShow = field.NewInt(table, "is_show")
	i.Grade = field.NewInt(table, "grade")
	i.CatType = field.NewInt(table, "cat_type")
	i.ExpireDay = field.NewInt(table, "expire_day")
	i.BlukExpireDay = field.NewInt(table, "bluk_expire_day")

	i.fillFieldMap()

	return i
}

func (i *insProductCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["cat_name"] = i.CatName
	i.fieldMap["keywords"] = i.Keywords
	i.fieldMap["cat_desc"] = i.CatDesc
	i.fieldMap["parent_id"] = i.ParentId
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["show_in_nav"] = i.ShowInNav
	i.fieldMap["style"] = i.Style
	i.fieldMap["is_show"] = i.IsShow
	i.fieldMap["grade"] = i.Grade
	i.fieldMap["cat_type"] = i.CatType
	i.fieldMap["expire_day"] = i.ExpireDay
	i.fieldMap["bluk_expire_day"] = i.BlukExpireDay
}

func (i insProductCategory) clone(db *gorm.DB) insProductCategory {
	i.insProductCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductCategory) replaceDB(db *gorm.DB) insProductCategory {
	i.insProductCategoryDo.ReplaceDB(db)
	return i
}

type insProductCategoryDo struct{ gen.DO }

func (i insProductCategoryDo) Debug() *insProductCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductCategoryDo) WithContext(ctx context.Context) *insProductCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductCategoryDo) ReadDB() *insProductCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductCategoryDo) WriteDB() *insProductCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductCategoryDo) Session(config *gorm.Session) *insProductCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductCategoryDo) Clauses(conds ...clause.Expression) *insProductCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductCategoryDo) Returning(value interface{}, columns ...string) *insProductCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductCategoryDo) Not(conds ...gen.Condition) *insProductCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductCategoryDo) Or(conds ...gen.Condition) *insProductCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductCategoryDo) Select(conds ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductCategoryDo) Where(conds ...gen.Condition) *insProductCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductCategoryDo) Order(conds ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductCategoryDo) Distinct(cols ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductCategoryDo) Omit(cols ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductCategoryDo) Group(cols ...field.Expr) *insProductCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductCategoryDo) Having(conds ...gen.Condition) *insProductCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductCategoryDo) Limit(limit int) *insProductCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductCategoryDo) Offset(offset int) *insProductCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductCategoryDo) Unscoped() *insProductCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductCategoryDo) Create(values ...*insbuy.InsProductCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductCategoryDo) CreateInBatches(values []*insbuy.InsProductCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductCategoryDo) Save(values ...*insbuy.InsProductCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductCategoryDo) First() (*insbuy.InsProductCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductCategory), nil
	}
}

func (i insProductCategoryDo) Take() (*insbuy.InsProductCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductCategory), nil
	}
}

func (i insProductCategoryDo) Last() (*insbuy.InsProductCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductCategory), nil
	}
}

func (i insProductCategoryDo) Find() ([]*insbuy.InsProductCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductCategory), err
}

func (i insProductCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductCategory, err error) {
	buf := make([]*insbuy.InsProductCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductCategoryDo) FindInBatches(result *[]*insbuy.InsProductCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductCategoryDo) Attrs(attrs ...field.AssignExpr) *insProductCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductCategoryDo) Assign(attrs ...field.AssignExpr) *insProductCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductCategoryDo) Joins(fields ...field.RelationField) *insProductCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductCategoryDo) Preload(fields ...field.RelationField) *insProductCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductCategoryDo) FirstOrInit() (*insbuy.InsProductCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductCategory), nil
	}
}

func (i insProductCategoryDo) FirstOrCreate() (*insbuy.InsProductCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductCategory), nil
	}
}

func (i insProductCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductCategoryDo) Delete(models ...*insbuy.InsProductCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductCategoryDo) withDO(do gen.Dao) *insProductCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
