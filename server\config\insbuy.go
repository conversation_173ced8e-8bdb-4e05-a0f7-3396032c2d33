package config

// InsBuy Ins系统配置
type InsBuy struct {
	StoreCode string `mapstructure:"store-code" json:"store-code" yaml:"store-code"` // 门店编号，为空时表示总店

	ExtPay ExtPayInsBuy `mapstructure:"ext-pay" json:"ext-pay" yaml:"ext-pay"` // 扩展服务-订单相关
}

type DbInsBuy struct {
	Dialect  string `mapstructure:"dialect" json:"dialect" yaml:"dialect"`    // 数据库类型
	Dsn      string `mapstructure:"dsn" json:"dsn" yaml:"dsn"`                // 数据库连接
	Prefix   string `mapstructure:"prefix" json:"prefix" yaml:"prefix"`       // 表前缀
	Singular bool   `mapstructure:"singular" json:"singular" yaml:"singular"` //是否开启全局禁用复数，true表示开启
}

type ExtPayInsBuy struct {
	Db DbInsBuy `mapstructure:"db" json:"db" yaml:"db"` // 数据库
}
