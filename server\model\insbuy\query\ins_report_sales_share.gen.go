// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportSalesShare(db *gorm.DB, opts ...gen.DOOption) insReportSalesShare {
	_insReportSalesShare := insReportSalesShare{}

	_insReportSalesShare.insReportSalesShareDo.UseDB(db, opts...)
	_insReportSalesShare.insReportSalesShareDo.UseModel(&insbuy.InsReportSalesShare{})

	tableName := _insReportSalesShare.insReportSalesShareDo.TableName()
	_insReportSalesShare.ALL = field.NewAsterisk(tableName)
	_insReportSalesShare.ID = field.NewUint(tableName, "id")
	_insReportSalesShare.StoreId = field.NewUint(tableName, "store_id")
	_insReportSalesShare.BusinessDay = field.NewTime(tableName, "business_day")
	_insReportSalesShare.UserId = field.NewUint(tableName, "user_id")
	_insReportSalesShare.Type = field.NewInt(tableName, "type")
	_insReportSalesShare.Status = field.NewInt(tableName, "status")
	_insReportSalesShare.Amount = field.NewFloat64(tableName, "amount")
	_insReportSalesShare.Adjustment = field.NewFloat64(tableName, "adjustment")
	_insReportSalesShare.OriginValue = field.NewFloat64(tableName, "origin_value")
	_insReportSalesShare.RuleId = field.NewUint(tableName, "rule_id")
	_insReportSalesShare.ProductId = field.NewUint(tableName, "product_id")
	_insReportSalesShare.Quantity = field.NewInt(tableName, "quantity")
	_insReportSalesShare.OrderId = field.NewUint(tableName, "order_id")
	_insReportSalesShare.OrgId = field.NewUint(tableName, "org_id")
	_insReportSalesShare.Ext = field.NewField(tableName, "ext")
	_insReportSalesShare.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportSalesShare.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportSalesShare.Store = insReportSalesShareBelongsToStore{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Store", "insbuy.InsStore"),
	}

	_insReportSalesShare.User = insReportSalesShareBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "system.SysUser"),
		Authority: struct {
			field.RelationField
			DataAuthorityId struct {
				field.RelationField
			}
			SysBaseMenus struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Authority", "system.SysAuthority"),
			DataAuthorityId: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Authority.DataAuthorityId", "system.SysAuthority"),
			},
			SysBaseMenus: struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("User.Authority.SysBaseMenus", "system.SysBaseMenu"),
				Parameters: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
				},
				MenuBtn: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
				},
				SysAuthoritys: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
				},
			},
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Authority.Users", "system.SysUser"),
			},
		},
		Authorities: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Authorities", "system.SysAuthority"),
		},
	}

	_insReportSalesShare.fillFieldMap()

	return _insReportSalesShare
}

type insReportSalesShare struct {
	insReportSalesShareDo

	ALL         field.Asterisk
	ID          field.Uint
	StoreId     field.Uint
	BusinessDay field.Time
	UserId      field.Uint
	Type        field.Int
	Status      field.Int
	Amount      field.Float64
	Adjustment  field.Float64
	OriginValue field.Float64
	RuleId      field.Uint
	ProductId   field.Uint
	Quantity    field.Int
	OrderId     field.Uint
	OrgId       field.Uint
	Ext         field.Field
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Store       insReportSalesShareBelongsToStore

	User insReportSalesShareBelongsToUser

	fieldMap map[string]field.Expr
}

func (i insReportSalesShare) Table(newTableName string) *insReportSalesShare {
	i.insReportSalesShareDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportSalesShare) As(alias string) *insReportSalesShare {
	i.insReportSalesShareDo.DO = *(i.insReportSalesShareDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportSalesShare) updateTableName(table string) *insReportSalesShare {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.UserId = field.NewUint(table, "user_id")
	i.Type = field.NewInt(table, "type")
	i.Status = field.NewInt(table, "status")
	i.Amount = field.NewFloat64(table, "amount")
	i.Adjustment = field.NewFloat64(table, "adjustment")
	i.OriginValue = field.NewFloat64(table, "origin_value")
	i.RuleId = field.NewUint(table, "rule_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.Quantity = field.NewInt(table, "quantity")
	i.OrderId = field.NewUint(table, "order_id")
	i.OrgId = field.NewUint(table, "org_id")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")

	i.fillFieldMap()

	return i
}

func (i *insReportSalesShare) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportSalesShare) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 19)
	i.fieldMap["id"] = i.ID
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["type"] = i.Type
	i.fieldMap["status"] = i.Status
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["adjustment"] = i.Adjustment
	i.fieldMap["origin_value"] = i.OriginValue
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["quantity"] = i.Quantity
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["org_id"] = i.OrgId
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt

}

func (i insReportSalesShare) clone(db *gorm.DB) insReportSalesShare {
	i.insReportSalesShareDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportSalesShare) replaceDB(db *gorm.DB) insReportSalesShare {
	i.insReportSalesShareDo.ReplaceDB(db)
	return i
}

type insReportSalesShareBelongsToStore struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportSalesShareBelongsToStore) Where(conds ...field.Expr) *insReportSalesShareBelongsToStore {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportSalesShareBelongsToStore) WithContext(ctx context.Context) *insReportSalesShareBelongsToStore {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportSalesShareBelongsToStore) Session(session *gorm.Session) *insReportSalesShareBelongsToStore {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportSalesShareBelongsToStore) Model(m *insbuy.InsReportSalesShare) *insReportSalesShareBelongsToStoreTx {
	return &insReportSalesShareBelongsToStoreTx{a.db.Model(m).Association(a.Name())}
}

type insReportSalesShareBelongsToStoreTx struct{ tx *gorm.Association }

func (a insReportSalesShareBelongsToStoreTx) Find() (result *insbuy.InsStore, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportSalesShareBelongsToStoreTx) Append(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportSalesShareBelongsToStoreTx) Replace(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportSalesShareBelongsToStoreTx) Delete(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportSalesShareBelongsToStoreTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportSalesShareBelongsToStoreTx) Count() int64 {
	return a.tx.Count()
}

type insReportSalesShareBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Authority struct {
		field.RelationField
		DataAuthorityId struct {
			field.RelationField
		}
		SysBaseMenus struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}
		Users struct {
			field.RelationField
		}
	}
	Authorities struct {
		field.RelationField
	}
}

func (a insReportSalesShareBelongsToUser) Where(conds ...field.Expr) *insReportSalesShareBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportSalesShareBelongsToUser) WithContext(ctx context.Context) *insReportSalesShareBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportSalesShareBelongsToUser) Session(session *gorm.Session) *insReportSalesShareBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportSalesShareBelongsToUser) Model(m *insbuy.InsReportSalesShare) *insReportSalesShareBelongsToUserTx {
	return &insReportSalesShareBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type insReportSalesShareBelongsToUserTx struct{ tx *gorm.Association }

func (a insReportSalesShareBelongsToUserTx) Find() (result *system.SysUser, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportSalesShareBelongsToUserTx) Append(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportSalesShareBelongsToUserTx) Replace(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportSalesShareBelongsToUserTx) Delete(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportSalesShareBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportSalesShareBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type insReportSalesShareDo struct{ gen.DO }

func (i insReportSalesShareDo) Debug() *insReportSalesShareDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportSalesShareDo) WithContext(ctx context.Context) *insReportSalesShareDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportSalesShareDo) ReadDB() *insReportSalesShareDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportSalesShareDo) WriteDB() *insReportSalesShareDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportSalesShareDo) Session(config *gorm.Session) *insReportSalesShareDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportSalesShareDo) Clauses(conds ...clause.Expression) *insReportSalesShareDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportSalesShareDo) Returning(value interface{}, columns ...string) *insReportSalesShareDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportSalesShareDo) Not(conds ...gen.Condition) *insReportSalesShareDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportSalesShareDo) Or(conds ...gen.Condition) *insReportSalesShareDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportSalesShareDo) Select(conds ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportSalesShareDo) Where(conds ...gen.Condition) *insReportSalesShareDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportSalesShareDo) Order(conds ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportSalesShareDo) Distinct(cols ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportSalesShareDo) Omit(cols ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportSalesShareDo) Join(table schema.Tabler, on ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportSalesShareDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportSalesShareDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportSalesShareDo) Group(cols ...field.Expr) *insReportSalesShareDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportSalesShareDo) Having(conds ...gen.Condition) *insReportSalesShareDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportSalesShareDo) Limit(limit int) *insReportSalesShareDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportSalesShareDo) Offset(offset int) *insReportSalesShareDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportSalesShareDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportSalesShareDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportSalesShareDo) Unscoped() *insReportSalesShareDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportSalesShareDo) Create(values ...*insbuy.InsReportSalesShare) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportSalesShareDo) CreateInBatches(values []*insbuy.InsReportSalesShare, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportSalesShareDo) Save(values ...*insbuy.InsReportSalesShare) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportSalesShareDo) First() (*insbuy.InsReportSalesShare, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportSalesShare), nil
	}
}

func (i insReportSalesShareDo) Take() (*insbuy.InsReportSalesShare, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportSalesShare), nil
	}
}

func (i insReportSalesShareDo) Last() (*insbuy.InsReportSalesShare, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportSalesShare), nil
	}
}

func (i insReportSalesShareDo) Find() ([]*insbuy.InsReportSalesShare, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportSalesShare), err
}

func (i insReportSalesShareDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportSalesShare, err error) {
	buf := make([]*insbuy.InsReportSalesShare, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportSalesShareDo) FindInBatches(result *[]*insbuy.InsReportSalesShare, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportSalesShareDo) Attrs(attrs ...field.AssignExpr) *insReportSalesShareDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportSalesShareDo) Assign(attrs ...field.AssignExpr) *insReportSalesShareDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportSalesShareDo) Joins(fields ...field.RelationField) *insReportSalesShareDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportSalesShareDo) Preload(fields ...field.RelationField) *insReportSalesShareDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportSalesShareDo) FirstOrInit() (*insbuy.InsReportSalesShare, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportSalesShare), nil
	}
}

func (i insReportSalesShareDo) FirstOrCreate() (*insbuy.InsReportSalesShare, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportSalesShare), nil
	}
}

func (i insReportSalesShareDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportSalesShare, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportSalesShareDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportSalesShareDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportSalesShareDo) Delete(models ...*insbuy.InsReportSalesShare) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportSalesShareDo) withDO(do gen.Dao) *insReportSalesShareDo {
	i.DO = *do.(*gen.DO)
	return i
}
