# 财务公式表达式系统 - 简化JSON格式示例

## 概述

基于您的反馈，我们简化了JSON公式格式，去除了不必要的元数据字段，保持系统的简洁性和实用性。

## 简化的JSON结构

### 1. 新的JSON格式

```json
{
  "expression": "[营业收入] - [营业成本]",
  "references": [1, 2],
  "description": "毛利润计算公式"
}
```

### 2. 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `expression` | string | 用户友好的显示表达式（使用分类名称） |
| `references` | array | 引用的分类ID列表 |
| `description` | string | 公式描述 |

**去除的字段：**
- ~~`version`~~ - 版本管理（不需要）
- ~~`storage_expr`~~ - 存储表达式（系统内部自动转换）
- ~~`code`~~ - 分类编码（从配置中获取）
- ~~`ref_type`~~ - 引用类型（从配置中判断）
- ~~`is_valid`~~ - 有效性标记（实时验证）
- ~~`metadata`~~ - 复杂元数据（简化为description）

## 实际业务场景示例

### 场景1：毛利润计算

**业务需求**：毛利润 = 营业收入 - 营业成本

**简化JSON配置**：
```json
{
  "expression": "[营业收入] - [营业成本]",
  "references": [1, 2],
  "description": "毛利润计算：营业收入减去营业成本"
}
```

**配置中心存储**：
```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "item_value": {
    "id": 100,
    "category_name": "毛利润",
    "is_calculated": 1,
    "calculation_formula": "{\"expression\":\"[营业收入] - [营业成本]\",\"references\":[1,2],\"description\":\"毛利润计算：营业收入减去营业成本\"}"
  }
}
```

### 场景2：毛利率计算

**业务需求**：毛利率 = 毛利润 / 营业收入 × 100%

**简化JSON配置**：
```json
{
  "expression": "[毛利润] / [营业收入] %",
  "references": [100, 1],
  "description": "毛利率：毛利润占营业收入的百分比"
}
```

### 场景3：期间费用合计

**业务需求**：期间费用合计 = 销售费用 + 管理费用 + 财务费用

**简化JSON配置**：
```json
{
  "expression": "[销售费用] + [管理费用] + [财务费用]",
  "references": [3, 4, 5],
  "description": "期间费用合计：三项费用的总和"
}
```

### 场景4：复合表达式

**业务需求**：毛利率 = (营业收入 - 营业成本) / 营业收入 × 100%

**简化JSON配置**：
```json
{
  "expression": "([营业收入] - [营业成本]) / [营业收入] %",
  "references": [1, 2],
  "description": "毛利率：复合计算公式"
}
```

## 代码使用示例

### 1. 创建简化JSON公式

```go
// 创建简化JSON处理器
configs := getCostTypeConfigs()
processor := NewSimpleJSONProcessor(configs)

// 创建公式
formulaJSON, err := processor.CreateFormulaJSON(
    "[营业收入] - [营业成本]",  // 表达式
    "毛利润计算公式",           // 描述
)
if err != nil {
    log.Printf("创建公式失败: %v", err)
    return
}

// 结果：{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润计算公式"}
```

### 2. 解析和验证公式

```go
// 验证公式
errors := processor.ValidateFormulaJSON(formulaJSON)
if len(errors) > 0 {
    for _, err := range errors {
        log.Printf("验证错误: %s - %s", err.Type, err.Message)
    }
}

// 获取显示表达式
displayExpr, err := processor.GetDisplayExpression(formulaJSON)
if err != nil {
    log.Printf("获取显示表达式失败: %v", err)
    return
}

fmt.Printf("显示表达式: %s", displayExpr)
// 输出: 显示表达式: [营业收入] - [营业成本]
```

### 3. 系统内部处理

```go
// 系统内部自动转换为存储格式进行计算
func (sjp *SimpleJSONProcessor) executeSimpleFormulaCalculation(config CostTypeConfigItem, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) *FinancialSummaryItem {
    // 解析JSON
    formulaJSON, err := sjp.builder.BuildFromJSON(config.CalculationFormula)
    if err != nil {
        // 如果不是JSON格式，直接作为表达式处理
        return sjp.executeDirectFormula(config.CalculationFormula, dataMap, timeColumns)
    }
    
    // 使用表达式进行计算
    return sjp.executeDirectFormula(formulaJSON.Expression, dataMap, timeColumns)
}
```

## 前端集成示例

### 1. 公式编辑器

```typescript
interface SimpleFormulaConfig {
  expression: string;
  references: number[];
  description: string;
}

// 保存公式
const saveFormula = async (expression: string, description: string) => {
  const response = await fetch('/api/formula/create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      expression: expression,
      description: description
    })
  });
  
  const formulaJSON = await response.text();
  console.log('生成的JSON:', formulaJSON);
};

// 使用示例
saveFormula('[营业收入] - [营业成本]', '毛利润计算公式');
```

### 2. 公式显示组件

```tsx
const SimpleFormulaDisplay: React.FC<{formulaJSON: string}> = ({formulaJSON}) => {
  const [config, setConfig] = useState<SimpleFormulaConfig | null>(null);
  
  useEffect(() => {
    try {
      const parsed = JSON.parse(formulaJSON);
      setConfig(parsed);
    } catch (error) {
      console.error('解析公式JSON失败:', error);
    }
  }, [formulaJSON]);
  
  if (!config) return <div>加载中...</div>;
  
  return (
    <div className="formula-display">
      <div className="expression">{config.expression}</div>
      <div className="description">{config.description}</div>
      <div className="references">
        引用分类: {config.references.join(', ')}
      </div>
    </div>
  );
};
```

## 优势对比

### 简化前的复杂JSON
```json
{
  "version": "1.0",
  "expression": "[营业收入] - [营业成本]",
  "storage_expr": "#1 - #2",
  "references": [
    {
      "id": 1,
      "name": "营业收入",
      "code": "REVENUE",
      "ref_type": "direct",
      "is_valid": true
    },
    {
      "id": 2,
      "name": "营业成本",
      "code": "COST",
      "ref_type": "direct",
      "is_valid": true
    }
  ],
  "metadata": {
    "created_at": "2025-01-01T10:00:00Z",
    "updated_at": "2025-01-01T10:00:00Z",
    "created_by": "admin",
    "updated_by": "admin",
    "description": "毛利润计算公式",
    "tags": ["利润", "核心指标"],
    "business_rule": "营业收入减去营业成本"
  }
}
```

### 简化后的简洁JSON
```json
{
  "expression": "[营业收入] - [营业成本]",
  "references": [1, 2],
  "description": "毛利润计算公式"
}
```

### 优势总结

1. **简洁性**：JSON大小减少约80%
2. **易读性**：结构清晰，一目了然
3. **易维护**：减少冗余字段，降低维护成本
4. **高效性**：解析和处理速度更快
5. **实用性**：保留核心功能，去除不必要的复杂性

## 数据一致性保障

### 1. 自动ID引用
- 系统内部自动将分类名称转换为ID引用进行计算
- 确保分类名称变更不影响计算结果

### 2. 实时验证
- 每次使用时验证引用的分类是否存在
- 自动更新references数组中的ID列表

### 3. 向后兼容
- 支持旧格式的自动转换
- 支持纯表达式字符串的直接处理

这个简化的设计既保证了系统的功能完整性，又大大提升了易用性和维护性，是一个更加实用的解决方案。
