# 待审核列表筛选规则文档

## 查询条件构建规范

### 1. 使用 gen.Condition 数组

```go
// ✅ 正确的实现方式
var conditions []gen.Condition
{
    // 固定条件
    conditions = append(conditions, db.Status.Eq("pending"))
    
    // 动态条件
    if req.UserID > 0 {
        conditions = append(conditions, db.UserID.Eq(req.UserID))
    }
    if req.LevelID > 0 {
        conditions = append(conditions, db.LevelID.Eq(req.LevelID))
    }
    
    // 时间条件
    if startTime := ParseDatetime(req.StartTime); !startTime.IsZero() {
        conditions = append(conditions, db.CreatedAt.Gte(startTime))
    }
    if endTime := ParseDatetime(req.EndTime); !endTime.IsZero() {
        endTime = endTime.AddDate(0, 0, 1)
        conditions = append(conditions, db.CreatedAt.Lt(endTime))
    }
}
dao := q.Where(conditions...)
```

### 2. 避免的实现方式

```go
// ❌ 避免链式调用
q = q.Where(db.Status.Eq("pending"))
if req.UserID > 0 {
    q = q.Where(db.UserID.Eq(req.UserID))
}
// ... 其他条件
```

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2024-01-18 | 初始版本，定义基础筛选规则 | Assistant |

---

**注意**: 本文档应该与代码实现保持同步，任何修改都应该同时更新文档。 