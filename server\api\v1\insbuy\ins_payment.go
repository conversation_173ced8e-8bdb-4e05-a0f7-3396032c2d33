package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsPaymentApi struct{}

var (
	TradeAccountVerify = utils.Rules{
		"TradeAccountId": {utils.NotEmpty()},
	}
)

// GetPaymentList 获取支付方式列表
// @Tags InsPayment
// @Summary 获取支付方式列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetPaymentListReq true "获取支付方式列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/getPaymentList [get]
func (InsPaymentApi *InsPaymentApi) GetPaymentList(c *gin.Context) {
	var req insbuyReq.GetPaymentListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insPaymentService.GetPaymentList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// PaymentAdminList 获取管理端支付方式列表
// @Tags InsPayment
// @Summary 获取管理端支付方式列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PaymentAdminListReq true "获取管理端支付方式列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/paymentAdminList [get]
func (InsPaymentApi *InsPaymentApi) PaymentAdminList(c *gin.Context) {
	var req insbuyReq.PaymentAdminListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insPaymentService.PaymentAdminList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// GetNetPayLogList 网络支付明细单
// @Tags InsPayment
// @Summary 网络支付明细单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetNetPayLogListReq true "网络支付明细单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/getNetPayLogList [get]
func (InsPaymentApi *InsPaymentApi) GetNetPayLogList(c *gin.Context) {
	var req insbuyReq.GetNetPayLogListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insPaymentService.GetNetPayLogList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		if req.Export() {
			exportType := insbuy.ETNetPayLogList.ToInt()
			if req.IsNotNetPay == 1 {
				exportType = insbuy.ETBillPayLogList.ToInt()
			}
			_, e := insImportService.ExcelCommonList(c, exportType, list)
			if e != nil {
				return
			}
			c.Abort()
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// NetPayLog 网络支付明细单状态查询
// @Tags InsPayment
// @Summary 网络支付明细单状态查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.NetPayLogReq true "网络支付明细单状态查询"
// @Success 200 {string} {object}  response.Response{data=insbuyResp.PayBusinessLogicRes,msg=string}
// @Router /insPayment/netPayLog [get]
func (InsPaymentApi *InsPaymentApi) NetPayLog(c *gin.Context) {
	var req insbuyReq.NetPayLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insPaymentService.NetPayLog(req); err != nil {
		global.GVA_LOG.Error("网络支付明细单状态查询!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ConfirmPayStatus 网络支付明细单确认成功
// @Tags InsPayment
// @Summary 网络支付明细单确认成功
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.NetPayLogReq true "网络支付明细单确认成功"
// @Success 200 {string} {object}  response.Response{data=insbuyResp.GetOrderPayStatusResp,msg=string}
// @Router /insPayment/confirmPayStatus [post]
func (InsPaymentApi *InsPaymentApi) ConfirmPayStatus(c *gin.Context) {
	var req insbuyReq.NetPayLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insPaymentService.ConfirmPayStatus(req); err != nil {
		global.GVA_LOG.Error("网络支付明细单修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ConfirmOrderPayTradeId 订单支付绑定重新确认
// @Tags InsPayment
// @Summary 订单支付绑定重新确认
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.NetPayLogReq true "订单支付绑定重新确认"
// @Success 200 {string} {object}  response.Response{data=insbuyResp.GetOrderPayStatusResp,msg=string}
// @Router /insPayment/confirmOrderPayTradeId [put]
func (InsPaymentApi *InsPaymentApi) ConfirmOrderPayTradeId(c *gin.Context) {
	var req insbuyReq.NetPayLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insPaymentService.ConfirmOrderPayTradeId(req); err != nil {
		global.GVA_LOG.Error("订单支付绑定重新确认失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GetAcctList 分页获取挂账记录列表
// @Tags InsPayment
// @Summary 分页获取挂账记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetAcctListReq true "分页获取挂账记录列表"
// @Success 200 {string} {object}  response.Response{data=insbuyResp.GetAcctListResp,msg=string}
// @Router /insPayment/getAcctList [get]
func (InsPaymentApi *InsPaymentApi) GetAcctList(c *gin.Context) {
	var req insbuyReq.GetAcctListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insPaymentService.GetAcctList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		if req.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETExportAcctList.ToInt(), list)
			if e != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// AcctRepay 挂账还款
// @Tags InsPayment
// @Summary 挂账还款
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AcctRepayReq true "挂账还款"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"挂账还款成功"}"
// @Router /insPayment/acctRepay [post]
func (InsPaymentApi *InsPaymentApi) AcctRepay(c *gin.Context) {
	var req insbuyReq.AcctRepayReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insPaymentService.AcctRepay(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// AcctRepayLog 挂账还款记录
// @Tags InsPayment
// @Summary 挂账还款记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AcctRepayLogReq true "挂账还款记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"挂账还款记录"}"
// @Router /insPayment/acctRepayLog [get]
func (InsPaymentApi *InsPaymentApi) AcctRepayLog(c *gin.Context) {
	var req insbuyReq.AcctRepayLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insPaymentService.AcctRepayLog(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// CreateTradeAccount 新增挂载额度
// @Tags InsPayment
// @Summary 新增挂载额度
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.TradeAccountReq true "新增挂载额度"
// @Success 200 {string} {object}  response.Response{data=insbuyResp.GetAcctListResp,msg=string}
// @Router /insPayment/createTradeAccount [post]
func (InsPaymentApi *InsPaymentApi) CreateTradeAccount(c *gin.Context) {
	var req insbuyReq.TradeAccountReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insPaymentService.CreateTradeAccount(req)
	if err != nil {
		global.GVA_LOG.Error("创建挂载额度失败!", zap.Error(err))
		response.FailWithMessage("创建挂载额度失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// GetTradeAccountList 挂载人管理
// @Tags InsPayment
// @Summary 挂载人管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.TradeAccountListSearch true "挂载人管理"
// @Success 200 {string} {object}  response.Response{data=response.PageResult,msg=string}
// @Router /insPayment/getTradeAccountList [get]
func (p *InsPaymentApi) GetTradeAccountList(c *gin.Context) {
	var req insbuyReq.TradeAccountListSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := insPaymentService.GetTradeAccountList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// AccountUserList 挂账人列表(客户端)
// @Tags InsPayment
// @Summary 挂载人管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.TradeAccountListSearch true "挂载人管理"
// @Success 200 {string} {object}  response.Response{data=response.PageResult,msg=string}
// @Router /insPayment/accountUserList [get]
func (p *InsPaymentApi) AccountUserList(c *gin.Context) {
	var req insbuyReq.TradeAccountListSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := insPaymentService.AccountUserList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// DeleteTradeAccount 删除挂载人
// @Tags InsPayment
// @Summary   删除挂载人
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.TradeAccountIdReq     true  "挂账人ID"
// @Success   200   {object}  response.Response{msg=string}  "删除挂账人"
// @Router    /insPayment/deleteTradeAccount [delete]
func (p *InsPaymentApi) DeleteTradeAccount(c *gin.Context) {
	var req insbuyReq.TradeAccountIdReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, TradeAccountVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insPaymentService.DeleteTradeAccount(req)
	if err != nil {
		global.GVA_LOG.Error("删除挂账人失败!", zap.Error(err))
		response.FailWithMessage("删除挂账人失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// SetTradeAccountStatus 设置挂载人状态
// @Tags InsPayment
// @Summary   设置挂载人状态
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.TradeAccountIdReq     true  "挂账人ID"
// @Success   200   {object}  response.Response{msg=string}  "删除挂账人"
// @Router    /insPayment/setTradeAccountStatus [put]
func (p *InsPaymentApi) SetTradeAccountStatus(c *gin.Context) {
	var req insbuyReq.TradeAccountStatusReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insPaymentService.SetTradeAccountStatus(req)
	response.Err(err, c)
}

// UpdateTradeAccount 更新挂载人
// @Tags      InsPayment
// @Summary   更新挂载人
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.TradeAccountReq     true  "更新挂载人"
// @Success   200   {object}  response.Response{msg=string}  "更新挂载人"
// @Router    /insPayment/updateTradeAccount [put]
func (p *InsPaymentApi) UpdateTradeAccount(c *gin.Context) {
	var req insbuyReq.TradeAccountReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, TradeAccountVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insPaymentService.UpdateTradeAccount(req)
	if err != nil {
		global.GVA_LOG.Error("更新备注失败!", zap.Error(err))
		response.FailWithMessage("更新备注失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// GetTradeAccountDetailList 获取挂账人详情
// @Tags InsPayment
// @Summary 获取挂账人详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.TradeAccountDetailListSearch true "获取挂账人详情"
// @Success 200 {string} {object}  response.Response{data=response.PageResult,msg=string}
// @Router /insPayment/getTradeAccountDetailList [get]
func (p *InsPaymentApi) GetTradeAccountDetailList(c *gin.Context) {
	var req insbuyReq.TradeAccountDetailListSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := insPaymentService.GetTradeAccountDetailList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
		return
	}
	if req.Export() {
		_, err = insImportService.ExcelCommonList(c, insbuy.ETTradeAccountList.ToInt(), list)
		if err != nil {
			return
		}
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// AcctRepayFromFinance 财务端销账
// @Tags InsPayment
// @Summary 财务端销账
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AcctRepayFromFinanceReq true "财务端销账"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"挂账还款成功"}"
// @Router /insPayment/acctRepayFromFinance [post]
func (InsPaymentApi *InsPaymentApi) AcctRepayFromFinance(c *gin.Context) {
	var req insbuyReq.AcctRepayFromFinanceReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insPaymentService.AcctRepayFromFinance(req)
	response.ResultErr(nil, err, c)
}
