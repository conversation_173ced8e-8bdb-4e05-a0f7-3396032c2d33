# /data/docker/crmchat/docker-compose.yml
version: "3.9"
services:
  crmchat:
    container_name: crmchat
    image: crpi-fvgftzj4vcg6aq5o.cn-shanghai.personal.cr.aliyuncs.com/insbuy/php:7.4      # 给新镜像起个名字
    working_dir: /var/www/html
    volumes:
      - ./www:/var/www/html
    ports:
      - "9501:9501"
    # 建议写绝对路径，避免找不到 thinksss
    command: ["php", "/var/www/CRMChat/crmchat/think", "swoole", "start", "-D", "--host=0.0.0.0", "--port=9501"]
    restart: unless-stopped

