// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotionExclusion(db *gorm.DB, opts ...gen.DOOption) insProductPromotionExclusion {
	_insProductPromotionExclusion := insProductPromotionExclusion{}

	_insProductPromotionExclusion.insProductPromotionExclusionDo.UseDB(db, opts...)
	_insProductPromotionExclusion.insProductPromotionExclusionDo.UseModel(&insbuy.InsProductPromotionExclusion{})

	tableName := _insProductPromotionExclusion.insProductPromotionExclusionDo.TableName()
	_insProductPromotionExclusion.ALL = field.NewAsterisk(tableName)
	_insProductPromotionExclusion.ID = field.NewUint(tableName, "id")
	_insProductPromotionExclusion.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotionExclusion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotionExclusion.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotionExclusion.PromotionId = field.NewUint(tableName, "promotion_id")
	_insProductPromotionExclusion.ExclusionType = field.NewInt(tableName, "exclusion_type")
	_insProductPromotionExclusion.TargetId = field.NewUint(tableName, "target_id")

	_insProductPromotionExclusion.fillFieldMap()

	return _insProductPromotionExclusion
}

type insProductPromotionExclusion struct {
	insProductPromotionExclusionDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	PromotionId   field.Uint
	ExclusionType field.Int
	TargetId      field.Uint

	fieldMap map[string]field.Expr
}

func (i insProductPromotionExclusion) Table(newTableName string) *insProductPromotionExclusion {
	i.insProductPromotionExclusionDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotionExclusion) As(alias string) *insProductPromotionExclusion {
	i.insProductPromotionExclusionDo.DO = *(i.insProductPromotionExclusionDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotionExclusion) updateTableName(table string) *insProductPromotionExclusion {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PromotionId = field.NewUint(table, "promotion_id")
	i.ExclusionType = field.NewInt(table, "exclusion_type")
	i.TargetId = field.NewUint(table, "target_id")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotionExclusion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotionExclusion) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["promotion_id"] = i.PromotionId
	i.fieldMap["exclusion_type"] = i.ExclusionType
	i.fieldMap["target_id"] = i.TargetId
}

func (i insProductPromotionExclusion) clone(db *gorm.DB) insProductPromotionExclusion {
	i.insProductPromotionExclusionDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotionExclusion) replaceDB(db *gorm.DB) insProductPromotionExclusion {
	i.insProductPromotionExclusionDo.ReplaceDB(db)
	return i
}

type insProductPromotionExclusionDo struct{ gen.DO }

func (i insProductPromotionExclusionDo) Debug() *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionExclusionDo) WithContext(ctx context.Context) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionExclusionDo) ReadDB() *insProductPromotionExclusionDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionExclusionDo) WriteDB() *insProductPromotionExclusionDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionExclusionDo) Session(config *gorm.Session) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionExclusionDo) Clauses(conds ...clause.Expression) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionExclusionDo) Returning(value interface{}, columns ...string) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionExclusionDo) Not(conds ...gen.Condition) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionExclusionDo) Or(conds ...gen.Condition) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionExclusionDo) Select(conds ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionExclusionDo) Where(conds ...gen.Condition) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionExclusionDo) Order(conds ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionExclusionDo) Distinct(cols ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionExclusionDo) Omit(cols ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionExclusionDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionExclusionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionExclusionDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionExclusionDo) Group(cols ...field.Expr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionExclusionDo) Having(conds ...gen.Condition) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionExclusionDo) Limit(limit int) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionExclusionDo) Offset(offset int) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionExclusionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionExclusionDo) Unscoped() *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionExclusionDo) Create(values ...*insbuy.InsProductPromotionExclusion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionExclusionDo) CreateInBatches(values []*insbuy.InsProductPromotionExclusion, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionExclusionDo) Save(values ...*insbuy.InsProductPromotionExclusion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionExclusionDo) First() (*insbuy.InsProductPromotionExclusion, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionExclusion), nil
	}
}

func (i insProductPromotionExclusionDo) Take() (*insbuy.InsProductPromotionExclusion, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionExclusion), nil
	}
}

func (i insProductPromotionExclusionDo) Last() (*insbuy.InsProductPromotionExclusion, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionExclusion), nil
	}
}

func (i insProductPromotionExclusionDo) Find() ([]*insbuy.InsProductPromotionExclusion, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotionExclusion), err
}

func (i insProductPromotionExclusionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotionExclusion, err error) {
	buf := make([]*insbuy.InsProductPromotionExclusion, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionExclusionDo) FindInBatches(result *[]*insbuy.InsProductPromotionExclusion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionExclusionDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionExclusionDo) Assign(attrs ...field.AssignExpr) *insProductPromotionExclusionDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionExclusionDo) Joins(fields ...field.RelationField) *insProductPromotionExclusionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionExclusionDo) Preload(fields ...field.RelationField) *insProductPromotionExclusionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionExclusionDo) FirstOrInit() (*insbuy.InsProductPromotionExclusion, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionExclusion), nil
	}
}

func (i insProductPromotionExclusionDo) FirstOrCreate() (*insbuy.InsProductPromotionExclusion, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionExclusion), nil
	}
}

func (i insProductPromotionExclusionDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotionExclusion, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionExclusionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionExclusionDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionExclusionDo) Delete(models ...*insbuy.InsProductPromotionExclusion) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionExclusionDo) withDO(do gen.Dao) *insProductPromotionExclusionDo {
	i.DO = *do.(*gen.DO)
	return i
}
