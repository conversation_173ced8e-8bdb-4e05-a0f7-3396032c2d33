// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageItemTemplate(db *gorm.DB, opts ...gen.DOOption) insProductPackageItemTemplate {
	_insProductPackageItemTemplate := insProductPackageItemTemplate{}

	_insProductPackageItemTemplate.insProductPackageItemTemplateDo.UseDB(db, opts...)
	_insProductPackageItemTemplate.insProductPackageItemTemplateDo.UseModel(&insbuy.InsProductPackageItemTemplate{})

	tableName := _insProductPackageItemTemplate.insProductPackageItemTemplateDo.TableName()
	_insProductPackageItemTemplate.ALL = field.NewAsterisk(tableName)
	_insProductPackageItemTemplate.ID = field.NewUint(tableName, "id")
	_insProductPackageItemTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageItemTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageItemTemplate.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPackageItemTemplate.StoreId = field.NewUint(tableName, "store_id")
	_insProductPackageItemTemplate.TemplateName = field.NewString(tableName, "template_name")
	_insProductPackageItemTemplate.IsFull = field.NewInt(tableName, "is_full")
	_insProductPackageItemTemplate.OptionName = field.NewString(tableName, "option_name")
	_insProductPackageItemTemplate.OptionNum = field.NewInt(tableName, "option_num")
	_insProductPackageItemTemplate.Remark = field.NewString(tableName, "remark")
	_insProductPackageItemTemplate.TemplateDetails = insProductPackageItemTemplateHasManyTemplateDetails{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("TemplateDetails", "insbuy.InsProductPackageItemTemplateDetails"),
	}

	_insProductPackageItemTemplate.fillFieldMap()

	return _insProductPackageItemTemplate
}

type insProductPackageItemTemplate struct {
	insProductPackageItemTemplateDo

	ALL             field.Asterisk
	ID              field.Uint
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	StoreId         field.Uint
	TemplateName    field.String
	IsFull          field.Int
	OptionName      field.String
	OptionNum       field.Int
	Remark          field.String
	TemplateDetails insProductPackageItemTemplateHasManyTemplateDetails

	fieldMap map[string]field.Expr
}

func (i insProductPackageItemTemplate) Table(newTableName string) *insProductPackageItemTemplate {
	i.insProductPackageItemTemplateDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageItemTemplate) As(alias string) *insProductPackageItemTemplate {
	i.insProductPackageItemTemplateDo.DO = *(i.insProductPackageItemTemplateDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageItemTemplate) updateTableName(table string) *insProductPackageItemTemplate {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.TemplateName = field.NewString(table, "template_name")
	i.IsFull = field.NewInt(table, "is_full")
	i.OptionName = field.NewString(table, "option_name")
	i.OptionNum = field.NewInt(table, "option_num")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageItemTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageItemTemplate) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 11)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["template_name"] = i.TemplateName
	i.fieldMap["is_full"] = i.IsFull
	i.fieldMap["option_name"] = i.OptionName
	i.fieldMap["option_num"] = i.OptionNum
	i.fieldMap["remark"] = i.Remark

}

func (i insProductPackageItemTemplate) clone(db *gorm.DB) insProductPackageItemTemplate {
	i.insProductPackageItemTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageItemTemplate) replaceDB(db *gorm.DB) insProductPackageItemTemplate {
	i.insProductPackageItemTemplateDo.ReplaceDB(db)
	return i
}

type insProductPackageItemTemplateHasManyTemplateDetails struct {
	db *gorm.DB

	field.RelationField
}

func (a insProductPackageItemTemplateHasManyTemplateDetails) Where(conds ...field.Expr) *insProductPackageItemTemplateHasManyTemplateDetails {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insProductPackageItemTemplateHasManyTemplateDetails) WithContext(ctx context.Context) *insProductPackageItemTemplateHasManyTemplateDetails {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insProductPackageItemTemplateHasManyTemplateDetails) Session(session *gorm.Session) *insProductPackageItemTemplateHasManyTemplateDetails {
	a.db = a.db.Session(session)
	return &a
}

func (a insProductPackageItemTemplateHasManyTemplateDetails) Model(m *insbuy.InsProductPackageItemTemplate) *insProductPackageItemTemplateHasManyTemplateDetailsTx {
	return &insProductPackageItemTemplateHasManyTemplateDetailsTx{a.db.Model(m).Association(a.Name())}
}

type insProductPackageItemTemplateHasManyTemplateDetailsTx struct{ tx *gorm.Association }

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Find() (result []*insbuy.InsProductPackageItemTemplateDetails, err error) {
	return result, a.tx.Find(&result)
}

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Append(values ...*insbuy.InsProductPackageItemTemplateDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Replace(values ...*insbuy.InsProductPackageItemTemplateDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Delete(values ...*insbuy.InsProductPackageItemTemplateDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Clear() error {
	return a.tx.Clear()
}

func (a insProductPackageItemTemplateHasManyTemplateDetailsTx) Count() int64 {
	return a.tx.Count()
}

type insProductPackageItemTemplateDo struct{ gen.DO }

func (i insProductPackageItemTemplateDo) Debug() *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageItemTemplateDo) WithContext(ctx context.Context) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageItemTemplateDo) ReadDB() *insProductPackageItemTemplateDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageItemTemplateDo) WriteDB() *insProductPackageItemTemplateDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageItemTemplateDo) Session(config *gorm.Session) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageItemTemplateDo) Clauses(conds ...clause.Expression) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageItemTemplateDo) Returning(value interface{}, columns ...string) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageItemTemplateDo) Not(conds ...gen.Condition) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageItemTemplateDo) Or(conds ...gen.Condition) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageItemTemplateDo) Select(conds ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageItemTemplateDo) Where(conds ...gen.Condition) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageItemTemplateDo) Order(conds ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageItemTemplateDo) Distinct(cols ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageItemTemplateDo) Omit(cols ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageItemTemplateDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageItemTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageItemTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageItemTemplateDo) Group(cols ...field.Expr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageItemTemplateDo) Having(conds ...gen.Condition) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageItemTemplateDo) Limit(limit int) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageItemTemplateDo) Offset(offset int) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageItemTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageItemTemplateDo) Unscoped() *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageItemTemplateDo) Create(values ...*insbuy.InsProductPackageItemTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageItemTemplateDo) CreateInBatches(values []*insbuy.InsProductPackageItemTemplate, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageItemTemplateDo) Save(values ...*insbuy.InsProductPackageItemTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageItemTemplateDo) First() (*insbuy.InsProductPackageItemTemplate, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplate), nil
	}
}

func (i insProductPackageItemTemplateDo) Take() (*insbuy.InsProductPackageItemTemplate, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplate), nil
	}
}

func (i insProductPackageItemTemplateDo) Last() (*insbuy.InsProductPackageItemTemplate, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplate), nil
	}
}

func (i insProductPackageItemTemplateDo) Find() ([]*insbuy.InsProductPackageItemTemplate, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageItemTemplate), err
}

func (i insProductPackageItemTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageItemTemplate, err error) {
	buf := make([]*insbuy.InsProductPackageItemTemplate, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageItemTemplateDo) FindInBatches(result *[]*insbuy.InsProductPackageItemTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageItemTemplateDo) Attrs(attrs ...field.AssignExpr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageItemTemplateDo) Assign(attrs ...field.AssignExpr) *insProductPackageItemTemplateDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageItemTemplateDo) Joins(fields ...field.RelationField) *insProductPackageItemTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateDo) Preload(fields ...field.RelationField) *insProductPackageItemTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateDo) FirstOrInit() (*insbuy.InsProductPackageItemTemplate, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplate), nil
	}
}

func (i insProductPackageItemTemplateDo) FirstOrCreate() (*insbuy.InsProductPackageItemTemplate, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplate), nil
	}
}

func (i insProductPackageItemTemplateDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageItemTemplate, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageItemTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageItemTemplateDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageItemTemplateDo) Delete(models ...*insbuy.InsProductPackageItemTemplate) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageItemTemplateDo) withDO(do gen.Dao) *insProductPackageItemTemplateDo {
	i.DO = *do.(*gen.DO)
	return i
}
