// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventoryBatch(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventoryBatch {
	_insWarehouseInventoryBatch := insWarehouseInventoryBatch{}

	_insWarehouseInventoryBatch.insWarehouseInventoryBatchDo.UseDB(db, opts...)
	_insWarehouseInventoryBatch.insWarehouseInventoryBatchDo.UseModel(&insbuy.InsWarehouseInventoryBatch{})

	tableName := _insWarehouseInventoryBatch.insWarehouseInventoryBatchDo.TableName()
	_insWarehouseInventoryBatch.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventoryBatch.ID = field.NewUint(tableName, "id")
	_insWarehouseInventoryBatch.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventoryBatch.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventoryBatch.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInventoryBatch.BatchSn = field.NewString(tableName, "batch_sn")
	_insWarehouseInventoryBatch.InoutDetailsId = field.NewUint(tableName, "inout_details_id")
	_insWarehouseInventoryBatch.InoutId = field.NewUint(tableName, "inout_id")
	_insWarehouseInventoryBatch.SupplierId = field.NewInt(tableName, "supplier_id")
	_insWarehouseInventoryBatch.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insWarehouseInventoryBatch.MaterialId = field.NewUint(tableName, "material_id")
	_insWarehouseInventoryBatch.Num = field.NewUint(tableName, "num")
	_insWarehouseInventoryBatch.Stock = field.NewUint(tableName, "stock")
	_insWarehouseInventoryBatch.Price = field.NewFloat64(tableName, "price")
	_insWarehouseInventoryBatch.PriceTotal = field.NewFloat64(tableName, "price_total")
	_insWarehouseInventoryBatch.IsRed = field.NewInt(tableName, "is_red")
	_insWarehouseInventoryBatch.Remark = field.NewString(tableName, "remark")
	_insWarehouseInventoryBatch.BatchTime = field.NewTime(tableName, "batch_time")
	_insWarehouseInventoryBatch.FromId = field.NewUint(tableName, "from_id")
	_insWarehouseInventoryBatch.BatchType = field.NewInt(tableName, "batch_type")

	_insWarehouseInventoryBatch.fillFieldMap()

	return _insWarehouseInventoryBatch
}

type insWarehouseInventoryBatch struct {
	insWarehouseInventoryBatchDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	BatchSn        field.String
	InoutDetailsId field.Uint
	InoutId        field.Uint
	SupplierId     field.Int
	WarehouseId    field.Uint
	MaterialId     field.Uint
	Num            field.Uint
	Stock          field.Uint
	Price          field.Float64
	PriceTotal     field.Float64
	IsRed          field.Int
	Remark         field.String
	BatchTime      field.Time
	FromId         field.Uint
	BatchType      field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventoryBatch) Table(newTableName string) *insWarehouseInventoryBatch {
	i.insWarehouseInventoryBatchDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventoryBatch) As(alias string) *insWarehouseInventoryBatch {
	i.insWarehouseInventoryBatchDo.DO = *(i.insWarehouseInventoryBatchDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventoryBatch) updateTableName(table string) *insWarehouseInventoryBatch {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.BatchSn = field.NewString(table, "batch_sn")
	i.InoutDetailsId = field.NewUint(table, "inout_details_id")
	i.InoutId = field.NewUint(table, "inout_id")
	i.SupplierId = field.NewInt(table, "supplier_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.MaterialId = field.NewUint(table, "material_id")
	i.Num = field.NewUint(table, "num")
	i.Stock = field.NewUint(table, "stock")
	i.Price = field.NewFloat64(table, "price")
	i.PriceTotal = field.NewFloat64(table, "price_total")
	i.IsRed = field.NewInt(table, "is_red")
	i.Remark = field.NewString(table, "remark")
	i.BatchTime = field.NewTime(table, "batch_time")
	i.FromId = field.NewUint(table, "from_id")
	i.BatchType = field.NewInt(table, "batch_type")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventoryBatch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventoryBatch) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 19)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["batch_sn"] = i.BatchSn
	i.fieldMap["inout_details_id"] = i.InoutDetailsId
	i.fieldMap["inout_id"] = i.InoutId
	i.fieldMap["supplier_id"] = i.SupplierId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["num"] = i.Num
	i.fieldMap["stock"] = i.Stock
	i.fieldMap["price"] = i.Price
	i.fieldMap["price_total"] = i.PriceTotal
	i.fieldMap["is_red"] = i.IsRed
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["batch_time"] = i.BatchTime
	i.fieldMap["from_id"] = i.FromId
	i.fieldMap["batch_type"] = i.BatchType
}

func (i insWarehouseInventoryBatch) clone(db *gorm.DB) insWarehouseInventoryBatch {
	i.insWarehouseInventoryBatchDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventoryBatch) replaceDB(db *gorm.DB) insWarehouseInventoryBatch {
	i.insWarehouseInventoryBatchDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryBatchDo struct{ gen.DO }

func (i insWarehouseInventoryBatchDo) Debug() *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryBatchDo) WithContext(ctx context.Context) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryBatchDo) ReadDB() *insWarehouseInventoryBatchDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryBatchDo) WriteDB() *insWarehouseInventoryBatchDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryBatchDo) Session(config *gorm.Session) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryBatchDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryBatchDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryBatchDo) Not(conds ...gen.Condition) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryBatchDo) Or(conds ...gen.Condition) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryBatchDo) Select(conds ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryBatchDo) Where(conds ...gen.Condition) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryBatchDo) Order(conds ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryBatchDo) Distinct(cols ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryBatchDo) Omit(cols ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryBatchDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryBatchDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryBatchDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryBatchDo) Group(cols ...field.Expr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryBatchDo) Having(conds ...gen.Condition) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryBatchDo) Limit(limit int) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryBatchDo) Offset(offset int) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryBatchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryBatchDo) Unscoped() *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryBatchDo) Create(values ...*insbuy.InsWarehouseInventoryBatch) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryBatchDo) CreateInBatches(values []*insbuy.InsWarehouseInventoryBatch, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryBatchDo) Save(values ...*insbuy.InsWarehouseInventoryBatch) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryBatchDo) First() (*insbuy.InsWarehouseInventoryBatch, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatch), nil
	}
}

func (i insWarehouseInventoryBatchDo) Take() (*insbuy.InsWarehouseInventoryBatch, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatch), nil
	}
}

func (i insWarehouseInventoryBatchDo) Last() (*insbuy.InsWarehouseInventoryBatch, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatch), nil
	}
}

func (i insWarehouseInventoryBatchDo) Find() ([]*insbuy.InsWarehouseInventoryBatch, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventoryBatch), err
}

func (i insWarehouseInventoryBatchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventoryBatch, err error) {
	buf := make([]*insbuy.InsWarehouseInventoryBatch, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryBatchDo) FindInBatches(result *[]*insbuy.InsWarehouseInventoryBatch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryBatchDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryBatchDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryBatchDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryBatchDo) Joins(fields ...field.RelationField) *insWarehouseInventoryBatchDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryBatchDo) Preload(fields ...field.RelationField) *insWarehouseInventoryBatchDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryBatchDo) FirstOrInit() (*insbuy.InsWarehouseInventoryBatch, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatch), nil
	}
}

func (i insWarehouseInventoryBatchDo) FirstOrCreate() (*insbuy.InsWarehouseInventoryBatch, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryBatch), nil
	}
}

func (i insWarehouseInventoryBatchDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventoryBatch, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryBatchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryBatchDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryBatchDo) Delete(models ...*insbuy.InsWarehouseInventoryBatch) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryBatchDo) withDO(do gen.Dao) *insWarehouseInventoryBatchDo {
	i.DO = *do.(*gen.DO)
	return i
}
