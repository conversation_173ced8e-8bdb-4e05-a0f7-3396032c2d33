package test

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	systemModel "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemResp "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"testing"
)

func Test_授权规则2(t *testing.T) {
	prepare()

	auth2rule_commit(t)
}
func auth2rule_commit_4_clear(t *testing.T, ctx context.Context, srv *system.Authority2Service, req1 []systemReq.Authority2RuleReq) {
	t.Log("清除规则")
	if len(req1) == 0 {
		return
	}
	db1 := global.GVA_DB
	db2 := db1.Model(&systemModel.SysAuthority2{})
	for _, r1 := range req1 {
		db2 = db2.Or("name=?", r1.Name)
	}
	db3 := db1.Where(db2).Delete(&systemModel.SysAuthority2{})
	if db3.Error != nil {
		t.Error(db3.Error)
	}
	t.Log("清除规则成功，数量: ", db3.RowsAffected)
}

func auth2rule_commit_4_update_overwrite(t *testing.T, ctx context.Context, srv *system.Authority2Service, req1 []systemReq.Authority2RuleReq) {
	t.Log("测试覆盖更新")
	var lastId []uint
	var buf []*systemResp.Authority2RuleResp
	for i, r1 := range req1 {
		if i > 0 {
			r1.ID = lastId[0]
		}
		a1, err := srv.CommitRule(r1)
		if err != nil {
			t.Errorf("CommitRule(%v) failed: %v", r1, err)
			t.Fatal(err)
		}
		if i == 0 {
			lastId = append(lastId, a1.ID)
		}
		buf = append(buf, a1)
	}
	t.Log(buf)

	//t.Log("准备删除")
	//srv.DeleteRule(systemReq.Authority2RuleDelReq{Ids: request.NewIDsFromUint(lastId)})
}

func auth2rule_commit_4_user(t *testing.T, ctx context.Context, srv *system.Authority2Service, req1 []systemReq.Authority2RuleReq) {
	t.Log("测试用户授权")
	var userId uint = 1
	lastId := make([]uint, 0, len(req1))
	var buf []*systemResp.Authority2RuleResp
	for _, r1 := range req1 {
		a1, err := srv.CommitRule(r1)
		if err != nil {
			t.Errorf("CommitRule(%v) failed: %v", r1, err)
			t.Fatal(err)
		}
		lastId = append(lastId, a1.ID)
		buf = append(buf, a1)
	}

	//for _, id := range lastId {
	//	_, err := srv.SetUserAuthorities(userId, id)
	//	if err != nil {
	//		t.Errorf("SetUserAuthorities(%v, %v) failed: %v", userId, id, err)
	//		t.Fatal(err)
	//	}
	//}
	_, err := srv.SetUserAuthorities(userId, lastId)
	if err != nil {
		t.Errorf("SetUserAuthorities(%v, %v) failed: %v", userId, lastId, err)
		t.Fatal(err)
	}
	_, err = srv.SetUserAuthorities(userId, lastId)
	if err != nil {
		t.Errorf("SetUserAuthorities(%v, %v) failed: %v", userId, lastId, err)
		t.Fatal(err)
	}

	r1, err := srv.GetAllRuleItemByUserId(userId)
	if err != nil {
		t.Errorf("GetAllRuleItemByUserId(%v) failed: %v", userId, err)
		t.Fatal(err)
	}
	t.Log(r1)
}

func auth2rule_commit(t *testing.T) {
	ctx := context.Background()
	srv := system.Authority2ServiceApp
	req1 := []systemReq.Authority2RuleReq{
		{
			ID:       0,
			Name:     "测试规则1",
			Priority: 2,
			Remark:   "这是一个演示规则，启用1、3、7、9，禁用5",
			Items: []systemReq.Authority2RuleItem{
				{Key: "test.btn1", Val: 1},
				{Key: "test.btn3", Val: 1},
				{Key: "test.btn5", Val: 2},
				{Key: "test.btn7", Val: 1},
				{Key: "test.btn9", Val: 1},
			},
		},
		{
			ID:       0,
			Name:     "测试规则2",
			Priority: 1,
			Remark:   "这是低优先级的规则，启用2、5、7、禁用3",
			Items: []systemReq.Authority2RuleItem{
				{Key: "test.btn2", Val: 1},
				{Key: "test.btn3", Val: 2},
				{Key: "test.btn5", Val: 1},
				{Key: "test.btn7", Val: 1},
			},
		},
		{
			ID:       0,
			Name:     "测试规则3",
			Priority: 1,
			Remark:   "这是低优先级的规则，没有任何权限",
			Items:    []systemReq.Authority2RuleItem{},
		},
		{
			ID:       0,
			Name:     "测试规则4",
			Priority: 1,
			Remark:   "启用2、5",
			Items: []systemReq.Authority2RuleItem{
				{Key: "test.btn2", Val: 1},
				{Key: "test.btn5", Val: 1},
			},
		},
	}
	if false {
		auth2rule_commit_4_clear(t, ctx, srv, req1)
		auth2rule_commit_4_update_overwrite(t, ctx, srv, req1[0:4])
	}

	//if false
	{
		auth2rule_commit_4_clear(t, ctx, srv, req1)
		auth2rule_commit_4_user(t, ctx, srv, req1)
	}

	{
		r1, err := srv.GetUserAuthorities(systemReq.Authority2UserListReq{UserId: 1, PageReq: request.PageReq{PageSize: 100, Page: 1}})
		if err != nil {
			t.Errorf("GetUserAuthorities(%v) failed: %v", 1, err)
			t.Fatal(err)
		}
		t.Log(r1)
	}

	{
		r1, err := srv.GetAllRuleItemByUserId(1)
		if err != nil {
			t.Errorf("GetAllRuleItemByUserId(%v) failed: %v", 1, err)
			t.Fatal(err)
		}
		t.Log(r1)
	}
}
