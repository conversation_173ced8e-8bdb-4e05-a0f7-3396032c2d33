// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportStatisticalRule(db *gorm.DB, opts ...gen.DOOption) insReportStatisticalRule {
	_insReportStatisticalRule := insReportStatisticalRule{}

	_insReportStatisticalRule.insReportStatisticalRuleDo.UseDB(db, opts...)
	_insReportStatisticalRule.insReportStatisticalRuleDo.UseModel(&insbuy.InsReportStatisticalRule{})

	tableName := _insReportStatisticalRule.insReportStatisticalRuleDo.TableName()
	_insReportStatisticalRule.ALL = field.NewAsterisk(tableName)
	_insReportStatisticalRule.ID = field.NewUint(tableName, "id")
	_insReportStatisticalRule.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportStatisticalRule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportStatisticalRule.Name = field.NewString(tableName, "name")
	_insReportStatisticalRule.StoreId = field.NewUint(tableName, "store_id")
	_insReportStatisticalRule.Code = field.NewString(tableName, "code")
	_insReportStatisticalRule.Desc = field.NewString(tableName, "desc")

	_insReportStatisticalRule.fillFieldMap()

	return _insReportStatisticalRule
}

type insReportStatisticalRule struct {
	insReportStatisticalRuleDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	Name      field.String
	StoreId   field.Uint
	Code      field.String
	Desc      field.String

	fieldMap map[string]field.Expr
}

func (i insReportStatisticalRule) Table(newTableName string) *insReportStatisticalRule {
	i.insReportStatisticalRuleDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportStatisticalRule) As(alias string) *insReportStatisticalRule {
	i.insReportStatisticalRuleDo.DO = *(i.insReportStatisticalRuleDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportStatisticalRule) updateTableName(table string) *insReportStatisticalRule {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Name = field.NewString(table, "name")
	i.StoreId = field.NewUint(table, "store_id")
	i.Code = field.NewString(table, "code")
	i.Desc = field.NewString(table, "desc")

	i.fillFieldMap()

	return i
}

func (i *insReportStatisticalRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportStatisticalRule) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["code"] = i.Code
	i.fieldMap["desc"] = i.Desc
}

func (i insReportStatisticalRule) clone(db *gorm.DB) insReportStatisticalRule {
	i.insReportStatisticalRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportStatisticalRule) replaceDB(db *gorm.DB) insReportStatisticalRule {
	i.insReportStatisticalRuleDo.ReplaceDB(db)
	return i
}

type insReportStatisticalRuleDo struct{ gen.DO }

func (i insReportStatisticalRuleDo) Debug() *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportStatisticalRuleDo) WithContext(ctx context.Context) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportStatisticalRuleDo) ReadDB() *insReportStatisticalRuleDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportStatisticalRuleDo) WriteDB() *insReportStatisticalRuleDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportStatisticalRuleDo) Session(config *gorm.Session) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportStatisticalRuleDo) Clauses(conds ...clause.Expression) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportStatisticalRuleDo) Returning(value interface{}, columns ...string) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportStatisticalRuleDo) Not(conds ...gen.Condition) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportStatisticalRuleDo) Or(conds ...gen.Condition) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportStatisticalRuleDo) Select(conds ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportStatisticalRuleDo) Where(conds ...gen.Condition) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportStatisticalRuleDo) Order(conds ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportStatisticalRuleDo) Distinct(cols ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportStatisticalRuleDo) Omit(cols ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportStatisticalRuleDo) Join(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportStatisticalRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportStatisticalRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportStatisticalRuleDo) Group(cols ...field.Expr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportStatisticalRuleDo) Having(conds ...gen.Condition) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportStatisticalRuleDo) Limit(limit int) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportStatisticalRuleDo) Offset(offset int) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportStatisticalRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportStatisticalRuleDo) Unscoped() *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportStatisticalRuleDo) Create(values ...*insbuy.InsReportStatisticalRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportStatisticalRuleDo) CreateInBatches(values []*insbuy.InsReportStatisticalRule, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportStatisticalRuleDo) Save(values ...*insbuy.InsReportStatisticalRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportStatisticalRuleDo) First() (*insbuy.InsReportStatisticalRule, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRule), nil
	}
}

func (i insReportStatisticalRuleDo) Take() (*insbuy.InsReportStatisticalRule, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRule), nil
	}
}

func (i insReportStatisticalRuleDo) Last() (*insbuy.InsReportStatisticalRule, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRule), nil
	}
}

func (i insReportStatisticalRuleDo) Find() ([]*insbuy.InsReportStatisticalRule, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportStatisticalRule), err
}

func (i insReportStatisticalRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportStatisticalRule, err error) {
	buf := make([]*insbuy.InsReportStatisticalRule, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportStatisticalRuleDo) FindInBatches(result *[]*insbuy.InsReportStatisticalRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportStatisticalRuleDo) Attrs(attrs ...field.AssignExpr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportStatisticalRuleDo) Assign(attrs ...field.AssignExpr) *insReportStatisticalRuleDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportStatisticalRuleDo) Joins(fields ...field.RelationField) *insReportStatisticalRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportStatisticalRuleDo) Preload(fields ...field.RelationField) *insReportStatisticalRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportStatisticalRuleDo) FirstOrInit() (*insbuy.InsReportStatisticalRule, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRule), nil
	}
}

func (i insReportStatisticalRuleDo) FirstOrCreate() (*insbuy.InsReportStatisticalRule, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRule), nil
	}
}

func (i insReportStatisticalRuleDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportStatisticalRule, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportStatisticalRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportStatisticalRuleDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportStatisticalRuleDo) Delete(models ...*insbuy.InsReportStatisticalRule) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportStatisticalRuleDo) withDO(do gen.Dao) *insReportStatisticalRuleDo {
	i.DO = *do.(*gen.DO)
	return i
}
