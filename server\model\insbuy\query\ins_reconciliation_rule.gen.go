// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReconciliationRule(db *gorm.DB, opts ...gen.DOOption) insReconciliationRule {
	_insReconciliationRule := insReconciliationRule{}

	_insReconciliationRule.insReconciliationRuleDo.UseDB(db, opts...)
	_insReconciliationRule.insReconciliationRuleDo.UseModel(&insbuy.InsReconciliationRule{})

	tableName := _insReconciliationRule.insReconciliationRuleDo.TableName()
	_insReconciliationRule.ALL = field.NewAsterisk(tableName)
	_insReconciliationRule.ID = field.NewUint(tableName, "id")
	_insReconciliationRule.CreatedAt = field.NewTime(tableName, "created_at")
	_insReconciliationRule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReconciliationRule.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReconciliationRule.StoreId = field.NewUint(tableName, "store_id")
	_insReconciliationRule.Dim = field.NewInt(tableName, "dim")
	_insReconciliationRule.Cycle = field.NewInt(tableName, "cycle")
	_insReconciliationRule.Status = field.NewUint(tableName, "status")
	_insReconciliationRule.Channel = field.NewString(tableName, "channel")
	_insReconciliationRule.Ext = field.NewField(tableName, "ext")

	_insReconciliationRule.fillFieldMap()

	return _insReconciliationRule
}

type insReconciliationRule struct {
	insReconciliationRuleDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	StoreId   field.Uint
	Dim       field.Int
	Cycle     field.Int
	Status    field.Uint
	Channel   field.String
	Ext       field.Field

	fieldMap map[string]field.Expr
}

func (i insReconciliationRule) Table(newTableName string) *insReconciliationRule {
	i.insReconciliationRuleDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReconciliationRule) As(alias string) *insReconciliationRule {
	i.insReconciliationRuleDo.DO = *(i.insReconciliationRuleDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReconciliationRule) updateTableName(table string) *insReconciliationRule {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Dim = field.NewInt(table, "dim")
	i.Cycle = field.NewInt(table, "cycle")
	i.Status = field.NewUint(table, "status")
	i.Channel = field.NewString(table, "channel")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insReconciliationRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReconciliationRule) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["dim"] = i.Dim
	i.fieldMap["cycle"] = i.Cycle
	i.fieldMap["status"] = i.Status
	i.fieldMap["channel"] = i.Channel
	i.fieldMap["ext"] = i.Ext
}

func (i insReconciliationRule) clone(db *gorm.DB) insReconciliationRule {
	i.insReconciliationRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReconciliationRule) replaceDB(db *gorm.DB) insReconciliationRule {
	i.insReconciliationRuleDo.ReplaceDB(db)
	return i
}

type insReconciliationRuleDo struct{ gen.DO }

func (i insReconciliationRuleDo) Debug() *insReconciliationRuleDo {
	return i.withDO(i.DO.Debug())
}

func (i insReconciliationRuleDo) WithContext(ctx context.Context) *insReconciliationRuleDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReconciliationRuleDo) ReadDB() *insReconciliationRuleDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReconciliationRuleDo) WriteDB() *insReconciliationRuleDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReconciliationRuleDo) Session(config *gorm.Session) *insReconciliationRuleDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReconciliationRuleDo) Clauses(conds ...clause.Expression) *insReconciliationRuleDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReconciliationRuleDo) Returning(value interface{}, columns ...string) *insReconciliationRuleDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReconciliationRuleDo) Not(conds ...gen.Condition) *insReconciliationRuleDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReconciliationRuleDo) Or(conds ...gen.Condition) *insReconciliationRuleDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReconciliationRuleDo) Select(conds ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReconciliationRuleDo) Where(conds ...gen.Condition) *insReconciliationRuleDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReconciliationRuleDo) Order(conds ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReconciliationRuleDo) Distinct(cols ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReconciliationRuleDo) Omit(cols ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReconciliationRuleDo) Join(table schema.Tabler, on ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReconciliationRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReconciliationRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReconciliationRuleDo) Group(cols ...field.Expr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReconciliationRuleDo) Having(conds ...gen.Condition) *insReconciliationRuleDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReconciliationRuleDo) Limit(limit int) *insReconciliationRuleDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReconciliationRuleDo) Offset(offset int) *insReconciliationRuleDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReconciliationRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReconciliationRuleDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReconciliationRuleDo) Unscoped() *insReconciliationRuleDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReconciliationRuleDo) Create(values ...*insbuy.InsReconciliationRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReconciliationRuleDo) CreateInBatches(values []*insbuy.InsReconciliationRule, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReconciliationRuleDo) Save(values ...*insbuy.InsReconciliationRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReconciliationRuleDo) First() (*insbuy.InsReconciliationRule, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationRule), nil
	}
}

func (i insReconciliationRuleDo) Take() (*insbuy.InsReconciliationRule, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationRule), nil
	}
}

func (i insReconciliationRuleDo) Last() (*insbuy.InsReconciliationRule, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationRule), nil
	}
}

func (i insReconciliationRuleDo) Find() ([]*insbuy.InsReconciliationRule, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReconciliationRule), err
}

func (i insReconciliationRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReconciliationRule, err error) {
	buf := make([]*insbuy.InsReconciliationRule, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReconciliationRuleDo) FindInBatches(result *[]*insbuy.InsReconciliationRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReconciliationRuleDo) Attrs(attrs ...field.AssignExpr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReconciliationRuleDo) Assign(attrs ...field.AssignExpr) *insReconciliationRuleDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReconciliationRuleDo) Joins(fields ...field.RelationField) *insReconciliationRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReconciliationRuleDo) Preload(fields ...field.RelationField) *insReconciliationRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReconciliationRuleDo) FirstOrInit() (*insbuy.InsReconciliationRule, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationRule), nil
	}
}

func (i insReconciliationRuleDo) FirstOrCreate() (*insbuy.InsReconciliationRule, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReconciliationRule), nil
	}
}

func (i insReconciliationRuleDo) FindByPage(offset int, limit int) (result []*insbuy.InsReconciliationRule, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReconciliationRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReconciliationRuleDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReconciliationRuleDo) Delete(models ...*insbuy.InsReconciliationRule) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReconciliationRuleDo) withDO(do gen.Dao) *insReconciliationRuleDo {
	i.DO = *do.(*gen.DO)
	return i
}
