// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTransferExecution(db *gorm.DB, opts ...gen.DOOption) insTransferExecution {
	_insTransferExecution := insTransferExecution{}

	_insTransferExecution.insTransferExecutionDo.UseDB(db, opts...)
	_insTransferExecution.insTransferExecutionDo.UseModel(&insbuy.InsTransferExecution{})

	tableName := _insTransferExecution.insTransferExecutionDo.TableName()
	_insTransferExecution.ALL = field.NewAsterisk(tableName)
	_insTransferExecution.ID = field.NewUint(tableName, "id")
	_insTransferExecution.CreatedAt = field.NewTime(tableName, "created_at")
	_insTransferExecution.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTransferExecution.ProcessID = field.NewUint(tableName, "process_id")
	_insTransferExecution.Name = field.NewString(tableName, "name")
	_insTransferExecution.Status = field.NewInt(tableName, "status")
	_insTransferExecution.HandledById = field.NewUint(tableName, "handled_by_id")
	_insTransferExecution.Ext = field.NewField(tableName, "ext")

	_insTransferExecution.fillFieldMap()

	return _insTransferExecution
}

type insTransferExecution struct {
	insTransferExecutionDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	ProcessID   field.Uint
	Name        field.String
	Status      field.Int
	HandledById field.Uint
	Ext         field.Field

	fieldMap map[string]field.Expr
}

func (i insTransferExecution) Table(newTableName string) *insTransferExecution {
	i.insTransferExecutionDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTransferExecution) As(alias string) *insTransferExecution {
	i.insTransferExecutionDo.DO = *(i.insTransferExecutionDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTransferExecution) updateTableName(table string) *insTransferExecution {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ProcessID = field.NewUint(table, "process_id")
	i.Name = field.NewString(table, "name")
	i.Status = field.NewInt(table, "status")
	i.HandledById = field.NewUint(table, "handled_by_id")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insTransferExecution) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTransferExecution) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["process_id"] = i.ProcessID
	i.fieldMap["name"] = i.Name
	i.fieldMap["status"] = i.Status
	i.fieldMap["handled_by_id"] = i.HandledById
	i.fieldMap["ext"] = i.Ext
}

func (i insTransferExecution) clone(db *gorm.DB) insTransferExecution {
	i.insTransferExecutionDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTransferExecution) replaceDB(db *gorm.DB) insTransferExecution {
	i.insTransferExecutionDo.ReplaceDB(db)
	return i
}

type insTransferExecutionDo struct{ gen.DO }

func (i insTransferExecutionDo) Debug() *insTransferExecutionDo {
	return i.withDO(i.DO.Debug())
}

func (i insTransferExecutionDo) WithContext(ctx context.Context) *insTransferExecutionDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTransferExecutionDo) ReadDB() *insTransferExecutionDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTransferExecutionDo) WriteDB() *insTransferExecutionDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTransferExecutionDo) Session(config *gorm.Session) *insTransferExecutionDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTransferExecutionDo) Clauses(conds ...clause.Expression) *insTransferExecutionDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTransferExecutionDo) Returning(value interface{}, columns ...string) *insTransferExecutionDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTransferExecutionDo) Not(conds ...gen.Condition) *insTransferExecutionDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTransferExecutionDo) Or(conds ...gen.Condition) *insTransferExecutionDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTransferExecutionDo) Select(conds ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTransferExecutionDo) Where(conds ...gen.Condition) *insTransferExecutionDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTransferExecutionDo) Order(conds ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTransferExecutionDo) Distinct(cols ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTransferExecutionDo) Omit(cols ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTransferExecutionDo) Join(table schema.Tabler, on ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTransferExecutionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTransferExecutionDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTransferExecutionDo) Group(cols ...field.Expr) *insTransferExecutionDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTransferExecutionDo) Having(conds ...gen.Condition) *insTransferExecutionDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTransferExecutionDo) Limit(limit int) *insTransferExecutionDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTransferExecutionDo) Offset(offset int) *insTransferExecutionDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTransferExecutionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTransferExecutionDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTransferExecutionDo) Unscoped() *insTransferExecutionDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTransferExecutionDo) Create(values ...*insbuy.InsTransferExecution) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTransferExecutionDo) CreateInBatches(values []*insbuy.InsTransferExecution, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTransferExecutionDo) Save(values ...*insbuy.InsTransferExecution) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTransferExecutionDo) First() (*insbuy.InsTransferExecution, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecution), nil
	}
}

func (i insTransferExecutionDo) Take() (*insbuy.InsTransferExecution, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecution), nil
	}
}

func (i insTransferExecutionDo) Last() (*insbuy.InsTransferExecution, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecution), nil
	}
}

func (i insTransferExecutionDo) Find() ([]*insbuy.InsTransferExecution, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTransferExecution), err
}

func (i insTransferExecutionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTransferExecution, err error) {
	buf := make([]*insbuy.InsTransferExecution, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTransferExecutionDo) FindInBatches(result *[]*insbuy.InsTransferExecution, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTransferExecutionDo) Attrs(attrs ...field.AssignExpr) *insTransferExecutionDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTransferExecutionDo) Assign(attrs ...field.AssignExpr) *insTransferExecutionDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTransferExecutionDo) Joins(fields ...field.RelationField) *insTransferExecutionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTransferExecutionDo) Preload(fields ...field.RelationField) *insTransferExecutionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTransferExecutionDo) FirstOrInit() (*insbuy.InsTransferExecution, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecution), nil
	}
}

func (i insTransferExecutionDo) FirstOrCreate() (*insbuy.InsTransferExecution, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecution), nil
	}
}

func (i insTransferExecutionDo) FindByPage(offset int, limit int) (result []*insbuy.InsTransferExecution, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTransferExecutionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTransferExecutionDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTransferExecutionDo) Delete(models ...*insbuy.InsTransferExecution) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTransferExecutionDo) withDO(do gen.Dao) *insTransferExecutionDo {
	i.DO = *do.(*gen.DO)
	return i
}
