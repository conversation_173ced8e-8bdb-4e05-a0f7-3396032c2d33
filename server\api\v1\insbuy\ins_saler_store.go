package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSalerStoreApi struct {
}

var insSalerStoreService = service.ServiceGroupApp.InsBuyServiceGroup.InsSalerStoreService

// CreateInsSalerStore 创建InsSalerStore
// @Tags InsSalerStore
// @Summary 创建InsSalerStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerStore true "创建InsSalerStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSalerStore/createInsSalerStore [post]
func (insSalerStoreApi *InsSalerStoreApi) CreateInsSalerStore(c *gin.Context) {
	var req insbuyReq.CreateInsSalerStoreReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerStoreService.CreateInsSalerStore(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSalerStore 删除InsSalerStore
// @Tags InsSalerStore
// @Summary 删除InsSalerStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerStore true "删除InsSalerStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSalerStore/deleteInsSalerStore [delete]
func (insSalerStoreApi *InsSalerStoreApi) DeleteInsSalerStore(c *gin.Context) {
	var insSalerStore insbuy.InsSalerStore
	err := c.ShouldBindJSON(&insSalerStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerStoreService.DeleteInsSalerStore(insSalerStore); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSalerStoreByIds 批量删除InsSalerStore
// @Tags InsSalerStore
// @Summary 批量删除InsSalerStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSalerStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insSalerStore/deleteInsSalerStoreByIds [delete]
func (insSalerStoreApi *InsSalerStoreApi) DeleteInsSalerStoreByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerStoreService.DeleteInsSalerStoreByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSalerStore 更新InsSalerStore
// @Tags InsSalerStore
// @Summary 更新InsSalerStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerStore true "更新InsSalerStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSalerStore/updateInsSalerStore [put]
func (insSalerStoreApi *InsSalerStoreApi) UpdateInsSalerStore(c *gin.Context) {
	var insSalerStore insbuy.InsSalerStore
	err := c.ShouldBindJSON(&insSalerStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerStoreService.UpdateInsSalerStore(insSalerStore); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSalerStore 用id查询InsSalerStore
// @Tags InsSalerStore
// @Summary 用id查询InsSalerStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsSalerStore true "用id查询InsSalerStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insSalerStore/findInsSalerStore [get]
func (insSalerStoreApi *InsSalerStoreApi) FindInsSalerStore(c *gin.Context) {
	var insSalerStore insbuy.InsSalerStore
	err := c.ShouldBindQuery(&insSalerStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsSalerStore, err := insSalerStoreService.GetInsSalerStore(insSalerStore.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsSalerStore": reinsSalerStore}, c)
	}
}

// GetInsSalerStoreList 分页获取InsSalerStore列表
// @Tags InsSalerStore
// @Summary 分页获取InsSalerStore列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSalerStoreSearch true "分页获取InsSalerStore列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSalerStore/getInsSalerStoreList [get]
func (insSalerStoreApi *InsSalerStoreApi) GetInsSalerStoreList(c *gin.Context) {
	var pageInfo insbuyReq.InsSalerStoreSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSalerStoreService.GetInsSalerStoreInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
