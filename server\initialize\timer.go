package initialize

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/task"
	"github.com/robfig/cron/v3"
	"reflect"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

func Timer() {
	if global.GVA_CONFIG.Timer.Start {
		for i := range global.GVA_CONFIG.Timer.Detail {
			go func(detail config.Detail) {
				var option []cron.Option
				if global.GVA_CONFIG.Timer.WithSeconds {
					option = append(option, cron.WithSeconds())
				}
				_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", global.GVA_CONFIG.Timer.Spec, func() {
					err := utils.ClearTable(global.GVA_DB, detail.TableName, detail.CompareField, detail.Interval)
					if err != nil {
						fmt.Println("timer error:", err)
					}
				}, option...)
				if err != nil {
					fmt.Println("add timer error:", err)
				}
			}(global.GVA_CONFIG.Timer.Detail[i])
		}

		// 收银系统任务列表
		for t := range global.GVA_CONFIG.Timer.InsTask {
			go func(insTask config.InsTask) {
				var option []cron.Option
				if insTask.WithSeconds {
					option = append(option, cron.WithSeconds())
				}
				_, err := global.GVA_Timer.AddTaskByFunc(insTask.TaskName, insTask.Spec, func() {
					funks := reflect.ValueOf(&task.InsbuyJob{})
					f := funks.MethodByName(insTask.TaskName)
					f.Call(nil)
				}, option...)
				if err != nil {
					fmt.Println("Insbuy task job error:", err)
				}
			}(global.GVA_CONFIG.Timer.InsTask[t])
		}
	}
}
