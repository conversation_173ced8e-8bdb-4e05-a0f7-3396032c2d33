package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CustomPaymentList 获取自定义支付方式列表
// @Tags InsPaymentBusiness
// @Summary 获取自定义支付方式列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CustomPaymentListReq true "获取自定义支付方式列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insPayment/customPaymentList [get]
func (InsPaymentApi *InsPaymentApi) CustomPaymentList(c *gin.Context) {
	var req insbuyReq.CustomPaymentListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insPaymentService.CustomPaymentList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// CreateCustomPayment 创建自定义支付方式
// @Tags InsPaymentBusiness
// @Summary 创建自定义支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CustomPaymentReq true "创建自定义支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insPayment/createCustomPayment [post]
func (InsPaymentApi *InsPaymentApi) CreateCustomPayment(c *gin.Context) {
	var req insbuyReq.CustomPaymentReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.CreateCustomPayment(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateCustomPayment 更新自定义支付方式
// @Tags InsPaymentBusiness
// @Summary 更新自定义支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CustomPaymentReq true "更新自定义支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insPayment/updateCustomPayment [put]
func (InsPaymentApi *InsPaymentApi) UpdateCustomPayment(c *gin.Context) {
	var req insbuyReq.CustomPaymentReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.UpdateCustomPayment(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteCustomPayment 删除自定义支付方式
// @Tags InsPaymentBusiness
// @Summary 删除自定义支付方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除自定义支付方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insPayment/deleteCustomPayment [delete]
func (InsPaymentApi *InsPaymentApi) DeleteCustomPayment(c *gin.Context) {
	var req request.GetById
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	insPaymentService.DeleteCustomPayment(req)
	response.OkWithMessage("删除成功", c)
}

// EnableCustomPayment 启用禁用
// @Tags InsPaymentBusiness
// @Summary 启用禁用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.EnableCustomPaymentReq true "启用禁用"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"操作成功"}"
// @Router /insPayment/enableCustomPayment [put]
func (InsPaymentApi *InsPaymentApi) EnableCustomPayment(c *gin.Context) {
	var req insbuyReq.EnableCustomPaymentReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.EnableCustomPayment(req); err != nil {
		global.GVA_LOG.Error("操作失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithMessage("操作成功", c)
	}
}
