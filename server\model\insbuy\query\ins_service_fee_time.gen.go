// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeTime(db *gorm.DB, opts ...gen.DOOption) insServiceFeeTime {
	_insServiceFeeTime := insServiceFeeTime{}

	_insServiceFeeTime.insServiceFeeTimeDo.UseDB(db, opts...)
	_insServiceFeeTime.insServiceFeeTimeDo.UseModel(&insbuy.InsServiceFeeTime{})

	tableName := _insServiceFeeTime.insServiceFeeTimeDo.TableName()
	_insServiceFeeTime.ALL = field.NewAsterisk(tableName)
	_insServiceFeeTime.ID = field.NewUint(tableName, "id")
	_insServiceFeeTime.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeTime.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeTime.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeTime.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeTime.WeekDay = field.NewInt(tableName, "week_day")
	_insServiceFeeTime.TimeRanges = field.NewField(tableName, "time_ranges")

	_insServiceFeeTime.fillFieldMap()

	return _insServiceFeeTime
}

type insServiceFeeTime struct {
	insServiceFeeTimeDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ServiceFeeId field.Uint
	WeekDay      field.Int
	TimeRanges   field.Field

	fieldMap map[string]field.Expr
}

func (i insServiceFeeTime) Table(newTableName string) *insServiceFeeTime {
	i.insServiceFeeTimeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeTime) As(alias string) *insServiceFeeTime {
	i.insServiceFeeTimeDo.DO = *(i.insServiceFeeTimeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeTime) updateTableName(table string) *insServiceFeeTime {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.WeekDay = field.NewInt(table, "week_day")
	i.TimeRanges = field.NewField(table, "time_ranges")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeTime) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeTime) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["week_day"] = i.WeekDay
	i.fieldMap["time_ranges"] = i.TimeRanges
}

func (i insServiceFeeTime) clone(db *gorm.DB) insServiceFeeTime {
	i.insServiceFeeTimeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeTime) replaceDB(db *gorm.DB) insServiceFeeTime {
	i.insServiceFeeTimeDo.ReplaceDB(db)
	return i
}

type insServiceFeeTimeDo struct{ gen.DO }

func (i insServiceFeeTimeDo) Debug() *insServiceFeeTimeDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeTimeDo) WithContext(ctx context.Context) *insServiceFeeTimeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeTimeDo) ReadDB() *insServiceFeeTimeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeTimeDo) WriteDB() *insServiceFeeTimeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeTimeDo) Session(config *gorm.Session) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeTimeDo) Clauses(conds ...clause.Expression) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeTimeDo) Returning(value interface{}, columns ...string) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeTimeDo) Not(conds ...gen.Condition) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeTimeDo) Or(conds ...gen.Condition) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeTimeDo) Select(conds ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeTimeDo) Where(conds ...gen.Condition) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeTimeDo) Order(conds ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeTimeDo) Distinct(cols ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeTimeDo) Omit(cols ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeTimeDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeTimeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeTimeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeTimeDo) Group(cols ...field.Expr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeTimeDo) Having(conds ...gen.Condition) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeTimeDo) Limit(limit int) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeTimeDo) Offset(offset int) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeTimeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeTimeDo) Unscoped() *insServiceFeeTimeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeTimeDo) Create(values ...*insbuy.InsServiceFeeTime) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeTimeDo) CreateInBatches(values []*insbuy.InsServiceFeeTime, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeTimeDo) Save(values ...*insbuy.InsServiceFeeTime) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeTimeDo) First() (*insbuy.InsServiceFeeTime, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeTime), nil
	}
}

func (i insServiceFeeTimeDo) Take() (*insbuy.InsServiceFeeTime, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeTime), nil
	}
}

func (i insServiceFeeTimeDo) Last() (*insbuy.InsServiceFeeTime, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeTime), nil
	}
}

func (i insServiceFeeTimeDo) Find() ([]*insbuy.InsServiceFeeTime, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeTime), err
}

func (i insServiceFeeTimeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeTime, err error) {
	buf := make([]*insbuy.InsServiceFeeTime, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeTimeDo) FindInBatches(result *[]*insbuy.InsServiceFeeTime, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeTimeDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeTimeDo) Assign(attrs ...field.AssignExpr) *insServiceFeeTimeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeTimeDo) Joins(fields ...field.RelationField) *insServiceFeeTimeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeTimeDo) Preload(fields ...field.RelationField) *insServiceFeeTimeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeTimeDo) FirstOrInit() (*insbuy.InsServiceFeeTime, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeTime), nil
	}
}

func (i insServiceFeeTimeDo) FirstOrCreate() (*insbuy.InsServiceFeeTime, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeTime), nil
	}
}

func (i insServiceFeeTimeDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeTime, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeTimeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeTimeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeTimeDo) Delete(models ...*insbuy.InsServiceFeeTime) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeTimeDo) withDO(do gen.Dao) *insServiceFeeTimeDo {
	i.DO = *do.(*gen.DO)
	return i
}
