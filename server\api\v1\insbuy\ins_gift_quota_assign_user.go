package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsGiftQuotaAssignUserApi struct {
}

var insGiftQuotaAssignUserService = service.ServiceGroupApp.InsBuyServiceGroup.InsGiftQuotaAssignUserService

// CreateInsGiftQuotaAssignUser 创建InsGiftQuotaAssignUser
// @Tags InsGiftQuotaAssignUser
// @Summary 创建InsGiftQuotaAssignUser
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsGiftQuotaAssignUser true "创建InsGiftQuotaAssignUser"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insGiftQuotaAssignUser/createInsGiftQuotaAssignUser [post]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) CreateInsGiftQuotaAssignUser(c *gin.Context) {
	var req insbuyReq.InsGiftQuotaAssignUserReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftQuotaAssignUserService.CreateInsGiftQuotaAssignUser(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsGiftQuotaAssignUser 删除InsGiftQuotaAssignUser
// @Tags InsGiftQuotaAssignUser
// @Summary 删除InsGiftQuotaAssignUser
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsGiftQuotaAssignUser true "删除InsGiftQuotaAssignUser"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insGiftQuotaAssignUser/deleteInsGiftQuotaAssignUser [delete]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) DeleteInsGiftQuotaAssignUser(c *gin.Context) {
	var insGiftQuotaAssignUser insbuy.InsGiftQuotaAssignUser
	err := c.ShouldBindJSON(&insGiftQuotaAssignUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftQuotaAssignUserService.DeleteInsGiftQuotaAssignUser(insGiftQuotaAssignUser); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsGiftQuotaAssignUserByIds 批量删除InsGiftQuotaAssignUser
// @Tags InsGiftQuotaAssignUser
// @Summary 批量删除InsGiftQuotaAssignUser
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsGiftQuotaAssignUser"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insGiftQuotaAssignUser/deleteInsGiftQuotaAssignUserByIds [delete]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) DeleteInsGiftQuotaAssignUserByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftQuotaAssignUserService.DeleteInsGiftQuotaAssignUserByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsGiftQuotaAssignUser 更新InsGiftQuotaAssignUser
// @Tags InsGiftQuotaAssignUser
// @Summary 更新InsGiftQuotaAssignUser
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsGiftQuotaAssignUser true "更新InsGiftQuotaAssignUser"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insGiftQuotaAssignUser/updateInsGiftQuotaAssignUser [put]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) UpdateInsGiftQuotaAssignUser(c *gin.Context) {
	var insGiftQuotaAssignUser insbuy.InsGiftQuotaAssignUser
	err := c.ShouldBindJSON(&insGiftQuotaAssignUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftQuotaAssignUserService.UpdateInsGiftQuotaAssignUser(insGiftQuotaAssignUser); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsGiftQuotaAssignUser 用id查询InsGiftQuotaAssignUser
// @Tags InsGiftQuotaAssignUser
// @Summary 用id查询InsGiftQuotaAssignUser
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsGiftQuotaAssignUser true "用id查询InsGiftQuotaAssignUser"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insGiftQuotaAssignUser/findInsGiftQuotaAssignUser [get]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) FindInsGiftQuotaAssignUser(c *gin.Context) {
	var insGiftQuotaAssignUser insbuy.InsGiftQuotaAssignUser
	err := c.ShouldBindQuery(&insGiftQuotaAssignUser)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsGiftQuotaAssignUser, err := insGiftQuotaAssignUserService.GetInsGiftQuotaAssignUser(insGiftQuotaAssignUser.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsGiftQuotaAssignUser": reinsGiftQuotaAssignUser}, c)
	}
}

// GetInsGiftQuotaAssignUserList 分页获取InsGiftQuotaAssignUser列表
// @Tags InsGiftQuotaAssignUser
// @Summary 分页获取InsGiftQuotaAssignUser列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsGiftQuotaAssignUserSearch true "分页获取InsGiftQuotaAssignUser列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insGiftQuotaAssignUser/getInsGiftQuotaAssignUserList [get]
func (insGiftQuotaAssignUserApi *InsGiftQuotaAssignUserApi) GetInsGiftQuotaAssignUserList(c *gin.Context) {
	var pageInfo insbuyReq.InsGiftQuotaAssignUserSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insGiftQuotaAssignUserService.GetInsGiftQuotaAssignUserInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
