# 路由设计规范

## 概述

本文档定义了项目中路由设计的标准规范，确保代码的一致性、可维护性和性能优化。

## 核心规则

### 1. 操作记录中间件使用规范

#### 1.1 基本原则
- **操作类请求**（POST/PUT/DELETE/PATCH）必须使用操作记录中间件 `middleware.OperationRecord()`
- **查询类请求**（GET）不使用操作记录中间件，以提高查询性能
- 路由分组管理，操作类和查询类请求分开定义

#### 1.2 分组命名规范
```go
// 操作记录路由组 - 需要记录操作日志
xxxRecordGroup := PrivateGroup.Group("模块路径").Use(middleware.OperationRecord())

// 查询路由组 - 不需要记录操作日志  
xxxQueryGroup := PrivateGroup.Group("模块路径")
```

#### 1.3 标准示例
```go
func (r *AdminUserRouter) InitAdminUserRouter(PrivateGroup *gin.RouterGroup) {
	adminUserApi := v1.ApiGroupApp.JyhappApiGroup.AdminUserApi
	
	// 操作记录路由组 - 需要记录操作日志的路由
	// 包括：创建、更新、删除、状态变更等操作
	adminUserRecordGroup := PrivateGroup.Group("admin/user").Use(middleware.OperationRecord())
	{
		adminUserRecordGroup.POST("create", adminUserApi.CreateUser)              // 创建用户
		adminUserRecordGroup.PUT("update", adminUserApi.UpdateUser)               // 更新用户
		adminUserRecordGroup.POST("toggle_status", adminUserApi.ToggleUserStatus) // 切换用户状态
		adminUserRecordGroup.DELETE("delete", adminUserApi.DeleteUser)            // 删除用户
	}
	
	// 查询路由组 - 不需要记录操作日志的路由
	// 包括：列表查询、详情查询等只读操作
	adminUserQueryGroup := PrivateGroup.Group("admin/user")
	{
		adminUserQueryGroup.GET("list", adminUserApi.GetUserList)     // 获取用户列表
		adminUserQueryGroup.GET("detail", adminUserApi.GetUserDetail) // 获取用户详情
	}
}
```

### 2. 请求方法分类

#### 2.1 需要操作记录的请求类型
- `POST` - 创建操作
- `PUT` - 更新操作
- `PATCH` - 部分更新操作
- `DELETE` - 删除操作
- 状态变更类操作（如启用/禁用）
- 批量操作

#### 2.2 不需要操作记录的请求类型
- `GET` - 查询操作
- 文件下载操作
- 导出操作（可选，根据业务需求）

### 3. 注释规范

#### 3.1 路由函数注释
每个路由初始化函数必须包含：
- 功能描述
- 路由规则说明
- 分组策略说明

#### 3.2 路由分组注释
```go
// 操作记录路由组 - 需要记录操作日志的路由
// 包括：创建、更新、删除、状态变更等操作
recordGroup := xxx

// 查询路由组 - 不需要记录操作日志的路由
// 包括：列表查询、详情查询等只读操作
queryGroup := xxx
```

#### 3.3 单个路由注释
每个路由注册必须有清晰的中文注释说明功能。

### 4. 路由组织结构

#### 4.1 目录结构
```
server/router/
├── system/          # 系统管理路由
├── jyhapp/          # 业务模块路由
│   ├── enter.go     # 路由组注册
│   ├── user.go      # 用户相关路由
│   ├── admin_user.go # 管理员用户路由
│   └── ...
└── example/         # 示例路由
```

#### 4.2 文件命名
- 使用小写字母和下划线
- 按功能模块划分文件
- 管理端路由使用 `admin_` 前缀

### 5. 性能优化考虑

#### 5.1 中间件使用
- 操作记录中间件有性能开销，仅在必要时使用
- 查询类接口不使用操作记录中间件
- 根据业务需求选择性使用其他中间件

#### 5.2 路由分组
- 合理使用路由分组减少重复配置
- 避免过深的路由嵌套
- 相同权限的路由放在同一分组

### 6. 安全规范

#### 6.1 权限控制
- 所有管理端路由必须经过身份验证
- 使用 `PrivateGroup` 确保JWT验证
- 合理配置Casbin权限控制

#### 6.2 操作审计
- 重要操作必须记录操作日志
- 敏感数据变更必须可追溯
- 操作记录包含用户信息和操作详情

### 7. 错误处理

#### 7.1 统一错误响应
- 使用项目统一的错误响应格式
- 提供有意义的错误信息
- 区分客户端错误和服务端错误

#### 7.2 日志记录
- 关键操作记录详细日志
- 错误信息记录完整堆栈
- 使用结构化日志格式

## 实施建议

### 1. 新功能开发
- 严格按照规范设计路由
- 代码审查时检查规范遵循情况
- 使用模板快速生成标准路由代码

### 2. 现有代码改造
- 逐步按规范重构现有路由
- 优先改造核心业务模块
- 保持向后兼容性

### 3. 团队协作
- 团队成员熟悉本规范
- 定期更新和完善规范
- 建立代码评审检查点

## 版本历史

- v1.0.0 (2024-12-19) - 初始版本，定义基础路由设计规范 