package insreport

import (
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"github.com/stretchr/testify/assert"
)

// TestSimpleJSONProcessor 测试简化JSON处理器
func TestSimpleJSONProcessor(t *testing.T) {
	// 创建测试配置
	configs := []CostTypeConfigItem{
		{Id: 1, CategoryName: "营业收入", CategoryCode: "REVENUE", IsCalculated: false, SortOrder: 100},
		{Id: 2, CategoryName: "营业成本", CategoryCode: "COST", IsCalculated: false, SortOrder: 200},
		{Id: 3, CategoryName: "销售费用", CategoryCode: "SALES_EXPENSE", IsCalculated: false, SortOrder: 400},
		{Id: 4, CategoryName: "管理费用", CategoryCode: "ADMIN_EXPENSE", IsCalculated: false, SortOrder: 500},
		{Id: 100, CategoryName: "毛利润", CategoryCode: "GROSS_PROFIT", IsCalculated: true, SortOrder: 300},
		{Id: 101, CategoryName: "毛利率", CategoryCode: "GROSS_MARGIN", IsCalculated: true, SortOrder: 350},
	}

	processor := NewSimpleJSONProcessor(configs)

	t.Run("创建简化JSON公式", func(t *testing.T) {
		formulaJSON, err := processor.CreateFormulaJSON(
			"[营业收入] - [营业成本]",
			"毛利润计算公式",
		)

		assert.NoError(t, err)
		assert.Contains(t, formulaJSON, "营业收入")
		assert.Contains(t, formulaJSON, "营业成本")
		assert.Contains(t, formulaJSON, "毛利润计算公式")
	})

	t.Run("验证JSON公式", func(t *testing.T) {
		validJSON := `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润计算"}`
		errors := processor.ValidateFormulaJSON(validJSON)
		assert.Empty(t, errors)

		invalidJSON := `{"expression":"[不存在的分类] - [营业成本]","references":[999,2],"description":"无效公式"}`
		errors = processor.ValidateFormulaJSON(invalidJSON)
		assert.NotEmpty(t, errors)
	})

	t.Run("依赖关系排序", func(t *testing.T) {
		// 创建有依赖关系的配置
		calculatedConfigs := []CostTypeConfigItem{
			{
				Id: 100, CategoryName: "毛利润", IsCalculated: true,
				CalculationFormula: `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润"}`,
			},
			{
				Id: 101, CategoryName: "毛利率", IsCalculated: true,
				CalculationFormula: `{"expression":"[毛利润] / [营业收入] %","references":[100,1],"description":"毛利率"}`,
			},
		}

		sorted := processor.sortConfigsByDependency(calculatedConfigs)

		// 毛利润应该在毛利率之前计算
		assert.Equal(t, uint(100), sorted[0].Id) // 毛利润
		assert.Equal(t, uint(101), sorted[1].Id) // 毛利率
	})

	t.Run("计算表达式", func(t *testing.T) {
		// 准备测试数据
		items := []FinancialSummaryItem{
			{
				CategoryId: 1, CategoryName: "营业收入",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 1000000, // 100万
					"2025-02": 1200000, // 120万
				},
				TotalAmount: 2200000, // 220万
			},
			{
				CategoryId: 2, CategoryName: "营业成本",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 600000, // 60万
					"2025-02": 720000, // 72万
				},
				TotalAmount: 1320000, // 132万
			},
		}

		dataMap := createDataMap(items)
		timeColumns := []string{"2025-01", "2025-02"}

		// 测试基本减法表达式（分类名称引用）
		result := processor.executeDirectFormula("[营业收入] - [营业成本]", dataMap, timeColumns)
		assert.NotNil(t, result)
		assert.Equal(t, jtypes.JPrice(400000), result.MonthlyAmounts["2025-01"]) // 100-60=40万
		assert.Equal(t, jtypes.JPrice(480000), result.MonthlyAmounts["2025-02"]) // 120-72=48万
		assert.Equal(t, jtypes.JPrice(880000), result.TotalAmount)               // 220-132=88万
	})

	t.Run("计算ID引用表达式", func(t *testing.T) {
		// 准备测试数据
		items := []FinancialSummaryItem{
			{
				CategoryId: 1, CategoryName: "营业收入",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 1000000, // 100万
				},
				TotalAmount: 1000000,
			},
			{
				CategoryId: 2, CategoryName: "营业成本",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 600000, // 60万
				},
				TotalAmount: 600000,
			},
		}

		dataMap := createDataMap(items)
		timeColumns := []string{"2025-01"}

		// 测试ID引用表达式
		result := processor.executeDirectFormula("#1 - #2", dataMap, timeColumns)
		assert.NotNil(t, result)
		assert.Equal(t, jtypes.JPrice(400000), result.MonthlyAmounts["2025-01"]) // 100-60=40万
		assert.Equal(t, jtypes.JPrice(400000), result.TotalAmount)               // 100-60=40万
	})

	t.Run("计算混合引用表达式", func(t *testing.T) {
		// 准备测试数据
		items := []FinancialSummaryItem{
			{
				CategoryId: 1, CategoryName: "营业收入",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 1000000, // 100万
				},
				TotalAmount: 1000000,
			},
			{
				CategoryId: 2, CategoryName: "营业成本",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 600000, // 60万
				},
				TotalAmount: 600000,
			},
		}

		dataMap := createDataMap(items)
		timeColumns := []string{"2025-01"}

		// 测试混合引用表达式：分类名称 + ID引用
		result := processor.executeDirectFormula("[营业收入] - #2", dataMap, timeColumns)
		assert.NotNil(t, result)
		assert.Equal(t, jtypes.JPrice(400000), result.MonthlyAmounts["2025-01"]) // 100-60=40万
		assert.Equal(t, jtypes.JPrice(400000), result.TotalAmount)               // 100-60=40万

		// 测试混合引用表达式：ID引用 + 分类名称
		result2 := processor.executeDirectFormula("#1 - [营业成本]", dataMap, timeColumns)
		assert.NotNil(t, result2)
		assert.Equal(t, jtypes.JPrice(400000), result2.MonthlyAmounts["2025-01"]) // 100-60=40万
		assert.Equal(t, jtypes.JPrice(400000), result2.TotalAmount)               // 100-60=40万
	})

	t.Run("计算百分比表达式", func(t *testing.T) {
		// 准备测试数据
		items := []FinancialSummaryItem{
			{
				CategoryId: 1, CategoryName: "营业收入",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 1000000, // 100万
				},
				TotalAmount: 1000000,
			},
			{
				CategoryId: 100, CategoryName: "毛利润",
				MonthlyAmounts: map[string]jtypes.JPrice{
					"2025-01": 400000, // 40万
				},
				TotalAmount: 400000,
			},
		}

		dataMap := createDataMap(items)
		timeColumns := []string{"2025-01"}

		// 测试百分比表达式
		result := processor.executeDirectFormula("[毛利润] / [营业收入] %", dataMap, timeColumns)
		assert.NotNil(t, result)
		// 40万 / 100万 * 100% = 40%，存储为4000（保持精度）
		assert.Equal(t, jtypes.JPrice(4000), result.MonthlyAmounts["2025-01"])
		assert.Equal(t, jtypes.JPrice(4000), result.TotalAmount)
	})

	t.Run("处理计算型分类", func(t *testing.T) {
		// 准备基础数据
		items := []FinancialSummaryItem{
			{
				CategoryId: 1, CategoryName: "营业收入", SortOrder: 100,
				MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 1000000},
				TotalAmount:    1000000,
			},
			{
				CategoryId: 2, CategoryName: "营业成本", SortOrder: 200,
				MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 600000},
				TotalAmount:    600000,
			},
		}

		// 添加计算型配置
		calculatedConfigs := []CostTypeConfigItem{
			{
				Id: 100, CategoryName: "毛利润", IsCalculated: true, SortOrder: 300,
				CalculationFormula: `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润"}`,
			},
		}

		timeColumns := []string{"2025-01"}
		result := processor.ProcessCalculatedCategoriesJSON(items, calculatedConfigs, timeColumns)

		// 验证结果包含计算型分类
		assert.Len(t, result, 3) // 原有2个 + 新增1个

		// 查找毛利润项
		var grossProfitItem *FinancialSummaryItem
		for i := range result {
			if result[i].CategoryId == 100 {
				grossProfitItem = &result[i]
				break
			}
		}

		assert.NotNil(t, grossProfitItem)
		assert.Equal(t, "毛利润", grossProfitItem.CategoryName)
		assert.Equal(t, jtypes.JPrice(400000), grossProfitItem.MonthlyAmounts["2025-01"])
		assert.Equal(t, jtypes.JPrice(400000), grossProfitItem.TotalAmount)
	})

	t.Run("循环依赖检测", func(t *testing.T) {
		// 创建循环依赖的配置
		cyclicConfigs := []CostTypeConfigItem{
			{
				Id: 100, CategoryName: "分类A", IsCalculated: true,
				CalculationFormula: `{"expression":"[分类B] + [营业收入]","references":[101,1],"description":"依赖B"}`,
			},
			{
				Id: 101, CategoryName: "分类B", IsCalculated: true,
				CalculationFormula: `{"expression":"[分类A] - [营业成本]","references":[100,2],"description":"依赖A"}`,
			},
		}

		// 排序应该能处理循环依赖而不崩溃
		sorted := processor.sortConfigsByDependency(cyclicConfigs)
		assert.Len(t, sorted, 2)
	})

	t.Run("错误处理", func(t *testing.T) {
		// 测试空表达式
		result := processor.executeDirectFormula("", nil, nil)
		assert.Nil(t, result)

		// 测试无效表达式
		result = processor.executeDirectFormula("[不存在的分类]", nil, nil)
		assert.Nil(t, result)

		// 测试不存在的ID引用
		result = processor.executeDirectFormula("#999", nil, nil)
		assert.Nil(t, result)

		// 测试无效的ID格式
		result = processor.executeDirectFormula("#abc", nil, nil)
		assert.Nil(t, result)

		// 测试除零
		items := []FinancialSummaryItem{
			{CategoryId: 1, MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 100}, TotalAmount: 100},
			{CategoryId: 2, MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 0}, TotalAmount: 0},
		}
		dataMap := createDataMap(items)

		result = processor.executeDirectFormula("[营业收入] / [营业成本]", dataMap, []string{"2025-01"})
		assert.NotNil(t, result)
		assert.Equal(t, jtypes.JPrice(0), result.MonthlyAmounts["2025-01"]) // 除零返回0

		// 测试ID引用除零
		result = processor.executeDirectFormula("#1 / #2", dataMap, []string{"2025-01"})
		assert.NotNil(t, result)
		assert.Equal(t, jtypes.JPrice(0), result.MonthlyAmounts["2025-01"]) // 除零返回0
	})

	t.Run("依赖关系提取测试", func(t *testing.T) {
		// 测试分类名称引用的依赖提取
		deps := processor.extractDependenciesFromExpression("[营业收入] - [营业成本]")
		assert.Contains(t, deps, uint(1)) // 营业收入
		assert.Contains(t, deps, uint(2)) // 营业成本

		// 测试ID引用的依赖提取
		deps = processor.extractDependenciesFromExpression("#1 - #2")
		assert.Contains(t, deps, uint(1))
		assert.Contains(t, deps, uint(2))

		// 测试混合引用的依赖提取
		deps = processor.extractDependenciesFromExpression("[营业收入] - #2")
		assert.Contains(t, deps, uint(1)) // 营业收入
		assert.Contains(t, deps, uint(2)) // ID引用

		// 测试复杂表达式的依赖提取
		deps = processor.extractDependenciesFromExpression("([营业收入] - #2) / [营业收入] %")
		assert.Contains(t, deps, uint(1)) // 营业收入（出现两次，但应该去重）
		assert.Contains(t, deps, uint(2)) // 营业成本
		assert.Len(t, deps, 2)            // 去重后应该只有2个
	})

	t.Run("表达式验证测试", func(t *testing.T) {
		// 测试有效的分类名称引用
		assert.True(t, processor.isValidExpression("[营业收入] - [营业成本]"))

		// 测试有效的ID引用
		assert.True(t, processor.isValidExpression("#1 - #2"))

		// 测试有效的混合引用
		assert.True(t, processor.isValidExpression("[营业收入] - #2"))

		// 测试无效的分类名称引用
		assert.False(t, processor.isValidExpression("[不存在的分类] - [营业成本]"))

		// 测试无效的ID引用
		assert.False(t, processor.isValidExpression("#999 - #2"))

		// 测试无效的ID格式
		assert.False(t, processor.isValidExpression("#abc - #2"))

		// 测试没有引用的表达式
		assert.False(t, processor.isValidExpression("100 + 200"))
	})
}

// TestValidationError 测试验证错误
func TestValidationError(t *testing.T) {
	configs := []CostTypeConfigItem{
		{Id: 1, CategoryName: "营业收入", CategoryCode: "REVENUE"},
	}

	processor := NewSimpleJSONProcessor(configs)

	t.Run("JSON格式错误", func(t *testing.T) {
		invalidJSON := `{"expression":"[营业收入] - [营业成本]","references":[1,2]` // 缺少结束括号
		errors := processor.ValidateFormulaJSON(invalidJSON)
		assert.NotEmpty(t, errors)
		assert.Equal(t, "JSON_PARSE_ERROR", errors[0].Type)
	})

	t.Run("表达式语法错误", func(t *testing.T) {
		invalidExprJSON := `{"expression":"[不存在的分类] - [营业成本]","references":[999,2],"description":"无效"}`
		errors := processor.ValidateFormulaJSON(invalidExprJSON)
		assert.NotEmpty(t, errors)
		assert.Equal(t, "SYNTAX_ERROR", errors[0].Type)
	})
}

// BenchmarkSimpleJSONProcessor 性能测试
func BenchmarkSimpleJSONProcessor(b *testing.B) {
	configs := []CostTypeConfigItem{
		{Id: 1, CategoryName: "营业收入", CategoryCode: "REVENUE"},
		{Id: 2, CategoryName: "营业成本", CategoryCode: "COST"},
	}

	processor := NewSimpleJSONProcessor(configs)

	items := []FinancialSummaryItem{
		{CategoryId: 1, MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 1000000}, TotalAmount: 1000000},
		{CategoryId: 2, MonthlyAmounts: map[string]jtypes.JPrice{"2025-01": 600000}, TotalAmount: 600000},
	}

	dataMap := createDataMap(items)
	timeColumns := []string{"2025-01"}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		processor.executeDirectFormula("[营业收入] - [营业成本]", dataMap, timeColumns)
	}
}
