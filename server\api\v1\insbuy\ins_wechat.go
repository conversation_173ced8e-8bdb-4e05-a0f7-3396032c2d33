package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"net/http"
)

type InsWechatApi struct {
}

// VerifyURL 验证服务器地址的有效性
func (ins *InsWechatApi) VerifyURL(c *gin.Context) {
	var req insReq.VerifyReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	store := c.Param("store")
	req.SetStoreId(store)
	c.String(http.StatusOK, insWechatService.VerifyURL(req))
	return
}

// Send 发送消息
func (ins *InsWechatApi) Send(c *gin.Context) {
	insWechatService.Send()
	response.OkWithMessage("发送成功", c)
	return
}

// IsSubscribe

// IsSubscribe 验证用户是否关注公众号
func (ins *InsWechatApi) IsSubscribe(c *gin.Context) {
	var req insReq.SubscribeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWechatService.IsSubscribe(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(resp, "查询成功", c)
	return
}

// GetUserInfo 查询用户信息-是否有openid
func (ins *InsWechatApi) GetUserInfo(c *gin.Context) {
	var req insReq.WxUserInfoReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWechatService.GetUserInfo(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(resp, "查询成功", c)
	return
}
