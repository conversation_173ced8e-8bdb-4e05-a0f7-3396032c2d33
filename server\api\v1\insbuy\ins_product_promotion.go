package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetProductPromotionList 获取商品促销列表
// @Tags InsProductPromotion
// @Summary 获取商品促销列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param pageSize query int false "pageSize"
// @Param page query int false "page"
// @Param title query string false "title"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getProductPromotionList [get]
func (insProductApi *InsProductApi) GetProductPromotionList(c *gin.Context) {
	var req insbuyReq.ProductPromotionListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, total, err := insProductService.GetProductPromotionList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, c)
	}
}

// GetClientPromotionList 获取客户端商品促销列表
// @Tags InsProductPromotion
// @Summary 获取商品促销列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param pageSize query int false "pageSize"
// @Param page query int false "page"
// @Param userType query int false "title"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getClientPromotionList [get]
func (insProductApi *InsProductApi) GetClientPromotionList(c *gin.Context) {
	var req insbuyReq.GetClientPromotionListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, total, err := insProductService.GetClientPromotionList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, c)
	}
}

// CreateProductPromotion  创建商品促销
// @Tags InsProductPromotion
// @Summary 创建商品促销
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ProductPromotionReq true "创建商品促销"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/createProductPromotion [post]
func (insProductApi *InsProductApi) CreateProductPromotion(c *gin.Context) {
	var req insbuyReq.ProductPromotionReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insProductService.CreateProductPromotion(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateProductPromotion 修改商品促销
// @Tags InsProductPromotion
// @Summary 修改商品促销
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ProductPromotionReq true "修改商品促销"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insProduct/updateProductPromotion/{id} [put]
func (insProductApi *InsProductApi) UpdateProductPromotion(c *gin.Context) {
	var req insbuyReq.ProductPromotionReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insProductService.UpdateProductPromotion(req)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// ProductPromotionDetail 获取商品促销详情
// @Tags InsProductPromotion
// @Summary 获取商品促销详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insProduct/getProductPromotionDetail/{id} [get]
func (insProductApi *InsProductApi) ProductPromotionDetail(c *gin.Context) {
	var req request.GetById
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insProductService.ProductPromotionDetail(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}
