// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareStaff(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareStaff {
	_insReportRuleSalesShareStaff := insReportRuleSalesShareStaff{}

	_insReportRuleSalesShareStaff.insReportRuleSalesShareStaffDo.UseDB(db, opts...)
	_insReportRuleSalesShareStaff.insReportRuleSalesShareStaffDo.UseModel(&insbuy.InsReportRuleSalesShareStaff{})

	tableName := _insReportRuleSalesShareStaff.insReportRuleSalesShareStaffDo.TableName()
	_insReportRuleSalesShareStaff.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareStaff.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareStaff.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareStaff.UserId = field.NewUint(tableName, "user_id")

	_insReportRuleSalesShareStaff.fillFieldMap()

	return _insReportRuleSalesShareStaff
}

type insReportRuleSalesShareStaff struct {
	insReportRuleSalesShareStaffDo

	ALL    field.Asterisk
	ID     field.Uint
	RuleId field.Uint
	UserId field.Uint

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareStaff) Table(newTableName string) *insReportRuleSalesShareStaff {
	i.insReportRuleSalesShareStaffDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareStaff) As(alias string) *insReportRuleSalesShareStaff {
	i.insReportRuleSalesShareStaffDo.DO = *(i.insReportRuleSalesShareStaffDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareStaff) updateTableName(table string) *insReportRuleSalesShareStaff {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.UserId = field.NewUint(table, "user_id")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareStaff) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareStaff) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 3)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["user_id"] = i.UserId
}

func (i insReportRuleSalesShareStaff) clone(db *gorm.DB) insReportRuleSalesShareStaff {
	i.insReportRuleSalesShareStaffDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareStaff) replaceDB(db *gorm.DB) insReportRuleSalesShareStaff {
	i.insReportRuleSalesShareStaffDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareStaffDo struct{ gen.DO }

func (i insReportRuleSalesShareStaffDo) Debug() *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareStaffDo) WithContext(ctx context.Context) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareStaffDo) ReadDB() *insReportRuleSalesShareStaffDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareStaffDo) WriteDB() *insReportRuleSalesShareStaffDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareStaffDo) Session(config *gorm.Session) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareStaffDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareStaffDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareStaffDo) Not(conds ...gen.Condition) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareStaffDo) Or(conds ...gen.Condition) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareStaffDo) Select(conds ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareStaffDo) Where(conds ...gen.Condition) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareStaffDo) Order(conds ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareStaffDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareStaffDo) Omit(cols ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareStaffDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareStaffDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareStaffDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareStaffDo) Group(cols ...field.Expr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareStaffDo) Having(conds ...gen.Condition) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareStaffDo) Limit(limit int) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareStaffDo) Offset(offset int) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareStaffDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareStaffDo) Unscoped() *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareStaffDo) Create(values ...*insbuy.InsReportRuleSalesShareStaff) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareStaffDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareStaff, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareStaffDo) Save(values ...*insbuy.InsReportRuleSalesShareStaff) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareStaffDo) First() (*insbuy.InsReportRuleSalesShareStaff, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareStaff), nil
	}
}

func (i insReportRuleSalesShareStaffDo) Take() (*insbuy.InsReportRuleSalesShareStaff, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareStaff), nil
	}
}

func (i insReportRuleSalesShareStaffDo) Last() (*insbuy.InsReportRuleSalesShareStaff, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareStaff), nil
	}
}

func (i insReportRuleSalesShareStaffDo) Find() ([]*insbuy.InsReportRuleSalesShareStaff, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareStaff), err
}

func (i insReportRuleSalesShareStaffDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareStaff, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareStaff, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareStaffDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareStaff, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareStaffDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareStaffDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareStaffDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareStaffDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareStaffDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareStaffDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareStaffDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareStaffDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareStaff, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareStaff), nil
	}
}

func (i insReportRuleSalesShareStaffDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareStaff, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareStaff), nil
	}
}

func (i insReportRuleSalesShareStaffDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareStaff, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareStaffDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareStaffDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareStaffDo) Delete(models ...*insbuy.InsReportRuleSalesShareStaff) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareStaffDo) withDO(do gen.Dao) *insReportRuleSalesShareStaffDo {
	i.DO = *do.(*gen.DO)
	return i
}
