package datasource

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"gorm.io/gen"
)

// ArtistActivity 艺人活动费用数据适配器
type ArtistActivity struct {
	Code DataSourceCode
}

// Query 查询艺人活动费用数据
func (a ArtistActivity) Query(ctx context.Context, q *query.Query, p SourceQueryParams) (res SourceQueryRes, err error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails

	var condition []gen.Condition
	{
		if !p.StartDate.IsZero() {
			condition = append(condition, dbResult.StartDate.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbResult.EndDate.Lte(p.EndDate))
		}
		if len(p.StoreIds) > 0 {
			condition = append(condition, dbResult.StoreID.In(p.StoreIds...))
		}
		condition = append(condition, dbResult.Code.Eq(a.Code.ToString()))
	}

	result := make([]SourceQueryItems, 0)
	err = dbResult.
		LeftJoin(dbDetails, dbDetails.ResultId.EqCol(dbResult.ID)).
		Where(condition...).Scan(&result)
	if err != nil {
		return
	}

	res.Data = result
	return
}

// Storage 存储艺人活动费用数据
func (a ArtistActivity) Storage(ctx context.Context, q *query.Query, p SourceStorageParams) (err error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	result := &insbuy.InsReportIntermediateResult{}
	err = dbResult.Where(
		dbResult.StoreID.In(p.StoreIds...)).
		Where(dbResult.Code.Eq(a.Code.ToString())).Scan(result)
	if err != nil {
		return
	}
	if result.ID == 0 && len(p.Data) > 0 {
		u := p.StoreIds[0]
		result = p.Data[u].Data
		err = dbResult.Create(result)
		if err != nil {
			return
		}
	}
	//增量附表数据-以最新数据为准
	id := result.ID
	//删除日期一样的数据
	times := make([]time.Time, 0)
	for _, v := range p.Data {
		for _, v2 := range v.List {
			times = append(times, v2.BusinessDay.Time)
		}
	}
	_, err = dbDetails.Where(dbDetails.BusinessDay.In(times...),
		dbDetails.ResultId.Eq(result.ID)).Delete()
	if err != nil {
		return
	}
	//写入数据
	for _, v := range p.Data {
		for i, _ := range v.List {
			v.List[i].ResultId = id
		}
		err = dbDetails.CreateInBatches(v.List, len(v.List))
		if err != nil {
			return
		}
	}

	return
}
