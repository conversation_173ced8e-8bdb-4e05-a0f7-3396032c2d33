package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSqlReportCateApi struct {
}

// CreateInsSqlReportCate 创建InsSqlReportCate
// @Tags 报表相关
// @Summary 创建InsSqlReportCate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSqlReportCate true "创建InsSqlReportCate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insReport/sql/cate/create [post]
func (insReportApi *InsReportApi) CreateInsSqlReportCate(c *gin.Context) {
	var insReport insbuy.InsSqlReportCate
	err := c.ShouldBindJSON(&insReport)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.CreateInsSqlReportCate(&insReport); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSqlReportCate 删除InsSqlReportCate
// @Tags 报表相关
// @Summary 删除InsSqlReportCate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSqlReportCate true "删除InsSqlReportCate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insReport/sql/cate/delete [delete]
func (insReportApi *InsReportApi) DeleteInsSqlReportCate(c *gin.Context) {
	var insReport insbuy.InsSqlReportCate
	err := c.ShouldBindJSON(&insReport)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.DeleteInsSqlReportCate(insReport); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSqlReportCateByIds 批量删除InsSqlReportCate
// @Tags 报表相关
// @Summary 批量删除InsSqlReportCate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSqlReportCate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insReport/sql/cates/delete [delete]
func (insReportApi *InsReportApi) DeleteInsSqlReportCateByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.DeleteInsSqlReportCateByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSqlReportCate 更新InsSqlReportCate
// @Tags 报表相关
// @Summary 更新InsSqlReportCate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSqlReportCate true "更新InsSqlReportCate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insReport/sql/cate/update [put]
func (insReportApi *InsReportApi) UpdateInsSqlReportCate(c *gin.Context) {
	var insReport insbuy.InsSqlReportCate
	err := c.ShouldBindJSON(&insReport)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.UpdateInsSqlReportCate(insReport); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSqlReportCate 用id查询InsSqlReportCate
// @Tags 报表相关
// @Summary 用id查询InsSqlReportCate
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsSqlReportCate true "用id查询InsSqlReportCate"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insReport/sql/cate [get]
func (insReportApi *InsReportApi) FindInsSqlReportCate(c *gin.Context) {
	var insReport insbuy.InsSqlReportCate
	err := c.ShouldBindQuery(&insReport)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsReport, err := reportService.GetInsSqlReportCate(insReport.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsReport": reinsReport}, c)
	}
}

// GetInsSqlReportCateList 分页获取InsSqlReportCate列表
// @Tags 报表相关
// @Summary 分页获取InsSqlReportCate列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSqlReportCateSearch true "分页获取InsSqlReportCate列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insReport/sql/cates [get]
func (insReportApi *InsReportApi) GetInsSqlReportCateList(c *gin.Context) {
	var pageInfo insbuyReq.InsSqlReportCateSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := reportService.GetInsSqlReportCateInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsSqlRule 创建InsSqlRule
// @Tags InsSqlRule
// @Summary 创建InsSqlRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SqlRuleReq true "创建InsSqlRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insReport/rule/create [post]
func (insReportApi *InsReportApi) CreateInsSqlRule(c *gin.Context) {
	var req insbuyReq.SqlRuleReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.CreateInsSqlRule(&req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSqlRule 删除InsSqlRule
// @Tags InsSqlRule
// @Summary 删除InsSqlRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSqlRule true "删除InsSqlRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insReport/rule/delete [delete]
func (insReportApi *InsReportApi) DeleteInsSqlRule(c *gin.Context) {
	var insSqlRule insbuy.InsSqlRule
	err := c.ShouldBindJSON(&insSqlRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.DeleteInsSqlRule(insSqlRule); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSqlRuleByIds 批量删除InsSqlRule
// @Tags InsSqlRule
// @Summary 批量删除InsSqlRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSqlRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insReport/rules/delete [delete]
func (insReportApi *InsReportApi) DeleteInsSqlRuleByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := reportService.DeleteInsSqlRuleByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSqlRule 更新InsSqlRule
// @Tags InsSqlRule
// @Summary 更新InsSqlRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SqlRuleReq true "更新InsSqlRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insReport/rule/update [put]
func (insReportApi *InsReportApi) UpdateInsSqlRule(c *gin.Context) {
	var req insbuyReq.SqlRuleReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.Rules{
		"id": {utils.NotEmpty()},
	})
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := reportService.UpdateInsSqlRule(&req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSqlRule 用id查询InsSqlRule
// @Tags InsSqlRule
// @Summary 用id查询InsSqlRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "用id查询InsSqlRule"
// @Success   200   {object}  response.Response{data=insbuyResp.SqlRuleDetailsResp,msg=string}  "获取赠送记录列表"
// @Router /insReport/rule [get]
func (insReportApi *InsReportApi) FindInsSqlRule(c *gin.Context) {
	var req request.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var reinsSqlRule insbuyResp.SqlRuleDetailsResp
	if reinsSqlRule, err = reportService.GetInsSqlRule(req.Uint()); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsSqlRule, c)
	}
}

// GetInsSqlRuleList 分页获取InsSqlRule列表
// @Tags InsSqlRule
// @Summary 分页获取InsSqlRule列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSqlRuleSearch true "分页获取InsSqlRule列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insReport/rules [get]
func (insReportApi *InsReportApi) GetInsSqlRuleList(c *gin.Context) {
	var req insbuyReq.InsSqlRuleSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := reportService.GetInsSqlRuleInfoList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}
