// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSqlReportCate(db *gorm.DB, opts ...gen.DOOption) insSqlReportCate {
	_insSqlReportCate := insSqlReportCate{}

	_insSqlReportCate.insSqlReportCateDo.UseDB(db, opts...)
	_insSqlReportCate.insSqlReportCateDo.UseModel(&insbuy.InsSqlReportCate{})

	tableName := _insSqlReportCate.insSqlReportCateDo.TableName()
	_insSqlReportCate.ALL = field.NewAsterisk(tableName)
	_insSqlReportCate.ID = field.NewUint(tableName, "id")
	_insSqlReportCate.CreatedAt = field.NewTime(tableName, "created_at")
	_insSqlReportCate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSqlReportCate.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSqlReportCate.StoreId = field.NewUint(tableName, "store_id")
	_insSqlReportCate.Name = field.NewString(tableName, "name")
	_insSqlReportCate.Sort = field.NewInt(tableName, "sort")
	_insSqlReportCate.Status = field.NewUint(tableName, "status")
	_insSqlReportCate.Remark = field.NewString(tableName, "remark")

	_insSqlReportCate.fillFieldMap()

	return _insSqlReportCate
}

type insSqlReportCate struct {
	insSqlReportCateDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	StoreId   field.Uint
	Name      field.String
	Sort      field.Int
	Status    field.Uint
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insSqlReportCate) Table(newTableName string) *insSqlReportCate {
	i.insSqlReportCateDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSqlReportCate) As(alias string) *insSqlReportCate {
	i.insSqlReportCateDo.DO = *(i.insSqlReportCateDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSqlReportCate) updateTableName(table string) *insSqlReportCate {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Sort = field.NewInt(table, "sort")
	i.Status = field.NewUint(table, "status")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insSqlReportCate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSqlReportCate) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["sort"] = i.Sort
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
}

func (i insSqlReportCate) clone(db *gorm.DB) insSqlReportCate {
	i.insSqlReportCateDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSqlReportCate) replaceDB(db *gorm.DB) insSqlReportCate {
	i.insSqlReportCateDo.ReplaceDB(db)
	return i
}

type insSqlReportCateDo struct{ gen.DO }

func (i insSqlReportCateDo) Debug() *insSqlReportCateDo {
	return i.withDO(i.DO.Debug())
}

func (i insSqlReportCateDo) WithContext(ctx context.Context) *insSqlReportCateDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSqlReportCateDo) ReadDB() *insSqlReportCateDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSqlReportCateDo) WriteDB() *insSqlReportCateDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSqlReportCateDo) Session(config *gorm.Session) *insSqlReportCateDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSqlReportCateDo) Clauses(conds ...clause.Expression) *insSqlReportCateDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSqlReportCateDo) Returning(value interface{}, columns ...string) *insSqlReportCateDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSqlReportCateDo) Not(conds ...gen.Condition) *insSqlReportCateDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSqlReportCateDo) Or(conds ...gen.Condition) *insSqlReportCateDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSqlReportCateDo) Select(conds ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSqlReportCateDo) Where(conds ...gen.Condition) *insSqlReportCateDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSqlReportCateDo) Order(conds ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSqlReportCateDo) Distinct(cols ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSqlReportCateDo) Omit(cols ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSqlReportCateDo) Join(table schema.Tabler, on ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSqlReportCateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSqlReportCateDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSqlReportCateDo) Group(cols ...field.Expr) *insSqlReportCateDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSqlReportCateDo) Having(conds ...gen.Condition) *insSqlReportCateDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSqlReportCateDo) Limit(limit int) *insSqlReportCateDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSqlReportCateDo) Offset(offset int) *insSqlReportCateDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSqlReportCateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSqlReportCateDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSqlReportCateDo) Unscoped() *insSqlReportCateDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSqlReportCateDo) Create(values ...*insbuy.InsSqlReportCate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSqlReportCateDo) CreateInBatches(values []*insbuy.InsSqlReportCate, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSqlReportCateDo) Save(values ...*insbuy.InsSqlReportCate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSqlReportCateDo) First() (*insbuy.InsSqlReportCate, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReportCate), nil
	}
}

func (i insSqlReportCateDo) Take() (*insbuy.InsSqlReportCate, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReportCate), nil
	}
}

func (i insSqlReportCateDo) Last() (*insbuy.InsSqlReportCate, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReportCate), nil
	}
}

func (i insSqlReportCateDo) Find() ([]*insbuy.InsSqlReportCate, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSqlReportCate), err
}

func (i insSqlReportCateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSqlReportCate, err error) {
	buf := make([]*insbuy.InsSqlReportCate, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSqlReportCateDo) FindInBatches(result *[]*insbuy.InsSqlReportCate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSqlReportCateDo) Attrs(attrs ...field.AssignExpr) *insSqlReportCateDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSqlReportCateDo) Assign(attrs ...field.AssignExpr) *insSqlReportCateDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSqlReportCateDo) Joins(fields ...field.RelationField) *insSqlReportCateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSqlReportCateDo) Preload(fields ...field.RelationField) *insSqlReportCateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSqlReportCateDo) FirstOrInit() (*insbuy.InsSqlReportCate, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReportCate), nil
	}
}

func (i insSqlReportCateDo) FirstOrCreate() (*insbuy.InsSqlReportCate, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlReportCate), nil
	}
}

func (i insSqlReportCateDo) FindByPage(offset int, limit int) (result []*insbuy.InsSqlReportCate, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSqlReportCateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSqlReportCateDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSqlReportCateDo) Delete(models ...*insbuy.InsSqlReportCate) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSqlReportCateDo) withDO(do gen.Dao) *insSqlReportCateDo {
	i.DO = *do.(*gen.DO)
	return i
}
