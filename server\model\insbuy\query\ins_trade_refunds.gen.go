// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeRefunds(db *gorm.DB, opts ...gen.DOOption) insTradeRefunds {
	_insTradeRefunds := insTradeRefunds{}

	_insTradeRefunds.insTradeRefundsDo.UseDB(db, opts...)
	_insTradeRefunds.insTradeRefundsDo.UseModel(&insbuy.InsTradeRefunds{})

	tableName := _insTradeRefunds.insTradeRefundsDo.TableName()
	_insTradeRefunds.ALL = field.NewAsterisk(tableName)
	_insTradeRefunds.ID = field.NewUint64(tableName, "id")
	_insTradeRefunds.OrderId = field.NewUint64(tableName, "order_id")
	_insTradeRefunds.Amount = field.NewFloat64(tableName, "amount")
	_insTradeRefunds.Status = field.NewInt(tableName, "status")
	_insTradeRefunds.Remark = field.NewString(tableName, "remark")
	_insTradeRefunds.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeRefunds.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeRefunds.OrderSn = field.NewString(tableName, "order_sn")
	_insTradeRefunds.RefundSn = field.NewString(tableName, "refund_sn")
	_insTradeRefunds.PayLogId = field.NewUint64(tableName, "pay_log_id")
	_insTradeRefunds.TradeId = field.NewUint64(tableName, "trade_id")
	_insTradeRefunds.PayCode = field.NewString(tableName, "pay_code")
	_insTradeRefunds.PayId = field.NewUint(tableName, "pay_id")
	_insTradeRefunds.BusinessId = field.NewUint(tableName, "business_id")
	_insTradeRefunds.BusinessCode = field.NewString(tableName, "business_code")

	_insTradeRefunds.fillFieldMap()

	return _insTradeRefunds
}

type insTradeRefunds struct {
	insTradeRefundsDo

	ALL          field.Asterisk
	ID           field.Uint64
	OrderId      field.Uint64
	Amount       field.Float64
	Status       field.Int
	Remark       field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time
	OrderSn      field.String
	RefundSn     field.String
	PayLogId     field.Uint64
	TradeId      field.Uint64
	PayCode      field.String
	PayId        field.Uint
	BusinessId   field.Uint
	BusinessCode field.String

	fieldMap map[string]field.Expr
}

func (i insTradeRefunds) Table(newTableName string) *insTradeRefunds {
	i.insTradeRefundsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeRefunds) As(alias string) *insTradeRefunds {
	i.insTradeRefundsDo.DO = *(i.insTradeRefundsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeRefunds) updateTableName(table string) *insTradeRefunds {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.OrderId = field.NewUint64(table, "order_id")
	i.Amount = field.NewFloat64(table, "amount")
	i.Status = field.NewInt(table, "status")
	i.Remark = field.NewString(table, "remark")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OrderSn = field.NewString(table, "order_sn")
	i.RefundSn = field.NewString(table, "refund_sn")
	i.PayLogId = field.NewUint64(table, "pay_log_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.PayCode = field.NewString(table, "pay_code")
	i.PayId = field.NewUint(table, "pay_id")
	i.BusinessId = field.NewUint(table, "business_id")
	i.BusinessCode = field.NewString(table, "business_code")

	i.fillFieldMap()

	return i
}

func (i *insTradeRefunds) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeRefunds) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["order_sn"] = i.OrderSn
	i.fieldMap["refund_sn"] = i.RefundSn
	i.fieldMap["pay_log_id"] = i.PayLogId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["pay_code"] = i.PayCode
	i.fieldMap["pay_id"] = i.PayId
	i.fieldMap["business_id"] = i.BusinessId
	i.fieldMap["business_code"] = i.BusinessCode
}

func (i insTradeRefunds) clone(db *gorm.DB) insTradeRefunds {
	i.insTradeRefundsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeRefunds) replaceDB(db *gorm.DB) insTradeRefunds {
	i.insTradeRefundsDo.ReplaceDB(db)
	return i
}

type insTradeRefundsDo struct{ gen.DO }

func (i insTradeRefundsDo) Debug() *insTradeRefundsDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeRefundsDo) WithContext(ctx context.Context) *insTradeRefundsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeRefundsDo) ReadDB() *insTradeRefundsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeRefundsDo) WriteDB() *insTradeRefundsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeRefundsDo) Session(config *gorm.Session) *insTradeRefundsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeRefundsDo) Clauses(conds ...clause.Expression) *insTradeRefundsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeRefundsDo) Returning(value interface{}, columns ...string) *insTradeRefundsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeRefundsDo) Not(conds ...gen.Condition) *insTradeRefundsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeRefundsDo) Or(conds ...gen.Condition) *insTradeRefundsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeRefundsDo) Select(conds ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeRefundsDo) Where(conds ...gen.Condition) *insTradeRefundsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeRefundsDo) Order(conds ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeRefundsDo) Distinct(cols ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeRefundsDo) Omit(cols ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeRefundsDo) Join(table schema.Tabler, on ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeRefundsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeRefundsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeRefundsDo) Group(cols ...field.Expr) *insTradeRefundsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeRefundsDo) Having(conds ...gen.Condition) *insTradeRefundsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeRefundsDo) Limit(limit int) *insTradeRefundsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeRefundsDo) Offset(offset int) *insTradeRefundsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeRefundsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeRefundsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeRefundsDo) Unscoped() *insTradeRefundsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeRefundsDo) Create(values ...*insbuy.InsTradeRefunds) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeRefundsDo) CreateInBatches(values []*insbuy.InsTradeRefunds, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeRefundsDo) Save(values ...*insbuy.InsTradeRefunds) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeRefundsDo) First() (*insbuy.InsTradeRefunds, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeRefunds), nil
	}
}

func (i insTradeRefundsDo) Take() (*insbuy.InsTradeRefunds, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeRefunds), nil
	}
}

func (i insTradeRefundsDo) Last() (*insbuy.InsTradeRefunds, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeRefunds), nil
	}
}

func (i insTradeRefundsDo) Find() ([]*insbuy.InsTradeRefunds, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeRefunds), err
}

func (i insTradeRefundsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeRefunds, err error) {
	buf := make([]*insbuy.InsTradeRefunds, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeRefundsDo) FindInBatches(result *[]*insbuy.InsTradeRefunds, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeRefundsDo) Attrs(attrs ...field.AssignExpr) *insTradeRefundsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeRefundsDo) Assign(attrs ...field.AssignExpr) *insTradeRefundsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeRefundsDo) Joins(fields ...field.RelationField) *insTradeRefundsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeRefundsDo) Preload(fields ...field.RelationField) *insTradeRefundsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeRefundsDo) FirstOrInit() (*insbuy.InsTradeRefunds, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeRefunds), nil
	}
}

func (i insTradeRefundsDo) FirstOrCreate() (*insbuy.InsTradeRefunds, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeRefunds), nil
	}
}

func (i insTradeRefundsDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeRefunds, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeRefundsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeRefundsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeRefundsDo) Delete(models ...*insbuy.InsTradeRefunds) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeRefundsDo) withDO(do gen.Dao) *insTradeRefundsDo {
	i.DO = *do.(*gen.DO)
	return i
}
