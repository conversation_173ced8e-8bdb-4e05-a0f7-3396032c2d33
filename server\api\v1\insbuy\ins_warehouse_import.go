package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseImportLogServiceApi struct {
}

// FindHaiChangImportLog 用id查询InsWarehouseHaiChangImportLog
// @Tags InsWarehouseHaiChangImportLog
// @Summary 用id查询InsWarehouseHaiChangImportLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsWarehouseHaiChangImportLog true "用id查询InsWarehouseHaiChangImportLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/findHaiChangImportLog [get]
func (P *InsWarehouseImportLogServiceApi) FindHaiChangImportLog(c *gin.Context) {
	var req insbuy.InsWarehouseHaiChangImportLog
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseHaiChangImportLog, err := insWarehouseImportLogService.GetHaiChangImportLog(req.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsWarehouseHaiChangImportLog": reinsWarehouseHaiChangImportLog}, c)
	}
}

// GetHaiChangImportLogList 分页获取InsWarehouseHaiChangImportLog列表
// @Tags InsWarehouseHaiChangImportLog
// @Summary 分页获取InsWarehouseHaiChangImportLog列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseHaiChangImportLogSearch true "分页获取InsWarehouseHaiChangImportLog列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getHaiChangImportLogList [get]
func (P *InsWarehouseImportLogServiceApi) GetHaiChangImportLogList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseHaiChangImportLogSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseImportLogService.GetHaiChangImportLogInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
