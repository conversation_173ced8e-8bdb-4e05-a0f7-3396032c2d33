package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsAuditApi struct {
}

// CreateInsAuditConfig 创建审核配置规则
// @Tags InsAudit
// @Summary 创建InsAuditConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateAuditConfig true "创建InsAuditConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insAudit/createInsAudit [post]
func (i *InsAuditApi) CreateInsAuditConfig(c *gin.Context) {
	var InsAudit insbuyReq.CreateAuditConfig
	if err := GinMustBind(c, &InsAudit); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insAuditService.CreateInsAuditConfig(&InsAudit); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsAuditConfig 删除InsAuditConfig
// @Tags InsAudit
// @Summary 删除InsAuditConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsAudit true "删除InsAuditConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insAudit/deleteInsAudit [delete]
func (i *InsAuditApi) DeleteInsAuditConfig(c *gin.Context) {
	var InsAudit insbuy.InsAudit
	err := c.ShouldBindJSON(&InsAudit)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insAuditService.DeleteInsAuditConfig(InsAudit); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsAuditConfigByIds 批量删除InsAuditConfigByIds
// @Tags InsAudit
// @Summary 批量删除InsAuditConfigByIds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsAudit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insAudit/deleteInsAuditByIds [delete]
func (i *InsAuditApi) DeleteInsAuditConfigByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insAuditService.DeleteInsAuditConfigByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsAuditConfig 更新UpdateInsAuditConfig
// @Tags InsAudit
// @Summary 更新InsAudit
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateAuditConfig true "更新InsAudit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insAudit/updateInsAudit [put]
func (i *InsAuditApi) UpdateInsAuditConfig(c *gin.Context) {
	var InsAudit insbuyReq.UpdateAuditConfig
	err := c.ShouldBindJSON(&InsAudit)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insAuditService.UpdateInsAuditConfig(InsAudit); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsAuditConfig 用id查询FindInsAuditConfig
// @Tags InsAudit
// @Summary 用id查询InsAuditConfig
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsAuditConfig true "用id查询FindInsAuditConfig"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insAudit/findInsAudit [get]
func (i *InsAuditApi) FindInsAuditConfig(c *gin.Context) {
	var InsAudit insbuy.InsAuditConfig
	err := c.ShouldBindQuery(&InsAudit)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reInsAudit, err := insAuditService.GetInsAuditConfig(InsAudit.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reInsAudit": reInsAudit}, c)
	}
}

// GetInsAuditConfigList 分页获取InsAuditConfigList列表
// @Tags InsAudit
// @Summary 分页获取InsAudit列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsAuditSearch true "分页获取InsAuditConfigList列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insAudit/getInsAuditList [get]
func (i *InsAuditApi) GetInsAuditConfigList(c *gin.Context) {
	var pageInfo insbuyReq.InsAuditSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insAuditService.GetInsAuditConfigInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
