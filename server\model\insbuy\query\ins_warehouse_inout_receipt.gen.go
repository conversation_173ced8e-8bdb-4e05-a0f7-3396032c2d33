// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutReceipt(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutReceipt {
	_insWarehouseInoutReceipt := insWarehouseInoutReceipt{}

	_insWarehouseInoutReceipt.insWarehouseInoutReceiptDo.UseDB(db, opts...)
	_insWarehouseInoutReceipt.insWarehouseInoutReceiptDo.UseModel(&insbuy.InsWarehouseInoutReceipt{})

	tableName := _insWarehouseInoutReceipt.insWarehouseInoutReceiptDo.TableName()
	_insWarehouseInoutReceipt.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutReceipt.Id = field.NewInt(tableName, "id")
	_insWarehouseInoutReceipt.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutReceipt.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutReceipt.Name = field.NewString(tableName, "name")
	_insWarehouseInoutReceipt.OperationTime = field.NewTime(tableName, "operation_time")
	_insWarehouseInoutReceipt.PurchaseId = field.NewInt(tableName, "purchase_id")
	_insWarehouseInoutReceipt.OperatorId = field.NewInt(tableName, "operator_id")
	_insWarehouseInoutReceipt.Details = field.NewString(tableName, "details")
	_insWarehouseInoutReceipt.InoutId = field.NewInt(tableName, "inout_id")
	_insWarehouseInoutReceipt.ReceiptType = field.NewInt(tableName, "receipt_type")

	_insWarehouseInoutReceipt.fillFieldMap()

	return _insWarehouseInoutReceipt
}

type insWarehouseInoutReceipt struct {
	insWarehouseInoutReceiptDo

	ALL           field.Asterisk
	Id            field.Int
	CreatedAt     field.Time
	UpdatedAt     field.Time
	Name          field.String
	OperationTime field.Time
	PurchaseId    field.Int
	OperatorId    field.Int
	Details       field.String
	InoutId       field.Int
	ReceiptType   field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutReceipt) Table(newTableName string) *insWarehouseInoutReceipt {
	i.insWarehouseInoutReceiptDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutReceipt) As(alias string) *insWarehouseInoutReceipt {
	i.insWarehouseInoutReceiptDo.DO = *(i.insWarehouseInoutReceiptDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutReceipt) updateTableName(table string) *insWarehouseInoutReceipt {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Name = field.NewString(table, "name")
	i.OperationTime = field.NewTime(table, "operation_time")
	i.PurchaseId = field.NewInt(table, "purchase_id")
	i.OperatorId = field.NewInt(table, "operator_id")
	i.Details = field.NewString(table, "details")
	i.InoutId = field.NewInt(table, "inout_id")
	i.ReceiptType = field.NewInt(table, "receipt_type")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutReceipt) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutReceipt) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.Id
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["operation_time"] = i.OperationTime
	i.fieldMap["purchase_id"] = i.PurchaseId
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["details"] = i.Details
	i.fieldMap["inout_id"] = i.InoutId
	i.fieldMap["receipt_type"] = i.ReceiptType
}

func (i insWarehouseInoutReceipt) clone(db *gorm.DB) insWarehouseInoutReceipt {
	i.insWarehouseInoutReceiptDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutReceipt) replaceDB(db *gorm.DB) insWarehouseInoutReceipt {
	i.insWarehouseInoutReceiptDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutReceiptDo struct{ gen.DO }

func (i insWarehouseInoutReceiptDo) Debug() *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutReceiptDo) WithContext(ctx context.Context) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutReceiptDo) ReadDB() *insWarehouseInoutReceiptDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutReceiptDo) WriteDB() *insWarehouseInoutReceiptDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutReceiptDo) Session(config *gorm.Session) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutReceiptDo) Clauses(conds ...clause.Expression) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutReceiptDo) Returning(value interface{}, columns ...string) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutReceiptDo) Not(conds ...gen.Condition) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutReceiptDo) Or(conds ...gen.Condition) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutReceiptDo) Select(conds ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutReceiptDo) Where(conds ...gen.Condition) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutReceiptDo) Order(conds ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutReceiptDo) Distinct(cols ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutReceiptDo) Omit(cols ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutReceiptDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutReceiptDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutReceiptDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutReceiptDo) Group(cols ...field.Expr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutReceiptDo) Having(conds ...gen.Condition) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutReceiptDo) Limit(limit int) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutReceiptDo) Offset(offset int) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutReceiptDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutReceiptDo) Unscoped() *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutReceiptDo) Create(values ...*insbuy.InsWarehouseInoutReceipt) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutReceiptDo) CreateInBatches(values []*insbuy.InsWarehouseInoutReceipt, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutReceiptDo) Save(values ...*insbuy.InsWarehouseInoutReceipt) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutReceiptDo) First() (*insbuy.InsWarehouseInoutReceipt, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutReceipt), nil
	}
}

func (i insWarehouseInoutReceiptDo) Take() (*insbuy.InsWarehouseInoutReceipt, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutReceipt), nil
	}
}

func (i insWarehouseInoutReceiptDo) Last() (*insbuy.InsWarehouseInoutReceipt, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutReceipt), nil
	}
}

func (i insWarehouseInoutReceiptDo) Find() ([]*insbuy.InsWarehouseInoutReceipt, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutReceipt), err
}

func (i insWarehouseInoutReceiptDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutReceipt, err error) {
	buf := make([]*insbuy.InsWarehouseInoutReceipt, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutReceiptDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutReceipt, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutReceiptDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutReceiptDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutReceiptDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutReceiptDo) Joins(fields ...field.RelationField) *insWarehouseInoutReceiptDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutReceiptDo) Preload(fields ...field.RelationField) *insWarehouseInoutReceiptDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutReceiptDo) FirstOrInit() (*insbuy.InsWarehouseInoutReceipt, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutReceipt), nil
	}
}

func (i insWarehouseInoutReceiptDo) FirstOrCreate() (*insbuy.InsWarehouseInoutReceipt, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutReceipt), nil
	}
}

func (i insWarehouseInoutReceiptDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutReceipt, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutReceiptDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutReceiptDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutReceiptDo) Delete(models ...*insbuy.InsWarehouseInoutReceipt) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutReceiptDo) withDO(do gen.Dao) *insWarehouseInoutReceiptDo {
	i.DO = *do.(*gen.DO)
	return i
}
