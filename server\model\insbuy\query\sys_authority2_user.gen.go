// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newSysAuthority2User(db *gorm.DB, opts ...gen.DOOption) sysAuthority2User {
	_sysAuthority2User := sysAuthority2User{}

	_sysAuthority2User.sysAuthority2UserDo.UseDB(db, opts...)
	_sysAuthority2User.sysAuthority2UserDo.UseModel(&system.SysAuthority2User{})

	tableName := _sysAuthority2User.sysAuthority2UserDo.TableName()
	_sysAuthority2User.ALL = field.NewAsterisk(tableName)
	_sysAuthority2User.ID = field.NewUint(tableName, "id")
	_sysAuthority2User.UserId = field.NewUint(tableName, "user_id")
	_sysAuthority2User.Authority2Id = field.NewUint(tableName, "authority2_id")
	_sysAuthority2User.CreatedAt = field.NewTime(tableName, "created_at")
	_sysAuthority2User.User = sysAuthority2UserBelongsToUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("User", "system.SysUser"),
		Authority: struct {
			field.RelationField
			DataAuthorityId struct {
				field.RelationField
			}
			SysBaseMenus struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("User.Authority", "system.SysAuthority"),
			DataAuthorityId: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Authority.DataAuthorityId", "system.SysAuthority"),
			},
			SysBaseMenus: struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("User.Authority.SysBaseMenus", "system.SysBaseMenu"),
				Parameters: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
				},
				MenuBtn: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
				},
				SysAuthoritys: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("User.Authority.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
				},
			},
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("User.Authority.Users", "system.SysUser"),
			},
		},
		Authorities: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("User.Authorities", "system.SysAuthority"),
		},
	}

	_sysAuthority2User.SysAuthority2 = sysAuthority2UserBelongsToSysAuthority2{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SysAuthority2", "system.SysAuthority2"),
		SysAuthority2Item: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("SysAuthority2.SysAuthority2Item", "system.SysAuthority2Item"),
		},
	}

	_sysAuthority2User.fillFieldMap()

	return _sysAuthority2User
}

type sysAuthority2User struct {
	sysAuthority2UserDo

	ALL          field.Asterisk
	ID           field.Uint
	UserId       field.Uint
	Authority2Id field.Uint
	CreatedAt    field.Time
	User         sysAuthority2UserBelongsToUser

	SysAuthority2 sysAuthority2UserBelongsToSysAuthority2

	fieldMap map[string]field.Expr
}

func (s sysAuthority2User) Table(newTableName string) *sysAuthority2User {
	s.sysAuthority2UserDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysAuthority2User) As(alias string) *sysAuthority2User {
	s.sysAuthority2UserDo.DO = *(s.sysAuthority2UserDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysAuthority2User) updateTableName(table string) *sysAuthority2User {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint(table, "id")
	s.UserId = field.NewUint(table, "user_id")
	s.Authority2Id = field.NewUint(table, "authority2_id")
	s.CreatedAt = field.NewTime(table, "created_at")

	s.fillFieldMap()

	return s
}

func (s *sysAuthority2User) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysAuthority2User) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["id"] = s.ID
	s.fieldMap["user_id"] = s.UserId
	s.fieldMap["authority2_id"] = s.Authority2Id
	s.fieldMap["created_at"] = s.CreatedAt

}

func (s sysAuthority2User) clone(db *gorm.DB) sysAuthority2User {
	s.sysAuthority2UserDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysAuthority2User) replaceDB(db *gorm.DB) sysAuthority2User {
	s.sysAuthority2UserDo.ReplaceDB(db)
	return s
}

type sysAuthority2UserBelongsToUser struct {
	db *gorm.DB

	field.RelationField

	Authority struct {
		field.RelationField
		DataAuthorityId struct {
			field.RelationField
		}
		SysBaseMenus struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}
		Users struct {
			field.RelationField
		}
	}
	Authorities struct {
		field.RelationField
	}
}

func (a sysAuthority2UserBelongsToUser) Where(conds ...field.Expr) *sysAuthority2UserBelongsToUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthority2UserBelongsToUser) WithContext(ctx context.Context) *sysAuthority2UserBelongsToUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthority2UserBelongsToUser) Session(session *gorm.Session) *sysAuthority2UserBelongsToUser {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthority2UserBelongsToUser) Model(m *system.SysAuthority2User) *sysAuthority2UserBelongsToUserTx {
	return &sysAuthority2UserBelongsToUserTx{a.db.Model(m).Association(a.Name())}
}

type sysAuthority2UserBelongsToUserTx struct{ tx *gorm.Association }

func (a sysAuthority2UserBelongsToUserTx) Find() (result *system.SysUser, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthority2UserBelongsToUserTx) Append(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthority2UserBelongsToUserTx) Replace(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthority2UserBelongsToUserTx) Delete(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthority2UserBelongsToUserTx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthority2UserBelongsToUserTx) Count() int64 {
	return a.tx.Count()
}

type sysAuthority2UserBelongsToSysAuthority2 struct {
	db *gorm.DB

	field.RelationField

	SysAuthority2Item struct {
		field.RelationField
	}
}

func (a sysAuthority2UserBelongsToSysAuthority2) Where(conds ...field.Expr) *sysAuthority2UserBelongsToSysAuthority2 {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthority2UserBelongsToSysAuthority2) WithContext(ctx context.Context) *sysAuthority2UserBelongsToSysAuthority2 {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthority2UserBelongsToSysAuthority2) Session(session *gorm.Session) *sysAuthority2UserBelongsToSysAuthority2 {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthority2UserBelongsToSysAuthority2) Model(m *system.SysAuthority2User) *sysAuthority2UserBelongsToSysAuthority2Tx {
	return &sysAuthority2UserBelongsToSysAuthority2Tx{a.db.Model(m).Association(a.Name())}
}

type sysAuthority2UserBelongsToSysAuthority2Tx struct{ tx *gorm.Association }

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Find() (result *system.SysAuthority2, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Append(values ...*system.SysAuthority2) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Replace(values ...*system.SysAuthority2) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Delete(values ...*system.SysAuthority2) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthority2UserBelongsToSysAuthority2Tx) Count() int64 {
	return a.tx.Count()
}

type sysAuthority2UserDo struct{ gen.DO }

func (s sysAuthority2UserDo) Debug() *sysAuthority2UserDo {
	return s.withDO(s.DO.Debug())
}

func (s sysAuthority2UserDo) WithContext(ctx context.Context) *sysAuthority2UserDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysAuthority2UserDo) ReadDB() *sysAuthority2UserDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysAuthority2UserDo) WriteDB() *sysAuthority2UserDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysAuthority2UserDo) Session(config *gorm.Session) *sysAuthority2UserDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysAuthority2UserDo) Clauses(conds ...clause.Expression) *sysAuthority2UserDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysAuthority2UserDo) Returning(value interface{}, columns ...string) *sysAuthority2UserDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysAuthority2UserDo) Not(conds ...gen.Condition) *sysAuthority2UserDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysAuthority2UserDo) Or(conds ...gen.Condition) *sysAuthority2UserDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysAuthority2UserDo) Select(conds ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysAuthority2UserDo) Where(conds ...gen.Condition) *sysAuthority2UserDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysAuthority2UserDo) Order(conds ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysAuthority2UserDo) Distinct(cols ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysAuthority2UserDo) Omit(cols ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysAuthority2UserDo) Join(table schema.Tabler, on ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysAuthority2UserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysAuthority2UserDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysAuthority2UserDo) Group(cols ...field.Expr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysAuthority2UserDo) Having(conds ...gen.Condition) *sysAuthority2UserDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysAuthority2UserDo) Limit(limit int) *sysAuthority2UserDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysAuthority2UserDo) Offset(offset int) *sysAuthority2UserDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysAuthority2UserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysAuthority2UserDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysAuthority2UserDo) Unscoped() *sysAuthority2UserDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysAuthority2UserDo) Create(values ...*system.SysAuthority2User) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysAuthority2UserDo) CreateInBatches(values []*system.SysAuthority2User, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysAuthority2UserDo) Save(values ...*system.SysAuthority2User) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysAuthority2UserDo) First() (*system.SysAuthority2User, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2User), nil
	}
}

func (s sysAuthority2UserDo) Take() (*system.SysAuthority2User, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2User), nil
	}
}

func (s sysAuthority2UserDo) Last() (*system.SysAuthority2User, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2User), nil
	}
}

func (s sysAuthority2UserDo) Find() ([]*system.SysAuthority2User, error) {
	result, err := s.DO.Find()
	return result.([]*system.SysAuthority2User), err
}

func (s sysAuthority2UserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*system.SysAuthority2User, err error) {
	buf := make([]*system.SysAuthority2User, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysAuthority2UserDo) FindInBatches(result *[]*system.SysAuthority2User, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysAuthority2UserDo) Attrs(attrs ...field.AssignExpr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysAuthority2UserDo) Assign(attrs ...field.AssignExpr) *sysAuthority2UserDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysAuthority2UserDo) Joins(fields ...field.RelationField) *sysAuthority2UserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysAuthority2UserDo) Preload(fields ...field.RelationField) *sysAuthority2UserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysAuthority2UserDo) FirstOrInit() (*system.SysAuthority2User, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2User), nil
	}
}

func (s sysAuthority2UserDo) FirstOrCreate() (*system.SysAuthority2User, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2User), nil
	}
}

func (s sysAuthority2UserDo) FindByPage(offset int, limit int) (result []*system.SysAuthority2User, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysAuthority2UserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysAuthority2UserDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysAuthority2UserDo) Delete(models ...*system.SysAuthority2User) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysAuthority2UserDo) withDO(do gen.Dao) *sysAuthority2UserDo {
	s.DO = *do.(*gen.DO)
	return s
}
