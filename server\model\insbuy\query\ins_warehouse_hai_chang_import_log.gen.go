// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseHaiChangImportLog(db *gorm.DB, opts ...gen.DOOption) insWarehouseHaiChangImportLog {
	_insWarehouseHaiChangImportLog := insWarehouseHaiChangImportLog{}

	_insWarehouseHaiChangImportLog.insWarehouseHaiChangImportLogDo.UseDB(db, opts...)
	_insWarehouseHaiChangImportLog.insWarehouseHaiChangImportLogDo.UseModel(&insbuy.InsWarehouseHaiChangImportLog{})

	tableName := _insWarehouseHaiChangImportLog.insWarehouseHaiChangImportLogDo.TableName()
	_insWarehouseHaiChangImportLog.ALL = field.NewAsterisk(tableName)
	_insWarehouseHaiChangImportLog.ID = field.NewUint(tableName, "id")
	_insWarehouseHaiChangImportLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseHaiChangImportLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseHaiChangImportLog.Name = field.NewString(tableName, "name")
	_insWarehouseHaiChangImportLog.FileName = field.NewString(tableName, "file_name")
	_insWarehouseHaiChangImportLog.Remark = field.NewString(tableName, "remark")

	_insWarehouseHaiChangImportLog.fillFieldMap()

	return _insWarehouseHaiChangImportLog
}

type insWarehouseHaiChangImportLog struct {
	insWarehouseHaiChangImportLogDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	Name      field.String
	FileName  field.String
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insWarehouseHaiChangImportLog) Table(newTableName string) *insWarehouseHaiChangImportLog {
	i.insWarehouseHaiChangImportLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseHaiChangImportLog) As(alias string) *insWarehouseHaiChangImportLog {
	i.insWarehouseHaiChangImportLogDo.DO = *(i.insWarehouseHaiChangImportLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseHaiChangImportLog) updateTableName(table string) *insWarehouseHaiChangImportLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Name = field.NewString(table, "name")
	i.FileName = field.NewString(table, "file_name")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseHaiChangImportLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseHaiChangImportLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["file_name"] = i.FileName
	i.fieldMap["remark"] = i.Remark
}

func (i insWarehouseHaiChangImportLog) clone(db *gorm.DB) insWarehouseHaiChangImportLog {
	i.insWarehouseHaiChangImportLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseHaiChangImportLog) replaceDB(db *gorm.DB) insWarehouseHaiChangImportLog {
	i.insWarehouseHaiChangImportLogDo.ReplaceDB(db)
	return i
}

type insWarehouseHaiChangImportLogDo struct{ gen.DO }

func (i insWarehouseHaiChangImportLogDo) Debug() *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseHaiChangImportLogDo) WithContext(ctx context.Context) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseHaiChangImportLogDo) ReadDB() *insWarehouseHaiChangImportLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseHaiChangImportLogDo) WriteDB() *insWarehouseHaiChangImportLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseHaiChangImportLogDo) Session(config *gorm.Session) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseHaiChangImportLogDo) Clauses(conds ...clause.Expression) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Returning(value interface{}, columns ...string) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseHaiChangImportLogDo) Not(conds ...gen.Condition) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Or(conds ...gen.Condition) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Select(conds ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Where(conds ...gen.Condition) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Order(conds ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Distinct(cols ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseHaiChangImportLogDo) Omit(cols ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseHaiChangImportLogDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseHaiChangImportLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseHaiChangImportLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseHaiChangImportLogDo) Group(cols ...field.Expr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseHaiChangImportLogDo) Having(conds ...gen.Condition) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseHaiChangImportLogDo) Limit(limit int) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseHaiChangImportLogDo) Offset(offset int) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseHaiChangImportLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseHaiChangImportLogDo) Unscoped() *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseHaiChangImportLogDo) Create(values ...*insbuy.InsWarehouseHaiChangImportLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseHaiChangImportLogDo) CreateInBatches(values []*insbuy.InsWarehouseHaiChangImportLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseHaiChangImportLogDo) Save(values ...*insbuy.InsWarehouseHaiChangImportLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseHaiChangImportLogDo) First() (*insbuy.InsWarehouseHaiChangImportLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangImportLog), nil
	}
}

func (i insWarehouseHaiChangImportLogDo) Take() (*insbuy.InsWarehouseHaiChangImportLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangImportLog), nil
	}
}

func (i insWarehouseHaiChangImportLogDo) Last() (*insbuy.InsWarehouseHaiChangImportLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangImportLog), nil
	}
}

func (i insWarehouseHaiChangImportLogDo) Find() ([]*insbuy.InsWarehouseHaiChangImportLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseHaiChangImportLog), err
}

func (i insWarehouseHaiChangImportLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseHaiChangImportLog, err error) {
	buf := make([]*insbuy.InsWarehouseHaiChangImportLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseHaiChangImportLogDo) FindInBatches(result *[]*insbuy.InsWarehouseHaiChangImportLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseHaiChangImportLogDo) Attrs(attrs ...field.AssignExpr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseHaiChangImportLogDo) Assign(attrs ...field.AssignExpr) *insWarehouseHaiChangImportLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseHaiChangImportLogDo) Joins(fields ...field.RelationField) *insWarehouseHaiChangImportLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseHaiChangImportLogDo) Preload(fields ...field.RelationField) *insWarehouseHaiChangImportLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseHaiChangImportLogDo) FirstOrInit() (*insbuy.InsWarehouseHaiChangImportLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangImportLog), nil
	}
}

func (i insWarehouseHaiChangImportLogDo) FirstOrCreate() (*insbuy.InsWarehouseHaiChangImportLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseHaiChangImportLog), nil
	}
}

func (i insWarehouseHaiChangImportLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseHaiChangImportLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseHaiChangImportLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseHaiChangImportLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseHaiChangImportLogDo) Delete(models ...*insbuy.InsWarehouseHaiChangImportLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseHaiChangImportLogDo) withDO(do gen.Dao) *insWarehouseHaiChangImportLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
