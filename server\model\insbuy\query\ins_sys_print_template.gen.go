// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSysPrintTemplate(db *gorm.DB, opts ...gen.DOOption) insSysPrintTemplate {
	_insSysPrintTemplate := insSysPrintTemplate{}

	_insSysPrintTemplate.insSysPrintTemplateDo.UseDB(db, opts...)
	_insSysPrintTemplate.insSysPrintTemplateDo.UseModel(&insbuy.InsSysPrintTemplate{})

	tableName := _insSysPrintTemplate.insSysPrintTemplateDo.TableName()
	_insSysPrintTemplate.ALL = field.NewAsterisk(tableName)
	_insSysPrintTemplate.ID = field.NewUint(tableName, "id")
	_insSysPrintTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_insSysPrintTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSysPrintTemplate.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSysPrintTemplate.CreatedBy = field.NewUint(tableName, "created_by")
	_insSysPrintTemplate.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insSysPrintTemplate.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insSysPrintTemplate.Code = field.NewString(tableName, "code")
	_insSysPrintTemplate.Ver = field.NewInt(tableName, "ver")
	_insSysPrintTemplate.Name = field.NewString(tableName, "name")
	_insSysPrintTemplate.Status = field.NewInt(tableName, "status")
	_insSysPrintTemplate.Type = field.NewInt(tableName, "type")
	_insSysPrintTemplate.Remark = field.NewString(tableName, "remark")
	_insSysPrintTemplate.HtmlHash = field.NewString(tableName, "html_hash")
	_insSysPrintTemplate.Html = field.NewString(tableName, "html")
	_insSysPrintTemplate.Example = field.NewField(tableName, "example")
	_insSysPrintTemplate.AttachID = field.NewString(tableName, "attach_id")

	_insSysPrintTemplate.fillFieldMap()

	return _insSysPrintTemplate
}

type insSysPrintTemplate struct {
	insSysPrintTemplateDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	CreatedBy field.Uint
	UpdatedBy field.Uint
	DeletedBy field.Uint
	Code      field.String
	Ver       field.Int
	Name      field.String
	Status    field.Int
	Type      field.Int
	Remark    field.String
	HtmlHash  field.String
	Html      field.String
	Example   field.Field
	AttachID  field.String

	fieldMap map[string]field.Expr
}

func (i insSysPrintTemplate) Table(newTableName string) *insSysPrintTemplate {
	i.insSysPrintTemplateDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSysPrintTemplate) As(alias string) *insSysPrintTemplate {
	i.insSysPrintTemplateDo.DO = *(i.insSysPrintTemplateDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSysPrintTemplate) updateTableName(table string) *insSysPrintTemplate {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.Code = field.NewString(table, "code")
	i.Ver = field.NewInt(table, "ver")
	i.Name = field.NewString(table, "name")
	i.Status = field.NewInt(table, "status")
	i.Type = field.NewInt(table, "type")
	i.Remark = field.NewString(table, "remark")
	i.HtmlHash = field.NewString(table, "html_hash")
	i.Html = field.NewString(table, "html")
	i.Example = field.NewField(table, "example")
	i.AttachID = field.NewString(table, "attach_id")

	i.fillFieldMap()

	return i
}

func (i *insSysPrintTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSysPrintTemplate) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["code"] = i.Code
	i.fieldMap["ver"] = i.Ver
	i.fieldMap["name"] = i.Name
	i.fieldMap["status"] = i.Status
	i.fieldMap["type"] = i.Type
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["html_hash"] = i.HtmlHash
	i.fieldMap["html"] = i.Html
	i.fieldMap["example"] = i.Example
	i.fieldMap["attach_id"] = i.AttachID
}

func (i insSysPrintTemplate) clone(db *gorm.DB) insSysPrintTemplate {
	i.insSysPrintTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSysPrintTemplate) replaceDB(db *gorm.DB) insSysPrintTemplate {
	i.insSysPrintTemplateDo.ReplaceDB(db)
	return i
}

type insSysPrintTemplateDo struct{ gen.DO }

func (i insSysPrintTemplateDo) Debug() *insSysPrintTemplateDo {
	return i.withDO(i.DO.Debug())
}

func (i insSysPrintTemplateDo) WithContext(ctx context.Context) *insSysPrintTemplateDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSysPrintTemplateDo) ReadDB() *insSysPrintTemplateDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSysPrintTemplateDo) WriteDB() *insSysPrintTemplateDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSysPrintTemplateDo) Session(config *gorm.Session) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSysPrintTemplateDo) Clauses(conds ...clause.Expression) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSysPrintTemplateDo) Returning(value interface{}, columns ...string) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSysPrintTemplateDo) Not(conds ...gen.Condition) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSysPrintTemplateDo) Or(conds ...gen.Condition) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSysPrintTemplateDo) Select(conds ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSysPrintTemplateDo) Where(conds ...gen.Condition) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSysPrintTemplateDo) Order(conds ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSysPrintTemplateDo) Distinct(cols ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSysPrintTemplateDo) Omit(cols ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSysPrintTemplateDo) Join(table schema.Tabler, on ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSysPrintTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSysPrintTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSysPrintTemplateDo) Group(cols ...field.Expr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSysPrintTemplateDo) Having(conds ...gen.Condition) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSysPrintTemplateDo) Limit(limit int) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSysPrintTemplateDo) Offset(offset int) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSysPrintTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSysPrintTemplateDo) Unscoped() *insSysPrintTemplateDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSysPrintTemplateDo) Create(values ...*insbuy.InsSysPrintTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSysPrintTemplateDo) CreateInBatches(values []*insbuy.InsSysPrintTemplate, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSysPrintTemplateDo) Save(values ...*insbuy.InsSysPrintTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSysPrintTemplateDo) First() (*insbuy.InsSysPrintTemplate, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysPrintTemplate), nil
	}
}

func (i insSysPrintTemplateDo) Take() (*insbuy.InsSysPrintTemplate, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysPrintTemplate), nil
	}
}

func (i insSysPrintTemplateDo) Last() (*insbuy.InsSysPrintTemplate, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysPrintTemplate), nil
	}
}

func (i insSysPrintTemplateDo) Find() ([]*insbuy.InsSysPrintTemplate, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSysPrintTemplate), err
}

func (i insSysPrintTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSysPrintTemplate, err error) {
	buf := make([]*insbuy.InsSysPrintTemplate, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSysPrintTemplateDo) FindInBatches(result *[]*insbuy.InsSysPrintTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSysPrintTemplateDo) Attrs(attrs ...field.AssignExpr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSysPrintTemplateDo) Assign(attrs ...field.AssignExpr) *insSysPrintTemplateDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSysPrintTemplateDo) Joins(fields ...field.RelationField) *insSysPrintTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSysPrintTemplateDo) Preload(fields ...field.RelationField) *insSysPrintTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSysPrintTemplateDo) FirstOrInit() (*insbuy.InsSysPrintTemplate, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysPrintTemplate), nil
	}
}

func (i insSysPrintTemplateDo) FirstOrCreate() (*insbuy.InsSysPrintTemplate, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysPrintTemplate), nil
	}
}

func (i insSysPrintTemplateDo) FindByPage(offset int, limit int) (result []*insbuy.InsSysPrintTemplate, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSysPrintTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSysPrintTemplateDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSysPrintTemplateDo) Delete(models ...*insbuy.InsSysPrintTemplate) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSysPrintTemplateDo) withDO(do gen.Dao) *insSysPrintTemplateDo {
	i.DO = *do.(*gen.DO)
	return i
}
