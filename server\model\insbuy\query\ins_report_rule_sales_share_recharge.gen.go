// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareRecharge(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareRecharge {
	_insReportRuleSalesShareRecharge := insReportRuleSalesShareRecharge{}

	_insReportRuleSalesShareRecharge.insReportRuleSalesShareRechargeDo.UseDB(db, opts...)
	_insReportRuleSalesShareRecharge.insReportRuleSalesShareRechargeDo.UseModel(&insbuy.InsReportRuleSalesShareRecharge{})

	tableName := _insReportRuleSalesShareRecharge.insReportRuleSalesShareRechargeDo.TableName()
	_insReportRuleSalesShareRecharge.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareRecharge.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareRecharge.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareRecharge.Amount = field.NewFloat64(tableName, "amount")
	_insReportRuleSalesShareRecharge.ShareParam = field.NewFloat64(tableName, "share_param")

	_insReportRuleSalesShareRecharge.fillFieldMap()

	return _insReportRuleSalesShareRecharge
}

type insReportRuleSalesShareRecharge struct {
	insReportRuleSalesShareRechargeDo

	ALL        field.Asterisk
	ID         field.Uint
	RuleId     field.Uint
	Amount     field.Float64
	ShareParam field.Float64

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareRecharge) Table(newTableName string) *insReportRuleSalesShareRecharge {
	i.insReportRuleSalesShareRechargeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareRecharge) As(alias string) *insReportRuleSalesShareRecharge {
	i.insReportRuleSalesShareRechargeDo.DO = *(i.insReportRuleSalesShareRechargeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareRecharge) updateTableName(table string) *insReportRuleSalesShareRecharge {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.Amount = field.NewFloat64(table, "amount")
	i.ShareParam = field.NewFloat64(table, "share_param")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareRecharge) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareRecharge) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 4)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["share_param"] = i.ShareParam
}

func (i insReportRuleSalesShareRecharge) clone(db *gorm.DB) insReportRuleSalesShareRecharge {
	i.insReportRuleSalesShareRechargeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareRecharge) replaceDB(db *gorm.DB) insReportRuleSalesShareRecharge {
	i.insReportRuleSalesShareRechargeDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareRechargeDo struct{ gen.DO }

func (i insReportRuleSalesShareRechargeDo) Debug() *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareRechargeDo) WithContext(ctx context.Context) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareRechargeDo) ReadDB() *insReportRuleSalesShareRechargeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareRechargeDo) WriteDB() *insReportRuleSalesShareRechargeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareRechargeDo) Session(config *gorm.Session) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareRechargeDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareRechargeDo) Not(conds ...gen.Condition) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Or(conds ...gen.Condition) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Select(conds ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Where(conds ...gen.Condition) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Order(conds ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareRechargeDo) Omit(cols ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareRechargeDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareRechargeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareRechargeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareRechargeDo) Group(cols ...field.Expr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareRechargeDo) Having(conds ...gen.Condition) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareRechargeDo) Limit(limit int) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareRechargeDo) Offset(offset int) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareRechargeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareRechargeDo) Unscoped() *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareRechargeDo) Create(values ...*insbuy.InsReportRuleSalesShareRecharge) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareRechargeDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareRecharge, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareRechargeDo) Save(values ...*insbuy.InsReportRuleSalesShareRecharge) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareRechargeDo) First() (*insbuy.InsReportRuleSalesShareRecharge, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareRecharge), nil
	}
}

func (i insReportRuleSalesShareRechargeDo) Take() (*insbuy.InsReportRuleSalesShareRecharge, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareRecharge), nil
	}
}

func (i insReportRuleSalesShareRechargeDo) Last() (*insbuy.InsReportRuleSalesShareRecharge, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareRecharge), nil
	}
}

func (i insReportRuleSalesShareRechargeDo) Find() ([]*insbuy.InsReportRuleSalesShareRecharge, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareRecharge), err
}

func (i insReportRuleSalesShareRechargeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareRecharge, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareRecharge, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareRechargeDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareRecharge, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareRechargeDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareRechargeDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareRechargeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareRechargeDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareRechargeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareRechargeDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareRechargeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareRechargeDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareRecharge, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareRecharge), nil
	}
}

func (i insReportRuleSalesShareRechargeDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareRecharge, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareRecharge), nil
	}
}

func (i insReportRuleSalesShareRechargeDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareRecharge, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareRechargeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareRechargeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareRechargeDo) Delete(models ...*insbuy.InsReportRuleSalesShareRecharge) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareRechargeDo) withDO(do gen.Dao) *insReportRuleSalesShareRechargeDo {
	i.DO = *do.(*gen.DO)
	return i
}
