// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderShoppingCartDetail(db *gorm.DB, opts ...gen.DOOption) insOrderShoppingCartDetail {
	_insOrderShoppingCartDetail := insOrderShoppingCartDetail{}

	_insOrderShoppingCartDetail.insOrderShoppingCartDetailDo.UseDB(db, opts...)
	_insOrderShoppingCartDetail.insOrderShoppingCartDetailDo.UseModel(&insbuy.InsOrderShoppingCartDetail{})

	tableName := _insOrderShoppingCartDetail.insOrderShoppingCartDetailDo.TableName()
	_insOrderShoppingCartDetail.ALL = field.NewAsterisk(tableName)
	_insOrderShoppingCartDetail.ID = field.NewUint(tableName, "id")
	_insOrderShoppingCartDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderShoppingCartDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderShoppingCartDetail.OrderShoppingCart = field.NewUint(tableName, "order_shopping_cart")
	_insOrderShoppingCartDetail.ProductId = field.NewUint(tableName, "product_id")
	_insOrderShoppingCartDetail.Num = field.NewInt(tableName, "num")
	_insOrderShoppingCartDetail.Discount = field.NewFloat64(tableName, "discount")
	_insOrderShoppingCartDetail.Remark = field.NewString(tableName, "remark")
	_insOrderShoppingCartDetail.RemarkExt = field.NewField(tableName, "remark_ext")
	_insOrderShoppingCartDetail.IsOrder = field.NewInt8(tableName, "is_order")
	_insOrderShoppingCartDetail.PackageId = field.NewUint(tableName, "package_id")
	_insOrderShoppingCartDetail.OptionItems = field.NewString(tableName, "option_items")
	_insOrderShoppingCartDetail.EntertainNum = field.NewFloat64(tableName, "entertain_num")

	_insOrderShoppingCartDetail.fillFieldMap()

	return _insOrderShoppingCartDetail
}

type insOrderShoppingCartDetail struct {
	insOrderShoppingCartDetailDo

	ALL               field.Asterisk
	ID                field.Uint
	CreatedAt         field.Time
	UpdatedAt         field.Time
	OrderShoppingCart field.Uint
	ProductId         field.Uint
	Num               field.Int
	Discount          field.Float64
	Remark            field.String
	RemarkExt         field.Field
	IsOrder           field.Int8
	PackageId         field.Uint
	OptionItems       field.String
	EntertainNum      field.Float64

	fieldMap map[string]field.Expr
}

func (i insOrderShoppingCartDetail) Table(newTableName string) *insOrderShoppingCartDetail {
	i.insOrderShoppingCartDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderShoppingCartDetail) As(alias string) *insOrderShoppingCartDetail {
	i.insOrderShoppingCartDetailDo.DO = *(i.insOrderShoppingCartDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderShoppingCartDetail) updateTableName(table string) *insOrderShoppingCartDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OrderShoppingCart = field.NewUint(table, "order_shopping_cart")
	i.ProductId = field.NewUint(table, "product_id")
	i.Num = field.NewInt(table, "num")
	i.Discount = field.NewFloat64(table, "discount")
	i.Remark = field.NewString(table, "remark")
	i.RemarkExt = field.NewField(table, "remark_ext")
	i.IsOrder = field.NewInt8(table, "is_order")
	i.PackageId = field.NewUint(table, "package_id")
	i.OptionItems = field.NewString(table, "option_items")
	i.EntertainNum = field.NewFloat64(table, "entertain_num")

	i.fillFieldMap()

	return i
}

func (i *insOrderShoppingCartDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderShoppingCartDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["order_shopping_cart"] = i.OrderShoppingCart
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["num"] = i.Num
	i.fieldMap["discount"] = i.Discount
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["remark_ext"] = i.RemarkExt
	i.fieldMap["is_order"] = i.IsOrder
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["option_items"] = i.OptionItems
	i.fieldMap["entertain_num"] = i.EntertainNum
}

func (i insOrderShoppingCartDetail) clone(db *gorm.DB) insOrderShoppingCartDetail {
	i.insOrderShoppingCartDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderShoppingCartDetail) replaceDB(db *gorm.DB) insOrderShoppingCartDetail {
	i.insOrderShoppingCartDetailDo.ReplaceDB(db)
	return i
}

type insOrderShoppingCartDetailDo struct{ gen.DO }

func (i insOrderShoppingCartDetailDo) Debug() *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderShoppingCartDetailDo) WithContext(ctx context.Context) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderShoppingCartDetailDo) ReadDB() *insOrderShoppingCartDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderShoppingCartDetailDo) WriteDB() *insOrderShoppingCartDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderShoppingCartDetailDo) Session(config *gorm.Session) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderShoppingCartDetailDo) Clauses(conds ...clause.Expression) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderShoppingCartDetailDo) Returning(value interface{}, columns ...string) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderShoppingCartDetailDo) Not(conds ...gen.Condition) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderShoppingCartDetailDo) Or(conds ...gen.Condition) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderShoppingCartDetailDo) Select(conds ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderShoppingCartDetailDo) Where(conds ...gen.Condition) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderShoppingCartDetailDo) Order(conds ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderShoppingCartDetailDo) Distinct(cols ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderShoppingCartDetailDo) Omit(cols ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderShoppingCartDetailDo) Join(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderShoppingCartDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderShoppingCartDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderShoppingCartDetailDo) Group(cols ...field.Expr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderShoppingCartDetailDo) Having(conds ...gen.Condition) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderShoppingCartDetailDo) Limit(limit int) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderShoppingCartDetailDo) Offset(offset int) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderShoppingCartDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderShoppingCartDetailDo) Unscoped() *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderShoppingCartDetailDo) Create(values ...*insbuy.InsOrderShoppingCartDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderShoppingCartDetailDo) CreateInBatches(values []*insbuy.InsOrderShoppingCartDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderShoppingCartDetailDo) Save(values ...*insbuy.InsOrderShoppingCartDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderShoppingCartDetailDo) First() (*insbuy.InsOrderShoppingCartDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCartDetail), nil
	}
}

func (i insOrderShoppingCartDetailDo) Take() (*insbuy.InsOrderShoppingCartDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCartDetail), nil
	}
}

func (i insOrderShoppingCartDetailDo) Last() (*insbuy.InsOrderShoppingCartDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCartDetail), nil
	}
}

func (i insOrderShoppingCartDetailDo) Find() ([]*insbuy.InsOrderShoppingCartDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderShoppingCartDetail), err
}

func (i insOrderShoppingCartDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderShoppingCartDetail, err error) {
	buf := make([]*insbuy.InsOrderShoppingCartDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderShoppingCartDetailDo) FindInBatches(result *[]*insbuy.InsOrderShoppingCartDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderShoppingCartDetailDo) Attrs(attrs ...field.AssignExpr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderShoppingCartDetailDo) Assign(attrs ...field.AssignExpr) *insOrderShoppingCartDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderShoppingCartDetailDo) Joins(fields ...field.RelationField) *insOrderShoppingCartDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderShoppingCartDetailDo) Preload(fields ...field.RelationField) *insOrderShoppingCartDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderShoppingCartDetailDo) FirstOrInit() (*insbuy.InsOrderShoppingCartDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCartDetail), nil
	}
}

func (i insOrderShoppingCartDetailDo) FirstOrCreate() (*insbuy.InsOrderShoppingCartDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCartDetail), nil
	}
}

func (i insOrderShoppingCartDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderShoppingCartDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderShoppingCartDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderShoppingCartDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderShoppingCartDetailDo) Delete(models ...*insbuy.InsOrderShoppingCartDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderShoppingCartDetailDo) withDO(do gen.Dao) *insOrderShoppingCartDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
