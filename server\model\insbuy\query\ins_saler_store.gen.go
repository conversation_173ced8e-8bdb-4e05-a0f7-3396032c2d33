// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSalerStore(db *gorm.DB, opts ...gen.DOOption) insSalerStore {
	_insSalerStore := insSalerStore{}

	_insSalerStore.insSalerStoreDo.UseDB(db, opts...)
	_insSalerStore.insSalerStoreDo.UseModel(&insbuy.InsSalerStore{})

	tableName := _insSalerStore.insSalerStoreDo.TableName()
	_insSalerStore.ALL = field.NewAsterisk(tableName)
	_insSalerStore.ID = field.NewUint(tableName, "id")
	_insSalerStore.CreatedAt = field.NewTime(tableName, "created_at")
	_insSalerStore.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSalerStore.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSalerStore.UserId = field.NewUint(tableName, "user_id")
	_insSalerStore.StoreId = field.NewUint(tableName, "store_id")

	_insSalerStore.fillFieldMap()

	return _insSalerStore
}

type insSalerStore struct {
	insSalerStoreDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserId    field.Uint
	StoreId   field.Uint

	fieldMap map[string]field.Expr
}

func (i insSalerStore) Table(newTableName string) *insSalerStore {
	i.insSalerStoreDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSalerStore) As(alias string) *insSalerStore {
	i.insSalerStoreDo.DO = *(i.insSalerStoreDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSalerStore) updateTableName(table string) *insSalerStore {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserId = field.NewUint(table, "user_id")
	i.StoreId = field.NewUint(table, "store_id")

	i.fillFieldMap()

	return i
}

func (i *insSalerStore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSalerStore) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["store_id"] = i.StoreId
}

func (i insSalerStore) clone(db *gorm.DB) insSalerStore {
	i.insSalerStoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSalerStore) replaceDB(db *gorm.DB) insSalerStore {
	i.insSalerStoreDo.ReplaceDB(db)
	return i
}

type insSalerStoreDo struct{ gen.DO }

func (i insSalerStoreDo) Debug() *insSalerStoreDo {
	return i.withDO(i.DO.Debug())
}

func (i insSalerStoreDo) WithContext(ctx context.Context) *insSalerStoreDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSalerStoreDo) ReadDB() *insSalerStoreDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSalerStoreDo) WriteDB() *insSalerStoreDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSalerStoreDo) Session(config *gorm.Session) *insSalerStoreDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSalerStoreDo) Clauses(conds ...clause.Expression) *insSalerStoreDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSalerStoreDo) Returning(value interface{}, columns ...string) *insSalerStoreDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSalerStoreDo) Not(conds ...gen.Condition) *insSalerStoreDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSalerStoreDo) Or(conds ...gen.Condition) *insSalerStoreDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSalerStoreDo) Select(conds ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSalerStoreDo) Where(conds ...gen.Condition) *insSalerStoreDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSalerStoreDo) Order(conds ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSalerStoreDo) Distinct(cols ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSalerStoreDo) Omit(cols ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSalerStoreDo) Join(table schema.Tabler, on ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSalerStoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSalerStoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSalerStoreDo) Group(cols ...field.Expr) *insSalerStoreDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSalerStoreDo) Having(conds ...gen.Condition) *insSalerStoreDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSalerStoreDo) Limit(limit int) *insSalerStoreDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSalerStoreDo) Offset(offset int) *insSalerStoreDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSalerStoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSalerStoreDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSalerStoreDo) Unscoped() *insSalerStoreDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSalerStoreDo) Create(values ...*insbuy.InsSalerStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSalerStoreDo) CreateInBatches(values []*insbuy.InsSalerStore, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSalerStoreDo) Save(values ...*insbuy.InsSalerStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSalerStoreDo) First() (*insbuy.InsSalerStore, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerStore), nil
	}
}

func (i insSalerStoreDo) Take() (*insbuy.InsSalerStore, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerStore), nil
	}
}

func (i insSalerStoreDo) Last() (*insbuy.InsSalerStore, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerStore), nil
	}
}

func (i insSalerStoreDo) Find() ([]*insbuy.InsSalerStore, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSalerStore), err
}

func (i insSalerStoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSalerStore, err error) {
	buf := make([]*insbuy.InsSalerStore, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSalerStoreDo) FindInBatches(result *[]*insbuy.InsSalerStore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSalerStoreDo) Attrs(attrs ...field.AssignExpr) *insSalerStoreDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSalerStoreDo) Assign(attrs ...field.AssignExpr) *insSalerStoreDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSalerStoreDo) Joins(fields ...field.RelationField) *insSalerStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSalerStoreDo) Preload(fields ...field.RelationField) *insSalerStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSalerStoreDo) FirstOrInit() (*insbuy.InsSalerStore, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerStore), nil
	}
}

func (i insSalerStoreDo) FirstOrCreate() (*insbuy.InsSalerStore, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerStore), nil
	}
}

func (i insSalerStoreDo) FindByPage(offset int, limit int) (result []*insbuy.InsSalerStore, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSalerStoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSalerStoreDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSalerStoreDo) Delete(models ...*insbuy.InsSalerStore) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSalerStoreDo) withDO(do gen.Dao) *insSalerStoreDo {
	i.DO = *do.(*gen.DO)
	return i
}
