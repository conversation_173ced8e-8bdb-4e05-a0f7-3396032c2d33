# 费用报销审批配置指南

## 概述

本指南介绍如何使用合同数据转换系统处理费用报销审批，特别是包含 `fieldList` 类型字段的复杂表单数据。

## 配置文件结构

### 基本配置

费用报销配置文件位于 `config/contract_mappings/expense_reimbursement.yaml`，包含以下主要部分：

1. **基础信息映射** - 申请编号、标题、状态等
2. **人员信息映射** - 发起人、部门等
3. **报销业务映射** - 报销事由、主体、总金额等
4. **费用明细映射** - fieldList 类型的特殊处理
5. **银行账户映射** - 收款账户信息
6. **验证规则** - 数据验证和默认值

### fieldList 类型特殊处理

#### 配置结构

```yaml
expense_details:
  source_path: "form.widget_expense_list.value"
  target_field: "expense_details"
  data_type: "fieldList"
  required: true
  description: "费用明细列表"
  field_list_config:
    expense_type:
      source_path: "widget16710060735460001.value"
      target_field: "expense_type"
      data_type: "string"
      required: true
      description: "费用类型"
    # ... 其他内部字段
```

#### 数据转换流程

1. **识别 fieldList 类型** - 系统自动检测 `data_type: "fieldList"`
2. **解析列表数据** - 将 JSON 数组解析为独立的记录
3. **字段映射** - 根据 `field_list_config` 映射每个内部字段
4. **生成明细记录** - 每行数据生成一个 `ExpenseDetailItem`
5. **合并到主记录** - 将明细列表附加到主数据结构

## 数据结构

### ExpenseDetailItem 结构

```go
type ExpenseDetailItem struct {
    ExpenseType      string    `json:"expense_type"`       // 费用类型
    Location         string    `json:"location"`           // 地点
    DateRange        string    `json:"date_range"`         // 日期区间
    StartDate        time.Time `json:"start_date"`         // 开始日期
    EndDate          time.Time `json:"end_date"`           // 结束日期
    VatInvoiceType   string    `json:"vat_invoice_type"`   // 增值税发票类型
    Amount           float64   `json:"amount"`             // 金额
    AmountCurrency   string    `json:"amount_currency"`    // 金额币种
    AmountCapital    string    `json:"amount_capital"`     // 金额大写
    RowIndex         int       `json:"row_index"`          // 行索引
}
```

### StandardContractData 扩展字段

```go
// 费用报销相关字段
ReimbursementReason string              `json:"reimbursement_reason"` // 报销事由
ReimbursementEntity string              `json:"reimbursement_entity"` // 报销主体
TotalAmount         float64             `json:"total_amount"`         // 费用总金额
ExpenseDetails      []ExpenseDetailItem `json:"expense_details"`      // 费用明细列表
RecordType          string              `json:"record_type"`          // 记录类型
```

## 使用方法

### 1. 配置验证测试

```go
func TestExpenseReimbursementConfig(t *testing.T) {
    tester := NewContractTransformerTester()
    
    // 加载配置文件
    err := tester.configMgr.LoadFromFile("expense_reimbursement.yaml")
    if err != nil {
        t.Fatalf("加载配置失败: %v", err)
    }
    
    // 验证配置规则
    rule, exists := tester.configMgr.GetRule("EXPENSE_REIMBURSEMENT_APPROVAL")
    // ... 验证逻辑
}
```

### 2. 数据转换测试

```go
func TestExpenseReimbursementWithSampleData(t *testing.T) {
    // 创建包含 fieldList 的示例数据
    sampleFormData := `[
        {
            "id": "widget_expense_list",
            "type": "fieldList",
            "value": [
                {
                    "widget16710060735460001": {"value": "交通费"},
                    "widget16710062249940001": {"value": 1200.00}
                }
            ]
        }
    ]`
    
    // 执行转换
    result, err := tester.transformer.Transform(context.Background(), contractDetails)
    // ... 验证结果
}
```

### 3. 通用数据导出

```go
func TestUniversalDataExportWithExpenseReimbursement(t *testing.T) {
    tester := NewContractTransformerTester()
    
    // 使用通用方法，自动识别费用报销类型
    err := tester.TestUniversalDataExport(instanceCode)
    if err != nil {
        t.Errorf("费用报销导出失败: %v", err)
    }
}
```

## 输出格式

### Excel 导出

转换后的数据会导出为 Excel 文件，包含：

1. **主表** - 包含所有标准化字段
2. **费用明细表** - 如果有费用明细，会创建单独的工作表
3. **附件详情表** - 如果有附件，会列出所有附件信息

### 文件命名

- 格式：`expense_reimbursement_export_{实例代码}_{时间戳}.xlsx`
- 示例：`expense_reimbursement_export_EXPENSE-001_20241230_143052.xlsx`

## 扩展指南

### 添加新的 fieldList 类型

1. **定义数据结构** - 在转换器中添加新的明细项结构
2. **配置字段映射** - 在 YAML 文件中定义 `field_list_config`
3. **实现处理方法** - 在 `processFieldList` 中添加新的处理分支
4. **添加字段设置** - 在 `setFieldValue` 中添加新字段的处理

### 自定义验证规则

```yaml
validation_rules:
  - field: "expense_details"
    rule: "custom"
    parameter: "min_items:1"
    message: "至少需要一条费用明细"
```

## 注意事项

1. **字段路径** - fieldList 内部字段的 `source_path` 是相对于列表项的路径
2. **数据类型** - 确保 `data_type` 正确设置为 "fieldList"
3. **必填验证** - fieldList 内部字段的必填验证独立于主字段
4. **性能考虑** - 大量明细数据可能影响转换性能
5. **错误处理** - 单个明细项错误不会中断整个转换过程

## 故障排除

### 常见问题

1. **配置加载失败** - 检查 YAML 语法和文件路径
2. **字段映射错误** - 验证 `source_path` 是否正确
3. **数据类型不匹配** - 确保源数据格式符合预期
4. **明细解析失败** - 检查 fieldList 数据结构

### 调试方法

1. **启用详细日志** - 查看转换过程的详细信息
2. **单步测试** - 使用单元测试验证各个组件
3. **数据检查** - 打印中间转换结果进行调试
