// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsQueueItem(db *gorm.DB, opts ...gen.DOOption) insQueueItem {
	_insQueueItem := insQueueItem{}

	_insQueueItem.insQueueItemDo.UseDB(db, opts...)
	_insQueueItem.insQueueItemDo.UseModel(&insbuy.InsQueueItem{})

	tableName := _insQueueItem.insQueueItemDo.TableName()
	_insQueueItem.ALL = field.NewAsterisk(tableName)
	_insQueueItem.ID = field.NewUint(tableName, "id")
	_insQueueItem.CreatedAt = field.NewTime(tableName, "created_at")
	_insQueueItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insQueueItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_insQueueItem.QueueSn = field.NewString(tableName, "queue_sn")
	_insQueueItem.StoreId = field.NewUint(tableName, "store_id")
	_insQueueItem.ChannelId = field.NewUint(tableName, "channel_id")
	_insQueueItem.Phone = field.NewString(tableName, "phone")
	_insQueueItem.PeopleCount = field.NewInt(tableName, "people_count")
	_insQueueItem.Status = field.NewInt(tableName, "status")
	_insQueueItem.EnqueueTime = field.NewTime(tableName, "enqueue_time")
	_insQueueItem.EstimatedCallTime = field.NewTime(tableName, "estimated_call_time")
	_insQueueItem.BusinessDay = field.NewTime(tableName, "business_day")
	_insQueueItem.ResourceId = field.NewUint(tableName, "resource_id")
	_insQueueItem.ResourceName = field.NewString(tableName, "resource_name")
	_insQueueItem.Position = field.NewFloat64(tableName, "position")
	_insQueueItem.EventHistory = insQueueItemHasManyEventHistory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("EventHistory", "insbuy.InsQueueItemEventHistory"),
	}

	_insQueueItem.Channel = insQueueItemBelongsToChannel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Channel", "insbuy.InsQueueChannel"),
		Store: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Channel.Store", "insbuy.InsStore"),
		},
	}

	_insQueueItem.fillFieldMap()

	return _insQueueItem
}

type insQueueItem struct {
	insQueueItemDo

	ALL               field.Asterisk
	ID                field.Uint
	CreatedAt         field.Time
	UpdatedAt         field.Time
	DeletedAt         field.Field
	QueueSn           field.String
	StoreId           field.Uint
	ChannelId         field.Uint
	Phone             field.String
	PeopleCount       field.Int
	Status            field.Int
	EnqueueTime       field.Time
	EstimatedCallTime field.Time
	BusinessDay       field.Time
	ResourceId        field.Uint
	ResourceName      field.String
	Position          field.Float64
	EventHistory      insQueueItemHasManyEventHistory

	Channel insQueueItemBelongsToChannel

	fieldMap map[string]field.Expr
}

func (i insQueueItem) Table(newTableName string) *insQueueItem {
	i.insQueueItemDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insQueueItem) As(alias string) *insQueueItem {
	i.insQueueItemDo.DO = *(i.insQueueItemDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insQueueItem) updateTableName(table string) *insQueueItem {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.QueueSn = field.NewString(table, "queue_sn")
	i.StoreId = field.NewUint(table, "store_id")
	i.ChannelId = field.NewUint(table, "channel_id")
	i.Phone = field.NewString(table, "phone")
	i.PeopleCount = field.NewInt(table, "people_count")
	i.Status = field.NewInt(table, "status")
	i.EnqueueTime = field.NewTime(table, "enqueue_time")
	i.EstimatedCallTime = field.NewTime(table, "estimated_call_time")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.ResourceId = field.NewUint(table, "resource_id")
	i.ResourceName = field.NewString(table, "resource_name")
	i.Position = field.NewFloat64(table, "position")

	i.fillFieldMap()

	return i
}

func (i *insQueueItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insQueueItem) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 18)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["queue_sn"] = i.QueueSn
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["channel_id"] = i.ChannelId
	i.fieldMap["phone"] = i.Phone
	i.fieldMap["people_count"] = i.PeopleCount
	i.fieldMap["status"] = i.Status
	i.fieldMap["enqueue_time"] = i.EnqueueTime
	i.fieldMap["estimated_call_time"] = i.EstimatedCallTime
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["resource_id"] = i.ResourceId
	i.fieldMap["resource_name"] = i.ResourceName
	i.fieldMap["position"] = i.Position

}

func (i insQueueItem) clone(db *gorm.DB) insQueueItem {
	i.insQueueItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insQueueItem) replaceDB(db *gorm.DB) insQueueItem {
	i.insQueueItemDo.ReplaceDB(db)
	return i
}

type insQueueItemHasManyEventHistory struct {
	db *gorm.DB

	field.RelationField
}

func (a insQueueItemHasManyEventHistory) Where(conds ...field.Expr) *insQueueItemHasManyEventHistory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insQueueItemHasManyEventHistory) WithContext(ctx context.Context) *insQueueItemHasManyEventHistory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insQueueItemHasManyEventHistory) Session(session *gorm.Session) *insQueueItemHasManyEventHistory {
	a.db = a.db.Session(session)
	return &a
}

func (a insQueueItemHasManyEventHistory) Model(m *insbuy.InsQueueItem) *insQueueItemHasManyEventHistoryTx {
	return &insQueueItemHasManyEventHistoryTx{a.db.Model(m).Association(a.Name())}
}

type insQueueItemHasManyEventHistoryTx struct{ tx *gorm.Association }

func (a insQueueItemHasManyEventHistoryTx) Find() (result []*insbuy.InsQueueItemEventHistory, err error) {
	return result, a.tx.Find(&result)
}

func (a insQueueItemHasManyEventHistoryTx) Append(values ...*insbuy.InsQueueItemEventHistory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insQueueItemHasManyEventHistoryTx) Replace(values ...*insbuy.InsQueueItemEventHistory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insQueueItemHasManyEventHistoryTx) Delete(values ...*insbuy.InsQueueItemEventHistory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insQueueItemHasManyEventHistoryTx) Clear() error {
	return a.tx.Clear()
}

func (a insQueueItemHasManyEventHistoryTx) Count() int64 {
	return a.tx.Count()
}

type insQueueItemBelongsToChannel struct {
	db *gorm.DB

	field.RelationField

	Store struct {
		field.RelationField
	}
}

func (a insQueueItemBelongsToChannel) Where(conds ...field.Expr) *insQueueItemBelongsToChannel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insQueueItemBelongsToChannel) WithContext(ctx context.Context) *insQueueItemBelongsToChannel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insQueueItemBelongsToChannel) Session(session *gorm.Session) *insQueueItemBelongsToChannel {
	a.db = a.db.Session(session)
	return &a
}

func (a insQueueItemBelongsToChannel) Model(m *insbuy.InsQueueItem) *insQueueItemBelongsToChannelTx {
	return &insQueueItemBelongsToChannelTx{a.db.Model(m).Association(a.Name())}
}

type insQueueItemBelongsToChannelTx struct{ tx *gorm.Association }

func (a insQueueItemBelongsToChannelTx) Find() (result *insbuy.InsQueueChannel, err error) {
	return result, a.tx.Find(&result)
}

func (a insQueueItemBelongsToChannelTx) Append(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insQueueItemBelongsToChannelTx) Replace(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insQueueItemBelongsToChannelTx) Delete(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insQueueItemBelongsToChannelTx) Clear() error {
	return a.tx.Clear()
}

func (a insQueueItemBelongsToChannelTx) Count() int64 {
	return a.tx.Count()
}

type insQueueItemDo struct{ gen.DO }

func (i insQueueItemDo) Debug() *insQueueItemDo {
	return i.withDO(i.DO.Debug())
}

func (i insQueueItemDo) WithContext(ctx context.Context) *insQueueItemDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insQueueItemDo) ReadDB() *insQueueItemDo {
	return i.Clauses(dbresolver.Read)
}

func (i insQueueItemDo) WriteDB() *insQueueItemDo {
	return i.Clauses(dbresolver.Write)
}

func (i insQueueItemDo) Session(config *gorm.Session) *insQueueItemDo {
	return i.withDO(i.DO.Session(config))
}

func (i insQueueItemDo) Clauses(conds ...clause.Expression) *insQueueItemDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insQueueItemDo) Returning(value interface{}, columns ...string) *insQueueItemDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insQueueItemDo) Not(conds ...gen.Condition) *insQueueItemDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insQueueItemDo) Or(conds ...gen.Condition) *insQueueItemDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insQueueItemDo) Select(conds ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insQueueItemDo) Where(conds ...gen.Condition) *insQueueItemDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insQueueItemDo) Order(conds ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insQueueItemDo) Distinct(cols ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insQueueItemDo) Omit(cols ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insQueueItemDo) Join(table schema.Tabler, on ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insQueueItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insQueueItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insQueueItemDo) Group(cols ...field.Expr) *insQueueItemDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insQueueItemDo) Having(conds ...gen.Condition) *insQueueItemDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insQueueItemDo) Limit(limit int) *insQueueItemDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insQueueItemDo) Offset(offset int) *insQueueItemDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insQueueItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insQueueItemDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insQueueItemDo) Unscoped() *insQueueItemDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insQueueItemDo) Create(values ...*insbuy.InsQueueItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insQueueItemDo) CreateInBatches(values []*insbuy.InsQueueItem, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insQueueItemDo) Save(values ...*insbuy.InsQueueItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insQueueItemDo) First() (*insbuy.InsQueueItem, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItem), nil
	}
}

func (i insQueueItemDo) Take() (*insbuy.InsQueueItem, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItem), nil
	}
}

func (i insQueueItemDo) Last() (*insbuy.InsQueueItem, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItem), nil
	}
}

func (i insQueueItemDo) Find() ([]*insbuy.InsQueueItem, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsQueueItem), err
}

func (i insQueueItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsQueueItem, err error) {
	buf := make([]*insbuy.InsQueueItem, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insQueueItemDo) FindInBatches(result *[]*insbuy.InsQueueItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insQueueItemDo) Attrs(attrs ...field.AssignExpr) *insQueueItemDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insQueueItemDo) Assign(attrs ...field.AssignExpr) *insQueueItemDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insQueueItemDo) Joins(fields ...field.RelationField) *insQueueItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insQueueItemDo) Preload(fields ...field.RelationField) *insQueueItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insQueueItemDo) FirstOrInit() (*insbuy.InsQueueItem, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItem), nil
	}
}

func (i insQueueItemDo) FirstOrCreate() (*insbuy.InsQueueItem, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItem), nil
	}
}

func (i insQueueItemDo) FindByPage(offset int, limit int) (result []*insbuy.InsQueueItem, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insQueueItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insQueueItemDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insQueueItemDo) Delete(models ...*insbuy.InsQueueItem) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insQueueItemDo) withDO(do gen.Dao) *insQueueItemDo {
	i.DO = *do.(*gen.DO)
	return i
}
