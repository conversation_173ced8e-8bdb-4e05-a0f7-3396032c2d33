package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyRes "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 报告相关
//
// jason.liao 2023.11.02

//
// 产品原型:
//
// https://axhub.im/ax10/1b9c69d019c0b18e/?g=1&id=2ts61p&p=%E6%95%B0%E6%8D%AE%E6%9F%A5%E7%9C%8B

type InsReportApi struct{}

// Summary
// @Summary 核心数据
// @Description 指定日期范围，按店铺统计营收
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportSummaryReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportSummaryResp} "查询成功"
// @Router /insReport/summary [get]
func (insReportApi *InsReportApi) Summary(c *gin.Context) {
	var req insbuyReq.InsReportSummaryReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportSummaryResp
	resp, err := reportService.Summary(req)
	response.ResultErr(resp, err, c)
}

// Today
// @Summary 当天实时数据
// @Description 日数据为最近一个经营周期（12点到未来的一个12点的数据）
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportTodayReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportTodayResp} "查询成功"
// @Router /insReport/today [get]
func (insReportApi *InsReportApi) Today(c *gin.Context) {
	var req insbuyReq.InsReportTodayReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportTodayResp
	resp, err := reportService.Today(req)
	response.ResultErr(resp, err, c)
}

// DetailProduct
// @Summary 按商品统计
// @Description 指定日期范围，统计商品相关的数据。
// @Description 按商品分类，统计销售额；
// @Description 按具体商品，统计销量、销售额、毛利，区别单品、套餐；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportDetailProductReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportDetailProductResp} "查询成功"
// @Router /insReport/detailProduct [get]
func (insReportApi *InsReportApi) DetailProduct(c *gin.Context) {
	var req insbuyReq.InsReportDetailProductReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportDetailProductResp
	resp, err := reportService.DetailProduct(req)
	response.ResultErr(resp, err, c)
}

// DetailStore
// @Summary 按店铺统计
// @Description 指定日期范围，按店铺统计营收。
// @Description 指标包括营业额、赠送额、销售提成、毛利 等；
// @Description 分组包括 按店、按天、按周、按月；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportDetailStoreReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportDetailStoreResp} "查询成功"
// @Router /insReport/detailStore [get]
func (insReportApi *InsReportApi) DetailStore(c *gin.Context) {
	var req insbuyReq.InsReportDetailStoreReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportDetailStoreResp
	resp, err := reportService.DetailStore(req)
	response.ResultErr(resp, err, c)
}

// DetailSeller
// @Summary 销售人员的业绩
// @Description 指定日期范围，按销售人员统计营收。
// @Description 指标包括营业额、赠送额、销售提成、盈利 等；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportDetailSellerReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportDetailSellerResp} "查询成功"
// @Router /insReport/detailSeller [get]
func (insReportApi *InsReportApi) DetailSeller(c *gin.Context) {
	var req insbuyReq.InsReportDetailSellerReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportDetailSellerResp
	resp, err := reportService.DetailSeller(req)
	response.ResultErr(resp, err, c)
}

// DetailStorageWine
// @Summary 存取洒统计
// @Description 指定日期范围，统计存取洒。
// @Description 类型包括 存酒、取酒、库存；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportDetailStorageWineReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportDetailStorageWineResp} "查询成功"
// @Router /insReport/detailStorageWine [get]
func (insReportApi *InsReportApi) DetailStorageWine(c *gin.Context) {
	var req insbuyReq.InsReportDetailStorageWineReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportDetailStorageWineResp
	resp, err := reportService.DetailStorageWine(req)
	response.ResultErr(resp, err, c)
}

// DetailTable
// @Summary 桌台业绩
// @Description 指定日期范围，统计桌台。
// @Description 指标包括当前的销售人员、营业额、赠送额、销售提成、毛利 等；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportDetailTableReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportDetailTableResp} "查询成功"
// @Router /insReport/detailTable [get]
func (insReportApi *InsReportApi) DetailTable(c *gin.Context) {
	var req insbuyReq.InsReportDetailTableReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportDetailTableResp
	resp, err := reportService.DetailTable(req)
	response.ResultErr(resp, err, c)
}

// Realtime
// @Summary 现场，实时报告今天最新营业数据
// @Description 实时报告今天最新营业数据
// @Description 指标包括 总营收、已结金额、未结金额、散客订单数、开台数、客单价-桌台、客单价-散客、上桌率、待出品订单、XX仓排队、平均出品耗时、今日沽清、库存报警商品数、待处理申请 等；
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.InsReportRealtimeReq false "参数"
// @Success 200 {object} response.Response{data=insbuyRes.InsReportRealtimeResp} "查询成功"
// @Router /insReport/realtime [get]
func (insReportApi *InsReportApi) Realtime(c *gin.Context) {
	var req insbuyReq.InsReportRealtimeReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	var resp *insbuyRes.InsReportRealtimeResp
	resp, err := reportService.Realtime(req)
	response.ResultErr(resp, err, c)
}

// DepositReportDataByType
// @Summary 获取存酒取酒报表数据
// @Security ApiKeyAuth
// @Description 获取存酒取酒报表数据
// @Description
// @Tags 报告相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.EchoReportDataByTypeReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insReport/depositReportDataByType [get]
func (insReportApi *InsReportApi) DepositReportDataByType(c *gin.Context) {
	var req insbuyReq.EchoReportDataByTypeReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
	}
	if list, total, err := reportService.DepositReportDataByType(req); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "查询成功", c)
	}
}

// InsertDepositReportData 存酒报表盘点数据
// @Tags 报告相关
// @Summary 存酒报表盘点数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsertDepositReportDataReq true "存酒报表盘点数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insReport/insertDepositReportData [post]
func (insReportApi *InsReportApi) InsertDepositReportData(c *gin.Context) {
	var req insbuyReq.InsertDepositReportDataReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = reportService.InsertDepositReportData(req)
	response.ResultErr(nil, err, c)
}

// RefreshDepositReport 刷新存取酒报表
// @Tags 报告相关
// @Summary 刷新存取酒报表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.RefreshDepositReportReq true "刷新存取酒报表"
// @Success   200   {object}  response.Response{}
// @Router /insReport/refreshDepositReport [post]
func (a *InsBookInApi) RefreshDepositReport(c *gin.Context) {
	var err error
	var req insbuyReq.RefreshDepositReportReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err = reportService.RefreshDepositReport(req); err != nil {
		global.GVA_LOG.Error("刷新存取酒报表失败", zap.Error(err))
		response.FailWithMessage("刷新存取酒报表失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("刷新存取酒报表成功", c)
	}
}
