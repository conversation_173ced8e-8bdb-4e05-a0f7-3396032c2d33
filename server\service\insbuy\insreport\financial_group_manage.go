package insreport

import (
	"context"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdata/datasource"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"gorm.io/gen"
)

//集团管报

// GroupDetailReportItem 集团明细报表数据项
type GroupDetailReportItem struct {
	BusinessMonth    string        `json:"business_month" comment:"管报月份"`      // 管报月份
	PaymentEntity    string        `json:"payment_entity" comment:"收款主体"`      // 收款主体
	BusinessEntity   string        `json:"business_entity" comment:"营业主体"`     // 营业主体
	BusinessLevel2   string        `json:"business_level2" comment:"营业主体二级"`   // 营业主体一级
	Amount           jtypes.JPrice `json:"amount" comment:"金额"`                // 金额
	TaxRate          string        `json:"tax_rate" comment:"税率"`              // 税率
	AmountExcludeTax jtypes.JPrice `json:"amount_exclude_tax" comment:"不含税金额"` // 不含税金额
	IncomeType       string        `json:"income_type" comment:"收入类型"`         // 收入类型
	Customer         string        `json:"customer" comment:"客户"`              // 客户
	Remark1          string        `json:"remark1" comment:"备注"`               // 备注
	Remark2          string        `json:"remark2" comment:"备注2"`              // 备注2
}

// GroupDetailReportList 集团明细报表查询接口
func GroupDetailReportList(ctx context.Context, q *query.Query, p FinancialWarehouseParams) (resp *SimpleStateReportResp, err error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	tdbResult := dbResult.As("s")

	// 构建查询条件
	var condition []gen.Condition
	{
		// 集团明细报表数据统一存储在总部（storeId = 0）
		condition = append(condition, tdbResult.StoreID.Eq(0))

		// 时间范围过滤
		if !p.StartDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Lte(p.EndDate))
		}

		// 数据源类型过滤
		condition = append(condition, tdbResult.Code.Eq(datasource.GroupDetailReportData.ToString()))
		condition = append(condition, tdbResult.Status.Eq(1)) // 只查询有效数据
	}

	// 内部DTO结构，用于接收数据库查询结果
	type GroupDetailReportDto struct {
		BusinessMonth    time.Time `json:"business_month" comment:"营业月份"`
		PaymentEntity    string    `json:"payment_entity" comment:"收款主体"`
		BusinessEntity   string    `json:"business_entity" comment:"营业主体"`
		BusinessLevel2   string    `json:"business_level2" comment:"营业主体二级"`
		Amount           string    `json:"amount" comment:"金额"`
		TaxRate          string    `json:"tax_rate" comment:"税率"`
		AmountExcludeTax string    `json:"amount_exclude_tax" comment:"不含税金额"`
		IncomeType       string    `json:"income_type" comment:"收入类型"`
		Customer         string    `json:"customer" comment:"客户"`
		Remark1          string    `json:"remark1" comment:"备注"`
		Remark2          string    `json:"remark2" comment:"备注2"`
	}

	t1 := make([]GroupDetailReportDto, 0)

	// 构建查询DAO
	dao1 := tdbResult.
		LeftJoin(dbDetails, tdbResult.ID.EqCol(dbDetails.ResultId)).
		Unscoped().As("s").Where(condition...).
		Select(
			tdbResult.StoreID.As("store_id"),
			dbDetails.BusinessDay.As("business_month"),
		)

	// 使用JSON提取函数获取字段数据
	jgorm.SelectAppend(dao1.(*gen.DO),
		jgorm.ClauseExpr("?->>'$.field.payment_entity' payment_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_entity' business_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_level2' business_level2", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount' amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.tax_rate' tax_rate", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount_exclude_tax' amount_exclude_tax", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.income_type' income_type", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.customer' customer", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.remark1' remark1", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.remark2' remark2", dbDetails.ResultData),
	)

	// 获取总记录数
	total, err := dao1.Count()
	if err != nil {
		return
	}

	resp = NewStateResp(t1)
	offset := resp.SetPageSize(p.PageNum, p.PageSize, total)
	err = dao1.Limit(p.PageSize).Offset(offset).Scan(&t1)
	if err != nil {
		return
	}
	// 数据转换和格式化
	t2 := make([]GroupDetailReportItem, 0)
	for _, v := range t1 {
		item := GroupDetailReportItem{
			BusinessMonth:    v.BusinessMonth.Format("2006-01"), // 格式化为YYYY-MM
			PaymentEntity:    v.PaymentEntity,
			BusinessEntity:   v.BusinessEntity,
			BusinessLevel2:   v.BusinessLevel2,
			Amount:           parsePrice(v.Amount),
			TaxRate:          v.TaxRate,
			AmountExcludeTax: parsePrice(v.AmountExcludeTax),
			IncomeType:       v.IncomeType,
			Customer:         v.Customer,
			Remark1:          v.Remark1,
			Remark2:          v.Remark2,
		}
		t2 = append(t2, item)
	}

	// 设置响应数据
	resp.List = t2

	// 设置时间范围
	if !p.StartDate.IsZero() {
		resp.StartDate = p.StartDate.Format("2006-01")
	}
	if !p.EndDate.IsZero() {
		resp.EndDate = p.EndDate.Format("2006-01")
	}

	return
}

// RegionalExpenseDetailItem 按地区费用明细数据项
type RegionalExpenseDetailItem struct {
	SerialNumber         int           `json:"serial_number" comment:"序号"`              // 序号
	CompletionTime       string        `json:"completion_time" comment:"完成时间"`          // 完成时间
	ReportMonth          string        `json:"report_month" comment:"管报月份"`             // 管报月份
	Title                string        `json:"title" comment:"标题"`                      // 标题
	PaymentEntity        string        `json:"payment_entity" comment:"付款主体"`           // 付款主体
	PaymentReason        string        `json:"payment_reason" comment:"付款事由"`           // 付款事由
	BusinessType         string        `json:"business_type" comment:"业务类型"`            // 业务类型
	ContractAmount       jtypes.JPrice `json:"contract_amount" comment:"合同签约金额"`        // 合同签约金额
	ContractPaidAmount   jtypes.JPrice `json:"contract_paid_amount" comment:"合同已付金额"`   // 合同已付金额
	CurrentRequestAmount jtypes.JPrice `json:"current_request_amount" comment:"本次请款金额"` // 本次请款金额
	PendingAmount        jtypes.JPrice `json:"pending_amount" comment:"待付款金额"`          // 待付款金额
	ReportConfirmAmount  jtypes.JPrice `json:"report_confirm_amount" comment:"管报确认金额"`  // 管报确认金额
	TaxRate              string        `json:"tax_rate" comment:"税率"`                   // 税率
	AmountExcludeTax     jtypes.JPrice `json:"amount_exclude_tax" comment:"不含税金额"`      // 不含税金额
	ExpenseCategory      string        `json:"expense_category" comment:"费用类别"`         // 费用类别
	IncludeInReport      string        `json:"include_in_report" comment:"是否纳入管报"`      // 是否纳入管报
	AccountName          string        `json:"account_name" comment:"户名"`               // 户名
	ReportEntity         string        `json:"report_entity" comment:"管报主体"`            // 管报主体
	ReportEntityDetail   string        `json:"report_entity_detail" comment:"管报主体明细"`   // 管报主体明细
	InitiatorName        string        `json:"initiator_name" comment:"发起人姓名"`          // 发起人姓名
	InitiatorDepartment  string        `json:"initiator_department" comment:"发起人部门"`    // 发起人部门
	Department           string        `json:"department" comment:"部门"`                 // 部门
}

// RegionalExpenseDetailList 按地区费用明细查询接口
func RegionalExpenseDetailList(ctx context.Context, q *query.Query, p FinancialWarehouseParams) (resp *SimpleStateReportResp, err error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	tdbResult := dbResult.As("s")

	// 构建查询条件
	var condition []gen.Condition
	{
		// 按地区费用明细数据统一存储在总部（storeId = 0）
		condition = append(condition, tdbResult.StoreID.Eq(0))

		// 时间范围过滤
		if !p.StartDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Lte(p.EndDate))
		}

		// 数据源类型过滤
		condition = append(condition, tdbResult.Code.Eq(datasource.RegionalExpenseDetailData.ToString()))
		condition = append(condition, tdbResult.Status.Eq(1)) // 只查询有效数据
	}

	// 内部DTO结构，用于接收数据库查询结果
	type RegionalExpenseDetailDto struct {
		SerialNumber         int       `json:"serial_number" comment:"序号"`
		CompletionTime       time.Time `json:"completion_time" comment:"完成时间"`
		ReportMonth          string    `json:"report_month" comment:"管报月份"`
		Title                string    `json:"title" comment:"标题"`
		PaymentEntity        string    `json:"payment_entity" comment:"付款主体"`
		PaymentReason        string    `json:"payment_reason" comment:"付款事由"`
		BusinessType         string    `json:"business_type" comment:"业务类型"`
		ContractAmount       string    `json:"contract_amount" comment:"合同签约金额"`
		ContractPaidAmount   string    `json:"contract_paid_amount" comment:"合同已付金额"`
		CurrentRequestAmount string    `json:"current_request_amount" comment:"本次请款金额"`
		PendingAmount        string    `json:"pending_amount" comment:"待付款金额"`
		ReportConfirmAmount  string    `json:"report_confirm_amount" comment:"管报确认金额"`
		TaxRate              string    `json:"tax_rate" comment:"税率"`
		AmountExcludeTax     string    `json:"amount_exclude_tax" comment:"不含税金额"`
		ExpenseCategory      string    `json:"expense_category" comment:"费用类别"`
		IncludeInReport      string    `json:"include_in_report" comment:"是否纳入管报"`
		AccountName          string    `json:"account_name" comment:"户名"`
		ReportEntity         string    `json:"report_entity" comment:"管报主体"`
		ReportEntityDetail   string    `json:"report_entity_detail" comment:"管报主体明细"`
		InitiatorName        string    `json:"initiator_name" comment:"发起人姓名"`
		InitiatorDepartment  string    `json:"initiator_department" comment:"发起人部门"`
		Department           string    `json:"department" comment:"部门"`
	}

	t1 := make([]RegionalExpenseDetailDto, 0)

	// 构建查询DAO
	dao1 := tdbResult.
		LeftJoin(dbDetails, tdbResult.ID.EqCol(dbDetails.ResultId)).
		Unscoped().As("s").Where(condition...).
		Select(
			tdbResult.StoreID.As("store_id"),
			dbDetails.BusinessDay.As("completion_time"),
		)

	// 使用JSON提取函数获取字段数据
	jgorm.SelectAppend(dao1.(*gen.DO),
		jgorm.ClauseExpr("?->'$.field.serial_number' serial_number", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_month' report_month", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.title' title", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.payment_entity' payment_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.payment_reason' payment_reason", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_type' business_type", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.contract_amount' contract_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.contract_paid_amount' contract_paid_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.current_request_amount' current_request_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.pending_amount' pending_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_confirm_amount' report_confirm_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.tax_rate' tax_rate", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount_exclude_tax' amount_exclude_tax", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.expense_category' expense_category", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.include_in_report' include_in_report", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.account_name' account_name", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_entity' report_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_entity_detail' report_entity_detail", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.initiator_name' initiator_name", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.initiator_department' initiator_department", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.department' department", dbDetails.ResultData),
	)
	total, err := dao1.Count()
	if err != nil {
		return
	}
	resp = NewStateResp(t1)
	offset := resp.SetPageSize(p.PageNum, p.PageSize, total)
	err = dao1.Limit(p.PageSize).Offset(offset).Scan(&t1)
	if err != nil {
		return
	}
	t2 := make([]RegionalExpenseDetailItem, 0)
	for _, v := range t1 {
		item := RegionalExpenseDetailItem{
			SerialNumber:         v.SerialNumber,
			CompletionTime:       v.CompletionTime.Format("2006-01-02"), // 格式化为YYYY-MM-DD
			ReportMonth:          v.CompletionTime.Month().String(),     // 格式化为YYYY-MM
			Title:                v.Title,
			PaymentEntity:        v.PaymentEntity,
			PaymentReason:        v.PaymentReason,
			BusinessType:         v.BusinessType,
			ContractAmount:       parsePrice(v.ContractAmount),
			ContractPaidAmount:   parsePrice(v.ContractPaidAmount),
			CurrentRequestAmount: parsePrice(v.CurrentRequestAmount),
			PendingAmount:        parsePrice(v.PendingAmount),
			ReportConfirmAmount:  parsePrice(v.ReportConfirmAmount),
			TaxRate:              v.TaxRate,
			AmountExcludeTax:     parsePrice(v.AmountExcludeTax),
			ExpenseCategory:      v.ExpenseCategory,
			IncludeInReport:      v.IncludeInReport,
			AccountName:          v.AccountName,
			ReportEntity:         v.ReportEntity,
			ReportEntityDetail:   v.ReportEntityDetail,
			InitiatorName:        v.InitiatorName,
			InitiatorDepartment:  v.InitiatorDepartment,
			Department:           v.Department,
		}
		t2 = append(t2, item)
	}

	// 设置响应数据
	resp.List = t2

	// 设置时间范围
	if !p.StartDate.IsZero() {
		resp.StartDate = p.StartDate.Format("2006-01")
	}
	if !p.EndDate.IsZero() {
		resp.EndDate = p.EndDate.Format("2006-01")
	}

	return
}

// FinancialSummaryItem 财务汇总数据项
type FinancialSummaryItem struct {
	CategoryId     uint                     `json:"category_id"`        // 分类ID
	CategoryName   string                   `json:"category_name"`      // 分类名称
	CategoryCode   string                   `json:"category_code"`      // 分类编码
	Level          uint                     `json:"level"`              // 分类级别 1:一级 2:二级 3:三级
	ParentId       uint                     `json:"parent_id"`          // 父级ID
	MonthlyAmounts map[string]jtypes.JPrice `json:"monthly_amounts"`    // 各月份金额
	TotalAmount    jtypes.JPrice            `json:"total_amount"`       // 总计金额
	SortOrder      uint                     `json:"sort_order"`         // 排序权重
	Children       []FinancialSummaryItem   `json:"children,omitempty"` // 子分类
}

// FinancialSummaryTotal 财务汇总统计
type FinancialSummaryTotal struct {
	MonthlyTotals map[string]jtypes.JPrice `json:"monthly_totals"` // 各月份总计
	GrandTotal    jtypes.JPrice            `json:"grand_total"`    // 总计
}

// RegionalFinancialSummaryReport 财务交叉统计报表接口
func RegionalFinancialSummaryReport(ctx context.Context, q *query.Query, p FinancialWarehouseParams) (resp *SimpleStateReportResp, err error) {
	// 1. 获取费用分类配置
	costTypeConfig, err := getCostTypeConfig(ctx, q, 0) // 使用总部配置
	if err != nil {
		return nil, err
	}

	// 2. 生成时间列
	timeColumns := generateTimeColumns(p.StartDate, p.EndDate)

	// 3. 查询费用数据 - 兼容现有参数
	expenseData, err := queryExpenseDataCompatible(ctx, q, p, timeColumns)
	if err != nil {
		return nil, err
	}
	// 4. 查询收入数据 - 兼容现有参数
	incomeData, err := queryIncomeDataCompatible(ctx, q, p, timeColumns)
	if err != nil {
		return nil, err
	}
	// 5. 合并数据并构建树形结构
	summaryItems := buildFinancialSummaryTree(costTypeConfig, expenseData, incomeData, timeColumns)

	// 6. 转换为兼容格式
	compatibleItems := convertToCompatibleFormat(summaryItems, timeColumns)

	// 7. 构建响应 - 使用现有的 SimpleStateReportResp 结构
	resp = NewStateResp([]FinancialSummaryItemCompatible{})

	// 8. 动态添加时间header
	resp.Headers = buildDynamicHeaders(timeColumns)

	// 9. 设置响应数据
	resp.List = compatibleItems

	// 10. 设置分页信息
	total := int64(len(compatibleItems))
	resp.SetPageSize(p.PageNum, p.PageSize, total)

	// 设置时间范围
	if !p.StartDate.IsZero() {
		resp.StartDate = p.StartDate.Format("2006-01")
	}
	if !p.EndDate.IsZero() {
		resp.EndDate = p.EndDate.Format("2006-01")
	}

	return resp, nil
}

// FinancialSummaryItemCompatible 兼容格式的财务汇总数据项
type FinancialSummaryItemCompatible struct {
	CategoryId   uint          `json:"category_id"`                   // 分类ID
	CategoryName string        `json:"category_name" comment:"项目/月份"` // 分类名称
	CategoryCode string        `json:"category_code"`                 // 分类编码
	Level        uint          `json:"level"`                         // 分类级别 1:一级 2:二级 3:三级
	ParentId     uint          `json:"parent_id"`                     // 父级ID
	TotalAmount  jtypes.JPrice `json:"total_amount"`                  // 总计金额
	SortOrder    uint          `json:"sort_order"`                    // 排序权重
	// 动态月份字段将通过map方式处理，在转换时动态添加到结构体中
	MonthlyData map[string]jtypes.JPrice `json:"-"` // 月份数据，不直接序列化
}

// queryExpenseDataCompatible 兼容版本的费用数据查询
func queryExpenseDataCompatible(ctx context.Context, q *query.Query, p FinancialWarehouseParams, timeColumns []string) ([]ExpenseDataItem, error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	tdbResult := dbResult.As("s")

	// 构建查询条件
	var condition []gen.Condition
	{
		// 按地区费用明细数据统一存储在总部（storeId = 0）
		condition = append(condition, tdbResult.StoreID.Eq(0))

		// 时间范围过滤
		if !p.StartDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Lte(p.EndDate))
		}
		condition = append(condition, tdbResult.Code.Eq(datasource.RegionalExpenseDetailData.ToString()))
		condition = append(condition, tdbResult.Status.Eq(1)) // 只查询有效数据
	}
	type ExpenseDto struct {
		PaymentEntity       string    `json:"payment_entity"`
		BusinessType        string    `json:"business_type"`
		BusinessMonth       time.Time `json:"business_month"`
		ExpenseCategory     string    `json:"expense_category"`
		ReportEntity        string    `json:"report_entity"`
		ReportRegion        string    `json:"report_region"` // 管报区域
		ReportConfirmAmount string    `json:"report_confirm_amount"`
		AmountExcludeTax    string    `json:"amount_exclude_tax"`
	}

	var expenseItems []ExpenseDto

	// 构建查询DAO
	dao := tdbResult.
		LeftJoin(dbDetails, tdbResult.ID.EqCol(dbDetails.ResultId)).
		Unscoped().As("s").Where(condition...).
		Select(
			tdbResult.StoreID.As("store_id"),
			dbDetails.BusinessDay.As("business_month"),
		)

	// 使用JSON提取函数获取字段数据
	jgorm.SelectAppend(dao.(*gen.DO),
		jgorm.ClauseExpr("?->>'$.field.payment_entity' payment_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_type' business_type", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_entity' business_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_level2' business_level2", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.expense_category' expense_category", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_entity' report_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_region' report_region", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.report_confirm_amount' report_confirm_amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount_exclude_tax' amount_exclude_tax", dbDetails.ResultData),
	)

	// 添加额外的过滤条件 - 兼容现有的ManageSearch结构
	if p.Area != "" {
		// 支持按管报主体或管报区域进行过滤
		jgorm.DbClause(dao,
			jgorm.DbClauseExpr("(?->>'$.field.report_region' LIKE ?)",
				dbDetails.ResultData, "%"+p.Area+"%"),
		)
	}
	if len(p.ReportEntity) > 0 {
		jgorm.DbClause(dao,
			jgorm.DbClauseExpr("(?->>'$.field.report_entity' in ?)",
				dbDetails.ResultData, p.ReportEntity),
		)
	}
	if len(p.ReportEntity2) > 0 {
		jgorm.DbClause(dao,
			jgorm.DbClauseExpr("(?->>'$.field.business_level2' in ?)",
				dbDetails.ResultData, p.ReportEntity2),
		)
	}

	err := dao.Scan(&expenseItems)
	if err != nil {
		return nil, err
	}

	// 转换为汇总数据
	var result []ExpenseDataItem
	for _, item := range expenseItems {
		month := item.BusinessMonth.Format("2006-01")

		// 选择金额字段 - 兼容现有的Typ字段（含税/不含税）
		var amount jtypes.JPrice
		if p.Typ == 1 { // 含税
			amount = parsePrice(item.ReportConfirmAmount)
		} else { // 不含税
			amount = parsePrice(item.AmountExcludeTax)
		}

		result = append(result, ExpenseDataItem{
			Month:            month,
			ExpenseCategory:  item.ExpenseCategory,
			Amount:           amount,
			AmountExcludeTax: parsePrice(item.AmountExcludeTax),
		})
	}

	return result, nil
}

// queryIncomeDataCompatible 兼容版本的收入数据查询
func queryIncomeDataCompatible(ctx context.Context, q *query.Query, p FinancialWarehouseParams, timeColumns []string) ([]IncomeDataItem, error) {
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	tdbResult := dbResult.As("s")

	// 构建查询条件
	var condition []gen.Condition
	{
		condition = append(condition, tdbResult.StoreID.Eq(0))

		if !p.StartDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Lte(p.EndDate))
		}

		// 数据源类型过滤 - 使用集团明细报表数据作为收入数据源
		condition = append(condition, tdbResult.Code.Eq(datasource.GroupDetailReportData.ToString()))
		condition = append(condition, tdbResult.Status.Eq(1)) // 只查询有效数据
	}

	type IncomeDto struct {
		PaymentEntity    string    `json:"payment_entity"`
		BusinessLevel    string    `json:"business_level"`
		BusinessLevel2   string    `json:"business_level2"`
		BusinessMonth    time.Time `json:"business_month"`
		IncomeType       string    `json:"income_type"`
		Amount           string    `json:"amount"`
		AmountExcludeTax string    `json:"amount_exclude_tax"`
	}

	var incomeItems []IncomeDto
	if p.Area != "" { //收入明细不会包含区域
		return nil, nil
	}
	// 构建查询DAO
	dao := tdbResult.
		LeftJoin(dbDetails, tdbResult.ID.EqCol(dbDetails.ResultId)).
		Unscoped().As("s").Where(condition...).
		Select(
			tdbResult.StoreID.As("store_id"),
			dbDetails.BusinessDay.As("business_month"),
		)

	// 使用JSON提取函数获取字段数据
	jgorm.SelectAppend(dao.(*gen.DO),
		jgorm.ClauseExpr("?->>'$.field.payment_entity' payment_entity", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_level' business_level", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.business_level2' business_level2", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.income_type' income_type", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount' amount", dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.amount_exclude_tax' amount_exclude_tax", dbDetails.ResultData),
	)

	if len(p.ReportEntity) > 0 {
		jgorm.DbClause(dao,
			jgorm.DbClauseExpr("(?->>'$.field.business_level' in ?)",
				dbDetails.ResultData, p.ReportEntity),
		)
	}
	if len(p.ReportEntity2) > 0 {
		jgorm.DbClause(dao,
			jgorm.DbClauseExpr("(?->>'$.field.business_level2' in ?)",
				dbDetails.ResultData, p.ReportEntity2),
		)
	}

	err := dao.Scan(&incomeItems)
	if err != nil {
		return nil, err
	}

	// 转换为汇总数据
	var result []IncomeDataItem
	for _, item := range incomeItems {
		month := item.BusinessMonth.Format("2006-01")

		// 选择金额字段 - 兼容现有的Typ字段（含税/不含税）
		var amount jtypes.JPrice
		if p.Typ == 1 { // 含税
			amount = parsePrice(item.Amount)
		} else { // 不含税
			amount = parsePrice(item.AmountExcludeTax)
		}

		result = append(result, IncomeDataItem{
			Month:            month,
			IncomeType:       item.IncomeType,
			Amount:           amount,
			AmountExcludeTax: parsePrice(item.AmountExcludeTax),
		})
	}

	return result, nil
}

// convertToCompatibleFormat 转换为兼容格式
func convertToCompatibleFormat(summaryItems []FinancialSummaryItem, timeColumns []string) []interface{} {
	var result []interface{}
	for _, item := range summaryItems {
		// 创建基础的兼容项
		compatibleItem := map[string]interface{}{
			"category_id":   item.CategoryId,
			"category_name": item.CategoryName,
			"category_code": item.CategoryCode,
			"level":         item.Level,
			"parent_id":     item.ParentId,
			"total_amount":  item.TotalAmount,
			"sort_order":    item.SortOrder,
		}

		// 动态添加月份字段
		for _, month := range timeColumns {
			if amount, exists := item.MonthlyAmounts[month]; exists {
				compatibleItem[month] = amount
			} else {
				compatibleItem[month] = jtypes.JPrice(0)
			}
		}

		result = append(result, compatibleItem)

		// 递归处理子项
		if len(item.Children) > 0 {
			childItems := convertToCompatibleFormat(item.Children, timeColumns)
			result = append(result, childItems...)
		}
	}
	return result
}

// buildDynamicHeaders 构建动态时间header
func buildDynamicHeaders(timeColumns []string) []SimpleReportRespDataHeader {
	var headers []SimpleReportRespDataHeader

	// 添加基础字段header
	headers = append(headers, SimpleReportRespDataHeader{
		Title: "项目/月份",
		Field: "category_name",
	})

	// 动态添加时间列header
	for _, month := range timeColumns {
		headers = append(headers, SimpleReportRespDataHeader{
			Title: month,
			Field: month,
		})
	}

	// 添加总计列header
	headers = append(headers, SimpleReportRespDataHeader{
		Title: "总计",
		Field: "total_amount",
	})

	return headers
}

// 注意：SimpleStateReportResp 已经有内置的 SetPageSize 方法，不需要重复定义

// getCostTypeConfig 获取费用分类配置
func getCostTypeConfig(ctx context.Context, q *query.Query, storeId uint) ([]CostTypeConfigItem, error) {
	dbCenter := q.InsConfigCenter
	dbDetails := q.InsConfigCenterDetail

	var condition []gen.Condition
	{
		condition = append(condition, dbCenter.DeletedAt.IsNull())
		condition = append(condition, dbDetails.DeletedAt.IsNull())
		condition = append(condition, dbCenter.Module.Eq(insbuy.ModuleReport.ToString()))
		condition = append(condition, dbCenter.KeyPath.Eq(insbuy.ReportGroupCostType.ToString()))
		condition = append(condition, dbCenter.StoreId.Eq(storeId))
	}

	type CostTypeDto struct {
		Id                 uint   `json:"id"`
		Level              uint   `json:"level"`
		ParentId           uint   `json:"parent_id"`
		CategoryName       string `json:"category_name"`
		CategoryCode       string `json:"category_code"`
		IsActive           uint   `json:"is_active"`
		SortOrder          uint   `json:"sort_order"`
		IsCalculated       uint   `json:"is_calculated"`
		CalculationFormula string `json:"calculation_formula"`
		DependsOn          string `json:"depends_on"` // JSON字符串，存储依赖ID数组
		CalculationType    string `json:"calculation_type"`
	}

	var items []CostTypeDto
	dao := dbCenter.
		Where(condition...).
		LeftJoin(dbDetails, dbCenter.ID.EqCol(dbDetails.ConfigId)).
		Select(dbDetails.ID.As("id"), dbDetails.ItemIndex.As("parent_id"))

	jgorm.SelectAppend(&dao.DO,
		jgorm.ClauseExpr("?->>'$.level' as level", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.category_name' as category_name", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.category_code' as category_code", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.is_active' as is_active", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.sort_order' as sort_order", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.is_calculated' as is_calculated", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.calculation_formula' as calculation_formula", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.depends_on' as depends_on", dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.calculation_type' as calculation_type", dbDetails.ItemValue),
	)

	err := dao.Order(dbDetails.ItemIndex).Scan(&items)
	if err != nil {
		return nil, err
	}

	// 转换为配置项
	configItems := make([]CostTypeConfigItem, 0)
	for _, item := range items {
		if item.IsActive == 1 {
			isCalculated := item.IsCalculated == 1
			configItems = append(configItems, CostTypeConfigItem{
				Id:                 item.Id,
				Level:              item.Level,
				ParentId:           item.ParentId,
				CategoryName:       item.CategoryName,
				CategoryCode:       item.CategoryCode,
				SortOrder:          item.SortOrder,
				IsCalculated:       isCalculated,
				CalculationFormula: item.CalculationFormula,
			})
		}
	}

	return configItems, nil
}

// CostTypeConfigItem 费用分类配置项
type CostTypeConfigItem struct {
	Id                 uint   `json:"id"`
	Level              uint   `json:"level"`
	ParentId           uint   `json:"parent_id"`
	CategoryName       string `json:"category_name"`
	CategoryCode       string `json:"category_code"`
	SortOrder          uint   `json:"sort_order"`
	IsCalculated       bool   `json:"is_calculated"`       // 是否为计算型分类
	CalculationFormula string `json:"calculation_formula"` // JSON格式的公式定义
}

// 删除旧的计算类型常量和上下文结构，使用新的公式处理器

// generateTimeColumns 生成时间列
func generateTimeColumns(startMonth, endMonth time.Time) []string {
	var columns []string
	current := time.Date(startMonth.Year(), startMonth.Month(), 1, 0, 0, 0, 0, time.Local)
	end := time.Date(endMonth.Year(), endMonth.Month(), 1, 0, 0, 0, 0, time.Local)

	for current.Before(end) || current.Equal(end) {
		columns = append(columns, current.Format("2006-01"))
		current = current.AddDate(0, 1, 0)
	}

	return columns
}

// ExpenseDataItem 费用数据项
type ExpenseDataItem struct {
	Month            string        `json:"month"`
	ExpenseCategory  string        `json:"expense_category"`
	Amount           jtypes.JPrice `json:"amount"`
	AmountExcludeTax jtypes.JPrice `json:"amount_exclude_tax"`
}

// IncomeDataItem 收入数据项
type IncomeDataItem struct {
	Month            string        `json:"month"`
	IncomeType       string        `json:"income_type"`
	Amount           jtypes.JPrice `json:"amount"`
	AmountExcludeTax jtypes.JPrice `json:"amount_exclude_tax"`
}

// buildFinancialSummaryTree 构建财务汇总树形结构
func buildFinancialSummaryTree(costTypeConfig []CostTypeConfigItem, expenseData []ExpenseDataItem, incomeData []IncomeDataItem, timeColumns []string) []FinancialSummaryItem {
	// 1. 创建分类映射
	categoryMap := make(map[uint]CostTypeConfigItem)
	for _, config := range costTypeConfig {
		categoryMap[config.Id] = config
	}

	// 2. 聚合费用数据
	expenseAggregated := aggregateExpenseData(expenseData, timeColumns)

	// 3. 聚合收入数据
	incomeAggregated := aggregateIncomeData(incomeData, timeColumns)
	// 4. 构建树形结构
	var rootItems []FinancialSummaryItem
	// 处理一级分类（排除计算型分类）
	for _, config := range costTypeConfig {
		if config.Level == 1 && !config.IsCalculated {
			item := FinancialSummaryItem{
				CategoryId:     config.Id,
				CategoryName:   config.CategoryName,
				CategoryCode:   config.CategoryCode,
				Level:          config.Level,
				ParentId:       config.ParentId,
				MonthlyAmounts: make(map[string]jtypes.JPrice),
				TotalAmount:    0,
				SortOrder:      config.SortOrder,
				Children:       []FinancialSummaryItem{},
			}

			// 初始化月份金额
			for _, month := range timeColumns {
				item.MonthlyAmounts[month] = 0
			}

			// 添加子分类
			item.Children = buildChildCategories(config.Id, categoryMap, expenseAggregated, incomeAggregated, timeColumns)

			// 计算父级汇总
			for _, child := range item.Children {
				for month, amount := range child.MonthlyAmounts {
					item.MonthlyAmounts[month] += amount
				}
				item.TotalAmount += child.TotalAmount
			}

			rootItems = append(rootItems, item)
		}
	}

	// 5. 处理计算型分类（使用简化JSON格式）
	jsonProcessor := NewSimpleJSONProcessor(costTypeConfig)
	rootItems = jsonProcessor.ProcessCalculatedCategoriesJSON(rootItems, costTypeConfig, timeColumns)

	return rootItems
}

// aggregateExpenseData 聚合费用数据
func aggregateExpenseData(expenseData []ExpenseDataItem, timeColumns []string) map[string]map[string]jtypes.JPrice {
	// 结构: map[category][month] = amount
	result := make(map[string]map[string]jtypes.JPrice)

	for _, item := range expenseData {
		if result[item.ExpenseCategory] == nil {
			result[item.ExpenseCategory] = make(map[string]jtypes.JPrice)
		}
		result[item.ExpenseCategory][item.Month] += item.Amount
	}

	return result
}

// aggregateIncomeData 聚合收入数据
func aggregateIncomeData(incomeData []IncomeDataItem, timeColumns []string) map[string]map[string]jtypes.JPrice {
	// 结构: map[incomeType][month] = amount
	result := make(map[string]map[string]jtypes.JPrice)

	for _, item := range incomeData {
		if result[item.IncomeType] == nil {
			result[item.IncomeType] = make(map[string]jtypes.JPrice)
		}
		result[item.IncomeType][item.Month] += item.Amount
	}

	return result
}

// buildChildCategories 构建子分类
func buildChildCategories(parentId uint, categoryMap map[uint]CostTypeConfigItem, expenseAggregated map[string]map[string]jtypes.JPrice, incomeAggregated map[string]map[string]jtypes.JPrice, timeColumns []string) []FinancialSummaryItem {
	var children []FinancialSummaryItem

	for _, config := range categoryMap {
		if config.ParentId == parentId && !config.IsCalculated {
			item := FinancialSummaryItem{
				CategoryId:     config.Id,
				CategoryName:   config.CategoryName,
				CategoryCode:   config.CategoryCode,
				Level:          config.Level,
				ParentId:       config.ParentId,
				MonthlyAmounts: make(map[string]jtypes.JPrice),
				TotalAmount:    0,
				SortOrder:      config.SortOrder,
				Children:       []FinancialSummaryItem{},
			}

			// 初始化月份金额
			for _, month := range timeColumns {
				item.MonthlyAmounts[month] = 0
			}

			// 如果是叶子节点，从聚合数据中获取金额
			if config.Level == 3 || !hasChildren(config.Id, categoryMap) {
				// 从费用数据中获取
				if monthlyData, exists := expenseAggregated[config.CategoryName]; exists {
					for month, amount := range monthlyData {
						item.MonthlyAmounts[month] = amount
						item.TotalAmount += amount
					}
				}
				// 从收入数据中获取（如果分类名称匹配）
				if monthlyData, exists := incomeAggregated[config.CategoryName]; exists {
					for month, amount := range monthlyData {
						item.MonthlyAmounts[month] += amount
						item.TotalAmount += amount
					}
				}
			} else {
				// 如果不是叶子节点，递归构建子分类
				item.Children = buildChildCategories(config.Id, categoryMap, expenseAggregated, incomeAggregated, timeColumns)

				// 计算父级汇总
				for _, child := range item.Children {
					for month, amount := range child.MonthlyAmounts {
						item.MonthlyAmounts[month] += amount
					}
					item.TotalAmount += child.TotalAmount
				}
			}

			children = append(children, item)
		}
	}

	return children
}

// createDataMap 创建分类ID到数据项的映射
func createDataMap(items []FinancialSummaryItem) map[uint]*FinancialSummaryItem {
	dataMap := make(map[uint]*FinancialSummaryItem)

	var addToMap func([]FinancialSummaryItem)
	addToMap = func(itemList []FinancialSummaryItem) {
		for i := range itemList {
			item := &itemList[i]
			dataMap[item.CategoryId] = item
			if len(item.Children) > 0 {
				addToMap(item.Children)
			}
		}
	}

	addToMap(items)
	return dataMap
}

// insertCalculatedItem 将计算项插入到适当的位置
func insertCalculatedItem(items []FinancialSummaryItem, calculatedItem *FinancialSummaryItem, config CostTypeConfigItem) []FinancialSummaryItem {
	// 根据配置的层级和父级ID决定插入位置
	if config.ParentId == 0 {
		// 顶级分类，按排序顺序插入到根级别
		items = insertItemByOrder(items, *calculatedItem)
	} else {
		// 子分类，需要找到父级并添加到其children中
		items = insertIntoParent(items, calculatedItem, config.ParentId)
	}
	return items
}

// insertItemByOrder 按排序顺序插入项目
func insertItemByOrder(items []FinancialSummaryItem, newItem FinancialSummaryItem) []FinancialSummaryItem {
	// 找到合适的插入位置
	insertIndex := len(items)
	for i, item := range items {
		if newItem.SortOrder < item.SortOrder {
			insertIndex = i
			break
		}
	}

	// 在指定位置插入
	if insertIndex == len(items) {
		// 插入到末尾
		items = append(items, newItem)
	} else {
		// 插入到中间位置
		items = append(items[:insertIndex+1], items[insertIndex:]...)
		items[insertIndex] = newItem
	}

	return items
}

// insertIntoParent 递归查找父级并插入子项
func insertIntoParent(items []FinancialSummaryItem, calculatedItem *FinancialSummaryItem, parentId uint) []FinancialSummaryItem {
	for i := range items {
		if items[i].CategoryId == parentId {
			// 按排序顺序插入到子项中
			items[i].Children = insertItemByOrder(items[i].Children, *calculatedItem)
			return items
		}
		if len(items[i].Children) > 0 {
			items[i].Children = insertIntoParent(items[i].Children, calculatedItem, parentId)
		}
	}
	return items
}

// hasChildren 检查是否有子分类
func hasChildren(parentId uint, categoryMap map[uint]CostTypeConfigItem) bool {
	for _, config := range categoryMap {
		if config.ParentId == parentId {
			return true
		}
	}
	return false
}

type FinancialFilters struct {
	Label      string                `json:"label"`
	FilterList []FinancialFilterItem `json:"filter_list"`
}

type FinancialFilterItem struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// RegionalFinancialFilters 集团管报-筛选项
func RegionalFinancialFilters() []FinancialFilters {
	return []FinancialFilters{
		{
			Label: "总部",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity",
					Value: "总部",
				},
			},
		},
		{
			Label: "物业",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity",
					Value: "物业",
				},
			},
		},
		{
			Label: "乐园",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity",
					Value: "乐园",
				},
			},
		},
		{
			Label: "乐园-通票&直播",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity2",
					Value: "乐园-通票",
				},
				{
					Label: "report_entity2",
					Value: "乐园-直播",
				},
			},
		},
		{
			Label: "乐园-直播",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity2",
					Value: "乐园-通票",
				},
			},
		},
		{
			Label: "乐园-广告",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity2",
					Value: "乐园-广告",
				},
			},
		},
		{
			Label: "成都",
			FilterList: []FinancialFilterItem{
				{
					Label: "area",
					Value: "chengdu",
				},
			},
		},
		{
			Label: "北京",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity2",
					Value: "总部-北京",
				},
			},
		},
		{
			Label: "上海",
			FilterList: []FinancialFilterItem{
				{
					Label: "area",
					Value: "shanghai",
				},
			},
		},
		{
			Label: "海外",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity2",
					Value: "总部-海外市场",
				},
			},
		},
		{
			Label: "艺人",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity",
					Value: "艺人",
				},
			},
		},
		{
			Label: "总部+乐园",
			FilterList: []FinancialFilterItem{
				{
					Label: "report_entity",
					Value: "总部",
				},
				{
					Label: "report_entity",
					Value: "乐园",
				},
			},
		},
	}
}
