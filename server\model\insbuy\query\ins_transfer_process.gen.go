// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTransferProcess(db *gorm.DB, opts ...gen.DOOption) insTransferProcess {
	_insTransferProcess := insTransferProcess{}

	_insTransferProcess.insTransferProcessDo.UseDB(db, opts...)
	_insTransferProcess.insTransferProcessDo.UseModel(&insbuy.InsTransferProcess{})

	tableName := _insTransferProcess.insTransferProcessDo.TableName()
	_insTransferProcess.ALL = field.NewAsterisk(tableName)
	_insTransferProcess.ID = field.NewUint(tableName, "id")
	_insTransferProcess.CreatedAt = field.NewTime(tableName, "created_at")
	_insTransferProcess.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTransferProcess.StoreId = field.NewUint(tableName, "store_id")
	_insTransferProcess.Type = field.NewString(tableName, "type")
	_insTransferProcess.Name = field.NewString(tableName, "name")
	_insTransferProcess.Description = field.NewString(tableName, "description")
	_insTransferProcess.Enabled = field.NewBool(tableName, "enabled")
	_insTransferProcess.Ext = field.NewField(tableName, "ext")
	_insTransferProcess.InsTransferProcessStep = insTransferProcessHasManyInsTransferProcessStep{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("InsTransferProcessStep", "insbuy.InsTransferProcessStep"),
	}

	_insTransferProcess.fillFieldMap()

	return _insTransferProcess
}

type insTransferProcess struct {
	insTransferProcessDo

	ALL                    field.Asterisk
	ID                     field.Uint
	CreatedAt              field.Time
	UpdatedAt              field.Time
	StoreId                field.Uint
	Type                   field.String
	Name                   field.String
	Description            field.String
	Enabled                field.Bool
	Ext                    field.Field
	InsTransferProcessStep insTransferProcessHasManyInsTransferProcessStep

	fieldMap map[string]field.Expr
}

func (i insTransferProcess) Table(newTableName string) *insTransferProcess {
	i.insTransferProcessDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTransferProcess) As(alias string) *insTransferProcess {
	i.insTransferProcessDo.DO = *(i.insTransferProcessDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTransferProcess) updateTableName(table string) *insTransferProcess {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Type = field.NewString(table, "type")
	i.Name = field.NewString(table, "name")
	i.Description = field.NewString(table, "description")
	i.Enabled = field.NewBool(table, "enabled")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insTransferProcess) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTransferProcess) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["type"] = i.Type
	i.fieldMap["name"] = i.Name
	i.fieldMap["description"] = i.Description
	i.fieldMap["enabled"] = i.Enabled
	i.fieldMap["ext"] = i.Ext

}

func (i insTransferProcess) clone(db *gorm.DB) insTransferProcess {
	i.insTransferProcessDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTransferProcess) replaceDB(db *gorm.DB) insTransferProcess {
	i.insTransferProcessDo.ReplaceDB(db)
	return i
}

type insTransferProcessHasManyInsTransferProcessStep struct {
	db *gorm.DB

	field.RelationField
}

func (a insTransferProcessHasManyInsTransferProcessStep) Where(conds ...field.Expr) *insTransferProcessHasManyInsTransferProcessStep {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insTransferProcessHasManyInsTransferProcessStep) WithContext(ctx context.Context) *insTransferProcessHasManyInsTransferProcessStep {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insTransferProcessHasManyInsTransferProcessStep) Session(session *gorm.Session) *insTransferProcessHasManyInsTransferProcessStep {
	a.db = a.db.Session(session)
	return &a
}

func (a insTransferProcessHasManyInsTransferProcessStep) Model(m *insbuy.InsTransferProcess) *insTransferProcessHasManyInsTransferProcessStepTx {
	return &insTransferProcessHasManyInsTransferProcessStepTx{a.db.Model(m).Association(a.Name())}
}

type insTransferProcessHasManyInsTransferProcessStepTx struct{ tx *gorm.Association }

func (a insTransferProcessHasManyInsTransferProcessStepTx) Find() (result []*insbuy.InsTransferProcessStep, err error) {
	return result, a.tx.Find(&result)
}

func (a insTransferProcessHasManyInsTransferProcessStepTx) Append(values ...*insbuy.InsTransferProcessStep) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insTransferProcessHasManyInsTransferProcessStepTx) Replace(values ...*insbuy.InsTransferProcessStep) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insTransferProcessHasManyInsTransferProcessStepTx) Delete(values ...*insbuy.InsTransferProcessStep) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insTransferProcessHasManyInsTransferProcessStepTx) Clear() error {
	return a.tx.Clear()
}

func (a insTransferProcessHasManyInsTransferProcessStepTx) Count() int64 {
	return a.tx.Count()
}

type insTransferProcessDo struct{ gen.DO }

func (i insTransferProcessDo) Debug() *insTransferProcessDo {
	return i.withDO(i.DO.Debug())
}

func (i insTransferProcessDo) WithContext(ctx context.Context) *insTransferProcessDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTransferProcessDo) ReadDB() *insTransferProcessDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTransferProcessDo) WriteDB() *insTransferProcessDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTransferProcessDo) Session(config *gorm.Session) *insTransferProcessDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTransferProcessDo) Clauses(conds ...clause.Expression) *insTransferProcessDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTransferProcessDo) Returning(value interface{}, columns ...string) *insTransferProcessDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTransferProcessDo) Not(conds ...gen.Condition) *insTransferProcessDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTransferProcessDo) Or(conds ...gen.Condition) *insTransferProcessDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTransferProcessDo) Select(conds ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTransferProcessDo) Where(conds ...gen.Condition) *insTransferProcessDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTransferProcessDo) Order(conds ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTransferProcessDo) Distinct(cols ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTransferProcessDo) Omit(cols ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTransferProcessDo) Join(table schema.Tabler, on ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTransferProcessDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTransferProcessDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTransferProcessDo) Group(cols ...field.Expr) *insTransferProcessDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTransferProcessDo) Having(conds ...gen.Condition) *insTransferProcessDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTransferProcessDo) Limit(limit int) *insTransferProcessDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTransferProcessDo) Offset(offset int) *insTransferProcessDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTransferProcessDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTransferProcessDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTransferProcessDo) Unscoped() *insTransferProcessDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTransferProcessDo) Create(values ...*insbuy.InsTransferProcess) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTransferProcessDo) CreateInBatches(values []*insbuy.InsTransferProcess, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTransferProcessDo) Save(values ...*insbuy.InsTransferProcess) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTransferProcessDo) First() (*insbuy.InsTransferProcess, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcess), nil
	}
}

func (i insTransferProcessDo) Take() (*insbuy.InsTransferProcess, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcess), nil
	}
}

func (i insTransferProcessDo) Last() (*insbuy.InsTransferProcess, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcess), nil
	}
}

func (i insTransferProcessDo) Find() ([]*insbuy.InsTransferProcess, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTransferProcess), err
}

func (i insTransferProcessDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTransferProcess, err error) {
	buf := make([]*insbuy.InsTransferProcess, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTransferProcessDo) FindInBatches(result *[]*insbuy.InsTransferProcess, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTransferProcessDo) Attrs(attrs ...field.AssignExpr) *insTransferProcessDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTransferProcessDo) Assign(attrs ...field.AssignExpr) *insTransferProcessDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTransferProcessDo) Joins(fields ...field.RelationField) *insTransferProcessDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTransferProcessDo) Preload(fields ...field.RelationField) *insTransferProcessDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTransferProcessDo) FirstOrInit() (*insbuy.InsTransferProcess, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcess), nil
	}
}

func (i insTransferProcessDo) FirstOrCreate() (*insbuy.InsTransferProcess, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcess), nil
	}
}

func (i insTransferProcessDo) FindByPage(offset int, limit int) (result []*insbuy.InsTransferProcess, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTransferProcessDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTransferProcessDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTransferProcessDo) Delete(models ...*insbuy.InsTransferProcess) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTransferProcessDo) withDO(do gen.Dao) *insTransferProcessDo {
	i.DO = *do.(*gen.DO)
	return i
}
