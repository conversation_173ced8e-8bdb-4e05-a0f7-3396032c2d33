// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseEmpties(db *gorm.DB, opts ...gen.DOOption) insWarehouseEmpties {
	_insWarehouseEmpties := insWarehouseEmpties{}

	_insWarehouseEmpties.insWarehouseEmptiesDo.UseDB(db, opts...)
	_insWarehouseEmpties.insWarehouseEmptiesDo.UseModel(&insbuy.InsWarehouseEmpties{})

	tableName := _insWarehouseEmpties.insWarehouseEmptiesDo.TableName()
	_insWarehouseEmpties.ALL = field.NewAsterisk(tableName)
	_insWarehouseEmpties.ID = field.NewUint(tableName, "id")
	_insWarehouseEmpties.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseEmpties.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseEmpties.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseEmpties.CreatedBy = field.NewUint(tableName, "created_by")
	_insWarehouseEmpties.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insWarehouseEmpties.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insWarehouseEmpties.StoreId = field.NewUint(tableName, "store_id")
	_insWarehouseEmpties.DeskId = field.NewUint(tableName, "desk_id")
	_insWarehouseEmpties.Type = field.NewUint(tableName, "type")
	_insWarehouseEmpties.Status = field.NewInt(tableName, "status")
	_insWarehouseEmpties.Remark = field.NewString(tableName, "remark")

	_insWarehouseEmpties.fillFieldMap()

	return _insWarehouseEmpties
}

type insWarehouseEmpties struct {
	insWarehouseEmptiesDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	CreatedBy field.Uint
	UpdatedBy field.Uint
	DeletedBy field.Uint
	StoreId   field.Uint
	DeskId    field.Uint
	Type      field.Uint
	Status    field.Int
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insWarehouseEmpties) Table(newTableName string) *insWarehouseEmpties {
	i.insWarehouseEmptiesDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseEmpties) As(alias string) *insWarehouseEmpties {
	i.insWarehouseEmptiesDo.DO = *(i.insWarehouseEmptiesDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseEmpties) updateTableName(table string) *insWarehouseEmpties {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.StoreId = field.NewUint(table, "store_id")
	i.DeskId = field.NewUint(table, "desk_id")
	i.Type = field.NewUint(table, "type")
	i.Status = field.NewInt(table, "status")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseEmpties) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseEmpties) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["type"] = i.Type
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
}

func (i insWarehouseEmpties) clone(db *gorm.DB) insWarehouseEmpties {
	i.insWarehouseEmptiesDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseEmpties) replaceDB(db *gorm.DB) insWarehouseEmpties {
	i.insWarehouseEmptiesDo.ReplaceDB(db)
	return i
}

type insWarehouseEmptiesDo struct{ gen.DO }

func (i insWarehouseEmptiesDo) Debug() *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseEmptiesDo) WithContext(ctx context.Context) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseEmptiesDo) ReadDB() *insWarehouseEmptiesDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseEmptiesDo) WriteDB() *insWarehouseEmptiesDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseEmptiesDo) Session(config *gorm.Session) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseEmptiesDo) Clauses(conds ...clause.Expression) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseEmptiesDo) Returning(value interface{}, columns ...string) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseEmptiesDo) Not(conds ...gen.Condition) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseEmptiesDo) Or(conds ...gen.Condition) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseEmptiesDo) Select(conds ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseEmptiesDo) Where(conds ...gen.Condition) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseEmptiesDo) Order(conds ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseEmptiesDo) Distinct(cols ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseEmptiesDo) Omit(cols ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseEmptiesDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseEmptiesDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseEmptiesDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseEmptiesDo) Group(cols ...field.Expr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseEmptiesDo) Having(conds ...gen.Condition) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseEmptiesDo) Limit(limit int) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseEmptiesDo) Offset(offset int) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseEmptiesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseEmptiesDo) Unscoped() *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseEmptiesDo) Create(values ...*insbuy.InsWarehouseEmpties) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseEmptiesDo) CreateInBatches(values []*insbuy.InsWarehouseEmpties, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseEmptiesDo) Save(values ...*insbuy.InsWarehouseEmpties) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseEmptiesDo) First() (*insbuy.InsWarehouseEmpties, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmpties), nil
	}
}

func (i insWarehouseEmptiesDo) Take() (*insbuy.InsWarehouseEmpties, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmpties), nil
	}
}

func (i insWarehouseEmptiesDo) Last() (*insbuy.InsWarehouseEmpties, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmpties), nil
	}
}

func (i insWarehouseEmptiesDo) Find() ([]*insbuy.InsWarehouseEmpties, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseEmpties), err
}

func (i insWarehouseEmptiesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseEmpties, err error) {
	buf := make([]*insbuy.InsWarehouseEmpties, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseEmptiesDo) FindInBatches(result *[]*insbuy.InsWarehouseEmpties, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseEmptiesDo) Attrs(attrs ...field.AssignExpr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseEmptiesDo) Assign(attrs ...field.AssignExpr) *insWarehouseEmptiesDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseEmptiesDo) Joins(fields ...field.RelationField) *insWarehouseEmptiesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseEmptiesDo) Preload(fields ...field.RelationField) *insWarehouseEmptiesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseEmptiesDo) FirstOrInit() (*insbuy.InsWarehouseEmpties, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmpties), nil
	}
}

func (i insWarehouseEmptiesDo) FirstOrCreate() (*insbuy.InsWarehouseEmpties, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmpties), nil
	}
}

func (i insWarehouseEmptiesDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseEmpties, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseEmptiesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseEmptiesDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseEmptiesDo) Delete(models ...*insbuy.InsWarehouseEmpties) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseEmptiesDo) withDO(do gen.Dao) *insWarehouseEmptiesDo {
	i.DO = *do.(*gen.DO)
	return i
}
