package insreport

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore"
	"go.uber.org/zap"
)

// ManualEditableItem 可手动编辑的报表项接口
type ManualEditableItem interface {
	GetManualEditKey() string                               // 获取手动编辑的唯一标识
	ApplyManualEdit(manualId uint, fields []insstore.Field) // 应用手动编辑数据，包含 ManualId
}

// ManualEditQueryParams 手动编辑查询参数
type ManualEditQueryParams struct {
	FinancialType int       `json:"financial_type"` // 财务报表类型
	StartDate     time.Time `json:"start_date"`     // 开始时间（可选）
	EndDate       time.Time `json:"end_date"`       // 结束时间（可选）
	StoreIds      []uint    `json:"store_ids"`      // 店铺ID列表（可选）
}

// ManualEditProcessor 通用手动编辑处理器
type ManualEditProcessor struct {
	ctx            context.Context
	q              *query.Query
	params         ManualEditQueryParams
	manualEditData map[string]insstore.SaleReportExt
}

// NewManualEditProcessor 创建通用手动编辑处理器
func NewManualEditProcessor(ctx context.Context, q *query.Query, params ManualEditQueryParams) *ManualEditProcessor {
	return &ManualEditProcessor{
		ctx:            ctx,
		q:              q,
		params:         params,
		manualEditData: make(map[string]insstore.SaleReportExt),
	}
}

// LoadManualEditData 批量加载手动编辑数据
func (p *ManualEditProcessor) LoadManualEditData(keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	// 构建查询参数，支持时间范围过滤
	queryParams := insstore.ManualReportParams{
		FinancialType: p.params.FinancialType,
		Key:           keys,
		StoreId:       p.params.StoreIds,
	}

	// 如果提供了时间范围，则加入查询条件
	if !p.params.StartDate.IsZero() {
		queryParams.StartDate = p.params.StartDate
	}
	if !p.params.EndDate.IsZero() {
		queryParams.EndDate = p.params.EndDate
	}

	manualList, err := insstore.ManualReportList(p.ctx, p.q, queryParams)
	if err != nil {
		return fmt.Errorf("查询手动编辑数据失败: %w", err)
	}

	for _, v := range manualList {
		ext := insstore.SaleReportExt{}
		ext.ManualId = v.ID
		if err := json.Unmarshal(v.Ext, &ext); err != nil {
			global.GVA_LOG.Warn("解析手动编辑数据失败", zap.Error(err), zap.Uint("manualId", v.ID))
			continue
		}
		p.manualEditData[v.Key] = ext
	}

	return nil
}

// ApplyManualEdit 对单个项目应用手动编辑数据
func (p *ManualEditProcessor) ApplyManualEdit(item ManualEditableItem) {
	key := item.GetManualEditKey()
	if ext, ok := p.manualEditData[key]; ok {
		item.ApplyManualEdit(ext.ManualId, ext.Fields)
	}
}

// BatchApplyManualEdit 批量应用手动编辑数据
func (p *ManualEditProcessor) BatchApplyManualEdit(items []ManualEditableItem) {
	appliedCount := 0
	for _, item := range items {
		key := item.GetManualEditKey()
		if _, exists := p.manualEditData[key]; exists {
			appliedCount++
		}
		p.ApplyManualEdit(item)
	}
}

// ProcessManualEditItems 通用的手动编辑数据处理流程 - 重新封装版本
func ProcessManualEditItems[T ManualEditableItem](ctx context.Context, q *query.Query, params ManualEditQueryParams, items []T) error {
	if len(items) == 0 {
		return nil
	}

	// 1. 创建处理器
	processor := NewManualEditProcessor(ctx, q, params)

	// 2. 提取所有需要的key
	keys := make([]string, len(items))
	for i, item := range items {
		keys[i] = item.GetManualEditKey()
	}

	// 3. 批量加载手动编辑数据（支持时间范围过滤）
	if err := processor.LoadManualEditData(keys); err != nil {
		return fmt.Errorf("加载手动编辑数据失败: %w", err)
	}

	// 4. 批量应用手动编辑数据
	manualEditableItems := make([]ManualEditableItem, len(items))
	for i := range items {
		manualEditableItems[i] = items[i]
	}
	processor.BatchApplyManualEdit(manualEditableItems)

	return nil
}

// ProcessManualEditItemsSimple 简化版本，向后兼容
func ProcessManualEditItemsSimple[T ManualEditableItem](ctx context.Context, q *query.Query, financialType int, items []T) error {
	params := ManualEditQueryParams{
		FinancialType: financialType,
	}
	return ProcessManualEditItems(ctx, q, params, items)
}
