// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsVerifyCode(db *gorm.DB, opts ...gen.DOOption) insVerifyCode {
	_insVerifyCode := insVerifyCode{}

	_insVerifyCode.insVerifyCodeDo.UseDB(db, opts...)
	_insVerifyCode.insVerifyCodeDo.UseModel(&insbuy.InsVerifyCode{})

	tableName := _insVerifyCode.insVerifyCodeDo.TableName()
	_insVerifyCode.ALL = field.NewAsterisk(tableName)
	_insVerifyCode.ID = field.NewUint(tableName, "id")
	_insVerifyCode.CreatedAt = field.NewTime(tableName, "created_at")
	_insVerifyCode.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insVerifyCode.Phone = field.NewString(tableName, "phone")
	_insVerifyCode.Type = field.NewUint(tableName, "type")
	_insVerifyCode.Code = field.NewString(tableName, "code")
	_insVerifyCode.Status = field.NewUint(tableName, "status")
	_insVerifyCode.Count_ = field.NewInt(tableName, "count")
	_insVerifyCode.Retry = field.NewInt(tableName, "retry")
	_insVerifyCode.Ip = field.NewString(tableName, "ip")

	_insVerifyCode.fillFieldMap()

	return _insVerifyCode
}

type insVerifyCode struct {
	insVerifyCodeDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	Phone     field.String
	Type      field.Uint
	Code      field.String
	Status    field.Uint
	Count_    field.Int
	Retry     field.Int
	Ip        field.String

	fieldMap map[string]field.Expr
}

func (i insVerifyCode) Table(newTableName string) *insVerifyCode {
	i.insVerifyCodeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insVerifyCode) As(alias string) *insVerifyCode {
	i.insVerifyCodeDo.DO = *(i.insVerifyCodeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insVerifyCode) updateTableName(table string) *insVerifyCode {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Phone = field.NewString(table, "phone")
	i.Type = field.NewUint(table, "type")
	i.Code = field.NewString(table, "code")
	i.Status = field.NewUint(table, "status")
	i.Count_ = field.NewInt(table, "count")
	i.Retry = field.NewInt(table, "retry")
	i.Ip = field.NewString(table, "ip")

	i.fillFieldMap()

	return i
}

func (i *insVerifyCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insVerifyCode) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["phone"] = i.Phone
	i.fieldMap["type"] = i.Type
	i.fieldMap["code"] = i.Code
	i.fieldMap["status"] = i.Status
	i.fieldMap["count"] = i.Count_
	i.fieldMap["retry"] = i.Retry
	i.fieldMap["ip"] = i.Ip
}

func (i insVerifyCode) clone(db *gorm.DB) insVerifyCode {
	i.insVerifyCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insVerifyCode) replaceDB(db *gorm.DB) insVerifyCode {
	i.insVerifyCodeDo.ReplaceDB(db)
	return i
}

type insVerifyCodeDo struct{ gen.DO }

func (i insVerifyCodeDo) Debug() *insVerifyCodeDo {
	return i.withDO(i.DO.Debug())
}

func (i insVerifyCodeDo) WithContext(ctx context.Context) *insVerifyCodeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insVerifyCodeDo) ReadDB() *insVerifyCodeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insVerifyCodeDo) WriteDB() *insVerifyCodeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insVerifyCodeDo) Session(config *gorm.Session) *insVerifyCodeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insVerifyCodeDo) Clauses(conds ...clause.Expression) *insVerifyCodeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insVerifyCodeDo) Returning(value interface{}, columns ...string) *insVerifyCodeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insVerifyCodeDo) Not(conds ...gen.Condition) *insVerifyCodeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insVerifyCodeDo) Or(conds ...gen.Condition) *insVerifyCodeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insVerifyCodeDo) Select(conds ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insVerifyCodeDo) Where(conds ...gen.Condition) *insVerifyCodeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insVerifyCodeDo) Order(conds ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insVerifyCodeDo) Distinct(cols ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insVerifyCodeDo) Omit(cols ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insVerifyCodeDo) Join(table schema.Tabler, on ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insVerifyCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insVerifyCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insVerifyCodeDo) Group(cols ...field.Expr) *insVerifyCodeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insVerifyCodeDo) Having(conds ...gen.Condition) *insVerifyCodeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insVerifyCodeDo) Limit(limit int) *insVerifyCodeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insVerifyCodeDo) Offset(offset int) *insVerifyCodeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insVerifyCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insVerifyCodeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insVerifyCodeDo) Unscoped() *insVerifyCodeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insVerifyCodeDo) Create(values ...*insbuy.InsVerifyCode) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insVerifyCodeDo) CreateInBatches(values []*insbuy.InsVerifyCode, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insVerifyCodeDo) Save(values ...*insbuy.InsVerifyCode) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insVerifyCodeDo) First() (*insbuy.InsVerifyCode, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVerifyCode), nil
	}
}

func (i insVerifyCodeDo) Take() (*insbuy.InsVerifyCode, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVerifyCode), nil
	}
}

func (i insVerifyCodeDo) Last() (*insbuy.InsVerifyCode, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVerifyCode), nil
	}
}

func (i insVerifyCodeDo) Find() ([]*insbuy.InsVerifyCode, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsVerifyCode), err
}

func (i insVerifyCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsVerifyCode, err error) {
	buf := make([]*insbuy.InsVerifyCode, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insVerifyCodeDo) FindInBatches(result *[]*insbuy.InsVerifyCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insVerifyCodeDo) Attrs(attrs ...field.AssignExpr) *insVerifyCodeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insVerifyCodeDo) Assign(attrs ...field.AssignExpr) *insVerifyCodeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insVerifyCodeDo) Joins(fields ...field.RelationField) *insVerifyCodeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insVerifyCodeDo) Preload(fields ...field.RelationField) *insVerifyCodeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insVerifyCodeDo) FirstOrInit() (*insbuy.InsVerifyCode, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVerifyCode), nil
	}
}

func (i insVerifyCodeDo) FirstOrCreate() (*insbuy.InsVerifyCode, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVerifyCode), nil
	}
}

func (i insVerifyCodeDo) FindByPage(offset int, limit int) (result []*insbuy.InsVerifyCode, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insVerifyCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insVerifyCodeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insVerifyCodeDo) Delete(models ...*insbuy.InsVerifyCode) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insVerifyCodeDo) withDO(do gen.Dao) *insVerifyCodeDo {
	i.DO = *do.(*gen.DO)
	return i
}
