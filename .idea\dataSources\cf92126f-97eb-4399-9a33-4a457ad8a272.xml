<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>MyISAM</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root||ALTER|G
|root||root|localhost|ALTER|G
|root||root||ALTER ROUTINE|G
|root||root|localhost|ALTER ROUTINE|G
|root||root||CREATE|G
|root||root|localhost|CREATE|G
|root||root||CREATE ROUTINE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root||CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE USER|G
|root||root|localhost|CREATE USER|G
|root||root||CREATE VIEW|G
|root||root|localhost|CREATE VIEW|G
|root||root||DELETE|G
|root||root|localhost|DELETE|G
|root||root||DROP|G
|root||root|localhost|DROP|G
|root||root||EVENT|G
|root||root|localhost|EVENT|G
|root||root||EXECUTE|G
|root||root|localhost|EXECUTE|G
|root||root||FILE|G
|root||root|localhost|FILE|G
|root||root||INDEX|G
|root||root|localhost|INDEX|G
|root||root||INSERT|G
|root||root|localhost|INSERT|G
|root||root||LOCK TABLES|G
|root||root|localhost|LOCK TABLES|G
|root||root||PROCESS|G
|root||root|localhost|PROCESS|G
|root||root||REFERENCES|G
|root||root|localhost|REFERENCES|G
|root||root||RELOAD|G
|root||root|localhost|RELOAD|G
|root||root||REPLICATION CLIENT|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION SLAVE|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root||SELECT|G
|root||root|localhost|SELECT|G
|root||root||SHOW DATABASES|G
|root||root|localhost|SHOW DATABASES|G
|root||root||SHOW VIEW|G
|root||root|localhost|SHOW VIEW|G
|root||root||SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root||SUPER|G
|root||root|localhost|SUPER|G
|root||root||TRIGGER|G
|root||root|localhost|TRIGGER|G
|root||root||UPDATE|G
|root||root|localhost|UPDATE|G
|root||root||grant option|G
|root||root|localhost|grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>5.7.26</ServerVersion>
    </root>
    <collation id="2" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="4" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="6" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="8" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="10" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="11" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="12" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="13" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="14" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="15" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="16" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="17" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="18" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="19" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="20" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="21" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="22" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="23" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="24" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="25" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="27" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="29" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="31" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="33" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="35" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="39" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="47" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="48" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="49" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="50" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="52" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="54" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="56" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="58" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="59" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="60" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="61" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="62" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="63" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="64" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="65" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="66" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="67" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="68" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="69" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="70" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="71" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="72" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="73" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="74" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="75" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="76" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="77" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="78" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="79" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="80" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="81" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="82" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="83" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="85" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="111" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="112" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="113" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="114" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="115" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="116" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="118" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="119" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="120" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="121" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="122" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="123" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="124" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="125" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="126" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="127" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="128" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="129" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="130" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="131" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="132" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="133" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="134" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="135" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="136" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="137" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="138" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="139" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="140" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="141" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="142" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="143" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="144" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="145" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="146" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="147" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="148" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="149" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="150" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="151" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="152" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="153" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="154" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="155" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="156" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="157" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="158" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="159" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="160" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="161" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="162" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="163" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="164" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="165" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="166" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="167" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="168" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="169" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="170" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="171" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="172" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="173" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="174" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="175" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="176" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="177" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="178" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="179" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="180" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="181" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="182" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="183" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="184" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="185" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="186" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="187" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="188" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="189" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="190" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="191" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="192" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="193" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="194" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="195" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="196" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="197" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="198" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="199" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="200" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="201" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="202" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="203" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="204" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="205" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="206" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="207" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="208" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="209" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="210" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="211" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="212" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="213" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="214" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="215" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="216" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="217" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="218" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="219" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="220" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="221" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="222" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="223" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <schema id="224" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="ai_dl350_com">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="backstage_cloud_">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="beijixing">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="228" parent="1" name="cdbycxjy_d">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="229" parent="1" name="chat">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="230" parent="1" name="cloud_log">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="231" parent="1" name="dadan2">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="232" parent="1" name="db2struct">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="233" parent="1" name="dgcms">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="234" parent="1" name="diguo">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="235" parent="1" name="dolphin">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="236" parent="1" name="ds_mall_none">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="237" parent="1" name="dyz">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="238" parent="1" name="fastadmin">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="239" parent="1" name="gpt">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="240" parent="1" name="guli">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="241" parent="1" name="hyperchain">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="242" parent="1" name="jinritemai">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="243" parent="1" name="jixiaotong">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="244" parent="1" name="mybatis_plus">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="245" parent="1" name="mysql">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="246" parent="1" name="new-article">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="247" parent="1" name="nft_url_dev_qgmz">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="248" parent="1" name="paifa">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="249" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="250" parent="1" name="qsxz">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="251" parent="1" name="qsxz-t">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="252" parent="1" name="secrecy">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="253" parent="1" name="shangxuebao">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="254" parent="1" name="shucang123">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="255" parent="1" name="sys">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="256" parent="1" name="test">
      <CollationName>utf8_unicode_ci</CollationName>
    </schema>
    <schema id="257" parent="1" name="test_response">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="258" parent="1" name="tianma">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="259" parent="1" name="tinode">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="260" parent="1" name="user_db">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="261" parent="1" name="zaolang">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="262" parent="1" name="zhaosheng_dl350_">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="263" parent="1" name="zhongzhan">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="264" parent="1" name="zhongzhan_imyun">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="265" parent="1" name="zhuzi-copyright">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="266" parent="1" name="zhuzi-site-zzjz">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="267" parent="1" name="zygj">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="268" parent="1" name="ins-dev">
      <IntrospectionTimestamp>2024-05-22.02:15:40</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2024-05-21.10:15:40</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="269" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <user id="270" parent="1" name="mysql.session">
      <Host>localhost</Host>
    </user>
    <user id="271" parent="1" name="mysql.sys">
      <Host>localhost</Host>
    </user>
    <user id="272" parent="1" name="root"/>
    <table id="273" parent="268" name="casbin_rule">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="274" parent="268" name="data_authorities">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="275" parent="268" name="exa_customers">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="276" parent="268" name="exa_file_chunks">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="277" parent="268" name="exa_file_upload_and_downloads">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="278" parent="268" name="exa_files">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="279" parent="268" name="foo1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="280" parent="268" name="hy_checkout">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="281" parent="268" name="hy_hangclear">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="282" parent="268" name="ins_act_bookdesk_conf">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="283" parent="268" name="ins_activity_content">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="284" parent="268" name="ins_gift_rule_detail">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="285" parent="268" name="ins_gift_rule_member">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="286" parent="268" name="ins_gift_rule_product">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="287" parent="268" name="ins_import_log">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="288" parent="268" name="ins_inventory">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="289" parent="268" name="ins_inventory_material_flow">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="290" parent="268" name="ins_inventory_product_flow">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="291" parent="268" name="ins_kitchen">
      <Comment>厨房</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="292" parent="268" name="ins_kitchen_order">
      <Comment>厨房订单</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="293" parent="268" name="ins_saler_store">
      <Comment>销售和店铺关联</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="294" parent="268" name="ins_service_fee">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="295" parent="268" name="ins_service_fee_applicable">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="296" parent="268" name="ins_service_fee_category">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="297" parent="268" name="ins_service_fee_desk">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="298" parent="268" name="ins_service_fee_desk_current">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="299" parent="268" name="ins_service_fee_exclusion">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="300" parent="268" name="ins_service_fee_product">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="301" parent="268" name="ins_service_fee_time">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="302" parent="268" name="ins_warehouse_inout_purchase">
      <Comment>出入库采购</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="303" parent="268" name="ins_warehouse_inout_receipt">
      <Comment>采购收据,出库单记录</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="304" parent="268" name="ins_warehouse_inout_receive">
      <Comment>领取出入库关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="305" parent="268" name="sys_dictionary_details">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="306" parent="268" name="sys_operation_records">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="307" parent="268" name="sys_operation_records1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="308" parent="268" name="sys_users">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="309" parent="273" name="id">
      <AutoIncrement>1010</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="310" parent="273" name="ptype">
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="311" parent="273" name="v0">
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="312" parent="273" name="v1">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="313" parent="273" name="v2">
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="314" parent="273" name="v3">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="315" parent="273" name="v4">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="316" parent="273" name="v5">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="317" parent="273" name="v6">
      <DasType>varchar(25)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="318" parent="273" name="v7">
      <DasType>varchar(25)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="319" parent="273" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="320" parent="273" name="idx_casbin_rule">
      <ColNames>ptype
v0
v1
v2
v3
v4
v5
v6
v7</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="321" parent="273" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="322" parent="273" name="idx_casbin_rule">
      <UnderlyingIndexName>idx_casbin_rule</UnderlyingIndexName>
    </key>
    <column id="323" parent="274" name="authority_id">
      <Comment>角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="324" parent="274" name="authority_type">
      <Comment>角色权限标记</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="325" parent="275" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="326" parent="275" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="327" parent="275" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="328" parent="275" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="329" parent="275" name="customer_name">
      <Comment>客户名</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="330" parent="275" name="customer_phone_data">
      <Comment>客户手机号</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="331" parent="275" name="sys_user_id">
      <Comment>管理ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="332" parent="275" name="sys_user_authority_id">
      <Comment>管理角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="333" parent="275" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="334" parent="275" name="idx_exa_customers_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="335" parent="275" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="336" parent="276" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="337" parent="276" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="338" parent="276" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="339" parent="276" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="340" parent="276" name="exa_file_id">
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="341" parent="276" name="file_chunk_number">
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="342" parent="276" name="file_chunk_path">
      <DasType>varchar(191)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="343" parent="276" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="344" parent="276" name="idx_exa_file_chunks_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="345" parent="276" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="346" parent="277" name="id">
      <AutoIncrement>407</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="347" parent="277" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="348" parent="277" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="349" parent="277" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="350" parent="277" name="name">
      <Comment>文件名</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="351" parent="277" name="url">
      <Comment>文件地址</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="352" parent="277" name="tag">
      <Comment>文件标签</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="353" parent="277" name="key">
      <Comment>编号</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="354" parent="277" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="355" parent="277" name="idx_exa_file_upload_and_downloads_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="356" parent="277" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="357" parent="278" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="358" parent="278" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="359" parent="278" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="360" parent="278" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="361" parent="278" name="file_name">
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="362" parent="278" name="file_md5">
      <DasType>varchar(191)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="363" parent="278" name="file_path">
      <DasType>varchar(191)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="364" parent="278" name="chunk_total">
      <DasType>bigint(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="365" parent="278" name="is_finish">
      <DasType>tinyint(1)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="366" parent="278" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="367" parent="278" name="idx_exa_files_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="368" parent="278" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="369" parent="279" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="370" parent="279" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="371" parent="279" name="age">
      <Comment>年龄</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="372" parent="279" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="373" parent="279" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="374" parent="280" name="id">
      <AutoIncrement>243968</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="375" parent="280" name="store_id">
      <Comment>分店</Comment>
      <DasType>varchar(16)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="376" parent="280" name="check_out_id">
      <Comment>账单id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="377" parent="280" name="check_out_voucher">
      <Comment>账单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="378" parent="280" name="voucher_type">
      <Comment>账单类型</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="379" parent="280" name="room_id">
      <Comment>房间id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="380" parent="280" name="room_name">
      <Comment>房间名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="381" parent="280" name="charge_total">
      <Comment>应收金额</Comment>
      <DasType>decimal(20,4 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="382" parent="280" name="fact_accept_charge">
      <Comment>实收金额</Comment>
      <DasType>decimal(20,4 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="383" parent="280" name="check_out_date_time">
      <Comment>账单时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="384" parent="280" name="fact_check_out_date">
      <Comment>实际归属日期</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="385" parent="280" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="386" parent="280" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="387" parent="280" name="s">
      <ColNames>store_id
check_out_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="388" parent="280" name="oid">
      <ColNames>check_out_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="389" parent="280" name="cd">
      <ColNames>check_out_date_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="390" parent="280" name="fcd">
      <ColNames>fact_check_out_date</ColNames>
      <Type>btree</Type>
    </index>
    <key id="391" parent="280" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="392" parent="280" name="s">
      <UnderlyingIndexName>s</UnderlyingIndexName>
    </key>
    <column id="393" parent="281" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="394" parent="281" name="hang_detail_id">
      <Comment>销账记录id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="395" parent="281" name="store_id">
      <Comment>所属店铺</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="396" parent="281" name="check_out_id">
      <Comment>关联订单号</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="397" parent="281" name="check_out_voucher">
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="398" parent="281" name="hang_total">
      <Comment>挂账总金额</Comment>
      <DasType>decimal(20,4 digit)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="399" parent="281" name="clear_charge">
      <Comment>销账金额</Comment>
      <DasType>decimal(20,4 digit)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="400" parent="281" name="left_charge">
      <Comment>剩余金额</Comment>
      <DasType>decimal(20,4 digit)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="401" parent="281" name="hang_at">
      <Comment>销账日期</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="402" parent="281" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="403" parent="281" name="hd">
      <ColNames>hang_detail_id
store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="404" parent="281" name="s">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="405" parent="281" name="c">
      <ColNames>check_out_id
store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="406" parent="281" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="407" parent="281" name="hd">
      <UnderlyingIndexName>hd</UnderlyingIndexName>
    </key>
    <column id="408" parent="282" name="id">
      <AutoIncrement>728</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="409" parent="282" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="410" parent="282" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="411" parent="282" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="412" parent="282" name="created_by">
      <Comment>创建人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="413" parent="282" name="updated_by">
      <Comment>更新人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="414" parent="282" name="deleted_by">
      <Comment>删除人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="415" parent="282" name="store_id">
      <Comment>门店ID</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="416" parent="282" name="desk_id">
      <Comment>卡座ID</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="417" parent="282" name="book_date">
      <Comment>预订日期</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="418" parent="282" name="begin_time">
      <Comment>预订开始时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="419" parent="282" name="end_time">
      <Comment>预订结束时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="420" parent="282" name="status">
      <Comment>状态 1启用 2禁用（默认）</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="421" parent="282" name="remark">
      <Comment>活动描述</Comment>
      <DasType>varchar(512)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="422" parent="282" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>15</Position>
    </column>
    <foreign-key id="423" parent="282" name="fk_ins_act_bookdesk_conf_store">
      <ColNames>store_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>ins_store</RefTableName>
    </foreign-key>
    <foreign-key id="424" parent="282" name="fk_ins_act_bookdesk_conf_desk">
      <ColNames>desk_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>ins_desk</RefTableName>
    </foreign-key>
    <index id="425" parent="282" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="426" parent="282" name="d">
      <ColNames>desk_id
store_id
book_date</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="427" parent="282" name="idx_ins_act_bookdesk_conf_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="428" parent="282" name="idx_ins_act_bookdesk_conf_store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="429" parent="282" name="idx_ins_act_bookdesk_conf_book_date">
      <ColNames>book_date</ColNames>
      <Type>btree</Type>
    </index>
    <key id="430" parent="282" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="431" parent="282" name="d">
      <UnderlyingIndexName>d</UnderlyingIndexName>
    </key>
    <column id="432" parent="283" name="id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="433" parent="283" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="434" parent="283" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="435" parent="283" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="436" parent="283" name="content">
      <Comment>文案</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="437" parent="283" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="438" parent="283" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="439" parent="283" name="idx_ins_activity_content_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="440" parent="283" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="441" parent="284" name="id">
      <AutoIncrement>225</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="442" parent="284" name="rule_id">
      <Comment>规则ID</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="443" parent="284" name="date_type">
      <Comment>规则周期：1月，2周，3天，4每单赠送限额</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="444" parent="284" name="price_type">
      <Comment>赠送定价方式： 1 固定价格 2 销售价 3 不限额度 4 商品限额</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="445" parent="284" name="amount">
      <Comment>数值</Comment>
      <DasType>float|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="446" parent="284" name="percent">
      <Comment>百分比</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="447" parent="284" name="limit">
      <Comment>上限金额</Comment>
      <DasType>float|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="448" parent="284" name="cycle">
      <Comment>循环累计</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="449" parent="284" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="450" parent="284" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="451" parent="284" name="product_price">
      <Comment>商品限额</Comment>
      <DasType>float|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="452" parent="284" name="base_quota">
      <Comment>基础额度</Comment>
      <DasType>float|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="453" parent="284" name="half_month_quota">
      <Comment>是否保留半月额度</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="454" parent="284" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="455" parent="284" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="456" parent="285" name="id">
      <AutoIncrement>495</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="457" parent="285" name="rule_id">
      <Comment>活动ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="458" parent="285" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="459" parent="285" name="created_at">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="460" parent="285" name="updated_at">
      <DasType>datetime|0s</DasType>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <index id="461" parent="285" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="462" parent="285" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="463" parent="286" name="id">
      <AutoIncrement>1522</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="464" parent="286" name="rule_id">
      <Comment>活动ID</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="465" parent="286" name="product_id">
      <Comment>商品ID</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="466" parent="286" name="price_type">
      <Comment>赠送定价方式： 1  固定价格 2 销售价</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="467" parent="286" name="value">
      <Comment>赠送价格</Comment>
      <DasType>float|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="468" parent="286" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="469" parent="286" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="470" parent="286" name="is_exclude">
      <Comment>是否是排除</Comment>
      <DasType>smallint(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="471" parent="286" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="472" parent="286" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="473" parent="287" name="id">
      <AutoIncrement>18</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="474" parent="287" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="475" parent="287" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="476" parent="287" name="filename">
      <Comment>文件名</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="477" parent="287" name="success_num">
      <Comment>成功次数</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="478" parent="287" name="fail_num">
      <Comment>失败次数</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="479" parent="287" name="data">
      <Comment>导入内容及导入结果</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="480" parent="287" name="export_link">
      <Comment>导出地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="481" parent="287" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="482" parent="287" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="483" parent="288" name="warehouse_id">
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="484" parent="288" name="product_id">
      <Comment>商品id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="485" parent="288" name="leaved_amount">
      <Comment>剩余可购买数量</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="486" parent="289" name="id">
      <AutoIncrement>1006</AutoIncrement>
      <Comment>主键id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="487" parent="289" name="warehouse_id">
      <Comment>出品仓库ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="488" parent="289" name="material_id">
      <Comment>原料id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="489" parent="289" name="order_detail_id">
      <Comment>订单明细id</Comment>
      <DasType>mediumtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="490" parent="289" name="quantity_trade">
      <Comment>本次购买扣减的数量</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="491" parent="289" name="created_at">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="492" parent="289" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <column id="493" parent="289" name="business_day">
      <Comment>营业日</Comment>
      <DasType>date|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="494" parent="289" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="495" parent="289" name="warehouse_id">
      <ColNames>warehouse_id
material_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="496" parent="289" name="bd">
      <ColNames>business_day</ColNames>
      <Type>btree</Type>
    </index>
    <key id="497" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="498" parent="290" name="id">
      <AutoIncrement>1388</AutoIncrement>
      <Comment>主键 id</Comment>
      <DasType>bigint(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="499" parent="290" name="warehouse_id">
      <Comment>出品仓库ID</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="500" parent="290" name="product_id">
      <Comment>商品规格 id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="501" parent="290" name="order_detail_id">
      <Comment>订单明细 id</Comment>
      <DasType>mediumtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="502" parent="290" name="quantity_trade">
      <Comment>本次购买扣减的数量</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="503" parent="290" name="status">
      <Comment>&apos;1待出品，2已出品&apos;</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="504" parent="290" name="created_at">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="505" parent="290" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="506" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="507" parent="290" name="warehouse_id">
      <ColNames>warehouse_id
product_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="508" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="509" parent="291" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>mediumint(8) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <index id="510" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="511" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="512" parent="292" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>订单ID</Comment>
      <DasType>mediumint(8) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <index id="513" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="514" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="515" parent="293" name="id">
      <AutoIncrement>467</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="516" parent="293" name="store_id">
      <Comment>门店id</Comment>
      <DasType>smallint(5) unsigned|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="517" parent="293" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="518" parent="293" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="519" parent="293" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="520" parent="293" name="user_id">
      <Comment>用户id</Comment>
      <DasType>smallint(5) unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="521" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="522" parent="293" name="idx_ins_saler_store_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="523" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="524" parent="294" name="id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="525" parent="294" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="526" parent="294" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="527" parent="294" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="528" parent="294" name="created_by">
      <Comment>创建人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="529" parent="294" name="updated_by">
      <Comment>更新人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="530" parent="294" name="deleted_by">
      <Comment>删除人</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="531" parent="294" name="store_id">
      <Comment>门店ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="532" parent="294" name="title">
      <Comment>促销标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="533" parent="294" name="status">
      <Comment>状态 1:启用 2:禁用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="534" parent="294" name="service_fee_type">
      <Comment>促销类型 1:整单促销 2:分类促销 3:商品促销</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="535" parent="294" name="all_day">
      <Comment>全天促销 1:是 2:否</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="536" parent="294" name="start_date">
      <Comment>开始日期</Comment>
      <DasType>date|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="537" parent="294" name="end_date">
      <Comment>结束日期</Comment>
      <DasType>date|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="538" parent="294" name="discount_rate">
      <Comment>整单折扣率</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="539" parent="294" name="applicable_to">
      <Comment>适用端口，如收银端，服务员端</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="540" parent="294" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="541" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="542" parent="294" name="idx_ins_service_fee_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="543" parent="294" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="544" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="545" parent="295" name="id">
      <AutoIncrement>49</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="546" parent="295" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="547" parent="295" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="548" parent="295" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="549" parent="295" name="service_fee_id">
      <Comment>关联的促销活动ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="550" parent="295" name="applicable">
      <Comment>适用端口，如收银端，服务员端</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="551" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="552" parent="295" name="idx_ins_service_fee_applicable_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="553" parent="295" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="554" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="555" parent="296" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="556" parent="296" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="557" parent="296" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="558" parent="296" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="559" parent="296" name="service_fee_id">
      <Comment>关联的促销活动ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="560" parent="296" name="category_id">
      <Comment>促销适用的分类ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="561" parent="296" name="discount_rate">
      <Comment>分类折扣率</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="562" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="563" parent="296" name="idx_ins_service_fee_category_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="564" parent="296" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="565" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="566" parent="297" name="id">
      <AutoIncrement>916</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="567" parent="297" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="568" parent="297" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="569" parent="297" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="570" parent="297" name="service_fee_id">
      <Comment>关联的服务费ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="571" parent="297" name="desk_id">
      <Comment>服务费适用的桌台ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="572" parent="297" name="discount_rate">
      <Comment>折扣率</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="573" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="574" parent="297" name="idx_ins_service_fee_desk_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="575" parent="297" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="576" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="577" parent="298" name="id">
      <AutoIncrement>31</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="578" parent="298" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="579" parent="298" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="580" parent="298" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="581" parent="298" name="open_desk_id">
      <Comment>开台ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="582" parent="298" name="store_id">
      <Comment>门店ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="583" parent="298" name="service_fee_id">
      <Comment>关联的服务费ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="584" parent="298" name="service_fee">
      <Comment>服务费</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="585" parent="298" name="service_fee_rate">
      <Comment>服务费率</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="586" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="587" parent="298" name="idx_ins_service_fee_desk_current_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="588" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="589" parent="299" name="id">
      <AutoIncrement>52</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="590" parent="299" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="591" parent="299" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="592" parent="299" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="593" parent="299" name="service_fee_id">
      <Comment>关联的促销活动ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="594" parent="299" name="exclusion_type">
      <Comment>排除类型，商品或分类</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="595" parent="299" name="target_id">
      <Comment>排除目标的ID，可以是商品ID或分类ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="596" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="597" parent="299" name="idx_ins_service_fee_exclusion_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="598" parent="299" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="599" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="600" parent="300" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="601" parent="300" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="602" parent="300" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="603" parent="300" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="604" parent="300" name="service_fee_id">
      <Comment>关联的促销活动ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="605" parent="300" name="product_id">
      <Comment>促销适用的商品ID</Comment>
      <DasType>int(11)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="606" parent="300" name="discount_rate">
      <Comment>分类折扣率</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="607" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="608" parent="300" name="idx_ins_service_fee_product_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="609" parent="300" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="610" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="611" parent="301" name="id">
      <AutoIncrement>133</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="612" parent="301" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="613" parent="301" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="614" parent="301" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="615" parent="301" name="service_fee_id">
      <Comment>关联的促销活动ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="616" parent="301" name="week_day">
      <Comment>适用星期，1代表星期一，7代表星期日</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="617" parent="301" name="time_ranges">
      <Comment>JSON格式的时间范围数组</Comment>
      <DasType>json|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="618" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="619" parent="301" name="idx_ins_service_fee_time_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="620" parent="301" name="sfid">
      <ColNames>service_fee_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="621" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="622" parent="302" name="id">
      <AutoIncrement>139</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="623" parent="302" name="purchase_sn">
      <Comment>领取单号</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="624" parent="302" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="625" parent="302" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="626" parent="302" name="status">
      <Comment>状态</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="627" parent="302" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="628" parent="302" name="supplier_id">
      <Comment>源仓库 or 采购供应商id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="629" parent="302" name="to_warehouse_id">
      <Comment>目标仓库</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="630" parent="302" name="purchase_time">
      <Comment>采购时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="631" parent="302" name="out_bound_operator">
      <Comment>出库人id 无实际作用</Comment>
      <DasType>int(11)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="632" parent="302" name="purchase_total">
      <Comment>采购总额</Comment>
      <DasType>decimal(10)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="633" parent="302" name="purchasers_id">
      <Comment>采购人</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="634" parent="302" name="warehouse_time">
      <Comment>入库时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="635" parent="302" name="deleted_at">
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="636" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="637" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="638" parent="303" name="id">
      <AutoIncrement>51</AutoIncrement>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="639" parent="303" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="640" parent="303" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="641" parent="303" name="name">
      <Comment>单据名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="642" parent="303" name="operation_time">
      <Comment>操作时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="643" parent="303" name="purchase_id">
      <Comment>采购单id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="644" parent="303" name="operator_id">
      <Comment>操作人id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="645" parent="303" name="details">
      <Comment>操作详情</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="646" parent="303" name="inout_id">
      <Comment>采购单id</Comment>
      <DasType>int(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="647" parent="303" name="receipt_type">
      <Comment>单据类型</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <index id="648" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="649" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="650" parent="304" name="id">
      <AutoIncrement>18</AutoIncrement>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="651" parent="304" name="receive_sn">
      <Comment>领取单号</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="652" parent="304" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP(3)</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="653" parent="304" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="654" parent="304" name="receiver">
      <Comment>领取人</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="655" parent="304" name="team_id">
      <Comment>部门id</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="656" parent="304" name="receive_time">
      <Comment>领取时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="657" parent="304" name="status">
      <Comment>状态</Comment>
      <DasType>tinyint(4)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="658" parent="304" name="inout_id">
      <Comment>出入库id</Comment>
      <DasType>int(10) unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="659" parent="304" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="660" parent="304" name="deleted_at">
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="661" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="662" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="663" parent="305" name="id">
      <AutoIncrement>162</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="664" parent="305" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="665" parent="305" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="666" parent="305" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="667" parent="305" name="label">
      <Comment>展示值</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="668" parent="305" name="value">
      <Comment>字典值</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="669" parent="305" name="status">
      <Comment>启用状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="670" parent="305" name="sort">
      <Comment>排序标记</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="671" parent="305" name="sys_dictionary_id">
      <Comment>关联标记</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="672" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="673" parent="305" name="idx_sys_dictionary_details_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="674" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="675" parent="306" name="id">
      <AutoIncrement>1568939</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="676" parent="306" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="677" parent="306" name="ip">
      <Comment>请求ip</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="678" parent="306" name="method">
      <Comment>请求方法</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="679" parent="306" name="path">
      <Comment>请求路径</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="680" parent="306" name="status">
      <Comment>请求状态</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="681" parent="306" name="latency">
      <Comment>延迟</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="682" parent="306" name="agent">
      <Comment>代理</Comment>
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="683" parent="306" name="error_message">
      <Comment>错误信息</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="684" parent="306" name="body">
      <Comment>请求Body</Comment>
      <DasType>longtext|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="685" parent="306" name="resp">
      <Comment>响应Body</Comment>
      <DasType>longtext|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="686" parent="306" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="687" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="688" parent="306" name="idx_sys_operation_records_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="689" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="690" parent="307" name="id">
      <AutoIncrement>1415323</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="691" parent="307" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="692" parent="307" name="ip">
      <Comment>请求ip</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="693" parent="307" name="method">
      <Comment>请求方法</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="694" parent="307" name="path">
      <Comment>请求路径</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="695" parent="307" name="status">
      <Comment>请求状态</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="696" parent="307" name="latency">
      <Comment>延迟</Comment>
      <DasType>bigint(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="697" parent="307" name="agent">
      <Comment>代理</Comment>
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="698" parent="307" name="error_message">
      <Comment>错误信息</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="699" parent="307" name="body">
      <Comment>请求Body</Comment>
      <DasType>text|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="700" parent="307" name="resp">
      <Comment>响应Body</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="701" parent="307" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="702" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="703" parent="307" name="idx_sys_operation_records_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="704" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="705" parent="308" name="id">
      <AutoIncrement>50</AutoIncrement>
      <DasType>bigint(20) unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="706" parent="308" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="707" parent="308" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="708" parent="308" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="709" parent="308" name="uuid">
      <Comment>用户UUID</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="710" parent="308" name="username">
      <Comment>用户登录名</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="711" parent="308" name="password">
      <Comment>用户登录密码</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="712" parent="308" name="nick_name">
      <Comment>用户昵称</Comment>
      <DasType>varchar(191)|0s</DasType>
      <DefaultExpression>&apos;系统用户&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="713" parent="308" name="side_mode">
      <Comment>用户侧边主题</Comment>
      <DasType>varchar(191)|0s</DasType>
      <DefaultExpression>&apos;light&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="714" parent="308" name="header_img">
      <Comment>用户头像</Comment>
      <DasType>varchar(191)|0s</DasType>
      <DefaultExpression>&apos;https://qmplusimg.henrongyi.top/gva_header.jpg&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="715" parent="308" name="base_color">
      <Comment>基础颜色</Comment>
      <DasType>varchar(191)|0s</DasType>
      <DefaultExpression>&apos;#fff&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="716" parent="308" name="active_color">
      <Comment>活跃颜色</Comment>
      <DasType>varchar(191)|0s</DasType>
      <DefaultExpression>&apos;#1890ff&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="717" parent="308" name="authority_id">
      <Comment>用户角色ID</Comment>
      <DasType>bigint(20) unsigned|0s</DasType>
      <DefaultExpression>888</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="718" parent="308" name="phone">
      <Comment>用户手机号</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="719" parent="308" name="email">
      <Comment>用户邮箱</Comment>
      <DasType>varchar(191)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="720" parent="308" name="enable">
      <Comment>用户是否被冻结 1正常 2冻结</Comment>
      <DasType>bigint(20)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="721" parent="308" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="722" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="723" parent="308" name="idx_sys_users_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="724" parent="308" name="idx_sys_users_uuid">
      <ColNames>uuid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="725" parent="308" name="idx_sys_users_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
    </index>
    <key id="726" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>