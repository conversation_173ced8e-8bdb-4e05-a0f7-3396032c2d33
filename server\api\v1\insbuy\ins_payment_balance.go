package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ManualBalanceChange 手动增减余额
// @Tags InsPayment
// @Summary 手动增减余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ManualBalanceChangeReq true "手动增减余额"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动增减余额成功"}"
// @Router /insPayment/manualBalance [post]
func (InsPaymentApi *InsPaymentApi) ManualBalanceChange(c *gin.Context) {
	var req insbuyReq.ManualBalanceChangeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.ManualBalanceChange(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// ActivityBalanceChange 活动增减余额
// @Tags InsPayment
// @Summary 活动增减余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ActivityBalanceChangeReq true "活动增减余额"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"活动增减余额成功"}"
// @Router /insPayment/activityBalance [post]
func (InsPaymentApi *InsPaymentApi) ActivityBalanceChange(c *gin.Context) {
	var req insbuyReq.ActivityBalanceChangeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insPaymentService.ActivityBalanceChange(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.Ok(c)
	}
}

func (InsPaymentApi *InsPaymentApi) InitBalanceRemark(c *gin.Context) {
	var req insbuyReq.InitBalanceRemarkReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	res, err := insPaymentService.InitBalanceRemark(req)
	response.ResultErr(res, err, c)
}
