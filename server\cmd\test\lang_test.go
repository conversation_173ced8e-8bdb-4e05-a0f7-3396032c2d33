package test

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore/lang"
	"testing"
)

func TestTranslator(t *testing.T) {
	translator, err := lang.NewTranslator(lang.TlAliYun)
	if err != nil {
		return
	}
	res, err := translator.Translate(context.Background(), query.Q, lang.TranslatorParams{})
	if err != nil {
		return
	}
	fmt.Println(res)
}

func TestBatchTranslate(t *testing.T) {
	translator, err := lang.NewTranslator(lang.TlAliYun)
	if err != nil {
		return
	}
	m := make(map[string]string, 0)
	m["1"] = "你哈"
	m["2"] = "ins新乐园"
	m["3"] = "充电宝抵扣"
	m["4"] = "【INS】测试长期票，不要删除谢谢"
	marshal, _ := json.Marshal(m)
	res, err := translator.BatchTranslate(context.Background(), query.Q, lang.TranslatorParams{
		SourceLanguage: "zh",
		TargetLanguage: "en",
		ApiType:        "translate_standard",
		SourceText:     string(marshal),
	})
	if err != nil {
		t.Errorf("err:%v\n", err)
		return
	}
	fmt.Println(res)
}
