package system

import (
	pluginService "github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
)

type ApiGroup struct {
	DBApi
	JwtApi
	BaseApi
	SystemApi
	CasbinApi
	AutoCodeApi
	SystemApiApi
	AuthorityApi
	DictionaryApi
	AuthorityMenuApi
	OperationRecordApi
	AutoCodeHistoryApi
	DictionaryDetailApi
	AuthorityBtnApi
	ChatGptApi
	Authority2Api
}

var (
	apiService              = service.ServiceGroupApp.SystemServiceGroup.ApiService
	jwtService              = service.ServiceGroupApp.SystemServiceGroup.JwtService
	menuService             = service.ServiceGroupApp.SystemServiceGroup.MenuService
	userService             = service.ServiceGroupApp.SystemServiceGroup.UserService
	initDBService           = service.ServiceGroupApp.SystemServiceGroup.InitDBService
	casbinService           = service.ServiceGroupApp.SystemServiceGroup.CasbinService
	autoCodeService         = service.ServiceGroupApp.SystemServiceGroup.AutoCodeService
	baseMenuService         = service.ServiceGroupApp.SystemServiceGroup.BaseMenuService
	authorityService        = service.ServiceGroupApp.SystemServiceGroup.AuthorityService
	authority2Service       = service.ServiceGroupApp.SystemServiceGroup.Authority2
	dictionaryService       = service.ServiceGroupApp.SystemServiceGroup.DictionaryService
	systemConfigService     = service.ServiceGroupApp.SystemServiceGroup.SystemConfigService
	operationRecordService  = service.ServiceGroupApp.SystemServiceGroup.OperationRecordService
	autoCodeHistoryService  = service.ServiceGroupApp.SystemServiceGroup.AutoCodeHistoryService
	dictionaryDetailService = service.ServiceGroupApp.SystemServiceGroup.DictionaryDetailService
	authorityBtnService     = service.ServiceGroupApp.SystemServiceGroup.AuthorityBtnService
	chatGptService          = service.ServiceGroupApp.SystemServiceGroup.ChatGptService
)

// 外部模块
var (
	insUserService         = service.ServiceGroupApp.InsBuyServiceGroup.InsCurrentUserService
	insSalerStoreService   = service.ServiceGroupApp.InsBuyServiceGroup.InsSalerStoreService
	insStoreService        = service.ServiceGroupApp.InsBuyServiceGroup.InsStoreService
	insSalerCodeService    = service.ServiceGroupApp.InsBuyServiceGroup.InsSalerCodeService
	insUserRegisterService = service.ServiceGroupApp.InsBuyServiceGroup.InsUserRegisterService
	insReportService       = service.ServiceGroupApp.InsBuyServiceGroup.Report
	insImportService       = service.ServiceGroupApp.InsBuyServiceGroup.InsImportService
)

// 插件模块
var (
	organizationService = pluginService.ServiceGroupApp.OrganizationService
)
