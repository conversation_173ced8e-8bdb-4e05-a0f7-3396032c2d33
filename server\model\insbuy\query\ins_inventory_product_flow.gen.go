// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsInventoryProductFlow(db *gorm.DB, opts ...gen.DOOption) insInventoryProductFlow {
	_insInventoryProductFlow := insInventoryProductFlow{}

	_insInventoryProductFlow.insInventoryProductFlowDo.UseDB(db, opts...)
	_insInventoryProductFlow.insInventoryProductFlowDo.UseModel(&insbuy.InsInventoryProductFlow{})

	tableName := _insInventoryProductFlow.insInventoryProductFlowDo.TableName()
	_insInventoryProductFlow.ALL = field.NewAsterisk(tableName)
	_insInventoryProductFlow.ID = field.NewUint(tableName, "id")
	_insInventoryProductFlow.CreatedAt = field.NewTime(tableName, "created_at")
	_insInventoryProductFlow.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insInventoryProductFlow.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insInventoryProductFlow.ProductId = field.NewInt(tableName, "product_id")
	_insInventoryProductFlow.OrderDetailId = field.NewUint(tableName, "order_detail_id")
	_insInventoryProductFlow.QuantityTrade = field.NewInt(tableName, "quantity_trade")
	_insInventoryProductFlow.Status = field.NewUint(tableName, "status")

	_insInventoryProductFlow.fillFieldMap()

	return _insInventoryProductFlow
}

type insInventoryProductFlow struct {
	insInventoryProductFlowDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	WarehouseId   field.Int
	ProductId     field.Int
	OrderDetailId field.Uint
	QuantityTrade field.Int
	Status        field.Uint

	fieldMap map[string]field.Expr
}

func (i insInventoryProductFlow) Table(newTableName string) *insInventoryProductFlow {
	i.insInventoryProductFlowDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insInventoryProductFlow) As(alias string) *insInventoryProductFlow {
	i.insInventoryProductFlowDo.DO = *(i.insInventoryProductFlowDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insInventoryProductFlow) updateTableName(table string) *insInventoryProductFlow {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.ProductId = field.NewInt(table, "product_id")
	i.OrderDetailId = field.NewUint(table, "order_detail_id")
	i.QuantityTrade = field.NewInt(table, "quantity_trade")
	i.Status = field.NewUint(table, "status")

	i.fillFieldMap()

	return i
}

func (i *insInventoryProductFlow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insInventoryProductFlow) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["order_detail_id"] = i.OrderDetailId
	i.fieldMap["quantity_trade"] = i.QuantityTrade
	i.fieldMap["status"] = i.Status
}

func (i insInventoryProductFlow) clone(db *gorm.DB) insInventoryProductFlow {
	i.insInventoryProductFlowDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insInventoryProductFlow) replaceDB(db *gorm.DB) insInventoryProductFlow {
	i.insInventoryProductFlowDo.ReplaceDB(db)
	return i
}

type insInventoryProductFlowDo struct{ gen.DO }

func (i insInventoryProductFlowDo) Debug() *insInventoryProductFlowDo {
	return i.withDO(i.DO.Debug())
}

func (i insInventoryProductFlowDo) WithContext(ctx context.Context) *insInventoryProductFlowDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insInventoryProductFlowDo) ReadDB() *insInventoryProductFlowDo {
	return i.Clauses(dbresolver.Read)
}

func (i insInventoryProductFlowDo) WriteDB() *insInventoryProductFlowDo {
	return i.Clauses(dbresolver.Write)
}

func (i insInventoryProductFlowDo) Session(config *gorm.Session) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Session(config))
}

func (i insInventoryProductFlowDo) Clauses(conds ...clause.Expression) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insInventoryProductFlowDo) Returning(value interface{}, columns ...string) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insInventoryProductFlowDo) Not(conds ...gen.Condition) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insInventoryProductFlowDo) Or(conds ...gen.Condition) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insInventoryProductFlowDo) Select(conds ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insInventoryProductFlowDo) Where(conds ...gen.Condition) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insInventoryProductFlowDo) Order(conds ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insInventoryProductFlowDo) Distinct(cols ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insInventoryProductFlowDo) Omit(cols ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insInventoryProductFlowDo) Join(table schema.Tabler, on ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insInventoryProductFlowDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insInventoryProductFlowDo) RightJoin(table schema.Tabler, on ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insInventoryProductFlowDo) Group(cols ...field.Expr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insInventoryProductFlowDo) Having(conds ...gen.Condition) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insInventoryProductFlowDo) Limit(limit int) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insInventoryProductFlowDo) Offset(offset int) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insInventoryProductFlowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insInventoryProductFlowDo) Unscoped() *insInventoryProductFlowDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insInventoryProductFlowDo) Create(values ...*insbuy.InsInventoryProductFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insInventoryProductFlowDo) CreateInBatches(values []*insbuy.InsInventoryProductFlow, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insInventoryProductFlowDo) Save(values ...*insbuy.InsInventoryProductFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insInventoryProductFlowDo) First() (*insbuy.InsInventoryProductFlow, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryProductFlow), nil
	}
}

func (i insInventoryProductFlowDo) Take() (*insbuy.InsInventoryProductFlow, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryProductFlow), nil
	}
}

func (i insInventoryProductFlowDo) Last() (*insbuy.InsInventoryProductFlow, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryProductFlow), nil
	}
}

func (i insInventoryProductFlowDo) Find() ([]*insbuy.InsInventoryProductFlow, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsInventoryProductFlow), err
}

func (i insInventoryProductFlowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsInventoryProductFlow, err error) {
	buf := make([]*insbuy.InsInventoryProductFlow, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insInventoryProductFlowDo) FindInBatches(result *[]*insbuy.InsInventoryProductFlow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insInventoryProductFlowDo) Attrs(attrs ...field.AssignExpr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insInventoryProductFlowDo) Assign(attrs ...field.AssignExpr) *insInventoryProductFlowDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insInventoryProductFlowDo) Joins(fields ...field.RelationField) *insInventoryProductFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insInventoryProductFlowDo) Preload(fields ...field.RelationField) *insInventoryProductFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insInventoryProductFlowDo) FirstOrInit() (*insbuy.InsInventoryProductFlow, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryProductFlow), nil
	}
}

func (i insInventoryProductFlowDo) FirstOrCreate() (*insbuy.InsInventoryProductFlow, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryProductFlow), nil
	}
}

func (i insInventoryProductFlowDo) FindByPage(offset int, limit int) (result []*insbuy.InsInventoryProductFlow, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insInventoryProductFlowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insInventoryProductFlowDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insInventoryProductFlowDo) Delete(models ...*insbuy.InsInventoryProductFlow) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insInventoryProductFlowDo) withDO(do gen.Dao) *insInventoryProductFlowDo {
	i.DO = *do.(*gen.DO)
	return i
}
