package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/apikit"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	systemRes "github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
)

type Authority2Api struct{}

// CreateRule
// @Tags      Authority2
// @Summary   创建授权规则
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body  systemReq.Authority2RuleReq  true  "权限项"
// @Success   200   {object}  response.Response{data=systemRes.SysAuthorityResponse,msg=string}  "创建规则"
// @Router    /authority2/createRule [post]
func (a *Authority2Api) CreateRule(c *gin.Context) {
	var req systemReq.Authority2RuleReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if req.ID > 0 {
		response.FailWithMessage("ID无效", c)
		return
	}
	var resp *systemRes.Authority2RuleResp
	resp, err := authority2Service.CommitRule(req)
	response.ResultErr(resp, err, c)
}

// DeleteRule
// @Tags      Authority2
// @Summary   删除权限规则
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body     systemReq.Authority2RuleDelReq            true  "删除权限规则"
// @Success   200   {object}  response.Response{data=systemRes.Authority2RuleDelResp,msg=string}  "删除权限规则"
// @Router    /authority2/deleteRule [post]
func (a *Authority2Api) DeleteRule(c *gin.Context) {
	var req systemReq.Authority2RuleDelReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	var resp *systemRes.Authority2RuleDelResp
	resp, err := authority2Service.DeleteRule(req)
	response.ResultErr(resp, err, c)
}

// UpdateRule
// @Tags      Authority2
// @Summary   更新权限规则
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2RuleReq true  "更新规则"
// @Success   200   {object}  response.Response{data=systemRes.Authority2RuleResp,msg=string}  "更新规则"
// @Router    /authority2/updateRule [post]
func (a *Authority2Api) UpdateRule(c *gin.Context) {
	var req systemReq.Authority2RuleReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if req.ID == 0 {
		response.FailWithMessage("ID不能为空", c)
		return
	}
	var resp *systemRes.Authority2RuleResp
	resp, err := authority2Service.CommitRule(req)
	response.ResultErr(resp, err, c)
}

// GetRuleList 分页查询
// @Tags      Authority2
// @Summary   分页查询权限规则
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2InfoListReq true  "分页查询"
// @Success   200   {object}  response.Response{data=systemRes.Authority2InfoListResp}  "分页查询"
// @Router    /authority2/listRule [post]
func (a *Authority2Api) GetRuleList(c *gin.Context) {
	var req systemReq.Authority2InfoListReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	req.FixPageSize(1, 10)
	var resp *systemRes.Authority2InfoListResp
	resp, err := authority2Service.ListRule(req)
	response.ResultErr(resp, err, c)
}

// GetRule 根据 id 获取规则详情
// @Tags      Authority2
// @Summary   根据 id 获取规则详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2InfoReq true  "获取规则详情"
// @Success   200   {object}  response.Response{data=systemRes.Authority2InfoResp}  "根据 id 获取规则详情"
// @Router    /authority2/getRule [get]
func (a *Authority2Api) GetRule(c *gin.Context) {
	var req systemReq.Authority2InfoReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	var resp *systemRes.Authority2InfoResp
	resp, err := authority2Service.GetRuleInfo(req)
	response.ResultErr(resp, err, c)
}

// SetUserAuthority 给用户授权
// @Tags      Authority2
// @Summary   给用户授权
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2UserSetReq true  "给用户授权"
// @Success   200   {object}  response.Response{data=systemRes.Authority2UserSetResp}  "给用户授权"
// @Router    /authority2/userSet [post]
func (a *Authority2Api) SetUserAuthority(c *gin.Context) {
	var req systemReq.Authority2UserSetReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	var authIds = req.AuthId.GetIDUnique()
	if req.UserId == 0 || len(authIds) == 0 {
		response.FailWithMessage("用户ID和规则ID不能为空", c)
		return
	}
	user, err := userService.FindUserById(int(req.UserId))
	if err != nil {
		response.FailWithMessage("用户不存在", c)
		return
	} else if user == nil {
		response.FailWithMessage("用户不存在", c)
		return
	}
	var resp *systemRes.Authority2UserSetResp
	cnt, err := authority2Service.SetUserAuthorities(req.UserId, authIds)
	if err == nil {
		if cnt > 0 {

		}
		resp = &systemRes.Authority2UserSetResp{}
	}
	response.ResultErr(resp, err, c)
}

// UnsetUserAuthority 取消用户授权
// @Tags      Authority2
// @Summary   取消用户授权
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2UserUnsetReq true  "取消用户授权"
// @Success   200   {object}  response.Response{data=systemRes.Authority2UserSetResp}  "取消用户授权"
// @Router    /authority2/userUnset [post]
func (a *Authority2Api) UnsetUserAuthority(c *gin.Context) {
	var req systemReq.Authority2UserUnsetReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	var authIds = req.AuthIds.GetID()
	if req.UserId == 0 || len(authIds) == 0 {
		response.FailWithMessage("用户ID和规则ID不能为空", c)
		return
	}
	user, err := userService.FindUserById(int(req.UserId))
	if err != nil {
		response.FailWithMessage("用户不存在", c)
		return
	} else if user == nil {
		response.FailWithMessage("用户不存在", c)
		return
	}
	var resp *systemRes.Authority2UserSetResp
	cnt, err := authority2Service.UnsetUserAuthority(req.UserId, authIds)
	if err == nil {
		if cnt > 0 {
		}
		resp = &systemRes.Authority2UserSetResp{}
	}
	response.ResultErr(resp, err, c)
}

// ResetUserAuthority 重置用户授权
// @Tags      Authority2
// @Summary   重置用户授权
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2UserResetReq true  "重置用户授权"
// @Success   200   {object}  response.Response{data=systemRes.Authority2UserResetResp}  "重置用户授权"
// @Router    /authority2/userReset [post]
func (a *Authority2Api) ResetUserAuthority(c *gin.Context) {
	var req systemReq.Authority2UserResetReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	var authIds = req.AuthIds.GetID()
	if req.UserId == 0 || len(authIds) == 0 {
		response.FailWithMessage("用户ID和规则ID不能为空", c)
		return
	}
	user, err := userService.FindUserById(int(req.UserId))
	if err != nil {
		response.FailWithMessage("用户不存在", c)
		return
	} else if user == nil {
		response.FailWithMessage("用户不存在", c)
		return
	}
	var resp *systemRes.Authority2UserResetResp
	cnt, info, err := authority2Service.ResetUserAuthority(req.UserId, req.AuthIds.GetIDUnique())
	if err == nil {
		resp = &systemRes.Authority2UserResetResp{Count: cnt, Info: info}
	}
	response.ResultErr(resp, err, c)
}

// GetUserAuthority 分页获取用户的授权规则列表
// @Tags      Authority2
// @Summary   分页获取用户的授权规则列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2UserListReq true  "分页获取用户的授权规则列表"
// @Success   200   {object}  response.Response{data=systemRes.Authority2UserListResp}  "分页获取用户的授权规则列表"
// @Router    /authority2/userList [GET]
func (a *Authority2Api) GetUserAuthority(c *gin.Context) {
	var req systemReq.Authority2UserListReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if req.UserId == 0 {
		req.UserId = utils.GetUserID(c)
	}
	req.FixPageSize(1, 10)
	var resp *systemRes.Authority2UserListResp
	resp, err := authority2Service.GetUserAuthorities(req)
	response.ResultErr(resp, err, c)
}

// GetAllRuleItemByUserId 根据用户ID获取所有授权项
// @Tags      Authority2
// @Summary   根据用户ID获取所有授权项
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      systemReq.Authority2UserItemsReq true  "分页获取用户的授权规则列表"
// @Success   200   {object}  response.Response{data=systemRes.Authority2UserItemsResp}  "根据用户ID获取所有授权项"
// @Router    /authority2/userListItems [get]
func (a *Authority2Api) GetAllRuleItemByUserId(c *gin.Context) {
	var req systemReq.Authority2UserItemsReq
	if err := apikit.GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if req.UserId == 0 {
		req.UserId = utils.GetUserID(c)
	}
	var resp *systemRes.Authority2UserItemsResp
	data, err := authority2Service.GetAllRuleItemByUserId(req.UserId)
	if err == nil {
		resp = &systemRes.Authority2UserItemsResp{List: data}
	}
	response.ResultErr(resp, err, c)
}
