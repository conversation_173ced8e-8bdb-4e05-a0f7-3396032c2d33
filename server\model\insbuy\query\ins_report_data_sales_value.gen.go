// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportDataSalesValue(db *gorm.DB, opts ...gen.DOOption) insReportDataSalesValue {
	_insReportDataSalesValue := insReportDataSalesValue{}

	_insReportDataSalesValue.insReportDataSalesValueDo.UseDB(db, opts...)
	_insReportDataSalesValue.insReportDataSalesValueDo.UseModel(&insbuy.InsReportDataSalesValue{})

	tableName := _insReportDataSalesValue.insReportDataSalesValueDo.TableName()
	_insReportDataSalesValue.ALL = field.NewAsterisk(tableName)
	_insReportDataSalesValue.ID = field.NewUint(tableName, "id")
	_insReportDataSalesValue.StoreId = field.NewUint(tableName, "store_id")
	_insReportDataSalesValue.BusinessDay = field.NewTime(tableName, "business_day")
	_insReportDataSalesValue.SalesmanId = field.NewUint(tableName, "salesman_id")
	_insReportDataSalesValue.OrgId = field.NewUint(tableName, "org_id")
	_insReportDataSalesValue.LeaderId = field.NewUint(tableName, "leader_id")
	_insReportDataSalesValue.WaiterId = field.NewUint(tableName, "waiter_id")
	_insReportDataSalesValue.DealAt = field.NewField(tableName, "deal_at")
	_insReportDataSalesValue.DealTime = field.NewField(tableName, "deal_time")
	_insReportDataSalesValue.WeekDay = field.NewInt(tableName, "week_day")
	_insReportDataSalesValue.IncomeType = field.NewInt(tableName, "income_type")
	_insReportDataSalesValue.ProductId = field.NewUint(tableName, "product_id")
	_insReportDataSalesValue.Quantity = field.NewInt(tableName, "quantity")
	_insReportDataSalesValue.ProductCategoryId = field.NewUint(tableName, "product_category_id")
	_insReportDataSalesValue.OrderId = field.NewUint(tableName, "order_id")
	_insReportDataSalesValue.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insReportDataSalesValue.DeskId = field.NewUint(tableName, "desk_id")
	_insReportDataSalesValue.VipCardId = field.NewUint(tableName, "vip_card_id")
	_insReportDataSalesValue.Amount = field.NewFloat64(tableName, "amount")
	_insReportDataSalesValue.OriginAmount = field.NewFloat64(tableName, "origin_amount")
	_insReportDataSalesValue.Remark = field.NewString(tableName, "remark")
	_insReportDataSalesValue.Ext = field.NewField(tableName, "ext")
	_insReportDataSalesValue.CreatedAt = field.NewTime(tableName, "created_at")

	_insReportDataSalesValue.fillFieldMap()

	return _insReportDataSalesValue
}

type insReportDataSalesValue struct {
	insReportDataSalesValueDo

	ALL               field.Asterisk
	ID                field.Uint
	StoreId           field.Uint
	BusinessDay       field.Time
	SalesmanId        field.Uint
	OrgId             field.Uint
	LeaderId          field.Uint
	WaiterId          field.Uint
	DealAt            field.Field
	DealTime          field.Field
	WeekDay           field.Int
	IncomeType        field.Int
	ProductId         field.Uint
	Quantity          field.Int
	ProductCategoryId field.Uint
	OrderId           field.Uint
	OpenDeskId        field.Uint
	DeskId            field.Uint
	VipCardId         field.Uint
	Amount            field.Float64
	OriginAmount      field.Float64
	Remark            field.String
	Ext               field.Field
	CreatedAt         field.Time

	fieldMap map[string]field.Expr
}

func (i insReportDataSalesValue) Table(newTableName string) *insReportDataSalesValue {
	i.insReportDataSalesValueDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportDataSalesValue) As(alias string) *insReportDataSalesValue {
	i.insReportDataSalesValueDo.DO = *(i.insReportDataSalesValueDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportDataSalesValue) updateTableName(table string) *insReportDataSalesValue {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.SalesmanId = field.NewUint(table, "salesman_id")
	i.OrgId = field.NewUint(table, "org_id")
	i.LeaderId = field.NewUint(table, "leader_id")
	i.WaiterId = field.NewUint(table, "waiter_id")
	i.DealAt = field.NewField(table, "deal_at")
	i.DealTime = field.NewField(table, "deal_time")
	i.WeekDay = field.NewInt(table, "week_day")
	i.IncomeType = field.NewInt(table, "income_type")
	i.ProductId = field.NewUint(table, "product_id")
	i.Quantity = field.NewInt(table, "quantity")
	i.ProductCategoryId = field.NewUint(table, "product_category_id")
	i.OrderId = field.NewUint(table, "order_id")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.DeskId = field.NewUint(table, "desk_id")
	i.VipCardId = field.NewUint(table, "vip_card_id")
	i.Amount = field.NewFloat64(table, "amount")
	i.OriginAmount = field.NewFloat64(table, "origin_amount")
	i.Remark = field.NewString(table, "remark")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")

	i.fillFieldMap()

	return i
}

func (i *insReportDataSalesValue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportDataSalesValue) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 23)
	i.fieldMap["id"] = i.ID
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["salesman_id"] = i.SalesmanId
	i.fieldMap["org_id"] = i.OrgId
	i.fieldMap["leader_id"] = i.LeaderId
	i.fieldMap["waiter_id"] = i.WaiterId
	i.fieldMap["deal_at"] = i.DealAt
	i.fieldMap["deal_time"] = i.DealTime
	i.fieldMap["week_day"] = i.WeekDay
	i.fieldMap["income_type"] = i.IncomeType
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["quantity"] = i.Quantity
	i.fieldMap["product_category_id"] = i.ProductCategoryId
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["vip_card_id"] = i.VipCardId
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["origin_amount"] = i.OriginAmount
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
}

func (i insReportDataSalesValue) clone(db *gorm.DB) insReportDataSalesValue {
	i.insReportDataSalesValueDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportDataSalesValue) replaceDB(db *gorm.DB) insReportDataSalesValue {
	i.insReportDataSalesValueDo.ReplaceDB(db)
	return i
}

type insReportDataSalesValueDo struct{ gen.DO }

func (i insReportDataSalesValueDo) Debug() *insReportDataSalesValueDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportDataSalesValueDo) WithContext(ctx context.Context) *insReportDataSalesValueDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportDataSalesValueDo) ReadDB() *insReportDataSalesValueDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportDataSalesValueDo) WriteDB() *insReportDataSalesValueDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportDataSalesValueDo) Session(config *gorm.Session) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportDataSalesValueDo) Clauses(conds ...clause.Expression) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportDataSalesValueDo) Returning(value interface{}, columns ...string) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportDataSalesValueDo) Not(conds ...gen.Condition) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportDataSalesValueDo) Or(conds ...gen.Condition) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportDataSalesValueDo) Select(conds ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportDataSalesValueDo) Where(conds ...gen.Condition) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportDataSalesValueDo) Order(conds ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportDataSalesValueDo) Distinct(cols ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportDataSalesValueDo) Omit(cols ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportDataSalesValueDo) Join(table schema.Tabler, on ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportDataSalesValueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportDataSalesValueDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportDataSalesValueDo) Group(cols ...field.Expr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportDataSalesValueDo) Having(conds ...gen.Condition) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportDataSalesValueDo) Limit(limit int) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportDataSalesValueDo) Offset(offset int) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportDataSalesValueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportDataSalesValueDo) Unscoped() *insReportDataSalesValueDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportDataSalesValueDo) Create(values ...*insbuy.InsReportDataSalesValue) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportDataSalesValueDo) CreateInBatches(values []*insbuy.InsReportDataSalesValue, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportDataSalesValueDo) Save(values ...*insbuy.InsReportDataSalesValue) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportDataSalesValueDo) First() (*insbuy.InsReportDataSalesValue, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportDataSalesValue), nil
	}
}

func (i insReportDataSalesValueDo) Take() (*insbuy.InsReportDataSalesValue, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportDataSalesValue), nil
	}
}

func (i insReportDataSalesValueDo) Last() (*insbuy.InsReportDataSalesValue, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportDataSalesValue), nil
	}
}

func (i insReportDataSalesValueDo) Find() ([]*insbuy.InsReportDataSalesValue, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportDataSalesValue), err
}

func (i insReportDataSalesValueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportDataSalesValue, err error) {
	buf := make([]*insbuy.InsReportDataSalesValue, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportDataSalesValueDo) FindInBatches(result *[]*insbuy.InsReportDataSalesValue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportDataSalesValueDo) Attrs(attrs ...field.AssignExpr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportDataSalesValueDo) Assign(attrs ...field.AssignExpr) *insReportDataSalesValueDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportDataSalesValueDo) Joins(fields ...field.RelationField) *insReportDataSalesValueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportDataSalesValueDo) Preload(fields ...field.RelationField) *insReportDataSalesValueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportDataSalesValueDo) FirstOrInit() (*insbuy.InsReportDataSalesValue, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportDataSalesValue), nil
	}
}

func (i insReportDataSalesValueDo) FirstOrCreate() (*insbuy.InsReportDataSalesValue, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportDataSalesValue), nil
	}
}

func (i insReportDataSalesValueDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportDataSalesValue, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportDataSalesValueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportDataSalesValueDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportDataSalesValueDo) Delete(models ...*insbuy.InsReportDataSalesValue) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportDataSalesValueDo) withDO(do gen.Dao) *insReportDataSalesValueDo {
	i.DO = *do.(*gen.DO)
	return i
}
