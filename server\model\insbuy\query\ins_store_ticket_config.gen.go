// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreTicketConfig(db *gorm.DB, opts ...gen.DOOption) insStoreTicketConfig {
	_insStoreTicketConfig := insStoreTicketConfig{}

	_insStoreTicketConfig.insStoreTicketConfigDo.UseDB(db, opts...)
	_insStoreTicketConfig.insStoreTicketConfigDo.UseModel(&insbuy.InsStoreTicketConfig{})

	tableName := _insStoreTicketConfig.insStoreTicketConfigDo.TableName()
	_insStoreTicketConfig.ALL = field.NewAsterisk(tableName)
	_insStoreTicketConfig.ID = field.NewUint(tableName, "id")
	_insStoreTicketConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreTicketConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreTicketConfig.DeletedAt = field.NewField(tableName, "deleted_at")
	_insStoreTicketConfig.StoreId = field.NewUint(tableName, "store_id")
	_insStoreTicketConfig.Source = field.NewString(tableName, "source")
	_insStoreTicketConfig.Ext = field.NewField(tableName, "ext")
	_insStoreTicketConfig.Type = field.NewUint(tableName, "type")

	_insStoreTicketConfig.fillFieldMap()

	return _insStoreTicketConfig
}

type insStoreTicketConfig struct {
	insStoreTicketConfigDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	StoreId   field.Uint
	Source    field.String
	Ext       field.Field
	Type      field.Uint

	fieldMap map[string]field.Expr
}

func (i insStoreTicketConfig) Table(newTableName string) *insStoreTicketConfig {
	i.insStoreTicketConfigDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreTicketConfig) As(alias string) *insStoreTicketConfig {
	i.insStoreTicketConfigDo.DO = *(i.insStoreTicketConfigDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreTicketConfig) updateTableName(table string) *insStoreTicketConfig {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Source = field.NewString(table, "source")
	i.Ext = field.NewField(table, "ext")
	i.Type = field.NewUint(table, "type")

	i.fillFieldMap()

	return i
}

func (i *insStoreTicketConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreTicketConfig) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["source"] = i.Source
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["type"] = i.Type
}

func (i insStoreTicketConfig) clone(db *gorm.DB) insStoreTicketConfig {
	i.insStoreTicketConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreTicketConfig) replaceDB(db *gorm.DB) insStoreTicketConfig {
	i.insStoreTicketConfigDo.ReplaceDB(db)
	return i
}

type insStoreTicketConfigDo struct{ gen.DO }

func (i insStoreTicketConfigDo) Debug() *insStoreTicketConfigDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreTicketConfigDo) WithContext(ctx context.Context) *insStoreTicketConfigDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreTicketConfigDo) ReadDB() *insStoreTicketConfigDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreTicketConfigDo) WriteDB() *insStoreTicketConfigDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreTicketConfigDo) Session(config *gorm.Session) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreTicketConfigDo) Clauses(conds ...clause.Expression) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreTicketConfigDo) Returning(value interface{}, columns ...string) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreTicketConfigDo) Not(conds ...gen.Condition) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreTicketConfigDo) Or(conds ...gen.Condition) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreTicketConfigDo) Select(conds ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreTicketConfigDo) Where(conds ...gen.Condition) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreTicketConfigDo) Order(conds ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreTicketConfigDo) Distinct(cols ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreTicketConfigDo) Omit(cols ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreTicketConfigDo) Join(table schema.Tabler, on ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreTicketConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreTicketConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreTicketConfigDo) Group(cols ...field.Expr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreTicketConfigDo) Having(conds ...gen.Condition) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreTicketConfigDo) Limit(limit int) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreTicketConfigDo) Offset(offset int) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreTicketConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreTicketConfigDo) Unscoped() *insStoreTicketConfigDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreTicketConfigDo) Create(values ...*insbuy.InsStoreTicketConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreTicketConfigDo) CreateInBatches(values []*insbuy.InsStoreTicketConfig, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreTicketConfigDo) Save(values ...*insbuy.InsStoreTicketConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreTicketConfigDo) First() (*insbuy.InsStoreTicketConfig, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTicketConfig), nil
	}
}

func (i insStoreTicketConfigDo) Take() (*insbuy.InsStoreTicketConfig, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTicketConfig), nil
	}
}

func (i insStoreTicketConfigDo) Last() (*insbuy.InsStoreTicketConfig, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTicketConfig), nil
	}
}

func (i insStoreTicketConfigDo) Find() ([]*insbuy.InsStoreTicketConfig, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreTicketConfig), err
}

func (i insStoreTicketConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreTicketConfig, err error) {
	buf := make([]*insbuy.InsStoreTicketConfig, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreTicketConfigDo) FindInBatches(result *[]*insbuy.InsStoreTicketConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreTicketConfigDo) Attrs(attrs ...field.AssignExpr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreTicketConfigDo) Assign(attrs ...field.AssignExpr) *insStoreTicketConfigDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreTicketConfigDo) Joins(fields ...field.RelationField) *insStoreTicketConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreTicketConfigDo) Preload(fields ...field.RelationField) *insStoreTicketConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreTicketConfigDo) FirstOrInit() (*insbuy.InsStoreTicketConfig, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTicketConfig), nil
	}
}

func (i insStoreTicketConfigDo) FirstOrCreate() (*insbuy.InsStoreTicketConfig, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTicketConfig), nil
	}
}

func (i insStoreTicketConfigDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreTicketConfig, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreTicketConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreTicketConfigDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreTicketConfigDo) Delete(models ...*insbuy.InsStoreTicketConfig) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreTicketConfigDo) withDO(do gen.Dao) *insStoreTicketConfigDo {
	i.DO = *do.(*gen.DO)
	return i
}
