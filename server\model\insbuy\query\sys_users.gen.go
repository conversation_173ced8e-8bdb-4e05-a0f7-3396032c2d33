// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newSysUser(db *gorm.DB, opts ...gen.DOOption) sysUser {
	_sysUser := sysUser{}

	_sysUser.sysUserDo.UseDB(db, opts...)
	_sysUser.sysUserDo.UseModel(&system.SysUser{})

	tableName := _sysUser.sysUserDo.TableName()
	_sysUser.ALL = field.NewAsterisk(tableName)
	_sysUser.ID = field.NewUint(tableName, "id")
	_sysUser.CreatedAt = field.NewTime(tableName, "created_at")
	_sysUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_sysUser.DeletedAt = field.NewField(tableName, "deleted_at")
	_sysUser.UUID = field.NewField(tableName, "uuid")
	_sysUser.Username = field.NewString(tableName, "username")
	_sysUser.Password = field.NewString(tableName, "password")
	_sysUser.NickName = field.NewString(tableName, "nick_name")
	_sysUser.SideMode = field.NewString(tableName, "side_mode")
	_sysUser.HeaderImg = field.NewString(tableName, "header_img")
	_sysUser.BaseColor = field.NewString(tableName, "base_color")
	_sysUser.ActiveColor = field.NewString(tableName, "active_color")
	_sysUser.AuthorityId = field.NewUint(tableName, "authority_id")
	_sysUser.Phone = field.NewString(tableName, "phone")
	_sysUser.Email = field.NewString(tableName, "email")
	_sysUser.Enable = field.NewInt(tableName, "enable")
	_sysUser.BlocSale = field.NewInt(tableName, "bloc_sale")
	_sysUser.Contact = field.NewString(tableName, "contact")
	_sysUser.Ext = field.NewField(tableName, "ext")
	_sysUser.Authority = sysUserHasOneAuthority{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Authority", "system.SysAuthority"),
		DataAuthorityId: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Authority.DataAuthorityId", "system.SysAuthority"),
		},
		SysBaseMenus: struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Authority.SysBaseMenus", "system.SysBaseMenu"),
			Parameters: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Authority.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
			},
			MenuBtn: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Authority.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
			},
			SysAuthoritys: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Authority.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
			},
		},
		Users: struct {
			field.RelationField
			Authority struct {
				field.RelationField
			}
			Authorities struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Authority.Users", "system.SysUser"),
			Authority: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Authority.Users.Authority", "system.SysAuthority"),
			},
			Authorities: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Authority.Users.Authorities", "system.SysAuthority"),
			},
		},
	}

	_sysUser.Authorities = sysUserManyToManyAuthorities{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Authorities", "system.SysAuthority"),
	}

	_sysUser.fillFieldMap()

	return _sysUser
}

type sysUser struct {
	sysUserDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	UUID        field.Field
	Username    field.String
	Password    field.String
	NickName    field.String
	SideMode    field.String
	HeaderImg   field.String
	BaseColor   field.String
	ActiveColor field.String
	AuthorityId field.Uint
	Phone       field.String
	Email       field.String
	Enable      field.Int
	BlocSale    field.Int
	Contact     field.String
	Ext         field.Field
	Authority   sysUserHasOneAuthority

	Authorities sysUserManyToManyAuthorities

	fieldMap map[string]field.Expr
}

func (s sysUser) Table(newTableName string) *sysUser {
	s.sysUserDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysUser) As(alias string) *sysUser {
	s.sysUserDo.DO = *(s.sysUserDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysUser) updateTableName(table string) *sysUser {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint(table, "id")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")
	s.UUID = field.NewField(table, "uuid")
	s.Username = field.NewString(table, "username")
	s.Password = field.NewString(table, "password")
	s.NickName = field.NewString(table, "nick_name")
	s.SideMode = field.NewString(table, "side_mode")
	s.HeaderImg = field.NewString(table, "header_img")
	s.BaseColor = field.NewString(table, "base_color")
	s.ActiveColor = field.NewString(table, "active_color")
	s.AuthorityId = field.NewUint(table, "authority_id")
	s.Phone = field.NewString(table, "phone")
	s.Email = field.NewString(table, "email")
	s.Enable = field.NewInt(table, "enable")
	s.BlocSale = field.NewInt(table, "bloc_sale")
	s.Contact = field.NewString(table, "contact")
	s.Ext = field.NewField(table, "ext")

	s.fillFieldMap()

	return s
}

func (s *sysUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysUser) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 21)
	s.fieldMap["id"] = s.ID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["uuid"] = s.UUID
	s.fieldMap["username"] = s.Username
	s.fieldMap["password"] = s.Password
	s.fieldMap["nick_name"] = s.NickName
	s.fieldMap["side_mode"] = s.SideMode
	s.fieldMap["header_img"] = s.HeaderImg
	s.fieldMap["base_color"] = s.BaseColor
	s.fieldMap["active_color"] = s.ActiveColor
	s.fieldMap["authority_id"] = s.AuthorityId
	s.fieldMap["phone"] = s.Phone
	s.fieldMap["email"] = s.Email
	s.fieldMap["enable"] = s.Enable
	s.fieldMap["bloc_sale"] = s.BlocSale
	s.fieldMap["contact"] = s.Contact
	s.fieldMap["ext"] = s.Ext

}

func (s sysUser) clone(db *gorm.DB) sysUser {
	s.sysUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysUser) replaceDB(db *gorm.DB) sysUser {
	s.sysUserDo.ReplaceDB(db)
	return s
}

type sysUserHasOneAuthority struct {
	db *gorm.DB

	field.RelationField

	DataAuthorityId struct {
		field.RelationField
	}
	SysBaseMenus struct {
		field.RelationField
		Parameters struct {
			field.RelationField
		}
		MenuBtn struct {
			field.RelationField
		}
		SysAuthoritys struct {
			field.RelationField
		}
	}
	Users struct {
		field.RelationField
		Authority struct {
			field.RelationField
		}
		Authorities struct {
			field.RelationField
		}
	}
}

func (a sysUserHasOneAuthority) Where(conds ...field.Expr) *sysUserHasOneAuthority {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysUserHasOneAuthority) WithContext(ctx context.Context) *sysUserHasOneAuthority {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysUserHasOneAuthority) Session(session *gorm.Session) *sysUserHasOneAuthority {
	a.db = a.db.Session(session)
	return &a
}

func (a sysUserHasOneAuthority) Model(m *system.SysUser) *sysUserHasOneAuthorityTx {
	return &sysUserHasOneAuthorityTx{a.db.Model(m).Association(a.Name())}
}

type sysUserHasOneAuthorityTx struct{ tx *gorm.Association }

func (a sysUserHasOneAuthorityTx) Find() (result *system.SysAuthority, err error) {
	return result, a.tx.Find(&result)
}

func (a sysUserHasOneAuthorityTx) Append(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysUserHasOneAuthorityTx) Replace(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysUserHasOneAuthorityTx) Delete(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysUserHasOneAuthorityTx) Clear() error {
	return a.tx.Clear()
}

func (a sysUserHasOneAuthorityTx) Count() int64 {
	return a.tx.Count()
}

type sysUserManyToManyAuthorities struct {
	db *gorm.DB

	field.RelationField
}

func (a sysUserManyToManyAuthorities) Where(conds ...field.Expr) *sysUserManyToManyAuthorities {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysUserManyToManyAuthorities) WithContext(ctx context.Context) *sysUserManyToManyAuthorities {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysUserManyToManyAuthorities) Session(session *gorm.Session) *sysUserManyToManyAuthorities {
	a.db = a.db.Session(session)
	return &a
}

func (a sysUserManyToManyAuthorities) Model(m *system.SysUser) *sysUserManyToManyAuthoritiesTx {
	return &sysUserManyToManyAuthoritiesTx{a.db.Model(m).Association(a.Name())}
}

type sysUserManyToManyAuthoritiesTx struct{ tx *gorm.Association }

func (a sysUserManyToManyAuthoritiesTx) Find() (result []*system.SysAuthority, err error) {
	return result, a.tx.Find(&result)
}

func (a sysUserManyToManyAuthoritiesTx) Append(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysUserManyToManyAuthoritiesTx) Replace(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysUserManyToManyAuthoritiesTx) Delete(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysUserManyToManyAuthoritiesTx) Clear() error {
	return a.tx.Clear()
}

func (a sysUserManyToManyAuthoritiesTx) Count() int64 {
	return a.tx.Count()
}

type sysUserDo struct{ gen.DO }

func (s sysUserDo) Debug() *sysUserDo {
	return s.withDO(s.DO.Debug())
}

func (s sysUserDo) WithContext(ctx context.Context) *sysUserDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysUserDo) ReadDB() *sysUserDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysUserDo) WriteDB() *sysUserDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysUserDo) Session(config *gorm.Session) *sysUserDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysUserDo) Clauses(conds ...clause.Expression) *sysUserDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysUserDo) Returning(value interface{}, columns ...string) *sysUserDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysUserDo) Not(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysUserDo) Or(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysUserDo) Select(conds ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysUserDo) Where(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysUserDo) Order(conds ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysUserDo) Distinct(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysUserDo) Omit(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysUserDo) Join(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysUserDo) Group(cols ...field.Expr) *sysUserDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysUserDo) Having(conds ...gen.Condition) *sysUserDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysUserDo) Limit(limit int) *sysUserDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysUserDo) Offset(offset int) *sysUserDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysUserDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysUserDo) Unscoped() *sysUserDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysUserDo) Create(values ...*system.SysUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysUserDo) CreateInBatches(values []*system.SysUser, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysUserDo) Save(values ...*system.SysUser) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysUserDo) First() (*system.SysUser, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysUser), nil
	}
}

func (s sysUserDo) Take() (*system.SysUser, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysUser), nil
	}
}

func (s sysUserDo) Last() (*system.SysUser, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysUser), nil
	}
}

func (s sysUserDo) Find() ([]*system.SysUser, error) {
	result, err := s.DO.Find()
	return result.([]*system.SysUser), err
}

func (s sysUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*system.SysUser, err error) {
	buf := make([]*system.SysUser, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysUserDo) FindInBatches(result *[]*system.SysUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysUserDo) Attrs(attrs ...field.AssignExpr) *sysUserDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysUserDo) Assign(attrs ...field.AssignExpr) *sysUserDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysUserDo) Joins(fields ...field.RelationField) *sysUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysUserDo) Preload(fields ...field.RelationField) *sysUserDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysUserDo) FirstOrInit() (*system.SysUser, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysUser), nil
	}
}

func (s sysUserDo) FirstOrCreate() (*system.SysUser, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysUser), nil
	}
}

func (s sysUserDo) FindByPage(offset int, limit int) (result []*system.SysUser, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysUserDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysUserDo) Delete(models ...*system.SysUser) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysUserDo) withDO(do gen.Dao) *sysUserDo {
	s.DO = *do.(*gen.DO)
	return s
}
