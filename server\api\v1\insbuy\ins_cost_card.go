package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsCostCardApi struct {
}

// CreateInsCostCard 创建成本卡
// @Tags InsCostCard
// @Summary 创建成本卡
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsCostCardCreate true "创建成本卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/createInsCostCard [post]
func (insCostCardApi *InsCostCardApi) CreateInsCostCard(c *gin.Context) {
	var insCostCard insbuyReq.InsCostCardCreate
	err := c.ShouldBindJSON(&insCostCard)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCostCardService.CreateInsCostCard(c, insCostCard); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsCostCard 删除成本卡
// @Tags InsCostCard
// @Summary 删除成本卡
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsCostCard true "删除成本卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insCostCard/deleteInsCostCard [delete]
func (insCostCardApi *InsCostCardApi) DeleteInsCostCard(c *gin.Context) {
	var insCostCard insbuy.InsCostCard
	err := c.ShouldBindJSON(&insCostCard)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCostCardService.DeleteInsCostCard(insCostCard); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsCostCardByIds 批量删除成本卡
// @Tags InsCostCard
// @Summary 批量删除成本卡
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除成本卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insCostCard/deleteInsCostCardByIds [delete]
func (insCostCardApi *InsCostCardApi) DeleteInsCostCardByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCostCardService.DeleteInsCostCardByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsCostCard 更新成本卡
// @Tags InsCostCard
// @Summary 更新成本卡
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsCostCardUpdate true "更新成本卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insCostCard/updateInsCostCard [put]
func (insCostCardApi *InsCostCardApi) UpdateInsCostCard(c *gin.Context) {
	var insCostCard insbuyReq.InsCostCardUpdate
	err := c.ShouldBindJSON(&insCostCard)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	insCostCardVerify := utils.Rules{
		"CostCardId": {utils.NotEmpty()},
	}
	if err = utils.Verify(insCostCard, insCostCardVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insCostCardService.UpdateInsCostCard(c, insCostCard); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsCostCard 用id查询成本卡
// @Tags InsCostCard
// @Summary 用id查询成本卡
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsCostCard true "用id查询成本卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insCostCard/findInsCostCard [get]
func (insCostCardApi *InsCostCardApi) FindInsCostCard(c *gin.Context) {
	var insCostCard insbuy.InsCostCard
	err := c.ShouldBindQuery(&insCostCard)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsCostCard, err := insCostCardService.GetInsCostCard(insCostCard.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsCostCard": reinsCostCard}, c)
	}
}

// GetInsCostCardList 分页获取成本卡列表
// @Tags InsCostCard
// @Summary 分页获取成本卡列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsCostCardSearch true "分页获取成本卡列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/getInsCostCardList [get]
func (insCostCardApi *InsCostCardApi) GetInsCostCardList(c *gin.Context) {
	var pageInfo insbuyReq.InsCostCardSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Export() {
		list, e1 := insCostCardService.GetInsCostCardExportList(insbuyReq.InsCostCardExportList{
			StoreId:      pageInfo.GetUintStoreId(),
			ProductName:  pageInfo.ProductName,
			MaterialName: pageInfo.MaterialName,
			Status:       pageInfo.Status,
		})
		if e1 != nil {
			err = e1
			return
		}
		_, e := insImportService.ExcelCommonList(c, insbuy.ETCostCardList.ToInt(), list)
		if e != nil {
			return
		}
		return
	}
	if list, total, err := insCostCardService.GetInsCostCardInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// UpdateInsCostCardStatus 更新成本卡状态
// @Tags InsGiftRules
// @Summary 更新成本卡状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsCostCardStatusUpdate true "更新成本卡状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insCostCard/updateInsCostCardStatus [put]
func (insCostCardApi *InsCostCardApi) UpdateInsCostCardStatus(c *gin.Context) {
	var insCostCard insbuyReq.InsCostCardStatusUpdate
	err := c.ShouldBindJSON(&insCostCard)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	verify := utils.Rules{
		"CostCardId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insCostCard, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insGiftRulesService.UpdateInsCostCardStatus(insCostCard); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// CostPriceList 成本价列表
// @Tags InsCostCard
// @Summary 成本价列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/costPriceList [get]
func (insCostCardApi *InsCostCardApi) CostPriceList(c *gin.Context) {
	var req insbuyReq.CostPriceListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insGiftRulesService.CostPriceList(req)
	response.ResultErr(list, err, c)
}

// AdjustPrice 调价
// @Tags InsCostCard
// @Summary 调价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AdjustPriceReq true "调价"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"调价成功"}"
// @Router /insCostCard/adjustPrice [put]
func (insCostCardApi *InsCostCardApi) AdjustPrice(c *gin.Context) {
	var req insbuyReq.AdjustPriceReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insGiftRulesService.AdjustPrice(req)
	response.Err(err, c)
}

// CostPriceHistory 成本价历史
// @Tags InsCostCard
// @Summary 成本价历史
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CostPriceHistoryReq true "成本价历史"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/costPriceHistory [get]
func (insCostCardApi *InsCostCardApi) CostPriceHistory(c *gin.Context) {
	var req insbuyReq.CostPriceHistoryReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insGiftRulesService.CostPriceHistory(c.Request.Context(), req)
	response.ResultErr(list, err, c)
}

// Preview 预览商品成本价
// @Tags InsCostCard
// @Summary 预览商品成本价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.PreviewProductCostPriceReq true "预览成本价"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/preview [get]
func (insCostCardApi *InsCostCardApi) Preview(c *gin.Context) {
	var req insbuyReq.PreviewProductCostPriceReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insGiftRulesService.PreviewProductCostPrice(req)
	response.ResultErr(list, err, c)
}

// Generate 生成商品成本价
// @Tags InsCostCard
// @Summary 生成商品成本价
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GenerateProductCostPriceReq true "生成商品成本价"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCostCard/generate [get]
func (insCostCardApi *InsCostCardApi) Generate(c *gin.Context) {
	var req insbuyReq.GenerateProductCostPriceReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insGiftRulesService.GenerateProductCostPrice(req)
	response.ResultErr(list, err, c)
}
