// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsInventoryMaterialFlow(db *gorm.DB, opts ...gen.DOOption) insInventoryMaterialFlow {
	_insInventoryMaterialFlow := insInventoryMaterialFlow{}

	_insInventoryMaterialFlow.insInventoryMaterialFlowDo.UseDB(db, opts...)
	_insInventoryMaterialFlow.insInventoryMaterialFlowDo.UseModel(&insbuy.InsInventoryMaterialFlow{})

	tableName := _insInventoryMaterialFlow.insInventoryMaterialFlowDo.TableName()
	_insInventoryMaterialFlow.ALL = field.NewAsterisk(tableName)
	_insInventoryMaterialFlow.Id = field.NewInt64(tableName, "id")
	_insInventoryMaterialFlow.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insInventoryMaterialFlow.MaterialId = field.NewInt(tableName, "material_id")
	_insInventoryMaterialFlow.OrderDetailId = field.NewUint64(tableName, "order_detail_id")
	_insInventoryMaterialFlow.QuantityTrade = field.NewInt(tableName, "quantity_trade")
	_insInventoryMaterialFlow.OrderSn = field.NewString(tableName, "order_sn")
	_insInventoryMaterialFlow.CreatedAt = field.NewTime(tableName, "created_at")
	_insInventoryMaterialFlow.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insInventoryMaterialFlow.BusinessDay = field.NewTime(tableName, "business_day")

	_insInventoryMaterialFlow.fillFieldMap()

	return _insInventoryMaterialFlow
}

type insInventoryMaterialFlow struct {
	insInventoryMaterialFlowDo

	ALL           field.Asterisk
	Id            field.Int64
	WarehouseId   field.Int
	MaterialId    field.Int
	OrderDetailId field.Uint64
	QuantityTrade field.Int
	OrderSn       field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time
	BusinessDay   field.Time

	fieldMap map[string]field.Expr
}

func (i insInventoryMaterialFlow) Table(newTableName string) *insInventoryMaterialFlow {
	i.insInventoryMaterialFlowDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insInventoryMaterialFlow) As(alias string) *insInventoryMaterialFlow {
	i.insInventoryMaterialFlowDo.DO = *(i.insInventoryMaterialFlowDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insInventoryMaterialFlow) updateTableName(table string) *insInventoryMaterialFlow {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt64(table, "id")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.OrderDetailId = field.NewUint64(table, "order_detail_id")
	i.QuantityTrade = field.NewInt(table, "quantity_trade")
	i.OrderSn = field.NewString(table, "order_sn")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insInventoryMaterialFlow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insInventoryMaterialFlow) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.Id
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["order_detail_id"] = i.OrderDetailId
	i.fieldMap["quantity_trade"] = i.QuantityTrade
	i.fieldMap["order_sn"] = i.OrderSn
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insInventoryMaterialFlow) clone(db *gorm.DB) insInventoryMaterialFlow {
	i.insInventoryMaterialFlowDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insInventoryMaterialFlow) replaceDB(db *gorm.DB) insInventoryMaterialFlow {
	i.insInventoryMaterialFlowDo.ReplaceDB(db)
	return i
}

type insInventoryMaterialFlowDo struct{ gen.DO }

func (i insInventoryMaterialFlowDo) Debug() *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Debug())
}

func (i insInventoryMaterialFlowDo) WithContext(ctx context.Context) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insInventoryMaterialFlowDo) ReadDB() *insInventoryMaterialFlowDo {
	return i.Clauses(dbresolver.Read)
}

func (i insInventoryMaterialFlowDo) WriteDB() *insInventoryMaterialFlowDo {
	return i.Clauses(dbresolver.Write)
}

func (i insInventoryMaterialFlowDo) Session(config *gorm.Session) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Session(config))
}

func (i insInventoryMaterialFlowDo) Clauses(conds ...clause.Expression) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insInventoryMaterialFlowDo) Returning(value interface{}, columns ...string) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insInventoryMaterialFlowDo) Not(conds ...gen.Condition) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insInventoryMaterialFlowDo) Or(conds ...gen.Condition) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insInventoryMaterialFlowDo) Select(conds ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insInventoryMaterialFlowDo) Where(conds ...gen.Condition) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insInventoryMaterialFlowDo) Order(conds ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insInventoryMaterialFlowDo) Distinct(cols ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insInventoryMaterialFlowDo) Omit(cols ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insInventoryMaterialFlowDo) Join(table schema.Tabler, on ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insInventoryMaterialFlowDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insInventoryMaterialFlowDo) RightJoin(table schema.Tabler, on ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insInventoryMaterialFlowDo) Group(cols ...field.Expr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insInventoryMaterialFlowDo) Having(conds ...gen.Condition) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insInventoryMaterialFlowDo) Limit(limit int) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insInventoryMaterialFlowDo) Offset(offset int) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insInventoryMaterialFlowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insInventoryMaterialFlowDo) Unscoped() *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insInventoryMaterialFlowDo) Create(values ...*insbuy.InsInventoryMaterialFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insInventoryMaterialFlowDo) CreateInBatches(values []*insbuy.InsInventoryMaterialFlow, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insInventoryMaterialFlowDo) Save(values ...*insbuy.InsInventoryMaterialFlow) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insInventoryMaterialFlowDo) First() (*insbuy.InsInventoryMaterialFlow, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryMaterialFlow), nil
	}
}

func (i insInventoryMaterialFlowDo) Take() (*insbuy.InsInventoryMaterialFlow, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryMaterialFlow), nil
	}
}

func (i insInventoryMaterialFlowDo) Last() (*insbuy.InsInventoryMaterialFlow, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryMaterialFlow), nil
	}
}

func (i insInventoryMaterialFlowDo) Find() ([]*insbuy.InsInventoryMaterialFlow, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsInventoryMaterialFlow), err
}

func (i insInventoryMaterialFlowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsInventoryMaterialFlow, err error) {
	buf := make([]*insbuy.InsInventoryMaterialFlow, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insInventoryMaterialFlowDo) FindInBatches(result *[]*insbuy.InsInventoryMaterialFlow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insInventoryMaterialFlowDo) Attrs(attrs ...field.AssignExpr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insInventoryMaterialFlowDo) Assign(attrs ...field.AssignExpr) *insInventoryMaterialFlowDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insInventoryMaterialFlowDo) Joins(fields ...field.RelationField) *insInventoryMaterialFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insInventoryMaterialFlowDo) Preload(fields ...field.RelationField) *insInventoryMaterialFlowDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insInventoryMaterialFlowDo) FirstOrInit() (*insbuy.InsInventoryMaterialFlow, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryMaterialFlow), nil
	}
}

func (i insInventoryMaterialFlowDo) FirstOrCreate() (*insbuy.InsInventoryMaterialFlow, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsInventoryMaterialFlow), nil
	}
}

func (i insInventoryMaterialFlowDo) FindByPage(offset int, limit int) (result []*insbuy.InsInventoryMaterialFlow, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insInventoryMaterialFlowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insInventoryMaterialFlowDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insInventoryMaterialFlowDo) Delete(models ...*insbuy.InsInventoryMaterialFlow) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insInventoryMaterialFlowDo) withDO(do gen.Dao) *insInventoryMaterialFlowDo {
	i.DO = *do.(*gen.DO)
	return i
}
