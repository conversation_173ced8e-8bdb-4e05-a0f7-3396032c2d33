// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeCategory(db *gorm.DB, opts ...gen.DOOption) insServiceFeeCategory {
	_insServiceFeeCategory := insServiceFeeCategory{}

	_insServiceFeeCategory.insServiceFeeCategoryDo.UseDB(db, opts...)
	_insServiceFeeCategory.insServiceFeeCategoryDo.UseModel(&insbuy.InsServiceFeeCategory{})

	tableName := _insServiceFeeCategory.insServiceFeeCategoryDo.TableName()
	_insServiceFeeCategory.ALL = field.NewAsterisk(tableName)
	_insServiceFeeCategory.ID = field.NewUint(tableName, "id")
	_insServiceFeeCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeCategory.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeCategory.CategoryId = field.NewUint(tableName, "category_id")
	_insServiceFeeCategory.DiscountRate = field.NewFloat64(tableName, "discount_rate")

	_insServiceFeeCategory.fillFieldMap()

	return _insServiceFeeCategory
}

type insServiceFeeCategory struct {
	insServiceFeeCategoryDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ServiceFeeId field.Uint
	CategoryId   field.Uint
	DiscountRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insServiceFeeCategory) Table(newTableName string) *insServiceFeeCategory {
	i.insServiceFeeCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeCategory) As(alias string) *insServiceFeeCategory {
	i.insServiceFeeCategoryDo.DO = *(i.insServiceFeeCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeCategory) updateTableName(table string) *insServiceFeeCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.CategoryId = field.NewUint(table, "category_id")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["discount_rate"] = i.DiscountRate
}

func (i insServiceFeeCategory) clone(db *gorm.DB) insServiceFeeCategory {
	i.insServiceFeeCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeCategory) replaceDB(db *gorm.DB) insServiceFeeCategory {
	i.insServiceFeeCategoryDo.ReplaceDB(db)
	return i
}

type insServiceFeeCategoryDo struct{ gen.DO }

func (i insServiceFeeCategoryDo) Debug() *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeCategoryDo) WithContext(ctx context.Context) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeCategoryDo) ReadDB() *insServiceFeeCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeCategoryDo) WriteDB() *insServiceFeeCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeCategoryDo) Session(config *gorm.Session) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeCategoryDo) Clauses(conds ...clause.Expression) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeCategoryDo) Returning(value interface{}, columns ...string) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeCategoryDo) Not(conds ...gen.Condition) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeCategoryDo) Or(conds ...gen.Condition) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeCategoryDo) Select(conds ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeCategoryDo) Where(conds ...gen.Condition) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeCategoryDo) Order(conds ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeCategoryDo) Distinct(cols ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeCategoryDo) Omit(cols ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeCategoryDo) Group(cols ...field.Expr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeCategoryDo) Having(conds ...gen.Condition) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeCategoryDo) Limit(limit int) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeCategoryDo) Offset(offset int) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeCategoryDo) Unscoped() *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeCategoryDo) Create(values ...*insbuy.InsServiceFeeCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeCategoryDo) CreateInBatches(values []*insbuy.InsServiceFeeCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeCategoryDo) Save(values ...*insbuy.InsServiceFeeCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeCategoryDo) First() (*insbuy.InsServiceFeeCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeCategory), nil
	}
}

func (i insServiceFeeCategoryDo) Take() (*insbuy.InsServiceFeeCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeCategory), nil
	}
}

func (i insServiceFeeCategoryDo) Last() (*insbuy.InsServiceFeeCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeCategory), nil
	}
}

func (i insServiceFeeCategoryDo) Find() ([]*insbuy.InsServiceFeeCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeCategory), err
}

func (i insServiceFeeCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeCategory, err error) {
	buf := make([]*insbuy.InsServiceFeeCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeCategoryDo) FindInBatches(result *[]*insbuy.InsServiceFeeCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeCategoryDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeCategoryDo) Assign(attrs ...field.AssignExpr) *insServiceFeeCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeCategoryDo) Joins(fields ...field.RelationField) *insServiceFeeCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeCategoryDo) Preload(fields ...field.RelationField) *insServiceFeeCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeCategoryDo) FirstOrInit() (*insbuy.InsServiceFeeCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeCategory), nil
	}
}

func (i insServiceFeeCategoryDo) FirstOrCreate() (*insbuy.InsServiceFeeCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeCategory), nil
	}
}

func (i insServiceFeeCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeCategoryDo) Delete(models ...*insbuy.InsServiceFeeCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeCategoryDo) withDO(do gen.Dao) *insServiceFeeCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
