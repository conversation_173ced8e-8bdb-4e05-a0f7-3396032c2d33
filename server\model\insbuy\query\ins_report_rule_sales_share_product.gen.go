// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareProduct(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareProduct {
	_insReportRuleSalesShareProduct := insReportRuleSalesShareProduct{}

	_insReportRuleSalesShareProduct.insReportRuleSalesShareProductDo.UseDB(db, opts...)
	_insReportRuleSalesShareProduct.insReportRuleSalesShareProductDo.UseModel(&insbuy.InsReportRuleSalesShareProduct{})

	tableName := _insReportRuleSalesShareProduct.insReportRuleSalesShareProductDo.TableName()
	_insReportRuleSalesShareProduct.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareProduct.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareProduct.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareProduct.ProductId = field.NewUint(tableName, "product_id")
	_insReportRuleSalesShareProduct.ShareModel = field.NewInt(tableName, "share_model")
	_insReportRuleSalesShareProduct.ShareParam = field.NewFloat64(tableName, "share_param")
	_insReportRuleSalesShareProduct.Dict = field.NewUint(tableName, "dict")
	_insReportRuleSalesShareProduct.Product = insReportRuleSalesShareProductBelongsToProduct{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Product", "insbuy.InsProduct"),
		Details: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Product.Details", "insbuy.InsProductDetails"),
		},
	}

	_insReportRuleSalesShareProduct.fillFieldMap()

	return _insReportRuleSalesShareProduct
}

type insReportRuleSalesShareProduct struct {
	insReportRuleSalesShareProductDo

	ALL        field.Asterisk
	ID         field.Uint
	RuleId     field.Uint
	ProductId  field.Uint
	ShareModel field.Int
	ShareParam field.Float64
	Dict       field.Uint
	Product    insReportRuleSalesShareProductBelongsToProduct

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareProduct) Table(newTableName string) *insReportRuleSalesShareProduct {
	i.insReportRuleSalesShareProductDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareProduct) As(alias string) *insReportRuleSalesShareProduct {
	i.insReportRuleSalesShareProductDo.DO = *(i.insReportRuleSalesShareProductDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareProduct) updateTableName(table string) *insReportRuleSalesShareProduct {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.ShareModel = field.NewInt(table, "share_model")
	i.ShareParam = field.NewFloat64(table, "share_param")
	i.Dict = field.NewUint(table, "dict")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareProduct) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["share_model"] = i.ShareModel
	i.fieldMap["share_param"] = i.ShareParam
	i.fieldMap["dict"] = i.Dict

}

func (i insReportRuleSalesShareProduct) clone(db *gorm.DB) insReportRuleSalesShareProduct {
	i.insReportRuleSalesShareProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareProduct) replaceDB(db *gorm.DB) insReportRuleSalesShareProduct {
	i.insReportRuleSalesShareProductDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareProductBelongsToProduct struct {
	db *gorm.DB

	field.RelationField

	Details struct {
		field.RelationField
	}
}

func (a insReportRuleSalesShareProductBelongsToProduct) Where(conds ...field.Expr) *insReportRuleSalesShareProductBelongsToProduct {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareProductBelongsToProduct) WithContext(ctx context.Context) *insReportRuleSalesShareProductBelongsToProduct {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareProductBelongsToProduct) Session(session *gorm.Session) *insReportRuleSalesShareProductBelongsToProduct {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareProductBelongsToProduct) Model(m *insbuy.InsReportRuleSalesShareProduct) *insReportRuleSalesShareProductBelongsToProductTx {
	return &insReportRuleSalesShareProductBelongsToProductTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareProductBelongsToProductTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareProductBelongsToProductTx) Find() (result *insbuy.InsProduct, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareProductBelongsToProductTx) Append(values ...*insbuy.InsProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareProductBelongsToProductTx) Replace(values ...*insbuy.InsProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareProductBelongsToProductTx) Delete(values ...*insbuy.InsProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareProductBelongsToProductTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareProductBelongsToProductTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareProductDo struct{ gen.DO }

func (i insReportRuleSalesShareProductDo) Debug() *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareProductDo) WithContext(ctx context.Context) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareProductDo) ReadDB() *insReportRuleSalesShareProductDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareProductDo) WriteDB() *insReportRuleSalesShareProductDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareProductDo) Session(config *gorm.Session) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareProductDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareProductDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareProductDo) Not(conds ...gen.Condition) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareProductDo) Or(conds ...gen.Condition) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareProductDo) Select(conds ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareProductDo) Where(conds ...gen.Condition) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareProductDo) Order(conds ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareProductDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareProductDo) Omit(cols ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareProductDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareProductDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareProductDo) Group(cols ...field.Expr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareProductDo) Having(conds ...gen.Condition) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareProductDo) Limit(limit int) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareProductDo) Offset(offset int) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareProductDo) Unscoped() *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareProductDo) Create(values ...*insbuy.InsReportRuleSalesShareProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareProductDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareProduct, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareProductDo) Save(values ...*insbuy.InsReportRuleSalesShareProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareProductDo) First() (*insbuy.InsReportRuleSalesShareProduct, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProduct), nil
	}
}

func (i insReportRuleSalesShareProductDo) Take() (*insbuy.InsReportRuleSalesShareProduct, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProduct), nil
	}
}

func (i insReportRuleSalesShareProductDo) Last() (*insbuy.InsReportRuleSalesShareProduct, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProduct), nil
	}
}

func (i insReportRuleSalesShareProductDo) Find() ([]*insbuy.InsReportRuleSalesShareProduct, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareProduct), err
}

func (i insReportRuleSalesShareProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareProduct, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareProduct, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareProductDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareProduct, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareProductDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareProductDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareProductDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareProductDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareProductDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareProductDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareProduct, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProduct), nil
	}
}

func (i insReportRuleSalesShareProductDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareProduct, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareProduct), nil
	}
}

func (i insReportRuleSalesShareProductDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareProduct, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareProductDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareProductDo) Delete(models ...*insbuy.InsReportRuleSalesShareProduct) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareProductDo) withDO(do gen.Dao) *insReportRuleSalesShareProductDo {
	i.DO = *do.(*gen.DO)
	return i
}
