// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsNotesDetails(db *gorm.DB, opts ...gen.DOOption) insNotesDetails {
	_insNotesDetails := insNotesDetails{}

	_insNotesDetails.insNotesDetailsDo.UseDB(db, opts...)
	_insNotesDetails.insNotesDetailsDo.UseModel(&insbuy.InsNotesDetails{})

	tableName := _insNotesDetails.insNotesDetailsDo.TableName()
	_insNotesDetails.ALL = field.NewAsterisk(tableName)
	_insNotesDetails.ID = field.NewUint(tableName, "id")
	_insNotesDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insNotesDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insNotesDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insNotesDetails.Label = field.NewString(tableName, "label")
	_insNotesDetails.Sort = field.NewUint(tableName, "sort")
	_insNotesDetails.NotesId = field.NewInt(tableName, "notes_id")
	_insNotesDetails.OperatorId = field.NewUint(tableName, "operator_id")

	_insNotesDetails.fillFieldMap()

	return _insNotesDetails
}

type insNotesDetails struct {
	insNotesDetailsDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	Label      field.String
	Sort       field.Uint
	NotesId    field.Int
	OperatorId field.Uint

	fieldMap map[string]field.Expr
}

func (i insNotesDetails) Table(newTableName string) *insNotesDetails {
	i.insNotesDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insNotesDetails) As(alias string) *insNotesDetails {
	i.insNotesDetailsDo.DO = *(i.insNotesDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insNotesDetails) updateTableName(table string) *insNotesDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Label = field.NewString(table, "label")
	i.Sort = field.NewUint(table, "sort")
	i.NotesId = field.NewInt(table, "notes_id")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insNotesDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insNotesDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["label"] = i.Label
	i.fieldMap["sort"] = i.Sort
	i.fieldMap["notes_id"] = i.NotesId
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insNotesDetails) clone(db *gorm.DB) insNotesDetails {
	i.insNotesDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insNotesDetails) replaceDB(db *gorm.DB) insNotesDetails {
	i.insNotesDetailsDo.ReplaceDB(db)
	return i
}

type insNotesDetailsDo struct{ gen.DO }

func (i insNotesDetailsDo) Debug() *insNotesDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insNotesDetailsDo) WithContext(ctx context.Context) *insNotesDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insNotesDetailsDo) ReadDB() *insNotesDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insNotesDetailsDo) WriteDB() *insNotesDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insNotesDetailsDo) Session(config *gorm.Session) *insNotesDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insNotesDetailsDo) Clauses(conds ...clause.Expression) *insNotesDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insNotesDetailsDo) Returning(value interface{}, columns ...string) *insNotesDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insNotesDetailsDo) Not(conds ...gen.Condition) *insNotesDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insNotesDetailsDo) Or(conds ...gen.Condition) *insNotesDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insNotesDetailsDo) Select(conds ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insNotesDetailsDo) Where(conds ...gen.Condition) *insNotesDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insNotesDetailsDo) Order(conds ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insNotesDetailsDo) Distinct(cols ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insNotesDetailsDo) Omit(cols ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insNotesDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insNotesDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insNotesDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insNotesDetailsDo) Group(cols ...field.Expr) *insNotesDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insNotesDetailsDo) Having(conds ...gen.Condition) *insNotesDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insNotesDetailsDo) Limit(limit int) *insNotesDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insNotesDetailsDo) Offset(offset int) *insNotesDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insNotesDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insNotesDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insNotesDetailsDo) Unscoped() *insNotesDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insNotesDetailsDo) Create(values ...*insbuy.InsNotesDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insNotesDetailsDo) CreateInBatches(values []*insbuy.InsNotesDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insNotesDetailsDo) Save(values ...*insbuy.InsNotesDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insNotesDetailsDo) First() (*insbuy.InsNotesDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotesDetails), nil
	}
}

func (i insNotesDetailsDo) Take() (*insbuy.InsNotesDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotesDetails), nil
	}
}

func (i insNotesDetailsDo) Last() (*insbuy.InsNotesDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotesDetails), nil
	}
}

func (i insNotesDetailsDo) Find() ([]*insbuy.InsNotesDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsNotesDetails), err
}

func (i insNotesDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsNotesDetails, err error) {
	buf := make([]*insbuy.InsNotesDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insNotesDetailsDo) FindInBatches(result *[]*insbuy.InsNotesDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insNotesDetailsDo) Attrs(attrs ...field.AssignExpr) *insNotesDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insNotesDetailsDo) Assign(attrs ...field.AssignExpr) *insNotesDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insNotesDetailsDo) Joins(fields ...field.RelationField) *insNotesDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insNotesDetailsDo) Preload(fields ...field.RelationField) *insNotesDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insNotesDetailsDo) FirstOrInit() (*insbuy.InsNotesDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotesDetails), nil
	}
}

func (i insNotesDetailsDo) FirstOrCreate() (*insbuy.InsNotesDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsNotesDetails), nil
	}
}

func (i insNotesDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsNotesDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insNotesDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insNotesDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insNotesDetailsDo) Delete(models ...*insbuy.InsNotesDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insNotesDetailsDo) withDO(do gen.Dao) *insNotesDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
