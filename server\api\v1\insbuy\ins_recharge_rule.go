package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsRechargeRuleApi struct {
}

// CreateInsRechargeRule 创建InsRechargeRule
// @Tags InsRechargeRule
// @Summary 创建InsRechargeRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsRechargeRuleReq true "创建InsRechargeRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insRechargeRule/createInsRechargeRule [post]
func (insRechargeRuleApi *InsRechargeRuleApi) CreateInsRechargeRule(c *gin.Context) {
	var insRechargeRule insbuyReq.CreateInsRechargeRuleReq
	err := c.ShouldBindJSON(&insRechargeRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	insRechargeRuleService.UserId = utils.GetUserID(c)
	insRechargeRuleService.StoreId = utils.GetHeaderStoreIdString(c)
	if err := insRechargeRuleService.CreateInsRechargeRule(insRechargeRule); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsRechargeRule 删除InsRechargeRule
// @Tags InsRechargeRule
// @Summary 删除InsRechargeRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsRechargeRule true "删除InsRechargeRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insRechargeRule/deleteInsRechargeRule [delete]
func (insRechargeRuleApi *InsRechargeRuleApi) DeleteInsRechargeRule(c *gin.Context) {
	var insRechargeRule insbuy.InsRechargeRule
	err := c.ShouldBindJSON(&insRechargeRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insRechargeRuleService.DeleteInsRechargeRule(insRechargeRule); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsRechargeRuleByIds 批量删除InsRechargeRule
// @Tags InsRechargeRule
// @Summary 批量删除InsRechargeRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsRechargeRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insRechargeRule/deleteInsRechargeRuleByIds [delete]
func (insRechargeRuleApi *InsRechargeRuleApi) DeleteInsRechargeRuleByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insRechargeRuleService.DeleteInsRechargeRuleByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsRechargeRule 更新InsRechargeRule
// @Tags InsRechargeRule
// @Summary 更新InsRechargeRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsRechargeRuleReq true "更新InsRechargeRule"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insRechargeRule/updateInsRechargeRule [put]
func (insRechargeRuleApi *InsRechargeRuleApi) UpdateInsRechargeRule(c *gin.Context) {
	var insRechargeRule insbuyReq.UpdateInsRechargeRuleReq
	err := c.ShouldBindJSON(&insRechargeRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insRechargeRuleService.UpdateInsRechargeRule(insRechargeRule); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsRechargeRule 用id查询InsRechargeRule
// @Tags InsRechargeRule
// @Summary 用id查询InsRechargeRule
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsRechargeRuleReq true "用id查询InsRechargeRule"
// @Success 200 {object}  response.Response{data=insbuyResp.GetInsRechargeRuleInfoDetails,msg=string}  "分页获取InsRechargeRule列表"
// @Router /insRechargeRule/findInsRechargeRule [get]
func (insRechargeRuleApi *InsRechargeRuleApi) FindInsRechargeRule(c *gin.Context) {
	var insRechargeRule insbuyReq.InsRechargeRuleReq
	err := c.ShouldBindQuery(&insRechargeRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsRechargeRule, err := insRechargeRuleService.GetInsRechargeRule(insRechargeRule.RuleId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsRechargeRule, c)
	}
}

// GetInsRechargeRuleList 分页获取InsRechargeRule列表
// @Tags InsRechargeRule
// @Summary 分页获取InsRechargeRule列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsRechargeRuleSearch true "分页获取InsRechargeRule列表"
// @Success 200 {object}  response.Response{data=insbuyResp.GetInsRechargeRuleInfoItem,msg=string}  "分页获取InsRechargeRule列表"
// @Router /insRechargeRule/getInsRechargeRuleList [get]
func (insRechargeRuleApi *InsRechargeRuleApi) GetInsRechargeRuleList(c *gin.Context) {
	var pageInfo insbuyReq.InsRechargeRuleSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list := make([]insbuyResp.GetInsRechargeRuleInfoItem, 0)
	var total int64
	if list, total, err = insRechargeRuleService.GetInsRechargeRuleInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetClientRechargeRuleList 分页获取InsRechargeRule列表,
// @Tags InsRechargeRule
// @Summary 分页获取InsRechargeRule列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetClientRechargeRuleReq true "分页获取InsRechargeRule列表"
// @Success 200 {object}  response.Response{data=insbuyResp.GetClientRechargeRuleListResp,msg=string}  "分页获取InsRechargeRule列表"
// @Router /insRechargeRule/getClientRechargeRuleList [get]
func (insRechargeRuleApi *InsRechargeRuleApi) GetClientRechargeRuleList(c *gin.Context) {
	var req insbuyReq.GetClientRechargeRuleReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insRechargeRuleService.GetClientRechargeRuleList(req); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(list, c)
	}
}
