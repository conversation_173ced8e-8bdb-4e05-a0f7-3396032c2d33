// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsLangMap(db *gorm.DB, opts ...gen.DOOption) insLangMap {
	_insLangMap := insLangMap{}

	_insLangMap.insLangMapDo.UseDB(db, opts...)
	_insLangMap.insLangMapDo.UseModel(&insbuy.InsLangMap{})

	tableName := _insLangMap.insLangMapDo.TableName()
	_insLangMap.ALL = field.NewAsterisk(tableName)
	_insLangMap.ID = field.NewUint(tableName, "id")
	_insLangMap.CreatedAt = field.NewTime(tableName, "created_at")
	_insLangMap.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insLangMap.DeletedAt = field.NewField(tableName, "deleted_at")
	_insLangMap.Hash = field.NewString(tableName, "hash")
	_insLangMap.Source = field.NewString(tableName, "source")
	_insLangMap.Target = field.NewString(tableName, "target")
	_insLangMap.Lang = field.NewString(tableName, "lang")

	_insLangMap.fillFieldMap()

	return _insLangMap
}

type insLangMap struct {
	insLangMapDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Hash      field.String
	Source    field.String
	Target    field.String
	Lang      field.String

	fieldMap map[string]field.Expr
}

func (i insLangMap) Table(newTableName string) *insLangMap {
	i.insLangMapDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insLangMap) As(alias string) *insLangMap {
	i.insLangMapDo.DO = *(i.insLangMapDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insLangMap) updateTableName(table string) *insLangMap {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Hash = field.NewString(table, "hash")
	i.Source = field.NewString(table, "source")
	i.Target = field.NewString(table, "target")
	i.Lang = field.NewString(table, "lang")

	i.fillFieldMap()

	return i
}

func (i *insLangMap) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insLangMap) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["hash"] = i.Hash
	i.fieldMap["source"] = i.Source
	i.fieldMap["target"] = i.Target
	i.fieldMap["lang"] = i.Lang
}

func (i insLangMap) clone(db *gorm.DB) insLangMap {
	i.insLangMapDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insLangMap) replaceDB(db *gorm.DB) insLangMap {
	i.insLangMapDo.ReplaceDB(db)
	return i
}

type insLangMapDo struct{ gen.DO }

func (i insLangMapDo) Debug() *insLangMapDo {
	return i.withDO(i.DO.Debug())
}

func (i insLangMapDo) WithContext(ctx context.Context) *insLangMapDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insLangMapDo) ReadDB() *insLangMapDo {
	return i.Clauses(dbresolver.Read)
}

func (i insLangMapDo) WriteDB() *insLangMapDo {
	return i.Clauses(dbresolver.Write)
}

func (i insLangMapDo) Session(config *gorm.Session) *insLangMapDo {
	return i.withDO(i.DO.Session(config))
}

func (i insLangMapDo) Clauses(conds ...clause.Expression) *insLangMapDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insLangMapDo) Returning(value interface{}, columns ...string) *insLangMapDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insLangMapDo) Not(conds ...gen.Condition) *insLangMapDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insLangMapDo) Or(conds ...gen.Condition) *insLangMapDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insLangMapDo) Select(conds ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insLangMapDo) Where(conds ...gen.Condition) *insLangMapDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insLangMapDo) Order(conds ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insLangMapDo) Distinct(cols ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insLangMapDo) Omit(cols ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insLangMapDo) Join(table schema.Tabler, on ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insLangMapDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insLangMapDo) RightJoin(table schema.Tabler, on ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insLangMapDo) Group(cols ...field.Expr) *insLangMapDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insLangMapDo) Having(conds ...gen.Condition) *insLangMapDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insLangMapDo) Limit(limit int) *insLangMapDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insLangMapDo) Offset(offset int) *insLangMapDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insLangMapDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insLangMapDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insLangMapDo) Unscoped() *insLangMapDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insLangMapDo) Create(values ...*insbuy.InsLangMap) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insLangMapDo) CreateInBatches(values []*insbuy.InsLangMap, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insLangMapDo) Save(values ...*insbuy.InsLangMap) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insLangMapDo) First() (*insbuy.InsLangMap, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangMap), nil
	}
}

func (i insLangMapDo) Take() (*insbuy.InsLangMap, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangMap), nil
	}
}

func (i insLangMapDo) Last() (*insbuy.InsLangMap, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangMap), nil
	}
}

func (i insLangMapDo) Find() ([]*insbuy.InsLangMap, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsLangMap), err
}

func (i insLangMapDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsLangMap, err error) {
	buf := make([]*insbuy.InsLangMap, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insLangMapDo) FindInBatches(result *[]*insbuy.InsLangMap, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insLangMapDo) Attrs(attrs ...field.AssignExpr) *insLangMapDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insLangMapDo) Assign(attrs ...field.AssignExpr) *insLangMapDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insLangMapDo) Joins(fields ...field.RelationField) *insLangMapDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insLangMapDo) Preload(fields ...field.RelationField) *insLangMapDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insLangMapDo) FirstOrInit() (*insbuy.InsLangMap, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangMap), nil
	}
}

func (i insLangMapDo) FirstOrCreate() (*insbuy.InsLangMap, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangMap), nil
	}
}

func (i insLangMapDo) FindByPage(offset int, limit int) (result []*insbuy.InsLangMap, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insLangMapDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insLangMapDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insLangMapDo) Delete(models ...*insbuy.InsLangMap) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insLangMapDo) withDO(do gen.Dao) *insLangMapDo {
	i.DO = *do.(*gen.DO)
	return i
}
