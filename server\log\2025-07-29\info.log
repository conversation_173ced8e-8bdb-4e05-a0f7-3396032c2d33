[insbuy]2025/07/29 - 13:25:40.816	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 13:25:40.890	[34minfo[0m	insbuy/ins_contract_sync.go:459	开始执行增量同步定时任务	{"traceId": "a220e315a46b3c7996b25bab71072018", "task": "IncrementalSyncTask"}
[insbuy]2025/07/29 - 13:25:40.890	[34minfo[0m	insbuy/ins_contract_sync.go:116	开始执行增量合同同步任务	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts"}
[insbuy]2025/07/29 - 13:25:40.890	[34minfo[0m	insbuy/ins_contract_config.go:31	获取所有审批代码完成	{"traceId": "32113b60845c794d0be9d64eaed37c13", "task": "GetAllApprovalCodes", "total_codes": 5, "codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5", "0B92F2B5-922F-4570-8A83-489E476FF811", "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "FAFE52D3-6F77-4676-B84A-507C90655149", "A0A75C81-7481-42CB-B180-F42BB1B8AAAE"]}
[insbuy]2025/07/29 - 13:25:40.890	[34minfo[0m	insbuy/ins_contract_sync.go:130	开始同步审批代码	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"}
[insbuy]2025/07/29 - 13:25:40.905	[34minfo[0m	insbuy/ins_contract_sync.go:331	获取最后同步时间成功	{"traceId": "1c6240ece0d1f716279aed5b4785ae4e", "task": "getLastSuccessfulSyncTime", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "last_sync_time": "[insbuy]2025/07/28 - 18:17:22.000", "sync_status": "success"}
[insbuy]2025/07/29 - 13:25:40.905	[34minfo[0m	insbuy/ins_contract_sync.go:158	准备执行增量同步	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "last_sync_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:40.905"}
[insbuy]2025/07/29 - 13:25:40.905	[34minfo[0m	insbuy/ins_contract.go:393	开始多审批代码批量同步合同数据	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100}
[insbuy]2025/07/29 - 13:25:40.906	[34minfo[0m	insbuy/ins_contract.go:431	开始同步审批代码	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1}
[insbuy]2025/07/29 - 13:25:40.906	[34minfo[0m	insbuy/ins_contract.go:574	开始同步单个审批代码	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "start_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:40.905"}
[insbuy]2025/07/29 - 13:25:40.942	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": ""}
[insbuy]2025/07/29 - 13:25:40.942	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": "", "page_size": 100}
[insbuy]2025/07/29 - 13:25:40.942	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "start_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:40.905", "page_size": 100}
[insbuy]2025/07/29 - 13:25:41.358	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "20250729141616BF110BA6BFCEDAB8AACB", "total": 12, "has_more": false}
[insbuy]2025/07/29 - 13:25:41.358	[34minfo[0m	insbuy/ins_contract.go:737	成功获取合同列表	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 12, "has_more": false}
[insbuy]2025/07/29 - 13:25:41.358	[34minfo[0m	insbuy/ins_contract.go:747	开始批量获取合同详情	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 12}
[insbuy]2025/07/29 - 13:25:41.358	[34minfo[0m	insfinance/contract.go:264	开始获取飞书合同详情	{"instance_codes": ["C29040EB-61ED-42EE-800D-B5FB3D182434", "1A8F5713-8DA6-4934-8A62-F4467DFD6019", "0333DE84-02CD-4237-AA1E-8E37A1B4C2FF", "D01FE0E2-5AC2-4324-ABE5-D9477B11C08F", "6E19F808-2237-4CC0-A8EB-ED3EB7F1F947", "F7882391-36DF-49A5-BD98-DEFC17C5976B", "33DC19A9-3B6D-4269-A752-EAE7E56B4B7C", "5A3A7F3B-B999-4420-B6F2-4D86F78F2EEF", "5A0E60E7-594F-4056-A278-EC27FEB177D8", "8630D0B7-9900-4FB6-BEBC-EF192CB6113E", "9A562293-9412-4E69-AB3B-18B3519377F4", "8313D860-F5EA-4416-AC6A-04829FB18278"], "count": 12}
[insbuy]2025/07/29 - 13:25:48.443	[34minfo[0m	insfinance/contract.go:311	批量获取飞书合同详情完成	{"total_success": 12, "total_failed": 0, "overall_success": true}
[insbuy]2025/07/29 - 13:25:48.444	[34minfo[0m	insbuy/ins_contract.go:760	批量获取合同详情完成	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "success_count": 12, "failed_count": 0}
[insbuy]2025/07/29 - 13:25:48.444	[34minfo[0m	insbuy/ins_contract.go:773	开始批量保存合同数据	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "contract_count": 12}
[insbuy]2025/07/29 - 13:25:48.444	[34minfo[0m	insbuy/ins_contract.go:810	开始批量保存合同数据	{"traceId": "5780f64d230ddac1f29c5b1bf9fb153d", "task": "saveContractDataBatch", "contract_count": 12}
[insbuy]2025/07/29 - 13:25:49.262	[34minfo[0m	insbuy/ins_contract.go:908	批量保存合同数据完成	{"traceId": "5780f64d230ddac1f29c5b1bf9fb153d", "task": "saveContractDataBatch", "contract_count": 12, "new_count": 12, "update_count": 0, "fail_count": 0, "error_count": 0}
[insbuy]2025/07/29 - 13:25:49.262	[34minfo[0m	insbuy/ins_contract.go:783	批量保存合同数据完成	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "new_count": 12, "update_count": 0, "fail_count": 0}
[insbuy]2025/07/29 - 13:25:49.262	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "record_count": 12, "success_count": 12, "failed_count": 0, "has_more": false, "page_duration": 8.3201344}
[insbuy]2025/07/29 - 13:25:49.300	[34minfo[0m	insbuy/ins_contract.go:653	单个审批代码同步完成	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "total_pages": 1, "processed_pages": 1, "total_records": 12, "new_records": 12, "failed_records": 0, "duration": "8.3564024s", "success": true}
[insbuy]2025/07/29 - 13:25:49.301	[34minfo[0m	insbuy/ins_contract.go:469	多审批代码批量同步完成	{"traceId": "a68cd219d158ac5d61571d55620e97f0", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "total_codes": 1, "success_codes": 1, "failed_codes": 0, "total_records": 12, "new_records": 12, "updated_records": 0, "failed_records": 0, "duration": "8.3957436s", "overall_success": true}
[insbuy]2025/07/29 - 13:25:49.301	[34minfo[0m	insbuy/ins_contract_sync.go:179	审批代码增量同步完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "new_records": 12, "updated_records": 0}
[insbuy]2025/07/29 - 13:25:49.301	[34minfo[0m	insbuy/ins_contract_sync.go:130	开始同步审批代码	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/07/29 - 13:25:49.313	[34minfo[0m	insbuy/ins_contract_sync.go:331	获取最后同步时间成功	{"traceId": "2aa2ab41cd059a8cf0d3b052f144c858", "task": "getLastSuccessfulSyncTime", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "last_sync_time": "[insbuy]2025/07/28 - 18:17:22.000", "sync_status": "success"}
[insbuy]2025/07/29 - 13:25:49.313	[34minfo[0m	insbuy/ins_contract_sync.go:158	准备执行增量同步	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "last_sync_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:49.313"}
[insbuy]2025/07/29 - 13:25:49.313	[34minfo[0m	insbuy/ins_contract.go:393	开始多审批代码批量同步合同数据	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100}
[insbuy]2025/07/29 - 13:25:49.313	[34minfo[0m	insbuy/ins_contract.go:431	开始同步审批代码	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1}
[insbuy]2025/07/29 - 13:25:49.313	[34minfo[0m	insbuy/ins_contract.go:574	开始同步单个审批代码	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "start_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:49.313"}
[insbuy]2025/07/29 - 13:25:49.344	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": ""}
[insbuy]2025/07/29 - 13:25:49.344	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": "", "page_size": 100}
[insbuy]2025/07/29 - 13:25:49.344	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "start_time": "[insbuy]2025/07/28 - 18:17:22.000", "end_time": "[insbuy]2025/07/29 - 13:25:49.313", "page_size": 100}
[insbuy]2025/07/29 - 13:25:49.448	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "20250729141624FDA04F2FA9F35DDCB1D9", "total": 6, "has_more": false}
[insbuy]2025/07/29 - 13:25:49.449	[34minfo[0m	insbuy/ins_contract.go:737	成功获取合同列表	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 6, "has_more": false}
[insbuy]2025/07/29 - 13:25:49.449	[34minfo[0m	insbuy/ins_contract.go:747	开始批量获取合同详情	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 6}
[insbuy]2025/07/29 - 13:25:49.449	[34minfo[0m	insfinance/contract.go:264	开始获取飞书合同详情	{"instance_codes": ["55470637-BDF1-4D0E-8CE8-88F30F2FAD95", "99FE9E24-FFD4-49BE-AF50-016A274254CF", "BD2F85A5-F965-450C-88A1-09F2CDCD883D", "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "EBB9DDC0-E516-4E07-8CA7-7A09C6F68D09", "CE24F0EE-39BF-4617-BF68-FCC84F0CDF8F"], "count": 6}
[insbuy]2025/07/29 - 13:25:55.441	[34minfo[0m	insfinance/contract.go:311	批量获取飞书合同详情完成	{"total_success": 6, "total_failed": 0, "overall_success": true}
[insbuy]2025/07/29 - 13:25:55.442	[34minfo[0m	insbuy/ins_contract.go:760	批量获取合同详情完成	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "success_count": 6, "failed_count": 0}
[insbuy]2025/07/29 - 13:25:55.442	[34minfo[0m	insbuy/ins_contract.go:773	开始批量保存合同数据	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "contract_count": 6}
[insbuy]2025/07/29 - 13:25:55.442	[34minfo[0m	insbuy/ins_contract.go:810	开始批量保存合同数据	{"traceId": "26e5899f06afcbc139eddf118e8ca606", "task": "saveContractDataBatch", "contract_count": 6}
[insbuy]2025/07/29 - 13:25:55.816	[34minfo[0m	insbuy/ins_contract.go:908	批量保存合同数据完成	{"traceId": "26e5899f06afcbc139eddf118e8ca606", "task": "saveContractDataBatch", "contract_count": 6, "new_count": 6, "update_count": 0, "fail_count": 0, "error_count": 0}
[insbuy]2025/07/29 - 13:25:55.817	[34minfo[0m	insbuy/ins_contract.go:783	批量保存合同数据完成	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "new_count": 6, "update_count": 0, "fail_count": 0}
[insbuy]2025/07/29 - 13:25:55.817	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "record_count": 6, "success_count": 6, "failed_count": 0, "has_more": false, "page_duration": 6.4724211}
[insbuy]2025/07/29 - 13:25:55.844	[34minfo[0m	insbuy/ins_contract.go:653	单个审批代码同步完成	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "total_pages": 1, "processed_pages": 1, "total_records": 6, "new_records": 6, "failed_records": 0, "duration": "6.5033043s", "success": true}
[insbuy]2025/07/29 - 13:25:55.845	[34minfo[0m	insbuy/ins_contract.go:469	多审批代码批量同步完成	{"traceId": "a74236290d9d09962b70cb401ceeae0f", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "total_codes": 1, "success_codes": 1, "failed_codes": 0, "total_records": 6, "new_records": 6, "updated_records": 0, "failed_records": 0, "duration": "6.5312636s", "overall_success": true}
[insbuy]2025/07/29 - 13:25:55.845	[34minfo[0m	insbuy/ins_contract_sync.go:179	审批代码增量同步完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "new_records": 6, "updated_records": 0}
[insbuy]2025/07/29 - 13:25:55.845	[34minfo[0m	insbuy/ins_contract_sync.go:130	开始同步审批代码	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8"}
[insbuy]2025/07/29 - 13:25:55.858	[34minfo[0m	insbuy/ins_contract_sync.go:331	获取最后同步时间成功	{"traceId": "e86d0ebc310cf8089de2b5dfea86d405", "task": "getLastSuccessfulSyncTime", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "last_sync_time": "[insbuy]2025/07/28 - 18:17:23.000", "sync_status": "success"}
[insbuy]2025/07/29 - 13:25:55.858	[34minfo[0m	insbuy/ins_contract_sync.go:158	准备执行增量同步	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "last_sync_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:55.858"}
[insbuy]2025/07/29 - 13:25:55.858	[34minfo[0m	insbuy/ins_contract.go:393	开始多审批代码批量同步合同数据	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100}
[insbuy]2025/07/29 - 13:25:55.858	[34minfo[0m	insbuy/ins_contract.go:431	开始同步审批代码	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1}
[insbuy]2025/07/29 - 13:25:55.858	[34minfo[0m	insbuy/ins_contract.go:574	开始同步单个审批代码	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "start_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:55.858"}
[insbuy]2025/07/29 - 13:25:55.886	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": ""}
[insbuy]2025/07/29 - 13:25:55.886	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": "", "page_size": 100}
[insbuy]2025/07/29 - 13:25:55.886	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "start_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:55.858", "page_size": 100}
[insbuy]2025/07/29 - 13:25:55.989	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "202507291416302BDCA401689579AAC600", "total": 1, "has_more": false}
[insbuy]2025/07/29 - 13:25:55.990	[34minfo[0m	insbuy/ins_contract.go:737	成功获取合同列表	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 1, "has_more": false}
[insbuy]2025/07/29 - 13:25:55.990	[34minfo[0m	insbuy/ins_contract.go:747	开始批量获取合同详情	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 1}
[insbuy]2025/07/29 - 13:25:55.990	[34minfo[0m	insfinance/contract.go:264	开始获取飞书合同详情	{"instance_codes": ["3E8C9EFA-382C-402D-9E02-A85AEEE462EB"], "count": 1}
[insbuy]2025/07/29 - 13:25:56.610	[34minfo[0m	insfinance/contract.go:311	批量获取飞书合同详情完成	{"total_success": 1, "total_failed": 0, "overall_success": true}
[insbuy]2025/07/29 - 13:25:56.610	[34minfo[0m	insbuy/ins_contract.go:760	批量获取合同详情完成	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "success_count": 1, "failed_count": 0}
[insbuy]2025/07/29 - 13:25:56.610	[34minfo[0m	insbuy/ins_contract.go:773	开始批量保存合同数据	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "contract_count": 1}
[insbuy]2025/07/29 - 13:25:56.610	[34minfo[0m	insbuy/ins_contract.go:810	开始批量保存合同数据	{"traceId": "81e75d5e493f3cb94b844e0a128674c3", "task": "saveContractDataBatch", "contract_count": 1}
[insbuy]2025/07/29 - 13:25:56.685	[34minfo[0m	insbuy/ins_contract.go:908	批量保存合同数据完成	{"traceId": "81e75d5e493f3cb94b844e0a128674c3", "task": "saveContractDataBatch", "contract_count": 1, "new_count": 1, "update_count": 0, "fail_count": 0, "error_count": 0}
[insbuy]2025/07/29 - 13:25:56.685	[34minfo[0m	insbuy/ins_contract.go:783	批量保存合同数据完成	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "new_count": 1, "update_count": 0, "fail_count": 0}
[insbuy]2025/07/29 - 13:25:56.685	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "record_count": 1, "success_count": 1, "failed_count": 0, "has_more": false, "page_duration": 0.799934}
[insbuy]2025/07/29 - 13:25:56.713	[34minfo[0m	insbuy/ins_contract.go:653	单个审批代码同步完成	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "index": 1, "total": 1, "total_pages": 1, "processed_pages": 1, "total_records": 1, "new_records": 1, "failed_records": 0, "duration": "827.1794ms", "success": true}
[insbuy]2025/07/29 - 13:25:56.713	[34minfo[0m	insbuy/ins_contract.go:469	多审批代码批量同步完成	{"traceId": "40c80977204efe46ed3c5a4f3a8eb089", "task": "SyncMultipleContractData", "approval_codes": ["A5D16C16-42DA-4080-BBE4-E53219EDDEC8"], "total_codes": 1, "batch_size": 20, "page_size": 100, "total_codes": 1, "success_codes": 1, "failed_codes": 0, "total_records": 1, "new_records": 1, "updated_records": 0, "failed_records": 0, "duration": "855.7273ms", "overall_success": true}
[insbuy]2025/07/29 - 13:25:56.713	[34minfo[0m	insbuy/ins_contract_sync.go:179	审批代码增量同步完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A5D16C16-42DA-4080-BBE4-E53219EDDEC8", "new_records": 1, "updated_records": 0}
[insbuy]2025/07/29 - 13:25:56.713	[34minfo[0m	insbuy/ins_contract_sync.go:130	开始同步审批代码	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149"}
[insbuy]2025/07/29 - 13:25:56.732	[34minfo[0m	insbuy/ins_contract_sync.go:331	获取最后同步时间成功	{"traceId": "de884fd564d545e980b2f0bee009f8c1", "task": "getLastSuccessfulSyncTime", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "last_sync_time": "[insbuy]2025/07/28 - 18:17:23.000", "sync_status": "success"}
[insbuy]2025/07/29 - 13:25:56.732	[34minfo[0m	insbuy/ins_contract_sync.go:158	准备执行增量同步	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "last_sync_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:56.732"}
[insbuy]2025/07/29 - 13:25:56.732	[34minfo[0m	insbuy/ins_contract.go:393	开始多审批代码批量同步合同数据	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100}
[insbuy]2025/07/29 - 13:25:56.732	[34minfo[0m	insbuy/ins_contract.go:431	开始同步审批代码	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1}
[insbuy]2025/07/29 - 13:25:56.732	[34minfo[0m	insbuy/ins_contract.go:574	开始同步单个审批代码	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "start_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:56.732"}
[insbuy]2025/07/29 - 13:25:56.762	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": ""}
[insbuy]2025/07/29 - 13:25:56.762	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": "", "page_size": 100}
[insbuy]2025/07/29 - 13:25:56.762	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "start_time": "[insbuy]2025/07/28 - 18:17:23.000", "end_time": "[insbuy]2025/07/29 - 13:25:56.732", "page_size": 100}
[insbuy]2025/07/29 - 13:25:56.857	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "202507291416314ADE8D5F2A8E1EA5526A", "total": 0, "has_more": false}
[insbuy]2025/07/29 - 13:25:56.857	[34minfo[0m	insbuy/ins_contract.go:733	当前页没有合同数据	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": ""}
[insbuy]2025/07/29 - 13:25:56.857	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "record_count": 0, "success_count": 0, "failed_count": 0, "has_more": false, "page_duration": 0.095338}
[insbuy]2025/07/29 - 13:25:56.890	[34minfo[0m	insbuy/ins_contract.go:653	单个审批代码同步完成	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 1, "total": 1, "total_pages": 1, "processed_pages": 1, "total_records": 0, "new_records": 0, "failed_records": 0, "duration": "124.6867ms", "success": true}
[insbuy]2025/07/29 - 13:25:56.891	[34minfo[0m	insbuy/ins_contract.go:469	多审批代码批量同步完成	{"traceId": "6fac89120a9f5b5be0abf160243de349", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "total_codes": 1, "success_codes": 1, "failed_codes": 0, "total_records": 0, "new_records": 0, "updated_records": 0, "failed_records": 0, "duration": "158.7558ms", "overall_success": true}
[insbuy]2025/07/29 - 13:25:56.891	[34minfo[0m	insbuy/ins_contract_sync.go:179	审批代码增量同步完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "new_records": 0, "updated_records": 0}
[insbuy]2025/07/29 - 13:25:56.891	[34minfo[0m	insbuy/ins_contract_sync.go:130	开始同步审批代码	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE"}
[insbuy]2025/07/29 - 13:25:56.906	[34minfo[0m	insbuy/ins_contract_sync.go:322	未找到成功同步记录，使用默认时间	{"traceId": "04a08d8d61f1b185bb04f789c76309f4", "task": "getLastSuccessfulSyncTime", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "default_time": "[insbuy]2025/06/29 - 13:25:56.906"}
[insbuy]2025/07/29 - 13:25:56.906	[34minfo[0m	insbuy/ins_contract_sync.go:158	准备执行增量同步	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "last_sync_time": "[insbuy]2025/06/29 - 13:25:56.906", "end_time": "[insbuy]2025/07/29 - 13:25:56.906"}
[insbuy]2025/07/29 - 13:25:56.906	[34minfo[0m	insbuy/ins_contract.go:393	开始多审批代码批量同步合同数据	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100}
[insbuy]2025/07/29 - 13:25:56.906	[34minfo[0m	insbuy/ins_contract.go:431	开始同步审批代码	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1}
[insbuy]2025/07/29 - 13:25:56.906	[34minfo[0m	insbuy/ins_contract.go:574	开始同步单个审批代码	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "start_time": "[insbuy]2025/06/29 - 13:25:56.906", "end_time": "[insbuy]2025/07/29 - 13:25:56.906"}
[insbuy]2025/07/29 - 13:25:56.936	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": ""}
[insbuy]2025/07/29 - 13:25:56.937	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "page_token": "", "page_size": 100}
[insbuy]2025/07/29 - 13:25:56.937	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "start_time": "[insbuy]2025/06/29 - 13:25:56.906", "end_time": "[insbuy]2025/07/29 - 13:25:56.906", "page_size": 100}
[insbuy]2025/07/29 - 13:25:57.113	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "20250729141631FDA04F2FA9F35DDCB711", "total": 100, "has_more": true}
[insbuy]2025/07/29 - 13:25:57.113	[34minfo[0m	insbuy/ins_contract.go:737	成功获取合同列表	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 100, "has_more": true}
[insbuy]2025/07/29 - 13:25:57.114	[34minfo[0m	insbuy/ins_contract.go:747	开始批量获取合同详情	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "instance_count": 100}
[insbuy]2025/07/29 - 13:25:57.114	[34minfo[0m	insfinance/contract.go:264	开始获取飞书合同详情	{"instance_codes": ["24F1D948-4F16-44C3-BE17-C348A5E4A242", "A3281BB6-A1B8-440F-9AC3-F9B3E0BECB7E", "2ACD5611-5E5E-4BAE-BC3D-F2F2DA7B1FD7", "C75EABBF-FEDA-4425-B1BD-957A9F0A68C6", "AB2C14A6-6C45-4D99-8FBD-43A7347BE9CA", "4EF423F1-520E-490C-9D7E-D21756579E44", "AE69FA2D-71B8-4DA6-A1EA-FE23C5513F25", "82B9395C-C27E-4EB7-960B-05216B2ACD75", "1074B8E6-AEE7-4BE2-A352-C29AB390D042", "1F4A4505-A54C-41E5-951F-379EE591B2A7", "3019F178-7CCF-4919-8987-22F55F3C9DC7", "731498A8-3CA6-4E7D-96D2-244EC43A6418", "A2640DFE-0E5E-4FE3-AA43-C0D4442C6418", "817CF429-8CDB-4AF5-93C7-FD43FCCFA937", "A997B9BC-544B-48CC-93EE-FB296CD14BBF", "62DDC6B5-C7A1-4D53-AA72-DB7C49732909", "F1C78506-3F2B-4018-BF70-4FA68123A011", "A8401203-0DF9-483B-A124-C7ECFD857266", "5B2A74EC-8596-4CC6-8C64-8BD73218CF1C", "835325AA-E619-4321-B1C6-EE3427E087FD", "5C5EF851-044A-4809-B0D4-9C8442912B81", "6AE7B507-01EB-49AC-97C8-07419A5ACD37", "F98B9C14-5885-46B3-ADEF-8DB08E0C14A5", "F0320628-8B90-4BF8-9453-F1707C08E42A", "65D7D27D-3FA4-40EF-A2A3-367995B4D6A0", "9C83A4FA-D1C6-4D7C-AE00-7D86E2B47BA3", "5A8C32D5-7726-4532-9C86-8864EC7B3461", "50A9A8C9-FE69-43C3-AF65-147CA08DCE3D", "2F1CE0E1-D2F3-4CBD-91CC-6A9100EB7134", "ED2953CA-5428-4E11-867B-68194D4102CE", "F6517312-0854-4206-B46F-627329CB7484", "84687496-86EF-40D3-A835-83749C24B908", "60259919-9FFA-49EE-B35F-67B790DFE1CB", "CBF37FB2-159B-407A-8003-64C83D231267", "6A7CF205-A90C-416F-AAEC-81FE35448544", "E9E6FDAE-68D6-4DAA-B9A9-25861A0BEAA5", "B97702D5-AB05-4125-9829-C62E10516D1D", "81AB7627-59A7-4E49-9EBA-2349ED7A0D13", "4F75A760-F7AE-4A52-927B-E527290E8134", "307EE13B-1C8F-4978-87CA-A201ADB5771F", "3B493A18-9030-403E-8DF2-0AB9EFCF2FFF", "FA208E32-F559-4DF7-81CE-085788DD07E7", "16DE0414-0368-4907-98C7-58B5D9843393", "856E1CB0-129A-498E-8C77-9DA77AA1CF35", "10CCD246-2EA6-44A8-B1B9-2F2642D1295B", "AEC4A7E4-47A1-4E50-BC36-477E1EDF7623", "352F04AF-1C26-4019-A28E-60DDE1E77F00", "C3A6EA25-C90E-475B-988A-BDF5FB9270C9", "53247457-4734-486A-A1DC-890E1053F576", "CEF085FB-01ED-417E-A49D-1A2FAE0A0E19", "BF983618-B212-4B62-A1EC-C5C5691B4263", "DF3CCC48-D0E2-4F0B-BAAF-8BDFF13F2616", "16CFB7BD-9154-4211-8261-A3F3077A0867", "A64C26BC-139A-4E73-9784-7F53048B0ECF", "48FFB1DC-18A5-4073-BC73-15FCB9EB1FAF", "C0A6B2CE-6D1B-423C-A9EC-015D3EEF4BFF", "82723D2B-8E0B-4114-AE78-368FE68E1F8A", "4FA1BAFE-A822-4EBB-935A-F48AF7CACEF9", "92073346-6CD7-4F56-854D-203688E2E335", "75140C16-1AB3-4758-BC81-64AC641DA633", "A0E907EC-4863-438D-8021-A28488D6C056", "5AEB03E0-3540-42DB-AA03-959B44B47237", "2EF6E250-7A13-479A-B3D2-998BC7BBFB9A", "E98E3A95-5CEE-4893-A001-69B6A048C958", "D59F064C-551A-49C8-BEC8-30620434D336", "9EECC31A-A0F5-4E66-A75C-0683F82E8B47", "3A06EA86-82D1-415A-B772-E0F59443BC81", "1D44D8EF-1643-4ED5-BD9E-C4CC574C7360", "19A154BB-824D-4271-BE12-08DAAC947D42", "BEA3C468-D08A-4762-B915-513F18AF53C6", "ECCAFDFC-DAA0-4418-8327-3A52D24E7522", "D50063F1-D52C-4E61-B48E-33B7023E2985", "49112A9D-832B-42F9-82E4-1EEFC13DD3A9", "3024EA27-CA0A-4649-8267-136E5F7548AA", "0E780431-1859-425E-AC92-52B99AF0047F", "C0A0E380-DFE7-47AA-A023-8C33C8DC9C28", "75933BD9-848B-4278-B2DA-B4140C472959", "85161F9E-F7AE-49DE-9344-972C7964F677", "EBFD3BE0-9D07-4426-B7C7-03179C7BFB6D", "EDFAF643-BD74-4156-98B1-4A0D4A452323", "D64D9274-170F-459F-8BDA-95105D859366", "BC82BB82-C060-4889-A3EF-D0938CA4A8F2", "B21304D6-DF17-4E9F-BA24-2E06CD45DABA", "5995F137-560E-4AE6-A6A7-3B52E5F5D3DD", "77FC0C54-590C-449D-87B4-783C29C4FB0D", "3DCA75F5-C094-43F6-A91B-FEB6D07EC8F3", "06DB6924-F6D1-4134-B4F3-9F4F9F80DA8F", "216ED2FF-26F5-4F1D-B931-D560E3E5A503", "F5BD98BB-354A-4E31-AD9F-25A86C8ED502", "3CEDCF4B-A296-4D83-9DEB-CFB078146200", "C8143DC8-1DA6-472C-9D20-BBF3CF9564E3", "177C9F94-9CBB-4C68-896D-1F4E4D0839D6", "E9925074-1D96-47B2-AC53-858D43A36685", "CECFFDEC-CCDB-4220-9EFD-52216FA09609", "FB4AFBA0-947F-4469-BC16-A79ADFD9EFA9", "240D5F00-583F-4F50-AA32-0CA2FAED9D6D", "93CACBC3-BE18-4281-9FD8-0DFEF5DA08D8", "5DE6E019-8C08-4B0C-89CB-E1A3C2ACDE9A", "C5295CB1-C952-4982-83EB-D0C5A02286F8", "9371EE14-BBBF-4AB2-BF70-02FF856A1F04"], "count": 100}
[insbuy]2025/07/29 - 13:27:14.712	[34minfo[0m	insfinance/contract.go:311	批量获取飞书合同详情完成	{"total_success": 100, "total_failed": 0, "overall_success": true}
[insbuy]2025/07/29 - 13:27:14.712	[34minfo[0m	insbuy/ins_contract.go:760	批量获取合同详情完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "success_count": 100, "failed_count": 0}
[insbuy]2025/07/29 - 13:27:14.712	[34minfo[0m	insbuy/ins_contract.go:773	开始批量保存合同数据	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "contract_count": 100}
[insbuy]2025/07/29 - 13:27:14.712	[34minfo[0m	insbuy/ins_contract.go:810	开始批量保存合同数据	{"traceId": "4d9946a4e1e83f0d447417095a0fba2d", "task": "saveContractDataBatch", "contract_count": 100}
[insbuy]2025/07/29 - 13:27:29.621	[34minfo[0m	insbuy/ins_contract.go:908	批量保存合同数据完成	{"traceId": "4d9946a4e1e83f0d447417095a0fba2d", "task": "saveContractDataBatch", "contract_count": 100, "new_count": 100, "update_count": 0, "fail_count": 0, "error_count": 0}
[insbuy]2025/07/29 - 13:27:29.621	[34minfo[0m	insbuy/ins_contract.go:783	批量保存合同数据完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "new_count": 100, "update_count": 0, "fail_count": 0}
[insbuy]2025/07/29 - 13:27:29.621	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "record_count": 100, "success_count": 100, "failed_count": 0, "has_more": true, "page_duration": 92.6848639}
[insbuy]2025/07/29 - 13:27:29.825	[34minfo[0m	insbuy/ins_contract.go:601	开始处理分页	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI="}
[insbuy]2025/07/29 - 13:27:29.825	[34minfo[0m	insbuy/ins_contract.go:706	调用飞书API获取合同列表	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "page_token": "IjEwMCI=", "page_size": 100}
[insbuy]2025/07/29 - 13:27:29.825	[34minfo[0m	insfinance/contract.go:188	开始获取飞书合同列表	{"approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "start_time": "[insbuy]2025/06/29 - 13:25:56.906", "end_time": "[insbuy]2025/07/29 - 13:25:56.906", "page_size": 100}
[insbuy]2025/07/29 - 13:27:29.925	[34minfo[0m	insfinance/contract.go:253	成功获取飞书合同列表	{"request_id": "202507291418042BDCA401689579AB0060", "total": 45, "has_more": false}
[insbuy]2025/07/29 - 13:27:29.925	[34minfo[0m	insbuy/ins_contract.go:737	成功获取合同列表	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "instance_count": 45, "has_more": false}
[insbuy]2025/07/29 - 13:27:29.925	[34minfo[0m	insbuy/ins_contract.go:747	开始批量获取合同详情	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "instance_count": 45}
[insbuy]2025/07/29 - 13:27:29.925	[34minfo[0m	insfinance/contract.go:264	开始获取飞书合同详情	{"instance_codes": ["6B1132A6-0909-4937-802E-7AB7A65893F9", "A49F7E8D-7AF6-441C-ABCE-6F0B45557924", "4C2C9E81-9699-4AED-BE10-82E9ADB049F2", "3705E062-3B06-4087-A029-819688749DA9", "48462759-3994-4C38-BE0D-0AB089924B6A", "1ECC1AC3-8BFB-4C27-9620-2A7D85253888", "FFCEF981-D201-48C0-9FD2-7590891EB015", "AC975E70-05E2-4FBD-A326-ED48F4B7D4E6", "87454D4C-149D-429E-AD5B-BC098AE9BE97", "A309806B-42B4-47B0-9F5D-F9C6966D7794", "32E459B9-F334-4C7F-A361-029CF00BF7E8", "07710C13-D949-465B-8DC4-033931654332", "021B7950-1056-4FE0-A0BC-0549B3FBDB41", "62595F4E-3DEF-480C-A079-7DE94256EEA0", "756D5FC9-A368-44C6-9B68-6A559785E1FC", "8226E432-1CD7-4A8C-A721-B330B4F5A8CD", "CB57E31D-4D7B-46FE-BAC3-96673303B729", "6C31000E-A991-4952-B557-9A56D4DC6277", "49C7E96B-2D35-4A53-8ACD-5FB2F46A5D67", "510D10B1-F622-4ED7-9026-063EFCE91E85", "00B84AA6-7F80-4568-B257-39CE7633EAF2", "D0FC41B6-728A-4AC0-8681-A1F91B2E5AD6", "9A23D4AB-D0ED-4E31-943D-D578C340AC90", "C791C770-EAB4-46B4-8D25-2272DE570D81", "DFB95722-B7BB-4F21-B01C-C7C80B81EA7F", "579E84C8-77A3-45DE-9272-BBAE9CEBF21A", "7B78C236-7363-4E0D-A78E-835B27EDF369", "FE90D07E-4238-4531-80B7-C2ADDC86D07A", "7846174F-8991-4281-998A-9CC28B17D702", "258AB802-076D-4B8D-A1BC-669C1299F9B6", "C116D915-8686-4B97-A3A2-F697796514A9", "7AC66EDD-B311-4AFA-8660-688B2E07011C", "D7E9EE77-8116-46C7-8BE4-9B8EC1A218E0", "5964E7DB-111B-4421-9A28-B449D6B09763", "F24AFC52-42CC-4112-840E-B7C78EE8D961", "E76EF589-E6E1-4DEC-A8DD-FA6DE57AF15D", "E62191C0-FC49-4D27-B2AB-789564202255", "37CF6FA5-BE5A-4662-9269-B371AC0F9923", "EB18E7AF-BA91-4984-9DFB-6534E25DD415", "FBBD6006-77CD-4865-BD87-189A40EC5A6F", "FFDD2914-8918-4DDB-B562-A620B27DD784", "6FDEA85E-299B-44AF-9BE3-E4FA3690D2DC", "69DD0CB2-D76B-4169-A163-B5ADBADEA2E4", "62400DB4-18B6-4565-B5E7-BA28D3656D88", "9284B240-0B4E-4974-9232-558CBEE5D94F"], "count": 45}
[insbuy]2025/07/29 - 13:28:03.492	[34minfo[0m	insfinance/contract.go:311	批量获取飞书合同详情完成	{"total_success": 45, "total_failed": 0, "overall_success": true}
[insbuy]2025/07/29 - 13:28:03.492	[34minfo[0m	insbuy/ins_contract.go:760	批量获取合同详情完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "success_count": 45, "failed_count": 0}
[insbuy]2025/07/29 - 13:28:03.492	[34minfo[0m	insbuy/ins_contract.go:773	开始批量保存合同数据	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "contract_count": 45}
[insbuy]2025/07/29 - 13:28:03.492	[34minfo[0m	insbuy/ins_contract.go:810	开始批量保存合同数据	{"traceId": "b774e0709c12414a780014395214377a", "task": "saveContractDataBatch", "contract_count": 45}
[insbuy]2025/07/29 - 13:28:06.554	[34minfo[0m	insbuy/ins_contract.go:908	批量保存合同数据完成	{"traceId": "b774e0709c12414a780014395214377a", "task": "saveContractDataBatch", "contract_count": 45, "new_count": 45, "update_count": 0, "fail_count": 0, "error_count": 0}
[insbuy]2025/07/29 - 13:28:06.555	[34minfo[0m	insbuy/ins_contract.go:783	批量保存合同数据完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "new_count": 45, "update_count": 0, "fail_count": 0}
[insbuy]2025/07/29 - 13:28:06.555	[34minfo[0m	insbuy/ins_contract.go:613	分页处理完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "record_count": 45, "success_count": 45, "failed_count": 0, "has_more": false, "page_duration": 36.7301286}
[insbuy]2025/07/29 - 13:28:06.587	[34minfo[0m	insbuy/ins_contract.go:653	单个审批代码同步完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "index": 1, "total": 1, "total_pages": 2, "processed_pages": 2, "total_records": 145, "new_records": 145, "failed_records": 0, "duration": "2m9.6491266s", "success": true}
[insbuy]2025/07/29 - 13:28:06.588	[34minfo[0m	insbuy/ins_contract.go:469	多审批代码批量同步完成	{"traceId": "98c975c6c8396ce7ce7d4a0f410fceac", "task": "SyncMultipleContractData", "approval_codes": ["A0A75C81-7481-42CB-B180-F42BB1B8AAAE"], "total_codes": 1, "batch_size": 20, "page_size": 100, "total_codes": 1, "success_codes": 1, "failed_codes": 0, "total_records": 145, "new_records": 145, "updated_records": 0, "failed_records": 0, "duration": "2m9.6822266s", "overall_success": true}
[insbuy]2025/07/29 - 13:28:06.588	[34minfo[0m	insbuy/ins_contract_sync.go:179	审批代码增量同步完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "approval_code": "A0A75C81-7481-42CB-B180-F42BB1B8AAAE", "new_records": 145, "updated_records": 0}
[insbuy]2025/07/29 - 13:28:06.588	[34minfo[0m	insbuy/ins_contract_sync.go:187	增量合同同步任务完成	{"traceId": "792119e29e0261edffea6aadea1b2b27", "task": "SyncIncrementalContracts", "total_approval_codes": 5, "total_new_records": 164, "total_updated_records": 0, "error_count": 0, "errors": []}
[insbuy]2025/07/29 - 13:28:06.588	[34minfo[0m	insbuy/ins_contract_sync.go:470	增量同步定时任务完成	{"traceId": "a220e315a46b3c7996b25bab71072018", "task": "IncrementalSyncTask"}
[insbuy]2025/07/29 - 13:39:40.628	[34minfo[0m	initialize/gorm.go:131	register table success
[insbuy]2025/07/29 - 13:39:40.723	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 13:39:50.135	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/29 - 13:39:50.255	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/29 - 13:39:50.268	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/29 - 13:39:50.274	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/29 - 17:55:12.461	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 17:55:12.538	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "fcf5fa52bcd5b90ec816654faa6931f9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 17:55:12.563	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "fcf5fa52bcd5b90ec816654faa6931f9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/29 - 17:55:12.564	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "55119bc186948a8da12504575f5fa452", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/29 - 17:55:12.564	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "55119bc186948a8da12504575f5fa452", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	insbuy/contract_transformer.go:257	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0005585}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0005585, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/29 - 17:55:12.565", "data_version": "1.0"}
[insbuy]2025/07/29 - 17:55:12.565	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/29 - 17:55:12.566	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "fcf5fa52bcd5b90ec816654faa6931f9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:02:45.772	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 18:02:45.847	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "30980106eeea44ecf28fb7fb38eaaab1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:02:45.872	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "30980106eeea44ecf28fb7fb38eaaab1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/29 - 18:02:45.872	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "82cbbf80d46bf2c412cd5c7c9cb0a961", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/29 - 18:02:45.873	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "82cbbf80d46bf2c412cd5c7c9cb0a961", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/29 - 18:02:45.873	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0005347}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0005347, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/29 - 18:02:45.874", "data_version": "1.0"}
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/29 - 18:02:45.874	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "30980106eeea44ecf28fb7fb38eaaab1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:06:37.037	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 18:06:37.112	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "54492e1acd6039289737d97d428cb1d9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:06:37.135	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "54492e1acd6039289737d97d428cb1d9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/29 - 18:06:37.135	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "cb7484477a2c052338fc8c033b3c5f12", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/29 - 18:06:37.136	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "cb7484477a2c052338fc8c033b3c5f12", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:61	成功转换为 insfinance.ContractDetails 格式	{"traceId": "54492e1acd6039289737d97d428cb1d9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0005326}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:161	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:164	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:172	转换结果:	{"success": false, "processing_time": 0.0005326, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:181	转换错误:
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/29 - 18:06:37.141	[34minfo[0m	test/contract_transformer_test.go:183	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:206	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:214	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:220	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:228	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:234	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:239	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/29 - 18:06:37.142	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/29 - 18:06:37.147	[34minfo[0m	test/contract_transformer_test.go:254	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/29 - 18:06:37.141", "data_version": "1.0"}
[insbuy]2025/07/29 - 18:06:37.147	[34minfo[0m	test/contract_transformer_test.go:262	=== 转换测试结果结束 ===
[insbuy]2025/07/29 - 18:06:37.147	[34minfo[0m	test/contract_transformer_test.go:72	数据库数据转换测试完成	{"traceId": "54492e1acd6039289737d97d428cb1d9", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:09:51.127	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/29 - 18:09:51.219	[34minfo[0m	test/contract_transformer_test.go:37	开始从数据库查询合同数据进行转换测试	{"traceId": "5da8946e98174bd38f33ff0c160b61cc", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
[insbuy]2025/07/29 - 18:09:51.242	[34minfo[0m	test/contract_transformer_test.go:46	成功查询到合同数据	{"traceId": "5da8946e98174bd38f33ff0c160b61cc", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "PENDING"}
[insbuy]2025/07/29 - 18:09:51.242	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "81d808e78012a4f80e9d58864d02df8c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/07/29 - 18:09:51.243	[34minfo[0m	insbuy/contract_mapping_config.go:71	映射规则文件加载完成	{"traceId": "81d808e78012a4f80e9d58864d02df8c", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/07/29 - 18:09:51.243	[34minfo[0m	insbuy/contract_transformer.go:199	开始转换合同数据	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL"}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	insbuy/contract_transformer.go:258	合同数据转换完成	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "success": false, "error_count": 5, "warning_count": 0, "processing_time": 0.0005955}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:160	=== 数据库数据转换测试结果 ===
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:163	原始数据库数据:	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_name": "费用报销", "status": "PENDING", "created_at": "[insbuy]2025/07/29 - 13:25:55.663"}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:171	转换结果:	{"success": false, "processing_time": 0.0005955, "error_count": 5, "warning_count": 0}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:180	转换错误:
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 1, "field": "application_number", "message": "申请编号不能为空", "value": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 2, "field": "title", "message": "标题不能为空", "value": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 3, "field": "application_status", "message": "申请状态不能为空", "value": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 4, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:182	错误详情	{"index": 5, "field": "initiator_user_id", "message": "发起人User ID不能为空", "value": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:205	标准化数据 - 基础信息:	{"application_number": "", "title": "", "application_status": "", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:213	标准化数据 - 人员信息:	{"initiator_user_id": "", "initiator_department_id": "", "serial_number": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:219	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "", "payment_currency": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:227	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:233	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:238	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:247	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:253	标准化数据 - 元数据:	{"source_instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "transform_time": "[insbuy]2025/07/29 - 18:09:51.244", "data_version": "1.0"}
[insbuy]2025/07/29 - 18:09:51.244	[34minfo[0m	test/contract_transformer_test.go:261	=== 转换测试结果结束 ===
[insbuy]2025/07/29 - 18:09:51.250	[34minfo[0m	test/contract_transformer_test.go:71	数据库数据转换测试完成	{"traceId": "5da8946e98174bd38f33ff0c160b61cc", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92"}
