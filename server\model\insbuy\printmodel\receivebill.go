package printmodel

import "time"

type TplReceiveBill struct {
	TplBase
	OutWarehouse     string               // 出库仓库
	ReceiveSn        string               // 领用单号
	Receiver         string               //领用人
	ReceiveTime      time.Time            //领用时间
	Items            []TplReceiveBillItem // 商品列表
	OutBoundOperator string               //出库人
}

type TplReceiveBillItem struct {
	Name   string // 商品名称
	Remark string // 备注（可选）
	Num    int    // 规格
	Unit   string // 套
}
