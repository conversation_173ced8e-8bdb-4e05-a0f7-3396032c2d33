// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterialStandard(db *gorm.DB, opts ...gen.DOOption) insMaterialStandard {
	_insMaterialStandard := insMaterialStandard{}

	_insMaterialStandard.insMaterialStandardDo.UseDB(db, opts...)
	_insMaterialStandard.insMaterialStandardDo.UseModel(&insbuy.InsMaterialStandard{})

	tableName := _insMaterialStandard.insMaterialStandardDo.TableName()
	_insMaterialStandard.ALL = field.NewAsterisk(tableName)
	_insMaterialStandard.ID = field.NewUint(tableName, "id")
	_insMaterialStandard.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterialStandard.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterialStandard.DeletedAt = field.NewField(tableName, "deleted_at")
	_insMaterialStandard.StandardMaterialName = field.NewString(tableName, "standard_material_name")
	_insMaterialStandard.StandardMaterialCode = field.NewString(tableName, "standard_material_code")

	_insMaterialStandard.fillFieldMap()

	return _insMaterialStandard
}

type insMaterialStandard struct {
	insMaterialStandardDo

	ALL                  field.Asterisk
	ID                   field.Uint
	CreatedAt            field.Time
	UpdatedAt            field.Time
	DeletedAt            field.Field
	StandardMaterialName field.String
	StandardMaterialCode field.String

	fieldMap map[string]field.Expr
}

func (i insMaterialStandard) Table(newTableName string) *insMaterialStandard {
	i.insMaterialStandardDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterialStandard) As(alias string) *insMaterialStandard {
	i.insMaterialStandardDo.DO = *(i.insMaterialStandardDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterialStandard) updateTableName(table string) *insMaterialStandard {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StandardMaterialName = field.NewString(table, "standard_material_name")
	i.StandardMaterialCode = field.NewString(table, "standard_material_code")

	i.fillFieldMap()

	return i
}

func (i *insMaterialStandard) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterialStandard) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["standard_material_name"] = i.StandardMaterialName
	i.fieldMap["standard_material_code"] = i.StandardMaterialCode
}

func (i insMaterialStandard) clone(db *gorm.DB) insMaterialStandard {
	i.insMaterialStandardDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterialStandard) replaceDB(db *gorm.DB) insMaterialStandard {
	i.insMaterialStandardDo.ReplaceDB(db)
	return i
}

type insMaterialStandardDo struct{ gen.DO }

func (i insMaterialStandardDo) Debug() *insMaterialStandardDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialStandardDo) WithContext(ctx context.Context) *insMaterialStandardDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialStandardDo) ReadDB() *insMaterialStandardDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialStandardDo) WriteDB() *insMaterialStandardDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialStandardDo) Session(config *gorm.Session) *insMaterialStandardDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialStandardDo) Clauses(conds ...clause.Expression) *insMaterialStandardDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialStandardDo) Returning(value interface{}, columns ...string) *insMaterialStandardDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialStandardDo) Not(conds ...gen.Condition) *insMaterialStandardDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialStandardDo) Or(conds ...gen.Condition) *insMaterialStandardDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialStandardDo) Select(conds ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialStandardDo) Where(conds ...gen.Condition) *insMaterialStandardDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialStandardDo) Order(conds ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialStandardDo) Distinct(cols ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialStandardDo) Omit(cols ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialStandardDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialStandardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialStandardDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialStandardDo) Group(cols ...field.Expr) *insMaterialStandardDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialStandardDo) Having(conds ...gen.Condition) *insMaterialStandardDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialStandardDo) Limit(limit int) *insMaterialStandardDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialStandardDo) Offset(offset int) *insMaterialStandardDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialStandardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialStandardDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialStandardDo) Unscoped() *insMaterialStandardDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialStandardDo) Create(values ...*insbuy.InsMaterialStandard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialStandardDo) CreateInBatches(values []*insbuy.InsMaterialStandard, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialStandardDo) Save(values ...*insbuy.InsMaterialStandard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialStandardDo) First() (*insbuy.InsMaterialStandard, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandard), nil
	}
}

func (i insMaterialStandardDo) Take() (*insbuy.InsMaterialStandard, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandard), nil
	}
}

func (i insMaterialStandardDo) Last() (*insbuy.InsMaterialStandard, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandard), nil
	}
}

func (i insMaterialStandardDo) Find() ([]*insbuy.InsMaterialStandard, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterialStandard), err
}

func (i insMaterialStandardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterialStandard, err error) {
	buf := make([]*insbuy.InsMaterialStandard, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialStandardDo) FindInBatches(result *[]*insbuy.InsMaterialStandard, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialStandardDo) Attrs(attrs ...field.AssignExpr) *insMaterialStandardDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialStandardDo) Assign(attrs ...field.AssignExpr) *insMaterialStandardDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialStandardDo) Joins(fields ...field.RelationField) *insMaterialStandardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialStandardDo) Preload(fields ...field.RelationField) *insMaterialStandardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialStandardDo) FirstOrInit() (*insbuy.InsMaterialStandard, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandard), nil
	}
}

func (i insMaterialStandardDo) FirstOrCreate() (*insbuy.InsMaterialStandard, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialStandard), nil
	}
}

func (i insMaterialStandardDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterialStandard, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialStandardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialStandardDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialStandardDo) Delete(models ...*insbuy.InsMaterialStandard) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialStandardDo) withDO(do gen.Dao) *insMaterialStandardDo {
	i.DO = *do.(*gen.DO)
	return i
}
