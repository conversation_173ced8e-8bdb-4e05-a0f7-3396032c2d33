package request

import (
	"github.com/xtulnx/jkit-go/jtime"
	"strconv"
	"strings"
	"time"
)

// PageInfo Paging common input parameter structure
type PageInfo struct {
	Page     int    `json:"page" form:"page"`         // 页码
	PageSize int    `json:"pageSize" form:"pageSize"` // 每页大小
	Keyword  string `json:"keyword" form:"keyword"`   //关键字
}

// GetPageParam
// limit := req.PageSize
//
//	offset := req.PageSize * (req.Page - 1)
//
// 获取 分页 参数
func (req *PageInfo) GetPageParam() (limit, offset int) {
	limit = req.PageSize
	offset = req.PageSize * (req.Page - 1)
	return
}

func (P *PageInfo) IsAllPage() bool {
	return P.Page == 0 && P.PageSize == -1
}

// SetAllPage 设置为不分页
func (P *PageInfo) SetAllPage() {
	P.Page = 0
	P.PageSize = -1
}

type ExportReq struct {
	IsExport int `json:"isExport" form:"isExport"` //是否导出
}

type AllStoreReq struct {
	IsAllStore int `json:"isAllStore" form:"isAllStore"` //是否所有店铺 1 是 0 否
}

func (P AllStoreReq) IsAll() bool {
	return P.IsAllStore == 1
}

// IsNotAll 不是所有店铺
func (P AllStoreReq) IsNotAll() bool {
	return P.IsAllStore == 0
}

// PageReq 分页
type PageReq struct {
	Page     int `form:"page" json:"page" query:"page"  example:"1"`             // 页号，从1开始
	PageSize int `form:"pageSize" json:"pageSize" query:"pageSize" example:"10"` // 分页大小，-1 表示不分页（但可能有上限）
}

func (P *PageReq) IsAllPage() bool {
	return P.Page == 0 && P.PageSize == -1
}

func (P *PageReq) FixPageSize(num, size int) {
	if P.Page <= 0 {
		P.Page = num
	}
	if P.PageSize <= 0 {
		P.PageSize = size
	}
}

// GetById Find by id structure
type GetById struct {
	ID int `json:"id" form:"id"` // 主键ID
}

func (r *GetById) Uint() uint {
	return uint(r.ID)
}

type IdsReq struct {
	Ids []int `json:"ids" form:"ids"`
	//入库 出库 类型
	DepositType int `json:"depositType" form:"depositType"` // 1 存酒 2 取酒
}

// GetAuthorityId Get role by id structure
type GetAuthorityId struct {
	AuthorityId uint `json:"authorityId" form:"authorityId"` // 角色ID
}

type Empty struct{}

// JIDs 用逗号分隔的整数列表
type JIDs string

func NewIDsFromString(v []string) JIDs {
	return JIDs(strings.Join(v, ","))
}
func NewIDsFromUint(v []uint) JIDs {
	ss := make([]string, 0, len(v))
	for _, i := range v {
		ss = append(ss, strconv.FormatUint(uint64(i), 10))
	}
	return JIDs(strings.Join(ss, ","))
}
func NewIDsFromInt(v []int) JIDs {
	ss := make([]string, 0, len(v))
	for _, i := range v {
		ss = append(ss, strconv.FormatUint(uint64(i), 10))
	}
	return JIDs(strings.Join(ss, ","))
}

func (id JIDs) GetIDInt() []int {
	if id == "" {
		return nil
	}
	ss1 := strings.Split(string(id), ",")
	id2 := make([]int, 0, len(ss1))
	for _, s := range ss1 {
		if n, ok := strconv.ParseUint(s, 10, 32); ok == nil {
			id2 = append(id2, int(n))
		}
	}
	return id2
}

func (id JIDs) GetID() []uint {
	if id == "" {
		return nil
	}
	ss1 := strings.Split(string(id), ",")
	id2 := make([]uint, 0, len(ss1))
	for _, s := range ss1 {
		if n, ok := strconv.ParseUint(s, 10, 32); ok == nil {
			id2 = append(id2, uint(n))
		}
	}
	return id2
}

func (id JIDs) GetIDUnique() []uint {
	if id == "" {
		return nil
	}
	ss1 := strings.Split(string(id), ",")
	m1 := make(map[uint]struct{})
	id2 := make([]uint, 0, len(ss1))
	for _, s := range ss1 {
		if s == "" {
			continue
		}
		if n, ok := strconv.ParseUint(s, 10, 32); ok == nil {
			var k = uint(n)
			if _, ok := m1[k]; ok {
				continue
			}
			m1[k] = struct{}{}
			id2 = append(id2, k)
		}
	}
	return id2
}

func (id JIDs) GetTag() []string {
	if id == "" {
		return nil
	}
	ss1 := strings.Split(string(id), ",")
	id2 := make([]string, 0, len(ss1))
	for _, s := range ss1 {
		if s != "" {
			id2 = append(id2, s)
		}
	}
	return id2
}

func (id JIDs) GetTagUnique() []string {
	if id == "" {
		return nil
	}
	ss1 := strings.Split(string(id), ",")
	m1 := make(map[string]struct{})
	id2 := make([]string, 0, len(ss1))
	for _, s := range ss1 {
		if s == "" {
			continue
		}
		if _, ok := m1[s]; ok {
			continue
		}
		m1[s] = struct{}{}
		id2 = append(id2, s)
	}
	return id2
}

type JDates string

// 取日期
func (J JDates) Date() time.Time {
	t1, _ := jtime.StringToDate(string(J))
	if !t1.IsZero() {
		y, m, d := t1.Date()
		t1 = time.Date(y, m, d, 0, 0, 0, 0, t1.Location())
	}
	return t1
}

// 取日期，本地
func (J JDates) DateLocal() time.Time {
	t1, _ := jtime.StringToDate(string(J))
	if !t1.IsZero() {
		t1 = t1.In(time.Local)
		y, m, d := t1.Date()
		t1 = time.Date(y, m, d, 0, 0, 0, 0, time.Local)
	}
	return t1
}

func (R ExportReq) Export() bool {
	return R.IsExport == 1
}

func (R ExportReq) NotExport() bool {
	return R.IsExport != 1
}
