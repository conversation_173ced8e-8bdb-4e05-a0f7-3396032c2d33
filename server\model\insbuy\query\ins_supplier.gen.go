// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSupplier(db *gorm.DB, opts ...gen.DOOption) insSupplier {
	_insSupplier := insSupplier{}

	_insSupplier.insSupplierDo.UseDB(db, opts...)
	_insSupplier.insSupplierDo.UseModel(&insbuy.InsSupplier{})

	tableName := _insSupplier.insSupplierDo.TableName()
	_insSupplier.ALL = field.NewAsterisk(tableName)
	_insSupplier.ID = field.NewUint(tableName, "id")
	_insSupplier.CreatedAt = field.NewTime(tableName, "created_at")
	_insSupplier.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSupplier.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSupplier.Code = field.NewString(tableName, "code")
	_insSupplier.Name = field.NewString(tableName, "name")
	_insSupplier.ContactName = field.NewString(tableName, "contact_name")
	_insSupplier.ContactEmail = field.NewString(tableName, "contact_email")
	_insSupplier.ContactPhone = field.NewString(tableName, "contact_phone")
	_insSupplier.Level = field.NewInt(tableName, "level")
	_insSupplier.Remark = field.NewString(tableName, "remark")
	_insSupplier.Address = field.NewString(tableName, "address")
	_insSupplier.Latitude = field.NewFloat64(tableName, "latitude")
	_insSupplier.Longitude = field.NewFloat64(tableName, "longitude")
	_insSupplier.ExtInfo = field.NewString(tableName, "ext_info")
	_insSupplier.CategoryTag = field.NewString(tableName, "category_tag")

	_insSupplier.fillFieldMap()

	return _insSupplier
}

type insSupplier struct {
	insSupplierDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Code         field.String
	Name         field.String
	ContactName  field.String
	ContactEmail field.String
	ContactPhone field.String
	Level        field.Int
	Remark       field.String
	Address      field.String
	Latitude     field.Float64
	Longitude    field.Float64
	ExtInfo      field.String
	CategoryTag  field.String

	fieldMap map[string]field.Expr
}

func (i insSupplier) Table(newTableName string) *insSupplier {
	i.insSupplierDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSupplier) As(alias string) *insSupplier {
	i.insSupplierDo.DO = *(i.insSupplierDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSupplier) updateTableName(table string) *insSupplier {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Code = field.NewString(table, "code")
	i.Name = field.NewString(table, "name")
	i.ContactName = field.NewString(table, "contact_name")
	i.ContactEmail = field.NewString(table, "contact_email")
	i.ContactPhone = field.NewString(table, "contact_phone")
	i.Level = field.NewInt(table, "level")
	i.Remark = field.NewString(table, "remark")
	i.Address = field.NewString(table, "address")
	i.Latitude = field.NewFloat64(table, "latitude")
	i.Longitude = field.NewFloat64(table, "longitude")
	i.ExtInfo = field.NewString(table, "ext_info")
	i.CategoryTag = field.NewString(table, "category_tag")

	i.fillFieldMap()

	return i
}

func (i *insSupplier) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSupplier) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["code"] = i.Code
	i.fieldMap["name"] = i.Name
	i.fieldMap["contact_name"] = i.ContactName
	i.fieldMap["contact_email"] = i.ContactEmail
	i.fieldMap["contact_phone"] = i.ContactPhone
	i.fieldMap["level"] = i.Level
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["address"] = i.Address
	i.fieldMap["latitude"] = i.Latitude
	i.fieldMap["longitude"] = i.Longitude
	i.fieldMap["ext_info"] = i.ExtInfo
	i.fieldMap["category_tag"] = i.CategoryTag
}

func (i insSupplier) clone(db *gorm.DB) insSupplier {
	i.insSupplierDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSupplier) replaceDB(db *gorm.DB) insSupplier {
	i.insSupplierDo.ReplaceDB(db)
	return i
}

type insSupplierDo struct{ gen.DO }

func (i insSupplierDo) Debug() *insSupplierDo {
	return i.withDO(i.DO.Debug())
}

func (i insSupplierDo) WithContext(ctx context.Context) *insSupplierDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSupplierDo) ReadDB() *insSupplierDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSupplierDo) WriteDB() *insSupplierDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSupplierDo) Session(config *gorm.Session) *insSupplierDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSupplierDo) Clauses(conds ...clause.Expression) *insSupplierDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSupplierDo) Returning(value interface{}, columns ...string) *insSupplierDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSupplierDo) Not(conds ...gen.Condition) *insSupplierDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSupplierDo) Or(conds ...gen.Condition) *insSupplierDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSupplierDo) Select(conds ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSupplierDo) Where(conds ...gen.Condition) *insSupplierDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSupplierDo) Order(conds ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSupplierDo) Distinct(cols ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSupplierDo) Omit(cols ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSupplierDo) Join(table schema.Tabler, on ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSupplierDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSupplierDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSupplierDo) Group(cols ...field.Expr) *insSupplierDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSupplierDo) Having(conds ...gen.Condition) *insSupplierDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSupplierDo) Limit(limit int) *insSupplierDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSupplierDo) Offset(offset int) *insSupplierDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSupplierDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSupplierDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSupplierDo) Unscoped() *insSupplierDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSupplierDo) Create(values ...*insbuy.InsSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSupplierDo) CreateInBatches(values []*insbuy.InsSupplier, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSupplierDo) Save(values ...*insbuy.InsSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSupplierDo) First() (*insbuy.InsSupplier, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSupplier), nil
	}
}

func (i insSupplierDo) Take() (*insbuy.InsSupplier, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSupplier), nil
	}
}

func (i insSupplierDo) Last() (*insbuy.InsSupplier, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSupplier), nil
	}
}

func (i insSupplierDo) Find() ([]*insbuy.InsSupplier, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSupplier), err
}

func (i insSupplierDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSupplier, err error) {
	buf := make([]*insbuy.InsSupplier, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSupplierDo) FindInBatches(result *[]*insbuy.InsSupplier, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSupplierDo) Attrs(attrs ...field.AssignExpr) *insSupplierDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSupplierDo) Assign(attrs ...field.AssignExpr) *insSupplierDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSupplierDo) Joins(fields ...field.RelationField) *insSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSupplierDo) Preload(fields ...field.RelationField) *insSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSupplierDo) FirstOrInit() (*insbuy.InsSupplier, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSupplier), nil
	}
}

func (i insSupplierDo) FirstOrCreate() (*insbuy.InsSupplier, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSupplier), nil
	}
}

func (i insSupplierDo) FindByPage(offset int, limit int) (result []*insbuy.InsSupplier, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSupplierDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSupplierDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSupplierDo) Delete(models ...*insbuy.InsSupplier) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSupplierDo) withDO(do gen.Dao) *insSupplierDo {
	i.DO = *do.(*gen.DO)
	return i
}
