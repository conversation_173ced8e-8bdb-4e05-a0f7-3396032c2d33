package consumedmodel

import (
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
)

type DeskConsumed struct {
	BusinessDay         string        `json:"businessDay"`
	Consumed            jtypes.JPrice `json:"consumed"`            //已消费
	GiveAmount          jtypes.JPrice `json:"giveAmount"`          //赠送金额
	PaidAmount          jtypes.JPrice `json:"paidAmount"`          //已付金额
	WaitAmount          jtypes.JPrice `json:"waitAmount"`          //待付金额
	DiscountFee         jtypes.JPrice `json:"discountFee"`         //优惠金额
	BalancePayAmount    jtypes.JPrice `json:"balancePayAmount"`    //会员支付金额
	CouponFee           jtypes.JPrice `json:"couponFee"`           //优惠券金额
	ServiceFee          jtypes.JPrice `json:"serviceFee"`          //服务费
	ErasePrice          jtypes.JPrice `json:"erasePrice"`          //抹零金额
	PlayerPrice         jtypes.JPrice `json:"playerPrice"`         //头号玩家优惠
	TicketAmount        jtypes.JPrice `json:"ticketAmount"`        //门票金额
	OfflineTicketAmount jtypes.JPrice `json:"offlineTicketAmount"` //线下门票金额
}

// GetDiscountFee 获取优惠总额
func (d *DeskConsumed) GetDiscountFee() jtypes.JPrice {
	return d.DiscountFee + d.ErasePrice
}
