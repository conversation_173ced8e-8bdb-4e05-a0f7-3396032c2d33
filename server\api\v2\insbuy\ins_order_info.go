package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsOrderInfoApi struct {
}

// CreateOrderInfo 创建订单
// @Tags InsOrderInfo
// @Summary 创建订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateOrderReq true "创建订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /v2/insOrderInfo/createOrderInfo [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateOrderInfo(c *gin.Context) {
	var req insbuyReq.CreateOrderReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if resp, err := insOrderInfoService.CreateOrderInfoV2(req); err != nil {
		global.GVA_LOG.Error("创建订单失败!", zap.Error(err))
		response.FailWithMessage("创建订单失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}
