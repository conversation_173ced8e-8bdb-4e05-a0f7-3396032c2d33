package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/render"
	"time"
)

type InsSysKitApi struct {
}

// QRCode 辅助，生成二维码
// @Tags InsSysKit
// @Summary 辅助，生成二维码
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.KitQRCodeReq true "二维码内容"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /kit/qr [get]
func (A *InsSysKitApi) QRCode(c *gin.Context) {
	var req insbuyReq.KitQRCodeReq
	err := c.Bind(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else if req.M == "" {
		response.ResultErr(nil, errno.BadRequest.WithMsg("无效内容"), c)
		return
	}
	if req.Sign != "" {
		//response.FailWithMessage("暂不支持", c)
		b1, err := insbuy.Utils.QRCode(req.M, req.Level, req.Size)
		if err != nil {
			response.ResultErr(nil, err, c)
		} else {
			c.Render(200, render.Data{
				ContentType: "image/png",
				Data:        b1,
			})
		}
	} else {
		b1, err := insSysPrintService.DecodeAndBuildQRCode(req.M)
		if err != nil {
			response.ResultErr(nil, err, c)
		} else {
			c.Render(200, render.Data{
				ContentType: "image/png",
				Data:        b1,
			})
		}
	}
}

// InvoiceApply 辅助，跳转到发票申请页面
// @Tags InsSysKit
// @Summary 发票申请页面
// @Param data body insbuyReq.KitInvoiceApplyReq true "二维码内容"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /kit/invoice/apply [get]
func (A *InsSysKitApi) InvoiceApply(c *gin.Context) {
	var err error
	var req insbuyReq.KitInvoiceApplyReq
	if err = c.ShouldBind(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	} else if req.OrderId == "" {
		response.FailWithMessage("无效订单", c)
		return
	}
	response.OkWithMessage("TODO: "+req.OrderId, c)
}

// 返回服务状态
//
//	如果未授权，则返回基本信息，包括 客户端 ip、服务端时间、编译时间、版本号
//	如果已授权，则返回更多信息
func (A *InsSysKitApi) Stat(c *gin.Context) {
	c.JSON(200, gin.H{
		"ip":      c.ClientIP(),
		"now":     time.Now().Format("2006-01-02 15:04:05"),
		"build":   global.BuildTime,
		"version": global.Version,
		"tag":     global.GitTag,
	})
}
