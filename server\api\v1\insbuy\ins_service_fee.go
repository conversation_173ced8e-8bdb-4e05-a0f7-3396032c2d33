package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsServiceFeeApi struct {
}

// GetServiceFeeList 获取服务费列表
// @Tags InsServiceFee
// @Summary 获取服务费列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param pageSize query int false "pageSize"
// @Param page query int false "page"
// @Param title query string false "title"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/getServiceFeeList [get]
func (i *InsServiceFeeApi) GetServiceFeeList(c *gin.Context) {
	var req insbuyReq.ServiceFeeListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, total, err := insServiceFeeService.GetServiceFeeList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, c)
	}
}

// GetClientServiceFeeList 获取客户端服务费列表
// @Tags InsServiceFee
// @Summary 获取服务费列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param pageSize query int false "pageSize"
// @Param page query int false "page"
// @Param userType query int false "title"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/getClientServiceFeeList [get]
func (i *InsServiceFeeApi) GetClientServiceFeeList(c *gin.Context) {
	var req insbuyReq.GetClientServiceFeeListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, total, err := insServiceFeeService.GetClientServiceFeeList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, c)
	}
}

// CreateServiceFee  创建服务费
// @Tags InsServiceFee
// @Summary 创建服务费
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ServiceFeeReq true "创建服务费"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/createServiceFee [post]
func (i *InsServiceFeeApi) CreateServiceFee(c *gin.Context) {
	var req insbuyReq.ServiceFeeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insServiceFeeService.CreateServiceFee(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateServiceFee 修改服务费
// @Tags InsServiceFee
// @Summary 修改服务费
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ServiceFeeReq true "修改服务费"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insServiceFee/updateServiceFee/{id} [put]
func (i *InsServiceFeeApi) UpdateServiceFee(c *gin.Context) {
	var req insbuyReq.ServiceFeeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insServiceFeeService.UpdateServiceFee(req)
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// ServiceFeeDetail 获取服务费详情
// @Tags InsServiceFee
// @Summary 获取服务费详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param id path int true "id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/getServiceFeeDetail/{id} [get]
func (i *InsServiceFeeApi) ServiceFeeDetail(c *gin.Context) {
	var req request.GetById
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insServiceFeeService.ServiceFeeDetail(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GetCurrentDeskServiceFee 获取当前桌台服务费
// @Tags InsServiceFee
// @Summary 获取当前桌台服务费
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param deskId query int true "openDeskId"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/getCurrentDeskServiceFee [get]
func (i *InsServiceFeeApi) GetCurrentDeskServiceFee(c *gin.Context) {
	var req insbuyReq.CurrentDeskServiceFeeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insServiceFeeService.GetCurrentDeskServiceFee(c.Request.Context(), req.GetUintStoreId(), req.OpenDeskId)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// SetDeskServiceFee 设置桌台服务费
// @Tags InsServiceFee
// @Summary 设置桌台服务费
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SetDeskServiceFeeReq true "设置桌台服务费"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insServiceFee/setDeskServiceFee [put]
func (i *InsServiceFeeApi) SetDeskServiceFee(c *gin.Context) {
	var req insbuyReq.SetDeskServiceFeeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insServiceFeeService.SetDeskServiceFee(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("设置失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
	} else {
		response.OkWithMessage("设置成功", c)
	}
}
