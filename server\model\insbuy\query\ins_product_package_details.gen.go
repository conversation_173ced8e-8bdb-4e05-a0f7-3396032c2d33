// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageDetails(db *gorm.DB, opts ...gen.DOOption) insProductPackageDetails {
	_insProductPackageDetails := insProductPackageDetails{}

	_insProductPackageDetails.insProductPackageDetailsDo.UseDB(db, opts...)
	_insProductPackageDetails.insProductPackageDetailsDo.UseModel(&insbuy.InsProductPackageDetails{})

	tableName := _insProductPackageDetails.insProductPackageDetailsDo.TableName()
	_insProductPackageDetails.ALL = field.NewAsterisk(tableName)
	_insProductPackageDetails.Id = field.NewInt(tableName, "id")
	_insProductPackageDetails.PackageProductId = field.NewInt(tableName, "package_product_id")
	_insProductPackageDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPackageDetails.SortOrder = field.NewInt(tableName, "sort_order")
	_insProductPackageDetails.ProductId = field.NewInt(tableName, "product_id")
	_insProductPackageDetails.ProductNum = field.NewInt(tableName, "product_num")
	_insProductPackageDetails.PackageId = field.NewInt(tableName, "package_id")

	_insProductPackageDetails.fillFieldMap()

	return _insProductPackageDetails
}

type insProductPackageDetails struct {
	insProductPackageDetailsDo

	ALL              field.Asterisk
	Id               field.Int
	PackageProductId field.Int
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	SortOrder        field.Int
	ProductId        field.Int
	ProductNum       field.Int
	PackageId        field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPackageDetails) Table(newTableName string) *insProductPackageDetails {
	i.insProductPackageDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageDetails) As(alias string) *insProductPackageDetails {
	i.insProductPackageDetailsDo.DO = *(i.insProductPackageDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageDetails) updateTableName(table string) *insProductPackageDetails {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.PackageProductId = field.NewInt(table, "package_product_id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.ProductId = field.NewInt(table, "product_id")
	i.ProductNum = field.NewInt(table, "product_num")
	i.PackageId = field.NewInt(table, "package_id")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.Id
	i.fieldMap["package_product_id"] = i.PackageProductId
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["product_num"] = i.ProductNum
	i.fieldMap["package_id"] = i.PackageId
}

func (i insProductPackageDetails) clone(db *gorm.DB) insProductPackageDetails {
	i.insProductPackageDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageDetails) replaceDB(db *gorm.DB) insProductPackageDetails {
	i.insProductPackageDetailsDo.ReplaceDB(db)
	return i
}

type insProductPackageDetailsDo struct{ gen.DO }

func (i insProductPackageDetailsDo) Debug() *insProductPackageDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageDetailsDo) WithContext(ctx context.Context) *insProductPackageDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageDetailsDo) ReadDB() *insProductPackageDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageDetailsDo) WriteDB() *insProductPackageDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageDetailsDo) Session(config *gorm.Session) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageDetailsDo) Clauses(conds ...clause.Expression) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageDetailsDo) Returning(value interface{}, columns ...string) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageDetailsDo) Not(conds ...gen.Condition) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageDetailsDo) Or(conds ...gen.Condition) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageDetailsDo) Select(conds ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageDetailsDo) Where(conds ...gen.Condition) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageDetailsDo) Order(conds ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageDetailsDo) Distinct(cols ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageDetailsDo) Omit(cols ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageDetailsDo) Group(cols ...field.Expr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageDetailsDo) Having(conds ...gen.Condition) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageDetailsDo) Limit(limit int) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageDetailsDo) Offset(offset int) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageDetailsDo) Unscoped() *insProductPackageDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageDetailsDo) Create(values ...*insbuy.InsProductPackageDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageDetailsDo) CreateInBatches(values []*insbuy.InsProductPackageDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageDetailsDo) Save(values ...*insbuy.InsProductPackageDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageDetailsDo) First() (*insbuy.InsProductPackageDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageDetails), nil
	}
}

func (i insProductPackageDetailsDo) Take() (*insbuy.InsProductPackageDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageDetails), nil
	}
}

func (i insProductPackageDetailsDo) Last() (*insbuy.InsProductPackageDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageDetails), nil
	}
}

func (i insProductPackageDetailsDo) Find() ([]*insbuy.InsProductPackageDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageDetails), err
}

func (i insProductPackageDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageDetails, err error) {
	buf := make([]*insbuy.InsProductPackageDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageDetailsDo) FindInBatches(result *[]*insbuy.InsProductPackageDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageDetailsDo) Attrs(attrs ...field.AssignExpr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageDetailsDo) Assign(attrs ...field.AssignExpr) *insProductPackageDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageDetailsDo) Joins(fields ...field.RelationField) *insProductPackageDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageDetailsDo) Preload(fields ...field.RelationField) *insProductPackageDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageDetailsDo) FirstOrInit() (*insbuy.InsProductPackageDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageDetails), nil
	}
}

func (i insProductPackageDetailsDo) FirstOrCreate() (*insbuy.InsProductPackageDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageDetails), nil
	}
}

func (i insProductPackageDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageDetailsDo) Delete(models ...*insbuy.InsProductPackageDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageDetailsDo) withDO(do gen.Dao) *insProductPackageDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
