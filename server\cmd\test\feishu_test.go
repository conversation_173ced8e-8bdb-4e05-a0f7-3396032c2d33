package test

import (
	"github.com/flipped-aurora/gin-vue-admin/server/utils/task"
	"testing"
)

func TestNewFeiShu(t *testing.T) {
	feishu := task.NewFeiShu("https://open.feishu.cn/open-apis/bot/v2/hook/4adbc4f1-fa07-437c-b5f4-6e953dbb1dca", task.SundriesCard())
	feishu.SetMarkdownContent("商品1 录入价:200 标准价：300,超出 30%\n商品1 录入价:200 标准价：300,超出 30%")
	err := feishu.SendFeiShuMessage()
	if err != nil {
		t.Logf("发送到飞书群错误%v", err)
		return
	}
}
