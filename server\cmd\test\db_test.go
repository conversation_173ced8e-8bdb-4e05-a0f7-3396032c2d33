package test

import (
	"context"
	"encoding"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"github.com/shirou/gopsutil/v3/mem"
	"gorm.io/gorm"
	"testing"
	"time"
)

func TestDB(t *testing.T) {
	prepare()
	REDI1(t)
}

func db01(t *testing.T) {
	type NotExistTable struct {
		ID          uint                  `gorm:"primary_key;AUTO_INCREMENT"`
		Name        string                `gorm:"type:varchar(100);NOT NULL;comment:名称"`
		BusinessDay jgorm.BusinessDayType `gorm:"type:date;comment:营业日;NOT NULL;index:bd" json:"business_day"`
		CreatedAt   time.Time             // 创建时间
		UpdatedAt   time.Time             // 更新时间
		DeletedAt   gorm.DeletedAt        `gorm:"index" json:"-"` // 删除时间
	}

	fnShow := func(v interface{}) {
		b1, _ := json.Marshal(v)
		t.Log(string(b1))
	}

	{
		v := []NotExistTable{
			{Name: "新建，指定值", BusinessDay: jgorm.NewBusinessDayType(2023, 1, 20)},
			{Name: "新建，默认值"},
			{Name: "新建，指定值", BusinessDay: jgorm.NewBusinessDayType(2023, 1, 22)},
			{Name: "新建，默认值"},
			{Name: "新建，指定值", BusinessDay: jgorm.NewBusinessDayType(2023, 1, 23)},
		}
		err := global.GVA_DB.Debug().Create(&v)
		fnShow(v)
		if err != nil {
			t.Error(err)
		}
	}
	t.Log("--------")
	{
		v := []NotExistTable{
			{Name: "新建，默认值"},
			{Name: "新建，默认值2"},
			{Name: "新建，默认值3"},
		}
		err := global.GVA_DB.Debug().Create(v)
		fnShow(v)
		if err != nil {
			t.Error(err)
		}
	}
	t.Log("--------")
	{
		v := NotExistTable{Name: "查询，默认"}
		var cnt int64
		err := global.GVA_DB.Debug().Model(&v).Count(&cnt)
		if err != nil {
			t.Error(err)
		} else {
			fnShow(v)
		}
	}
	t.Log("--------")
	{
		v := NotExistTable{Name: "删除，默认"}
		err := global.GVA_DB.Debug().Delete(&v)
		if err != nil {
			t.Error(err)
		} else {
			fnShow(v)
		}
	}
}

type MetricsData struct {
	HostIP   string      `json:"host_ip"`
	HostType string      `json:"host_type"`
	Data     interface{} `json:"data"`
	Time     int64       `json:"time"`
}

var _ encoding.BinaryMarshaler = new(MetricsData)
var _ encoding.BinaryUnmarshaler = new(MetricsData)

func (m *MetricsData) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, m)
}

func (m *MetricsData) MarshalBinary() (data []byte, err error) {
	return json.Marshal(m)
}

func REDI1(t *testing.T) {
	vmem, err := mem.VirtualMemoryWithContext(context.Background())
	if err != nil {
		t.Fatalf(err.Error())
		return
	}

	data := map[string]interface{}{
		"used":      vmem.Used,
		"free":      vmem.Free,
		"total":     vmem.Total,
		"percent":   fmt.Sprintf("%.2f", vmem.UsedPercent),
		"available": vmem.Available,
	}

	value := &MetricsData{
		HostIP:   "*******",
		HostType: "api_svc",
		Data:     data,
		Time:     0,
	}
	global.GVA_REDIS.Set(context.Background(), "aaaaaaa", value, 0).Err()
	return
}
