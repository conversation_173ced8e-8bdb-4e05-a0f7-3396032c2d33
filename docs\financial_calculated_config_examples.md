# 财务交叉统计报表 - 计算型分类配置示例

## 概述

本文档展示如何在配置中心定义计算型财务分类，以支持复杂的财务计算逻辑。

## 配置结构

### 基础字段

```json
{
  "id": 100,
  "level": 1,
  "parent_id": 0,
  "category_name": "经营利润",
  "category_code": "OPERATING_PROFIT",
  "is_active": 1,
  "sort_order": 999,
  "is_calculated": true,
  "calculation_formula": "SUBTRACT:1,2",
  "depends_on": "[1,2]",
  "calculation_type": "SUBTRACT"
}
```

### 字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `is_calculated` | boolean | 是否为计算型分类 | `true` |
| `calculation_formula` | string | 计算公式 | `"SUBTRACT:1,2"` |
| `depends_on` | string | 依赖的分类ID列表(JSON格式) | `"[1,2]"` |
| `calculation_type` | string | 计算类型 | `"SUBTRACT"` |

## 支持的计算类型

### 1. 加法运算 (ADD/SUM)

**用途**: 多项费用求和

```json
{
  "id": 101,
  "category_name": "总费用",
  "is_calculated": true,
  "calculation_formula": "ADD:10,11,12",
  "calculation_type": "ADD",
  "depends_on": "[10,11,12]"
}
```

**说明**: 将ID为10、11、12的分类金额相加

### 2. 减法运算 (SUBTRACT)

**用途**: 利润计算

```json
{
  "id": 102,
  "category_name": "经营利润",
  "is_calculated": true,
  "calculation_formula": "SUBTRACT:1,2",
  "calculation_type": "SUBTRACT",
  "depends_on": "[1,2]"
}
```

**说明**: 收入类合计(ID:1) - 成本类合计(ID:2)

### 3. 百分比计算 (PERCENTAGE)

**用途**: 比率分析

```json
{
  "id": 103,
  "category_name": "利润率",
  "is_calculated": true,
  "calculation_formula": "PERCENTAGE:102,1",
  "calculation_type": "PERCENTAGE",
  "depends_on": "[102,1]"
}
```

**说明**: 经营利润(ID:102) / 收入类合计(ID:1) × 100%

## 实际业务场景示例

### 场景1: 基础利润计算

```json
[
  {
    "id": 1,
    "category_name": "收入类合计",
    "level": 1,
    "is_calculated": false
  },
  {
    "id": 2,
    "category_name": "成本类合计",
    "level": 1,
    "is_calculated": false
  },
  {
    "id": 100,
    "category_name": "毛利润",
    "level": 1,
    "is_calculated": true,
    "calculation_formula": "SUBTRACT:1,2",
    "calculation_type": "SUBTRACT",
    "depends_on": "[1,2]",
    "sort_order": 900
  }
]
```

### 场景2: 多层级计算

```json
[
  {
    "id": 10,
    "category_name": "管理费用",
    "level": 2,
    "is_calculated": false
  },
  {
    "id": 11,
    "category_name": "销售费用",
    "level": 2,
    "is_calculated": false
  },
  {
    "id": 12,
    "category_name": "财务费用",
    "level": 2,
    "is_calculated": false
  },
  {
    "id": 101,
    "category_name": "期间费用合计",
    "level": 1,
    "is_calculated": true,
    "calculation_formula": "ADD:10,11,12",
    "calculation_type": "ADD",
    "depends_on": "[10,11,12]",
    "sort_order": 910
  },
  {
    "id": 102,
    "category_name": "营业利润",
    "level": 1,
    "is_calculated": true,
    "calculation_formula": "SUBTRACT:100,101",
    "calculation_type": "SUBTRACT",
    "depends_on": "[100,101]",
    "sort_order": 920
  }
]
```

### 场景3: 财务比率分析

```json
[
  {
    "id": 200,
    "category_name": "毛利率",
    "level": 1,
    "is_calculated": true,
    "calculation_formula": "PERCENTAGE:100,1",
    "calculation_type": "PERCENTAGE",
    "depends_on": "[100,1]",
    "sort_order": 950
  },
  {
    "id": 201,
    "category_name": "营业利润率",
    "level": 1,
    "is_calculated": true,
    "calculation_formula": "PERCENTAGE:102,1",
    "calculation_type": "PERCENTAGE",
    "depends_on": "[102,1]",
    "sort_order": 960
  }
]
```

## 公式格式说明

### 简单公式格式

支持以下格式的简单公式：

```
ADD:1,2,3        # 将ID为1,2,3的分类相加
SUBTRACT:1,2     # ID为1的分类减去ID为2的分类
PERCENTAGE:1,2   # ID为1除以ID为2乘以100%
SUM:1,2,3,4      # 等同于ADD，多项求和
```

### 公式解析规则

1. **操作符**: 公式开头的操作类型（ADD、SUBTRACT、PERCENTAGE等）
2. **分隔符**: 使用冒号(:)分隔操作符和操作数
3. **操作数**: 使用逗号(,)分隔多个分类ID
4. **ID格式**: 必须是有效的数字ID

## 依赖关系处理

### 计算顺序

系统会自动处理依赖关系：

1. **第一阶段**: 处理所有基础分类（从数据源直接获取）
2. **第二阶段**: 处理无依赖的计算型分类
3. **第三阶段**: 处理有依赖的计算型分类

### 依赖配置

```json
{
  "depends_on": "[1,2,3]"  // JSON数组格式，包含依赖的分类ID
}
```

## 注意事项

### 1. 配置验证

- 确保 `depends_on` 中的ID在系统中存在
- 避免循环依赖
- 计算型分类的 `sort_order` 建议设置较大值，确保在基础分类之后显示

### 2. 性能考虑

- 计算型分类会在基础数据聚合完成后执行
- 复杂的依赖关系可能影响计算性能
- 建议合理设计分类层级，避免过深的依赖链

### 3. 数据准确性

- 计算结果会自动处理每个月份的数据
- 百分比计算会自动处理除零情况
- 所有计算都基于已有的分类数据

## 扩展性

### 未来支持的功能

1. **条件计算**: 基于条件的动态计算
2. **函数调用**: 支持更复杂的数学函数
3. **跨期计算**: 支持同比、环比等时间维度计算
4. **自定义公式**: 支持更灵活的公式表达式

### 配置中心集成

计算型分类配置完全集成到现有的配置中心系统中：

- 支持在线编辑和验证
- 支持配置版本管理
- 支持配置生效时间控制
- 支持多环境配置同步

## 测试验证

### 配置测试

在配置新的计算型分类后，建议进行以下测试：

1. **数据验证**: 检查计算结果是否符合预期
2. **边界测试**: 测试零值、负值等边界情况
3. **性能测试**: 验证计算性能是否满足要求
4. **依赖测试**: 确保依赖关系正确处理

### 示例测试数据

```json
{
  "test_scenario": "基础利润计算",
  "input_data": {
    "收入类合计": {"2025-01": 1000000, "2025-02": 1200000},
    "成本类合计": {"2025-01": 600000, "2025-02": 700000}
  },
  "expected_result": {
    "毛利润": {"2025-01": 400000, "2025-02": 500000}
  }
}
```

这个配置系统为财务交叉统计报表提供了强大的计算能力，支持复杂的财务分析需求。
