package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsCouponApi struct {
}

// CreateCoupon 创建优惠券
// @Tags InsCoupon
// @Summary 创建优惠券
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CouponReq true "创建优惠券"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCoupon/createCoupon [post]
func (insCouponApi *InsCouponApi) CreateCoupon(c *gin.Context) {
	var coupon insbuyReq.CouponReq
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.CreateCoupon(c, coupon); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateCoupon 更新优惠券
// @Tags InsCoupon
// @Summary 更新优惠券
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CouponReq true "更新优惠券"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insCoupon/updateCoupon [put]
func (insCouponApi *InsCouponApi) UpdateCoupon(c *gin.Context) {
	var coupon insbuyReq.CouponReq
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.UpdateCoupon(c, coupon); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteCoupon 删除优惠券
// @Tags InsCoupon
// @Summary 删除优惠券
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsCoupon true "删除优惠券"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insCoupon/deleteCoupon [delete]
func (insCouponApi *InsCouponApi) DeleteCoupon(c *gin.Context) {
	var coupon insbuyReq.CouponReq
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.DeleteCoupon(c, coupon); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// GetCouponList 分页获取优惠券列表
// @Tags InsCoupon
// @Summary 分页获取优惠券列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param info body insbuyReq.CouponListReq true "分页获取优惠券列表"
// @Success 200 {object}  response.Response{data=insbuyResp.CouponListResp,msg=string} "获取成功"
// @Router /insCoupon/getCouponList [get]
func (insCouponApi *InsCouponApi) GetCouponList(c *gin.Context) {
	var pageInfo insbuyReq.CouponListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list := make([]insbuyResp.CouponListResp, 0)
	total := int64(0)
	if list, total, err = insCouponService.GetCouponList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetCouponDetail 获取优惠券详情
// @Tags InsCoupon
// @Summary 获取优惠券详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "获取优惠券详情"
// @Success 200 {object} response.Response{data=insbuyResp.CouponDetailResp,msg=string} "查询成功
// @Router /insCoupon/getCouponDetail [get]
func (insCouponApi *InsCouponApi) GetCouponDetail(c *gin.Context) {
	var coupon request.GetById
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insCouponService.GetCouponDetail(c, coupon); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// CreateSendCouponRule 保存发券规则
// @Tags InsCoupon
// @Summary 保存发券规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SendRuleReq true "保存发券规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCoupon/createSendCouponRule [post]
func (insCouponApi *InsCouponApi) CreateSendCouponRule(c *gin.Context) {
	var sendCoupon insbuyReq.SendRuleReq
	err := GinMustBind(c, &sendCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.CreateSendCouponRule(c, sendCoupon); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateSendCouponRule 更新发券规则
// @Tags InsCoupon
// @Summary 更新发券规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SendRuleReq true "更新发券规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insCoupon/updateSendCouponRule [put]
func (insCouponApi *InsCouponApi) UpdateSendCouponRule(c *gin.Context) {
	var sendCoupon insbuyReq.SendRuleReq
	err := GinMustBind(c, &sendCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.UpdateSendCouponRule(c, sendCoupon); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// DeleteSendCouponRule 删除发券规则
// @Tags InsCoupon
// @Summary 删除发券规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsCouponSendRule true "删除发券规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insCoupon/deleteSendCouponRule [delete]
func (insCouponApi *InsCouponApi) DeleteSendCouponRule(c *gin.Context) {
	var sendCoupon insbuyReq.SendRuleReq
	err := GinMustBind(c, &sendCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.DeleteSendCouponRule(c, sendCoupon); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// SendCoupon 发送优惠券
// @Tags InsCoupon
// @Summary 发送优惠券
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SendCouponReq true "发送优惠券"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCoupon/sendCoupon [put]
func (insCouponApi *InsCouponApi) SendCoupon(c *gin.Context) {
	var sendCoupon insbuyReq.SendCouponReq
	err := GinMustBind(c, &sendCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.SendCoupon(c, sendCoupon); err != nil {
		global.GVA_LOG.Error("发送失败!", zap.Error(err))
		response.FailWithMessage("发送失败", c)
	} else {
		response.OkWithMessage("发送成功", c)
	}
}

// SendCouponList 分页获取发券列表
// @Tags InsCoupon
// @Summary 分页获取发券列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param info body insbuyReq.SendCouponListReq true "分页获取发券列表"
// @Success 200 {object} response.Response{data=insbuyResp.SendCouponListResp,msg=string} "获取成功"
// @Router /insCoupon/sendCouponList [get]
func (insCouponApi *InsCouponApi) SendCouponList(c *gin.Context) {
	var pageInfo insbuyReq.SendCouponListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insCouponService.SendCouponList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// SendCouponDetail 获取发券详情
// @Tags InsCoupon
// @Summary 获取发券详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "获取发券详情"
// @Success 200 {object} response.Response{data=insbuyResp.SendCouponDetailResp,msg=string} "查询成功
// @Router /insCoupon/sendCouponDetail [get]
func (insCouponApi *InsCouponApi) SendCouponDetail(c *gin.Context) {
	var coupon request.GetById
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insCouponService.SendCouponDetail(c, coupon); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// SendCouponDetailList 获取发券详情列表
// @Tags InsCoupon
// @Summary 获取发券详情列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetById true "获取发券详情列表"
// @Success 200 {object} response.Response{data=insbuyResp.SendCouponDetailResp,msg=string} "查询成功
// @Router /insCoupon/sendCouponDetailList [get]
func (insCouponApi *InsCouponApi) SendCouponDetailList(c *gin.Context) {
	var coupon insbuyReq.SendCouponDetailListReq
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, total, err := insCouponService.SendCouponDetailList(c, coupon); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     coupon.Page,
			PageSize: coupon.PageSize,
		}, "查询成功", c)
	}
}

// ReceiveCouponList 获取领取优惠券列表
// @Tags InsCoupon
// @Summary 获取领取优惠券列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param info body insbuyReq.ReceiveCouponListReq true "获取领取优惠券列表"
// @Success 200 {object} response.Response{data=insbuyResp.ReceiveCouponListResp,msg=string} "获取成功"
// @Router /insCoupon/receiveCouponList [get]
func (insCouponApi *InsCouponApi) ReceiveCouponList(c *gin.Context) {
	var pageInfo insbuyReq.ReceiveCouponListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insCouponService.ReceiveCouponList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CalculateMemberList 获取会员列表
// @Tags InsCoupon
// @Summary 获取会员列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param info body insbuyReq.MemberListReq true "获取会员列表"
// @Success 200 {object} response.Response{data=insbuyResp.MemberListResp,msg=string} "获取成功"
// @Router /insCoupon/calculateMemberList [post]
func (insCouponApi *InsCouponApi) CalculateMemberList(c *gin.Context) {
	var pageInfo insbuyReq.MemberListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insCouponService.CalculateMemberList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// MemberCouponList 会员优惠券列表
// @Tags InsCoupon
// @Summary 会员优惠券列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param info body insbuyReq.MemberCouponListReq true "获取会员优惠券列表"
// @Success 200 {object} response.Response{data=insbuyResp.MemberCouponListResp,msg=string} "获取成功"
// @Router /insCoupon/memberCouponList [post]
func (insCouponApi *InsCouponApi) MemberCouponList(c *gin.Context) {
	var pageInfo insbuyReq.MemberCouponListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insCouponService.MemberCouponList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ReceiveCouponSendCode 领取优惠券发送验证码
// @Tags InsCoupon
// @Summary 领取优惠券发送验证码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SendCodeReq true "领取优惠券发送验证码"
// @Success 200 {object} response.Response{data=insbuyResp.ReceiveCouponSendSmsResp,msg=string} "获取成功"
// @Router /insCoupon/receiveCouponSendCode [post]
func (insCouponApi *InsCouponApi) ReceiveCouponSendCode(c *gin.Context) {
	var receiveCoupon insbuyReq.SendCodeReq
	err := GinMustBind(c, &receiveCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insCouponService.ReceiveCouponSendCode(c, receiveCoupon); err != nil {
		global.GVA_LOG.Error("发送失败!", zap.Error(err))
		response.FailWithMessage("发送失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ReceiveCoupon 领取优惠券
// @Tags InsCoupon
// @Summary 领取优惠券
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ReceiveCouponReq true "领取优惠券"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insCoupon/receiveCoupon [post]
func (insCouponApi *InsCouponApi) ReceiveCoupon(c *gin.Context) {
	var receiveCoupon insbuyReq.ReceiveCouponReq
	err := GinMustBind(c, &receiveCoupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insCouponService.ReceiveCoupon(c, receiveCoupon); err != nil {
		global.GVA_LOG.Error("领取失败!", zap.Error(err))
		response.FailWithMessage("领取失败", c)
	} else {
		response.OkWithMessage("领取成功", c)
	}
}

// ReceiveCouponDetail 领取优惠券详情
// @Tags InsCoupon
// @Summary 领取优惠券详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ReceiveCouponDetailReq true "领取优惠券详情"
// @Success 200 {object} response.Response{data=insbuyResp.MemberCouponListResp,msg=string} "查询成功
// @Router /insCoupon/receiveCouponDetail [get]
func (insCouponApi *InsCouponApi) ReceiveCouponDetail(c *gin.Context) {
	var coupon insbuyReq.ReceiveCouponDetailReq
	err := GinMustBind(c, &coupon)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insCouponService.ReceiveCouponDetail(c, coupon); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}
