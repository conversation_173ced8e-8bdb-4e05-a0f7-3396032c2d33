// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShare(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShare {
	_insReportRuleSalesShare := insReportRuleSalesShare{}

	_insReportRuleSalesShare.insReportRuleSalesShareDo.UseDB(db, opts...)
	_insReportRuleSalesShare.insReportRuleSalesShareDo.UseModel(&insbuy.InsReportRuleSalesShare{})

	tableName := _insReportRuleSalesShare.insReportRuleSalesShareDo.TableName()
	_insReportRuleSalesShare.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShare.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShare.StoreId = field.NewUint(tableName, "store_id")
	_insReportRuleSalesShare.Name = field.NewString(tableName, "name")
	_insReportRuleSalesShare.Priority = field.NewInt(tableName, "priority")
	_insReportRuleSalesShare.Status = field.NewInt(tableName, "status")
	_insReportRuleSalesShare.RuleType = field.NewInt(tableName, "rule_type")
	_insReportRuleSalesShare.GoodsType = field.NewInt(tableName, "goods_type")
	_insReportRuleSalesShare.ShareType = field.NewInt(tableName, "share_type")
	_insReportRuleSalesShare.ShareModel = field.NewInt(tableName, "share_model")
	_insReportRuleSalesShare.ShareParam = field.NewFloat64(tableName, "share_param")
	_insReportRuleSalesShare.FreeAmount = field.NewFloat64(tableName, "free_amount")
	_insReportRuleSalesShare.LeaderShareParam = field.NewFloat64(tableName, "leader_share_param")
	_insReportRuleSalesShare.RechargeShareModel = field.NewInt(tableName, "recharge_share_model")
	_insReportRuleSalesShare.IsAllDay = field.NewInt(tableName, "is_all_day")
	_insReportRuleSalesShare.StartDate = field.NewField(tableName, "start_date")
	_insReportRuleSalesShare.EndDate = field.NewField(tableName, "end_date")
	_insReportRuleSalesShare.WeekSet = field.NewString(tableName, "week_set")
	_insReportRuleSalesShare.Remark = field.NewString(tableName, "remark")
	_insReportRuleSalesShare.Ext = field.NewField(tableName, "ext")
	_insReportRuleSalesShare.CreatedBy = field.NewUint(tableName, "created_by")
	_insReportRuleSalesShare.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportRuleSalesShare.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insReportRuleSalesShare.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportRuleSalesShare.TimeFrame = insReportRuleSalesShareHasManyTimeFrame{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("TimeFrame", "insbuy.InsReportRuleSalesShareTimeFrame"),
	}

	_insReportRuleSalesShare.Staff = insReportRuleSalesShareHasManyStaff{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Staff", "insbuy.InsReportRuleSalesShareStaff"),
	}

	_insReportRuleSalesShare.Org = insReportRuleSalesShareHasManyOrg{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Org", "insbuy.InsReportRuleSalesShareOrg"),
	}

	_insReportRuleSalesShare.Product = insReportRuleSalesShareHasManyProduct{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Product", "insbuy.InsReportRuleSalesShareProduct"),
		Product: struct {
			field.RelationField
			Details struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("Product.Product", "insbuy.InsProduct"),
			Details: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("Product.Product.Details", "insbuy.InsProductDetails"),
			},
		},
	}

	_insReportRuleSalesShare.Category = insReportRuleSalesShareHasManyCategory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Category", "insbuy.InsReportRuleSalesShareProductCategory"),
	}

	_insReportRuleSalesShare.Recharge = insReportRuleSalesShareHasManyRecharge{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Recharge", "insbuy.InsReportRuleSalesShareRecharge"),
	}

	_insReportRuleSalesShare.Store = insReportRuleSalesShareBelongsToStore{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Store", "insbuy.InsStore"),
	}

	_insReportRuleSalesShare.fillFieldMap()

	return _insReportRuleSalesShare
}

type insReportRuleSalesShare struct {
	insReportRuleSalesShareDo

	ALL                field.Asterisk
	ID                 field.Uint
	StoreId            field.Uint
	Name               field.String
	Priority           field.Int
	Status             field.Int
	RuleType           field.Int
	GoodsType          field.Int
	ShareType          field.Int
	ShareModel         field.Int
	ShareParam         field.Float64
	FreeAmount         field.Float64
	LeaderShareParam   field.Float64
	RechargeShareModel field.Int
	IsAllDay           field.Int
	StartDate          field.Field
	EndDate            field.Field
	WeekSet            field.String
	Remark             field.String
	Ext                field.Field
	CreatedBy          field.Uint
	CreatedAt          field.Time
	UpdatedBy          field.Uint
	UpdatedAt          field.Time
	TimeFrame          insReportRuleSalesShareHasManyTimeFrame

	Staff insReportRuleSalesShareHasManyStaff

	Org insReportRuleSalesShareHasManyOrg

	Product insReportRuleSalesShareHasManyProduct

	Category insReportRuleSalesShareHasManyCategory

	Recharge insReportRuleSalesShareHasManyRecharge

	Store insReportRuleSalesShareBelongsToStore

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShare) Table(newTableName string) *insReportRuleSalesShare {
	i.insReportRuleSalesShareDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShare) As(alias string) *insReportRuleSalesShare {
	i.insReportRuleSalesShareDo.DO = *(i.insReportRuleSalesShareDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShare) updateTableName(table string) *insReportRuleSalesShare {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Priority = field.NewInt(table, "priority")
	i.Status = field.NewInt(table, "status")
	i.RuleType = field.NewInt(table, "rule_type")
	i.GoodsType = field.NewInt(table, "goods_type")
	i.ShareType = field.NewInt(table, "share_type")
	i.ShareModel = field.NewInt(table, "share_model")
	i.ShareParam = field.NewFloat64(table, "share_param")
	i.FreeAmount = field.NewFloat64(table, "free_amount")
	i.LeaderShareParam = field.NewFloat64(table, "leader_share_param")
	i.RechargeShareModel = field.NewInt(table, "recharge_share_model")
	i.IsAllDay = field.NewInt(table, "is_all_day")
	i.StartDate = field.NewField(table, "start_date")
	i.EndDate = field.NewField(table, "end_date")
	i.WeekSet = field.NewString(table, "week_set")
	i.Remark = field.NewString(table, "remark")
	i.Ext = field.NewField(table, "ext")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.UpdatedAt = field.NewTime(table, "updated_at")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShare) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShare) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 30)
	i.fieldMap["id"] = i.ID
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["priority"] = i.Priority
	i.fieldMap["status"] = i.Status
	i.fieldMap["rule_type"] = i.RuleType
	i.fieldMap["goods_type"] = i.GoodsType
	i.fieldMap["share_type"] = i.ShareType
	i.fieldMap["share_model"] = i.ShareModel
	i.fieldMap["share_param"] = i.ShareParam
	i.fieldMap["free_amount"] = i.FreeAmount
	i.fieldMap["leader_share_param"] = i.LeaderShareParam
	i.fieldMap["recharge_share_model"] = i.RechargeShareModel
	i.fieldMap["is_all_day"] = i.IsAllDay
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["week_set"] = i.WeekSet
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["updated_at"] = i.UpdatedAt

}

func (i insReportRuleSalesShare) clone(db *gorm.DB) insReportRuleSalesShare {
	i.insReportRuleSalesShareDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShare) replaceDB(db *gorm.DB) insReportRuleSalesShare {
	i.insReportRuleSalesShareDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareHasManyTimeFrame struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareHasManyTimeFrame) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyTimeFrame {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyTimeFrame) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyTimeFrame {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyTimeFrame) Session(session *gorm.Session) *insReportRuleSalesShareHasManyTimeFrame {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyTimeFrame) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyTimeFrameTx {
	return &insReportRuleSalesShareHasManyTimeFrameTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyTimeFrameTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyTimeFrameTx) Find() (result []*insbuy.InsReportRuleSalesShareTimeFrame, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyTimeFrameTx) Append(values ...*insbuy.InsReportRuleSalesShareTimeFrame) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyTimeFrameTx) Replace(values ...*insbuy.InsReportRuleSalesShareTimeFrame) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyTimeFrameTx) Delete(values ...*insbuy.InsReportRuleSalesShareTimeFrame) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyTimeFrameTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyTimeFrameTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareHasManyStaff struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareHasManyStaff) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyStaff {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyStaff) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyStaff {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyStaff) Session(session *gorm.Session) *insReportRuleSalesShareHasManyStaff {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyStaff) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyStaffTx {
	return &insReportRuleSalesShareHasManyStaffTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyStaffTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyStaffTx) Find() (result []*insbuy.InsReportRuleSalesShareStaff, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyStaffTx) Append(values ...*insbuy.InsReportRuleSalesShareStaff) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyStaffTx) Replace(values ...*insbuy.InsReportRuleSalesShareStaff) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyStaffTx) Delete(values ...*insbuy.InsReportRuleSalesShareStaff) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyStaffTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyStaffTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareHasManyOrg struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareHasManyOrg) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyOrg {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyOrg) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyOrg {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyOrg) Session(session *gorm.Session) *insReportRuleSalesShareHasManyOrg {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyOrg) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyOrgTx {
	return &insReportRuleSalesShareHasManyOrgTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyOrgTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyOrgTx) Find() (result []*insbuy.InsReportRuleSalesShareOrg, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyOrgTx) Append(values ...*insbuy.InsReportRuleSalesShareOrg) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyOrgTx) Replace(values ...*insbuy.InsReportRuleSalesShareOrg) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyOrgTx) Delete(values ...*insbuy.InsReportRuleSalesShareOrg) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyOrgTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyOrgTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareHasManyProduct struct {
	db *gorm.DB

	field.RelationField

	Product struct {
		field.RelationField
		Details struct {
			field.RelationField
		}
	}
}

func (a insReportRuleSalesShareHasManyProduct) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyProduct {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyProduct) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyProduct {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyProduct) Session(session *gorm.Session) *insReportRuleSalesShareHasManyProduct {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyProduct) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyProductTx {
	return &insReportRuleSalesShareHasManyProductTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyProductTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyProductTx) Find() (result []*insbuy.InsReportRuleSalesShareProduct, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyProductTx) Append(values ...*insbuy.InsReportRuleSalesShareProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyProductTx) Replace(values ...*insbuy.InsReportRuleSalesShareProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyProductTx) Delete(values ...*insbuy.InsReportRuleSalesShareProduct) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyProductTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyProductTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareHasManyCategory struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareHasManyCategory) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyCategory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyCategory) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyCategory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyCategory) Session(session *gorm.Session) *insReportRuleSalesShareHasManyCategory {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyCategory) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyCategoryTx {
	return &insReportRuleSalesShareHasManyCategoryTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyCategoryTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyCategoryTx) Find() (result []*insbuy.InsReportRuleSalesShareProductCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyCategoryTx) Append(values ...*insbuy.InsReportRuleSalesShareProductCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyCategoryTx) Replace(values ...*insbuy.InsReportRuleSalesShareProductCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyCategoryTx) Delete(values ...*insbuy.InsReportRuleSalesShareProductCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyCategoryTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyCategoryTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareHasManyRecharge struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareHasManyRecharge) Where(conds ...field.Expr) *insReportRuleSalesShareHasManyRecharge {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareHasManyRecharge) WithContext(ctx context.Context) *insReportRuleSalesShareHasManyRecharge {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareHasManyRecharge) Session(session *gorm.Session) *insReportRuleSalesShareHasManyRecharge {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareHasManyRecharge) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareHasManyRechargeTx {
	return &insReportRuleSalesShareHasManyRechargeTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareHasManyRechargeTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareHasManyRechargeTx) Find() (result []*insbuy.InsReportRuleSalesShareRecharge, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareHasManyRechargeTx) Append(values ...*insbuy.InsReportRuleSalesShareRecharge) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareHasManyRechargeTx) Replace(values ...*insbuy.InsReportRuleSalesShareRecharge) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareHasManyRechargeTx) Delete(values ...*insbuy.InsReportRuleSalesShareRecharge) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareHasManyRechargeTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareHasManyRechargeTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareBelongsToStore struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportRuleSalesShareBelongsToStore) Where(conds ...field.Expr) *insReportRuleSalesShareBelongsToStore {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportRuleSalesShareBelongsToStore) WithContext(ctx context.Context) *insReportRuleSalesShareBelongsToStore {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportRuleSalesShareBelongsToStore) Session(session *gorm.Session) *insReportRuleSalesShareBelongsToStore {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportRuleSalesShareBelongsToStore) Model(m *insbuy.InsReportRuleSalesShare) *insReportRuleSalesShareBelongsToStoreTx {
	return &insReportRuleSalesShareBelongsToStoreTx{a.db.Model(m).Association(a.Name())}
}

type insReportRuleSalesShareBelongsToStoreTx struct{ tx *gorm.Association }

func (a insReportRuleSalesShareBelongsToStoreTx) Find() (result *insbuy.InsStore, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportRuleSalesShareBelongsToStoreTx) Append(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportRuleSalesShareBelongsToStoreTx) Replace(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportRuleSalesShareBelongsToStoreTx) Delete(values ...*insbuy.InsStore) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportRuleSalesShareBelongsToStoreTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportRuleSalesShareBelongsToStoreTx) Count() int64 {
	return a.tx.Count()
}

type insReportRuleSalesShareDo struct{ gen.DO }

func (i insReportRuleSalesShareDo) Debug() *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareDo) WithContext(ctx context.Context) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareDo) ReadDB() *insReportRuleSalesShareDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareDo) WriteDB() *insReportRuleSalesShareDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareDo) Session(config *gorm.Session) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareDo) Not(conds ...gen.Condition) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareDo) Or(conds ...gen.Condition) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareDo) Select(conds ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareDo) Where(conds ...gen.Condition) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareDo) Order(conds ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareDo) Omit(cols ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareDo) Group(cols ...field.Expr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareDo) Having(conds ...gen.Condition) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareDo) Limit(limit int) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareDo) Offset(offset int) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareDo) Unscoped() *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareDo) Create(values ...*insbuy.InsReportRuleSalesShare) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShare, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareDo) Save(values ...*insbuy.InsReportRuleSalesShare) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareDo) First() (*insbuy.InsReportRuleSalesShare, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShare), nil
	}
}

func (i insReportRuleSalesShareDo) Take() (*insbuy.InsReportRuleSalesShare, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShare), nil
	}
}

func (i insReportRuleSalesShareDo) Last() (*insbuy.InsReportRuleSalesShare, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShare), nil
	}
}

func (i insReportRuleSalesShareDo) Find() ([]*insbuy.InsReportRuleSalesShare, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShare), err
}

func (i insReportRuleSalesShareDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShare, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShare, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShare, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareDo) FirstOrInit() (*insbuy.InsReportRuleSalesShare, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShare), nil
	}
}

func (i insReportRuleSalesShareDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShare, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShare), nil
	}
}

func (i insReportRuleSalesShareDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShare, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareDo) Delete(models ...*insbuy.InsReportRuleSalesShare) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareDo) withDO(do gen.Dao) *insReportRuleSalesShareDo {
	i.DO = *do.(*gen.DO)
	return i
}
