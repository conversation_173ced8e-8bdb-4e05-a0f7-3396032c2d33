// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageItemTemplateDetails(db *gorm.DB, opts ...gen.DOOption) insProductPackageItemTemplateDetails {
	_insProductPackageItemTemplateDetails := insProductPackageItemTemplateDetails{}

	_insProductPackageItemTemplateDetails.insProductPackageItemTemplateDetailsDo.UseDB(db, opts...)
	_insProductPackageItemTemplateDetails.insProductPackageItemTemplateDetailsDo.UseModel(&insbuy.InsProductPackageItemTemplateDetails{})

	tableName := _insProductPackageItemTemplateDetails.insProductPackageItemTemplateDetailsDo.TableName()
	_insProductPackageItemTemplateDetails.ALL = field.NewAsterisk(tableName)
	_insProductPackageItemTemplateDetails.ID = field.NewUint(tableName, "id")
	_insProductPackageItemTemplateDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageItemTemplateDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageItemTemplateDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPackageItemTemplateDetails.TemplateId = field.NewUint(tableName, "template_id")
	_insProductPackageItemTemplateDetails.StoreId = field.NewUint(tableName, "store_id")
	_insProductPackageItemTemplateDetails.ItemId = field.NewInt(tableName, "item_id")
	_insProductPackageItemTemplateDetails.ProductId = field.NewInt(tableName, "product_id")
	_insProductPackageItemTemplateDetails.Num = field.NewInt(tableName, "num")
	_insProductPackageItemTemplateDetails.IfGive = field.NewInt(tableName, "if_give")

	_insProductPackageItemTemplateDetails.fillFieldMap()

	return _insProductPackageItemTemplateDetails
}

type insProductPackageItemTemplateDetails struct {
	insProductPackageItemTemplateDetailsDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	TemplateId field.Uint
	StoreId    field.Uint
	ItemId     field.Int
	ProductId  field.Int
	Num        field.Int
	IfGive     field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPackageItemTemplateDetails) Table(newTableName string) *insProductPackageItemTemplateDetails {
	i.insProductPackageItemTemplateDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageItemTemplateDetails) As(alias string) *insProductPackageItemTemplateDetails {
	i.insProductPackageItemTemplateDetailsDo.DO = *(i.insProductPackageItemTemplateDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageItemTemplateDetails) updateTableName(table string) *insProductPackageItemTemplateDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.TemplateId = field.NewUint(table, "template_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.ItemId = field.NewInt(table, "item_id")
	i.ProductId = field.NewInt(table, "product_id")
	i.Num = field.NewInt(table, "num")
	i.IfGive = field.NewInt(table, "if_give")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageItemTemplateDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageItemTemplateDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["template_id"] = i.TemplateId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["num"] = i.Num
	i.fieldMap["if_give"] = i.IfGive
}

func (i insProductPackageItemTemplateDetails) clone(db *gorm.DB) insProductPackageItemTemplateDetails {
	i.insProductPackageItemTemplateDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageItemTemplateDetails) replaceDB(db *gorm.DB) insProductPackageItemTemplateDetails {
	i.insProductPackageItemTemplateDetailsDo.ReplaceDB(db)
	return i
}

type insProductPackageItemTemplateDetailsDo struct{ gen.DO }

func (i insProductPackageItemTemplateDetailsDo) Debug() *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageItemTemplateDetailsDo) WithContext(ctx context.Context) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageItemTemplateDetailsDo) ReadDB() *insProductPackageItemTemplateDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageItemTemplateDetailsDo) WriteDB() *insProductPackageItemTemplateDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageItemTemplateDetailsDo) Session(config *gorm.Session) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageItemTemplateDetailsDo) Clauses(conds ...clause.Expression) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Returning(value interface{}, columns ...string) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageItemTemplateDetailsDo) Not(conds ...gen.Condition) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Or(conds ...gen.Condition) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Select(conds ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Where(conds ...gen.Condition) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Order(conds ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Distinct(cols ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageItemTemplateDetailsDo) Omit(cols ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageItemTemplateDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageItemTemplateDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageItemTemplateDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageItemTemplateDetailsDo) Group(cols ...field.Expr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageItemTemplateDetailsDo) Having(conds ...gen.Condition) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageItemTemplateDetailsDo) Limit(limit int) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageItemTemplateDetailsDo) Offset(offset int) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageItemTemplateDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageItemTemplateDetailsDo) Unscoped() *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageItemTemplateDetailsDo) Create(values ...*insbuy.InsProductPackageItemTemplateDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageItemTemplateDetailsDo) CreateInBatches(values []*insbuy.InsProductPackageItemTemplateDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageItemTemplateDetailsDo) Save(values ...*insbuy.InsProductPackageItemTemplateDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageItemTemplateDetailsDo) First() (*insbuy.InsProductPackageItemTemplateDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateDetails), nil
	}
}

func (i insProductPackageItemTemplateDetailsDo) Take() (*insbuy.InsProductPackageItemTemplateDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateDetails), nil
	}
}

func (i insProductPackageItemTemplateDetailsDo) Last() (*insbuy.InsProductPackageItemTemplateDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateDetails), nil
	}
}

func (i insProductPackageItemTemplateDetailsDo) Find() ([]*insbuy.InsProductPackageItemTemplateDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageItemTemplateDetails), err
}

func (i insProductPackageItemTemplateDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageItemTemplateDetails, err error) {
	buf := make([]*insbuy.InsProductPackageItemTemplateDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageItemTemplateDetailsDo) FindInBatches(result *[]*insbuy.InsProductPackageItemTemplateDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageItemTemplateDetailsDo) Attrs(attrs ...field.AssignExpr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageItemTemplateDetailsDo) Assign(attrs ...field.AssignExpr) *insProductPackageItemTemplateDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageItemTemplateDetailsDo) Joins(fields ...field.RelationField) *insProductPackageItemTemplateDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateDetailsDo) Preload(fields ...field.RelationField) *insProductPackageItemTemplateDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateDetailsDo) FirstOrInit() (*insbuy.InsProductPackageItemTemplateDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateDetails), nil
	}
}

func (i insProductPackageItemTemplateDetailsDo) FirstOrCreate() (*insbuy.InsProductPackageItemTemplateDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateDetails), nil
	}
}

func (i insProductPackageItemTemplateDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageItemTemplateDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageItemTemplateDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageItemTemplateDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageItemTemplateDetailsDo) Delete(models ...*insbuy.InsProductPackageItemTemplateDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageItemTemplateDetailsDo) withDO(do gen.Dao) *insProductPackageItemTemplateDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
