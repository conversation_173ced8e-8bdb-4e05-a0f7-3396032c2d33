package test

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore"
	"github.com/xtulnx/jkit-go/jtime"
	"testing"
)

// 测试同步线下门票
func TestSync(t *testing.T) {
	prepare()
	query.Q.Transaction(func(tx *query.Query) error {
		err := insstore.SyncTicketConsume(context.Background(), tx, insstore.SyncTicketConsumeParams{
			StoreId:   1,
			StartTime: jtime.Str2Time("2024-11-01 06:00:00"),
			EndTime:   jtime.Str2Time("2024-11-30 06:00:00"),
		})
		if err != nil {
			t.Fatalf("同步线下门票失败: %v", err)
			return err
		}
		return nil
	})

}

// 通联同步测试
func TestSync2(t *testing.T) {
	prepare()
	_ = query.Q.Transaction(func(tx *query.Query) error {
		err := insstore.SyncTicketConsume(context.Background(), tx, insstore.SyncTicketConsumeParams{
			StoreId: 6,
			Day:     jtime.Str2Time("2024-10-16"),
		})
		if err != nil {
			t.Fatalf("同步线下门票失败: %v", err)
			return err
		}
		return nil
	})

}
