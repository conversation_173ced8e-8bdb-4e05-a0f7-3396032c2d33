// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductActivityDetail(db *gorm.DB, opts ...gen.DOOption) insProductActivityDetail {
	_insProductActivityDetail := insProductActivityDetail{}

	_insProductActivityDetail.insProductActivityDetailDo.UseDB(db, opts...)
	_insProductActivityDetail.insProductActivityDetailDo.UseModel(&insbuy.InsProductActivityDetail{})

	tableName := _insProductActivityDetail.insProductActivityDetailDo.TableName()
	_insProductActivityDetail.ALL = field.NewAsterisk(tableName)
	_insProductActivityDetail.ID = field.NewUint(tableName, "id")
	_insProductActivityDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductActivityDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductActivityDetail.ActivityId = field.NewInt(tableName, "activity_id")
	_insProductActivityDetail.ProductId = field.NewInt(tableName, "product_id")
	_insProductActivityDetail.FormulaId = field.NewInt(tableName, "formula_id")
	_insProductActivityDetail.Value = field.NewFloat64(tableName, "value")

	_insProductActivityDetail.fillFieldMap()

	return _insProductActivityDetail
}

type insProductActivityDetail struct {
	insProductActivityDetailDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	ActivityId field.Int
	ProductId  field.Int
	FormulaId  field.Int
	Value      field.Float64

	fieldMap map[string]field.Expr
}

func (i insProductActivityDetail) Table(newTableName string) *insProductActivityDetail {
	i.insProductActivityDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductActivityDetail) As(alias string) *insProductActivityDetail {
	i.insProductActivityDetailDo.DO = *(i.insProductActivityDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductActivityDetail) updateTableName(table string) *insProductActivityDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ActivityId = field.NewInt(table, "activity_id")
	i.ProductId = field.NewInt(table, "product_id")
	i.FormulaId = field.NewInt(table, "formula_id")
	i.Value = field.NewFloat64(table, "value")

	i.fillFieldMap()

	return i
}

func (i *insProductActivityDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductActivityDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["activity_id"] = i.ActivityId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["formula_id"] = i.FormulaId
	i.fieldMap["value"] = i.Value
}

func (i insProductActivityDetail) clone(db *gorm.DB) insProductActivityDetail {
	i.insProductActivityDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductActivityDetail) replaceDB(db *gorm.DB) insProductActivityDetail {
	i.insProductActivityDetailDo.ReplaceDB(db)
	return i
}

type insProductActivityDetailDo struct{ gen.DO }

func (i insProductActivityDetailDo) Debug() *insProductActivityDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductActivityDetailDo) WithContext(ctx context.Context) *insProductActivityDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductActivityDetailDo) ReadDB() *insProductActivityDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductActivityDetailDo) WriteDB() *insProductActivityDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductActivityDetailDo) Session(config *gorm.Session) *insProductActivityDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductActivityDetailDo) Clauses(conds ...clause.Expression) *insProductActivityDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductActivityDetailDo) Returning(value interface{}, columns ...string) *insProductActivityDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductActivityDetailDo) Not(conds ...gen.Condition) *insProductActivityDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductActivityDetailDo) Or(conds ...gen.Condition) *insProductActivityDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductActivityDetailDo) Select(conds ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductActivityDetailDo) Where(conds ...gen.Condition) *insProductActivityDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductActivityDetailDo) Order(conds ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductActivityDetailDo) Distinct(cols ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductActivityDetailDo) Omit(cols ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductActivityDetailDo) Join(table schema.Tabler, on ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductActivityDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductActivityDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductActivityDetailDo) Group(cols ...field.Expr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductActivityDetailDo) Having(conds ...gen.Condition) *insProductActivityDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductActivityDetailDo) Limit(limit int) *insProductActivityDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductActivityDetailDo) Offset(offset int) *insProductActivityDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductActivityDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductActivityDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductActivityDetailDo) Unscoped() *insProductActivityDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductActivityDetailDo) Create(values ...*insbuy.InsProductActivityDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductActivityDetailDo) CreateInBatches(values []*insbuy.InsProductActivityDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductActivityDetailDo) Save(values ...*insbuy.InsProductActivityDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductActivityDetailDo) First() (*insbuy.InsProductActivityDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityDetail), nil
	}
}

func (i insProductActivityDetailDo) Take() (*insbuy.InsProductActivityDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityDetail), nil
	}
}

func (i insProductActivityDetailDo) Last() (*insbuy.InsProductActivityDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityDetail), nil
	}
}

func (i insProductActivityDetailDo) Find() ([]*insbuy.InsProductActivityDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductActivityDetail), err
}

func (i insProductActivityDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductActivityDetail, err error) {
	buf := make([]*insbuy.InsProductActivityDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductActivityDetailDo) FindInBatches(result *[]*insbuy.InsProductActivityDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductActivityDetailDo) Attrs(attrs ...field.AssignExpr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductActivityDetailDo) Assign(attrs ...field.AssignExpr) *insProductActivityDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductActivityDetailDo) Joins(fields ...field.RelationField) *insProductActivityDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductActivityDetailDo) Preload(fields ...field.RelationField) *insProductActivityDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductActivityDetailDo) FirstOrInit() (*insbuy.InsProductActivityDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityDetail), nil
	}
}

func (i insProductActivityDetailDo) FirstOrCreate() (*insbuy.InsProductActivityDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityDetail), nil
	}
}

func (i insProductActivityDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductActivityDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductActivityDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductActivityDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductActivityDetailDo) Delete(models ...*insbuy.InsProductActivityDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductActivityDetailDo) withDO(do gen.Dao) *insProductActivityDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
