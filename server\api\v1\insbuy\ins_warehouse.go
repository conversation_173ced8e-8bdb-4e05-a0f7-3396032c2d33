package insbuy

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseApi struct {
}

// CreateInsWarehouse 创建InsWarehouse
// @Tags InsWarehouse
// @Summary 创建InsWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouse true "创建InsWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/createInsWarehouse [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouse(c *gin.Context) {
	var insWarehouse insbuy.InsWarehouse
	if err := GinMustBind(c, &insWarehouse); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	ctx, storeId := c.Request.Context(), utils.GetHeaderStoreIdUint(c)
	if err := insWarehouseService.CreateInsWarehouse(ctx, storeId, &insWarehouse); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsWarehouse 删除InsWarehouse
// @Tags InsWarehouse
// @Summary 删除InsWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouse true "删除InsWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insWarehouse/deleteInsWarehouse [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouse(c *gin.Context) {
	var insWarehouse insbuy.InsWarehouse
	err := c.ShouldBindJSON(&insWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouse(insWarehouse); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsWarehouseByIds 批量删除InsWarehouse
// @Tags InsWarehouse
// @Summary 批量删除InsWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insWarehouse/deleteInsWarehouseByIds [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsWarehouse 更新InsWarehouse
// @Tags InsWarehouse
// @Summary 更新InsWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouse true "更新InsWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateInsWarehouse [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouse(c *gin.Context) {
	var insWarehouse insbuy.InsWarehouse
	err := c.ShouldBindJSON(&insWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouse(insWarehouse); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsWarehouse 用id查询InsWarehouse
// @Tags InsWarehouse
// @Summary 用id查询InsWarehouse
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsWarehouse true "用id查询InsWarehouse"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/findInsWarehouse [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouse(c *gin.Context) {
	var insWarehouse insbuy.InsWarehouse
	err := c.ShouldBindQuery(&insWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouse, err := insWarehouseService.GetInsWarehouse(insWarehouse.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsWarehouse": reinsWarehouse}, c)
	}
}

// GetInsWarehouseList 分页获取InsWarehouse列表
// @Tags InsWarehouse
// @Summary 分页获取InsWarehouse列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseSearch true "分页获取InsWarehouse列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Export() {
		list, e1 := insWarehouseService.ExportInsWarehouseInfoList(pageInfo)
		if e1 != nil {
			err = e1
			return
		}
		_, e := insImportService.ExcelCommonList(c, insbuy.ETWarehouseList.ToInt(), list)
		if e != nil {
			return
		}
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetAllInsWarehouseList 分页获取InsWarehouse列表
// @Tags InsWarehouse
// @Summary 分页获取InsWarehouse列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseSearch true "分页获取InsWarehouse列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getAllInsWarehouseList [get]
func (InsWarehouseApi *InsWarehouseApi) GetAllInsWarehouseList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseService.GetAllInsWarehouseList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInoutWarehouseList 获取出入库仓库列表
// @Tags InsWarehouse
// @Summary 获取出入库仓库列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetInoutWarehouseListReq true "获取出入库仓库列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInoutWarehouseList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInoutWarehouseList(c *gin.Context) {
	var pageInfo insbuyReq.GetInoutWarehouseListReq
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehouseService.GetInoutWarehouseList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// CreateInsWarehouseInventory 创建库存
// @Tags InsWarehouseInventory
// @Summary 创建InsWarehouseInventory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsWarehouseInventoryCreate true "创建库存记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/createInsWarehouseInventory [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInventory(c *gin.Context) {
	var insWarehouseInventory insbuyReq.InsWarehouseInventoryCreate
	if err := GinMustBind(c, &insWarehouseInventory); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	verify := utils.Rules{
		"InoutTypeId":  {utils.NotEmpty()},
		"WarehouseId":  {utils.NotEmpty()},
		"PurchaseType": {utils.NotEmpty()},
	}
	if err := utils.Verify(insWarehouseInventory, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.CreateInsWarehouseInventory(insWarehouseInventory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsWarehouseInventory 删除InsWarehouseInventory
// @Tags InsWarehouseInventory
// @Summary 删除InsWarehouseInventory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouseInventory true "删除InsWarehouseInventory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInventory [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInventory(c *gin.Context) {
	var insWarehouseInventory insbuy.InsWarehouseInventory
	err := c.ShouldBindJSON(&insWarehouseInventory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseInventory(insWarehouseInventory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsWarehouseInventoryByIds 批量删除InsWarehouseInventory
// @Tags InsWarehouseInventory
// @Summary 批量删除InsWarehouseInventory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsWarehouseInventory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInventoryByIds [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInventoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseInventoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsWarehouseInventory 更新InsWarehouseInventory
// @Tags InsWarehouseInventory
// @Summary 更新InsWarehouseInventory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouseInventory true "更新InsWarehouseInventory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateInsWarehouseInventory [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseInventory(c *gin.Context) {
	var insWarehouseInventory insbuy.InsWarehouseInventory
	err := c.ShouldBindJSON(&insWarehouseInventory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseInventory(insWarehouseInventory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsWarehouseInventory 用库存主键id查询库存明细
// @Tags InsWarehouseInventory
// @Summary 用库存主键id查询库存明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.WarehouseInventory true "用库存主键id查询库存明细"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/findInsWarehouseInventory [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouseInventory(c *gin.Context) {
	var insWarehouseInventory insbuyReq.WarehouseInventory
	err := c.ShouldBindQuery(&insWarehouseInventory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"InventoryId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insWarehouseInventory, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if reinsWarehouseInventory, err := insWarehouseService.GetInsWarehouseInventory(insWarehouseInventory); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsWarehouseInventory": reinsWarehouseInventory}, c)
	}
}

// GetInsWarehouseInventoryInfoList 分页获取库存列表
// @Tags InsWarehouseInventory
// @Summary 分页获取库存列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInventorySearch true "分页获取库存列表"
// @Success 200 {object} response.Response{data=insbuyResp.InsWarehouseInventoryList,msg=string}"
// @Router /insWarehouse/getInsWarehouseInventoryInfoList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInventoryInfoList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInventorySearch
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInventoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInventoryProductList 分页获取库存商品明细
// @Tags InsWarehouseInventory
// @Summary 分页获取库存商品明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInventoryProductSearch true "分页获取库存商品明细"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInventoryProductList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInventoryProductList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInventoryProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Page":     {utils.NotEmpty()},
		"PageSize": {utils.NotEmpty()},
	}
	if err := utils.Verify(pageInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := insWarehouseService.GetInsWarehouseInventoryProductList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInventoryChangeList 分页获取库存变更记录
// @Tags InsWarehouseInventory
// @Summary 分页获取库存变更记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInventorySearch true "分页获取库存变更记录列表"
// @Success 200 {object} response.Response{data=insbuyResp.InsWarehouseInventoryChangeItem,msg=string}"
// @Router /insWarehouse/getInsWarehouseInventoryChangeList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInventoryChangeList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInventoryChangeSearch
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInventoryChangeList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInventoryUnique 分页获取库存商品唯一编码
// @Tags InsWarehouseInventory
// @Summary 分页获取库存商品唯一编码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInventoryUnique true "分页获取库存商品唯一编码"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInventoryUnique [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInventoryUnique(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInventoryUnique
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInventoryUnique(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInventoryChangeDetail 分页获取库存变更明细记录
// @Tags InsWarehouseInventory
// @Summary 分页获取库存变更明细记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInventoryChangeDetailSearch true "分页获取库存变更明细记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInventoryChangeDetail [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInventoryChangeDetail(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInventoryChangeDetailSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"InventoryId": {utils.NotEmpty()},
	}
	if err := utils.Verify(pageInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := insWarehouseService.GetInsWarehouseInventoryChangeDetail(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInoutTypeList 分页获取库存类型列表
// @Tags InsWarehouseInout
// @Summary 分页获取库存类型列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInoutTypeSearch true "分页获取InsWarehouseInoutType列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInoutTypeList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutTypeList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInoutTypeSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInoutTypeInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsWarehouseInout 创建InsWarehouseInout
// @Tags InsWarehouseInout
// @Summary 创建InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsWarehouseInout true "创建InsWarehouseInout"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/createInsWarehouseInout [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInout(c *gin.Context) {
	var insWarehouseInout insbuyReq.CreateInsWarehouseInout
	err := GinMustBind(c, &insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insWarehouseService.CreateInsWarehouseInout(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithDetailed(res, "创建成功", c)
	}
}

// DeleteInsWarehouseInout 删除InsWarehouseInout
// @Tags InsWarehouseInout
// @Summary 删除InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DeleteInsWarehouseInoutReq true "删除InsWarehouseInout"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInout [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInout(c *gin.Context) {
	var req insbuyReq.DeleteInsWarehouseInoutReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseInout(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsWarehouseInoutByIds 批量删除InsWarehouseInout
// @Tags InsWarehouseInout
// @Summary 批量删除InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsWarehouseInout"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInoutByIds [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInoutByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	deletedBy := utils.GetUserID(c)
	if err := insWarehouseService.DeleteInsWarehouseInoutByIds(IDS, deletedBy); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsWarehouseInout 更新InsWarehouseInout
// @Tags InsWarehouseInout
// @Summary 更新InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsWarehouseInout true "更新InsWarehouseInout"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateInsWarehouseInout [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseInout(c *gin.Context) {
	var insWarehouseInout insbuyReq.UpdateInsWarehouseInout
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseInout(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsWarehouseInout 用id查询InsWarehouseInout
// @Tags InsWarehouseInout
// @Summary 用id查询InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetInsWarehouseInout true "用id查询InsWarehouseInout"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsWarehouseInout,msg=string}  "查询成功"
// @Router /insWarehouse/findInsWarehouseInout [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouseInout(c *gin.Context) {
	var insWarehouseInout insbuyReq.GetInsWarehouseInout
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseInout, err := insWarehouseService.FindInsWarehouseInout(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsWarehouseInout, c)
	}
}

// GetInsWarehouseInoutList 分页获取GetInsWarehouseInoutInfoList列表
// @Tags InsWarehouseInout
// @Summary 分页获取GetInsWarehouseInoutInfoList列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInoutSearch true "分页获取GetInsWarehouseInoutInfoList列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInoutList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInoutSearch
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInoutInfoList(pageInfo); err != nil {
		if pageInfo.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInList 分页获取入库单列表
// @Tags InsWarehouseInout
// @Summary 分页获取入库单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInSearch true "分页获取入库单列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseInList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInSearch
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsProductSupplierList 获取供应商原料列表
// @Tags InsWarehouseInout
// @Summary 获取供应商原料列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductSupplierSearch true "获取供应商原料列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsProductSupplierItem,msg=string}  "获取供应商商品列表"
// @Router /insWarehouse/getInsProductSupplierList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsProductSupplierList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductSupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, total, err := insWarehouseService.GetInsProductSupplierList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsProductWarehouseList 获取仓库商品列表
// @Tags InsWarehouseInout
// @Summary 获取仓库商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsProductWarehouseSearch true "获取仓库商品列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsProductWarehouseItem,msg=string}  "获取仓库商品列表"
// @Router /insWarehouse/getInsProductWarehouseList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsProductWarehouseList(c *gin.Context) {
	var pageInfo insbuyReq.InsProductWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	res := make([]insbuyResp.GetInsProductWarehouseItem, 0)
	if res, err = insWarehouseService.GetInsProductWarehouseList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// SundriesMaterialList 日杂原料列表
// @Tags InsWarehouseInout
// @Summary 日杂原料列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.SundriesMaterialReq true "日杂原料列表"
// @Success   200   {object}  response.Response{data=insbuyResp.SundriesMaterialListResp,msg=string}  "日杂原料列表"
// @Router /insWarehouse/sundriesMaterialList [get]
func (InsWarehouseApi *InsWarehouseApi) SundriesMaterialList(c *gin.Context) {
	var req insbuyReq.SundriesMaterialReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insWarehouseService.SundriesMaterialList(req)
	if req.Export() {
		_, e := insImportService.ExcelCommonList(c, insbuy.ETSundriesMaterialList.ToInt(), list.List)
		if e != nil {
			return
		}
		return
	}
	response.ResultErr(list, err, c)
}

// GetInsWarehouseInoutDetailsList 采购商品明细表
// @Tags InsWarehouseInout
// @Summary 采购商品明细表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetWarehouseInoutDetailsList true "采购商品明细表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsWarehouseInoutDetailsItem,msg=string}  "采购商品明细表"
// @Router /insWarehouse/getInsWarehouseInoutDetailsList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutDetailsList(c *gin.Context) {
	var pageInfo insbuyReq.GetWarehouseInoutDetailsList
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insWarehouseService.GetInsWarehouseInoutDetailsList(insbuy.SupplierType, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// CreateInsWarehouseInoutDetails 添加采购明细商品
// @Tags InsWarehouseInout
// @Summary 添加采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsWarehouseInoutDetails true "添加采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/createInsWarehouseInoutDetails [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInoutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.CreateInsWarehouseInoutDetails
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.CreateInsWarehouseInoutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateInsWarehouseInoutDetails 修改采购明细商品
// @Tags InsWarehouseInout
// @Summary 修改采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsWarehouseInoutDetails true "修改采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insWarehouse/updateInsWarehouseInoutDetails [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseInoutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.UpdateInsWarehouseInoutDetails
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseInoutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// SaveInoutDetails 保存采购明细商品
// @Tags InsWarehouseInout
// @Summary 保存采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SaveInoutDetailsReq true "保存采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"保存成功"}"
// @Router /insWarehouse/saveInoutDetails [post]
func (InsWarehouseApi *InsWarehouseApi) SaveInoutDetails(c *gin.Context) {
	var req insbuyReq.SaveInoutDetailsReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.SaveInoutDetails(req); err != nil {
		global.GVA_LOG.Error("保存失败!", zap.Error(err))
		response.FailWithMessage("保存失败", c)
	} else {
		response.OkWithMessage("保存成功", c)
	}
}

// UpdateInoutStatus 更新采购单状态
// @Tags InsWarehouseInout
// @Summary 更新采购单状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInoutStatusReq true "更新采购单状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateInoutStatus [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInoutStatus(c *gin.Context) {
	var req insbuyReq.UpdateInoutStatusReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInoutStatus(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// SundriesListDetailsList 获取日杂采购明细商品
// @Tags InsWarehouseInout
// @Summary 获取日杂采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetWarehouseInoutDetailsList true "获取日杂采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/sundriesListDetailsList [get]
func (InsWarehouseApi *InsWarehouseApi) SundriesListDetailsList(c *gin.Context) {
	var req insbuyReq.GetWarehouseInoutDetailsList
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	list, err := insWarehouseService.SundriesListDetailsList(req)
	response.ResultErr(list, err, c)
}

// DeleteInsWarehouseInoutDetails 移除采购明细商品
// @Tags InsWarehouseInout
// @Summary 移除采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.DeleteInsWarehouseInoutDetails true "移除采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"移除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInoutDetails [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInoutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.DeleteInsWarehouseInoutDetails
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseInoutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("移除失败!", zap.Error(err))
		response.FailWithMessage("移除失败", c)
	} else {
		response.OkWithMessage("移除成功", c)
	}
}

// StartInsWarehouseInout 发起采购
// @Tags InsWarehouseInout
// @Summary 发起采购
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartInsWarehouseInout true "发起采购"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"采购成功"}"
// @Router /insWarehouse/startInsWarehouseInout [put]
func (InsWarehouseApi *InsWarehouseApi) StartInsWarehouseInout(c *gin.Context) {
	var insWarehouseInout insbuyReq.StartInsWarehouseInout
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.StartPurchase(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("采购失败!", zap.Error(err))
		response.FailWithMessage("采购失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("采购成功", c)
	}
}

// StartInsWarehouseOut 发起出库
// @Tags InsWarehouseInout
// @Summary 发起采购
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartInsWarehouseInout true "发起出库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发起出库成功"}"
// @Router /insWarehouse/startInsWarehouseOut [put]
func (InsWarehouseApi *InsWarehouseApi) StartInsWarehouseOut(c *gin.Context) {
	var insWarehouseInout insbuyReq.StartInsWarehouseInout
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.StartInsWarehouseInout(insbuy.WarehouseType, insWarehouseInout); err != nil {
		global.GVA_LOG.Error("出库失败!", zap.Error(err))
		response.FailWithMessage("出库失败"+err.Error(), c)
	} else {
		response.OkWithMessage("出库成功", c)
	}
}

// BatchStartInout 批量发起采购
// @Tags InsWarehouseInout
// @Summary 批量发起采购
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchStartInoutReq true "批量发起采购"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量发起采购成功"}"
// @Router /insWarehouse/batchStartInout [put]
func (InsWarehouseApi *InsWarehouseApi) BatchStartInout(c *gin.Context) {
	var req insbuyReq.BatchStartInoutReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.BatchStartInout(insbuy.WarehouseType, req); err != nil {
		global.GVA_LOG.Error("批量发起采购失败!", zap.Error(err))
		response.FailWithMessage("批量发起采购失败"+err.Error(), c)
	} else {
		response.OkWithMessage("批量发起采购成功", c)
	}
}

// GetInsWarehouseInoutLogList 采购单操作记录
// @Tags InsWarehouseInout
// @Summary 采购单操作记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsWarehouseInoutLogSearch true "采购单操作记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/getInsWarehouseInoutLogList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutLogList(c *gin.Context) {
	var insWarehouseInout insbuyReq.InsWarehouseInoutLogSearch
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	getInsWarehouseInoutLogListVerify := utils.Rules{
		"InoutId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insWarehouseInout, getInsWarehouseInoutLogListVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, total, err := insWarehouseService.GetInsWarehouseInoutLogList(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     insWarehouseInout.Page,
			PageSize: insWarehouseInout.PageSize,
		}, "获取成功", c)
	}
}

// GetInsWarehouseInoutReceiptList 采购单单据列表
// @Tags InsWarehouseInout
// @Summary 采购单单据列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsWarehouseInoutReceiptSearch true "采购单单据列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/getInsWarehouseInoutReceiptList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutReceiptList(c *gin.Context) {
	var insWarehouseInout insbuyReq.InsWarehouseInoutReceiptSearch
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insWarehouseService.GetInsWarehouseInoutReceiptList(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

//入库相关-------

// FindInsWarehouseInoutStorage 用id查询采购商品入库
// @Tags InsWarehouseInout
// @Summary 用id查询采购商品入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetInsWarehouseInoutStorage true "用id查询采购商品入库"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsWarehouseInoutStorage,msg=string}  "查询成功"
// @Router /insWarehouse/findInsWarehouseInoutStorage [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouseInoutStorage(c *gin.Context) {
	var insWarehouseInout insbuyReq.GetInsWarehouseInoutStorage
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseInout, err := insWarehouseService.GetInsWarehouseInoutStorage(insWarehouseInout.InoutId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsWarehouseInout, c)
	}
}

// GetWarehouseInoutDetailsList 采购商品入库商品明细表
// @Tags InsWarehouseInout
// @Summary 采购商品入库商品明细表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetWarehouseInoutDetailsList true "采购商品入库商品明细表"
// @Success   200   {object}  response.Response{data=insbuyResp.PurchaseDetailsListField,msg=string}  "采购商品入库商品明细表"
// @Router /insWarehouse/getWarehouseInoutDetailsList [get]
func (InsWarehouseApi *InsWarehouseApi) GetWarehouseInoutDetailsList(c *gin.Context) {
	var pageInfo insbuyReq.GetWarehouseInoutDetailsList
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insWarehouseService.GetInsWarehouseInoutDetailsList(insbuy.SupplierType, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// UpdateInsWarehouseInoutStorage 修改采购入库明细商品
// @Tags InsWarehouseInout
// @Summary 修改采购入库明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsWarehouseInoutStorage true "修改采购入库明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insWarehouse/updateInsWarehouseInoutStorage [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseInoutStorage(c *gin.Context) {
	var insWarehouseInout insbuyReq.UpdateInsWarehouseInoutStorage
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseInoutStorage(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// CreateInsWarehouseInoutStorage 商品入库添加采购明细商品
// @Tags InsWarehouseInout
// @Summary 商品入库添加采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsWarehouseInoutStorage true "商品入库添加采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/createInsWarehouseInoutStorage [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInoutStorage(c *gin.Context) {
	var insWarehouseInout insbuyReq.CreateInsWarehouseInoutStorage
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.CreateInsWarehouseInoutStorage(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsWarehouseInoutDetails 移除采购明细商品
// @Tags InsWarehouseInout
// @Summary 移除采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.DeleteInsWarehouseInoutStorage true "移除采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"移除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInoutStorage [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseInoutStorage(c *gin.Context) {
	var insWarehouseInout insbuyReq.DeleteInsWarehouseInoutStorage
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseInoutStorage(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("移除失败!", zap.Error(err))
		response.FailWithMessage("移除失败", c)
	} else {
		response.OkWithMessage("移除成功", c)
	}
}

//出库-----------

// GetWarehouseOutList 分页获取GetWarehouseOutList列表
// @Tags InsWarehouseInout
// @Summary 分页获取GetWarehouseOutList列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseOutSearch true "分页获取GetWarehouseOutList列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getWarehouseOutList [get]
func (InsWarehouseApi *InsWarehouseApi) GetWarehouseOutList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseOutSearch
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insWarehouseService.GetWarehouseOutList(pageInfo); err != nil {
		if pageInfo.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// FindInsWarehouseOutDetails 用id查询出库详情
// @Tags InsWarehouseInout
// @Summary 用id查询采购出库详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetWarehouseOutDetails true "用id查询采购出库详情"
// @Success   200   {object}  response.Response{data=insbuyResp.GetWarehouseOutDetails,msg=string}  "查询成功"
// @Router /insWarehouse/findInsWarehouseOutDetails [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouseOutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.GetWarehouseOutDetails
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseInout, err := insWarehouseService.NewInsWarehouseOutService().GetWarehouseOutDetails(insWarehouseInout.InoutId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsWarehouseInout, c)
	}
}

// GetWarehouseOutDetailsList 出库商品明细表
// @Tags InsWarehouseInout
// @Summary 出库商品入库商品明细表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetWarehouseInoutDetailsList true "出库商品明细表"
// @Success   200   {object}  response.Response{data=insbuyResp.WarehouseOutDetailsField,msg=string}  "出库商品明细表"
// @Router /insWarehouse/getWarehouseOutDetailsList [get]
func (InsWarehouseApi *InsWarehouseApi) GetWarehouseOutDetailsList(c *gin.Context) {
	var pageInfo insbuyReq.GetWarehouseInoutDetailsList
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insWarehouseService.GetInsWarehouseInoutDetailsList(insbuy.WarehouseType, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// UpdateInsWarehouseOutDetails 修改出库商品明细
// @Tags InsWarehouseInout
// @Summary 修改出库商品明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsWarehouseOutDetails true "修改出库商品明细"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insWarehouse/updateInsWarehouseOutDetails [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseOutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.UpdateInsWarehouseOutDetails
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseOutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// CreateInsWarehouseOutDetails 商品出库添加采购明细商品
// @Tags InsWarehouseInout
// @Summary 商品出库添加采购明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsWarehouseOutDetails true "商品出库添加采购明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/createInsWarehouseOutDetails [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseOutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.CreateInsWarehouseOutDetails
	err := c.ShouldBindJSON(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.CreateInsWarehouseOutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsWarehouseOutDetails 移除出库明细商品
// @Tags InsWarehouseInout
// @Summary 移除出库明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.DeleteInsWarehouseOutDetails true "移除出库明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"移除成功"}"
// @Router /insWarehouse/deleteInsWarehouseInoutStorage [delete]
func (InsWarehouseApi *InsWarehouseApi) DeleteInsWarehouseOutDetails(c *gin.Context) {
	var insWarehouseInout insbuyReq.DeleteInsWarehouseOutDetails
	err := c.ShouldBindJSON(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.DeleteInsWarehouseOutDetails(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("移除失败!", zap.Error(err))
		response.FailWithMessage("移除失败", c)
	} else {
		response.OkWithMessage("移除成功", c)
	}
}

// CreateInsWarehouseInoutDetailUnique 采购录入唯一编码
// @Tags InsWarehouseInout
// @Summary 采购录入唯一编码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateInsWarehouseInoutDetailUnique true "采购录入唯一编码请求参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/createInsWarehouseInoutDetailUnique [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInoutDetailUnique(c *gin.Context) {
	var insWarehouseInout insbuyReq.CreateInsWarehouseInoutDetailUnique
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.CreateInsWarehouseInoutDetailUnique(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// GetInsWarehouseInoutDetailUniqueList 录入唯一编码列表
// @Tags InsWarehouseInout
// @Summary 录入唯一编码列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetInsWarehouseInoutDetailUniqueList true "录入唯一编码列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsWarehouseInoutDetailUniqueList,msg=string}  "录入唯一编码列表"
// @Router /insWarehouse/getInsWarehouseInoutDetailUniqueList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutDetailUniqueList(c *gin.Context) {
	var pageInfo insbuyReq.GetInsWarehouseInoutDetailUniqueList
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if res, err := insWarehouseService.GetInsWarehouseInoutDetailUniqueList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// ConfirmOutWarehouse 确认入库
// @Tags InsWarehouseInout
// @Summary 确认出库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfirmInWarehouseReq true "确认入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/confirmOutWarehouse [put]
func (InsWarehouseApi *InsWarehouseApi) ConfirmOutWarehouse(c *gin.Context) {
	var insWarehouseInout insbuyReq.ConfirmInWarehouseReq
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.ConfirmInWarehouse(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("确认失败!", zap.Error(err))
		response.FailWithMessage("确认失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("确认成功", c)
	}
}

// ConfirmInWarehouseBatch 批量确认入库
// @Tags InsWarehouseInout
// @Summary 批量确认入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfirmInWarehouseReq true "批量确认入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加成功"}"
// @Router /insWarehouse/confirmInWarehouseBatch [put]
func (InsWarehouseApi *InsWarehouseApi) ConfirmInWarehouseBatch(c *gin.Context) {
	var insWarehouseInout insbuyReq.ConfirmInWarehouseReq
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.ConfirmInWarehouseBatch(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("确认失败!", zap.Error(err))
		response.FailWithMessage("确认失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("确认成功", c)
	}
}

// GetInsWarehouseInoutReceiveList 获取领用列表
// @Tags InsWarehouseInout
// @Summary 获取领用列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseInoutInoutReceiveSearch true "获取领用列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsWarehouseInoutReceiveListResp,msg=string}  "获取领用列表"
// @Router /insWarehouse/getInsWarehouseInoutReceiveList [get]
func (InsWarehouseApi *InsWarehouseApi) GetInsWarehouseInoutReceiveList(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseInoutInoutReceiveSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseService.GetInsWarehouseInoutReceiveList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsWarehouseInoutReceive 创建领用单
// @Tags InsWarehouseInout
// @Summary 创建领用单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SaveInsWarehouseInoutReceive true "创建领用单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/createInsWarehouseInoutReceive [post]
func (InsWarehouseApi *InsWarehouseApi) CreateInsWarehouseInoutReceive(c *gin.Context) {
	var insWarehouseInout insbuyReq.SaveInsWarehouseInoutReceive
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if res, err := insWarehouseService.CreateInsWarehouseInoutReceive(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithDetailed(res, "创建成功", c)
	}
}

// UpdateInsWarehouseInoutReceive 修改领用单
// @Tags InsWarehouseInout
// @Summary 修改领用单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SaveInsWarehouseInoutReceive true "修改领用单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insWarehouse/updateInsWarehouseInoutReceive [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateInsWarehouseInoutReceive(c *gin.Context) {
	var insWarehouseInout insbuyReq.SaveInsWarehouseInoutReceive
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.UpdateInsWarehouseInoutReceive(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// FindInsWarehouseInoutReceive 用id查询领用详情
// @Tags InsWarehouseInout
// @Summary 用id查询InsWarehouseInout
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsWarehouseInoutReceive true "用id查询领用详情"
// @Success   200   {object}  response.Response{data=insbuyResp.FindInsWarehouseInoutReceiveResp,msg=string}  "查询成功"
// @Router /insWarehouse/findInsWarehouseInoutReceive [get]
func (InsWarehouseApi *InsWarehouseApi) FindInsWarehouseInoutReceive(c *gin.Context) {
	var insWarehouseInout insbuyReq.FindInsWarehouseInoutReceive
	err := c.ShouldBindQuery(&insWarehouseInout)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseInout, err := insWarehouseService.FindInsWarehouseInoutReceive(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsWarehouseInout, c)
	}
}

// StartInsWarehouseInoutReceive 发起领用
// @Tags InsWarehouseInout
// @Summary 发起领用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartInsWarehouseInout true "发起领用"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"领用成功"}"
// @Router /insWarehouse/startInsWarehouseInoutReceive [put]
func (InsWarehouseApi *InsWarehouseApi) StartInsWarehouseInoutReceive(c *gin.Context) {
	var insWarehouseInout insbuyReq.StartInsWarehouseInout
	if err := GinMustBind(c, &insWarehouseInout); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.StartWarehouse(insWarehouseInout); err != nil {
		global.GVA_LOG.Error("领用失败!", zap.Error(err))
		response.FailWithMessage("领用失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("领用成功", c)
	}
}

// SourceSnToInoutId 来源sn转换为inoutId
// @Tags InsWarehouseInout
// @Summary 来源sn转换为inoutId
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.SourceSnToInoutIdReq true "来源sn转换为inoutId"
// @Success   200   {object}  response.Response{data=insbuyResp.SourceSnToInoutIdResp,msg=string}  "查询成功"
// @Router /insWarehouse/sourceSnToInoutId [get]
func (InsWarehouseApi *InsWarehouseApi) SourceSnToInoutId(c *gin.Context) {
	var req insbuyReq.SourceSnToInoutIdReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsWarehouseInout, err := insWarehouseService.SourceSnToInoutId(req.SourceSn); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsWarehouseInout, c)
	}
}

// AddMaterialByName 手动添加原料
// @Tags InsWarehouseInout
// @Summary 手动添加原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AddMaterialByNameReq true "手动添加原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/addMaterialByName [post]
func (InsWarehouseApi *InsWarehouseApi) AddMaterialByName(c *gin.Context) {
	var req insbuyReq.AddMaterialByNameReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if res, err := insWarehouseService.AddMaterialByName(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(res, "创建成功", c)
	}
}

// GetWarehouseDetail  获取仓库详情
// @Tags InsWarehouse
// @Summary 获取仓库详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsMaterialDetails true "获取仓库详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getWarehouseDetail [get]
func (InsWarehouseApi *InsWarehouseApi) GetWarehouseDetail(c *gin.Context) {
	var req insbuyReq.InsMaterialDetails
	err := c.ShouldBindQuery(&req)
	err = GinBindAfter(c, &req, err)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if res, err := insWarehouseService.GetWarehouseDetail(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(res, "获取成功", c)
	}
}

// RejectInout 驳回
// @Tags rejectInout
// @Summary 驳回
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RejectInoutReq true "驳回"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"驳回"}"
// @Router /insWarehouse/rejectInout [put]
func (InsWarehouseApi *InsWarehouseApi) RejectInout(c *gin.Context) {
	var req insbuyReq.RejectInoutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.RejectInout(req); err != nil {
		global.GVA_LOG.Error("驳回失败!", zap.Error(err))
		response.FailWithMessage("驳回失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("驳回成功", c)
	}
}

// RedFlushInout 冲红
// @Tags rejectInout
// @Summary 冲红
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RedFlushInoutReq true "冲红"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"冲红"}"
// @Router /insWarehouse/redFlushInout [put]
func (InsWarehouseApi *InsWarehouseApi) RedFlushInout(c *gin.Context) {
	var req insbuyReq.RedFlushInoutReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.RedFlushInout(req); err != nil {
		global.GVA_LOG.Error("冲红失败!", zap.Error(err))
		response.FailWithMessage("冲红失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("冲红成功", c)
	}
}

// RedFlushInoutAgain 冲红状态重新发起
// @Tags rejectInout
// @Summary 冲红状态重新发起
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RedFlushInoutReq true "冲红状态重新发起"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"冲红状态重新发起"}"
// @Router /insWarehouse/redFlushInoutAgain [put]
func (InsWarehouseApi *InsWarehouseApi) RedFlushInoutAgain(c *gin.Context) {
	var req insbuyReq.RedFlushInoutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseService.RedFlushInoutAgain(req); err != nil {
		global.GVA_LOG.Error("重新发起失败!", zap.Error(err))
		response.FailWithMessage("重新发起失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("重新发起成功", c)
	}
}

// CopyInoutDetails 复制另一条数据的明细
// @Tags InsWarehouseInout
// @Summary 复制另一条数据的明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CopyInoutDetailsReq true "复制另一条数据的明细"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"复制成功"}"
// @Router /insWarehouse/copyInoutDetails [post]
func (InsWarehouseApi *InsWarehouseApi) CopyInoutDetails(c *gin.Context) {
	var req insbuyReq.CopyInoutDetailsReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseService.CopyInoutDetails(req); err != nil {
		global.GVA_LOG.Error("复制失败!", zap.Error(err))
		response.FailWithMessage("复制失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(nil, "复制成功", c)
	}
}

// GetWarehouseInventoryDay 分页获取库存进销存列表
// @Tags InsWarehouse
// @Summary 分页获取每日库存列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.WarehouseInventoryDay true "分页获取库存进销存列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getWarehouseInventoryDay [get]
func (InsWarehouseApi *InsWarehouseApi) GetWarehouseInventoryDay(c *gin.Context) {
	var pageInfo insbuyReq.WarehouseInventoryDay
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseService.GetWarehouseInventoryDay(c, pageInfo); err != nil {
		if pageInfo.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.IsExport == 1 {
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ChangeInventory 库存变更
// @Tags InsWarehouse
// @Summary 库存变更
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ChangeInventoryReq true "库存变更"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/changeInventory [put]
func (InsWarehouseApi *InsWarehouseApi) ChangeInventory(c *gin.Context) {
	var req insbuyReq.ChangeInventoryReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err := insWarehouseService.ChangeInventory(c, req)
	response.Err(err, c)
}

// GenerateAllStoreSaleLog 生成店铺销售记录
// @Tags InsWarehouse
// @Summary 生成店铺销售记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BusinessDaySaleLogParams true "生成店铺销售记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/allStoreSaleLog [post]
func (InsWarehouseApi *InsWarehouseApi) GenerateAllStoreSaleLog(c *gin.Context) {
	var req insbuyReq.BusinessDaySaleLogReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err := insWarehouseService.GenerateAllStoreSaleLog(context.Background(), query.Q, req)
	response.Err(err, c)
}

// GenerateAllStoreCostPrice 生成全店的调拨成本
// @Tags InsWarehouse
// @Summary 生成全店的调拨成本
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StatisticalCostPriceReq true "生成全店的调拨成本"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/allStoreCostPrice [post]
func (InsWarehouseApi *InsWarehouseApi) GenerateAllStoreCostPrice(c *gin.Context) {
	var req insbuyReq.StatisticalCostPriceReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	err := insWarehouseService.GenerateAllStoreCostPrice(context.Background(), query.Q, req)
	response.Err(err, c)
}

// TransferScript 调拨库存成本记录
// @Tags InsWarehouse
// @Summary 调拨库存成本记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.TransferScriptReq true "调拨库存成本记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/transferScript [post]
func (InsWarehouseApi *InsWarehouseApi) TransferScript(c *gin.Context) {
	var req insbuyReq.TransferScriptReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.TransferScript(req.GetCtx(), req)
	response.Err(err, c)
}

// WineList 存酒借入列表
// @Tags InsWarehouse
// @Summary 存酒借入列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseOutSearch true "存酒借入列表"
// @Success 200 {object} response.Response{data=insbuyResp.GetInsWarehouseOutInfoBase,msg=string}"
// @Router /insWarehouse/wineList [get]
func (InsWarehouseApi *InsWarehouseApi) WineList(c *gin.Context) {
	var req insbuyReq.InsWarehouseOutSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insWarehouseService.WineList(req)
	response.OkWithData(list, c)
}

// WineOverview 存酒借入归还概览
// @Tags InsWarehouse
// @Summary 存酒借入归还概览
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseOutSearch true "存酒借入归还概览"
// @Success 200 {object} response.Response{data=insbuyResp.GetInsWarehouseOutInfoBase,msg=string}"
// @Router /insWarehouse/wineOverview [get]
func (InsWarehouseApi *InsWarehouseApi) WineOverview(c *gin.Context) {
	var req insbuyReq.InsWarehouseOutSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insWarehouseService.WineOverview(req)
	response.OkWithData(list, c)
}

// SundriesList 日杂调拨列表
// @Tags InsWarehouse
// @Summary 日杂调拨列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseOutSearch true "日杂调拨列表"
// @Success 200 {object} response.Response{data=insbuyResp.SundriesListResp,msg=string}"
// @Router /insWarehouse/sundriesList [get]
func (InsWarehouseApi *InsWarehouseApi) SundriesList(c *gin.Context) {
	var req insbuyReq.InsWarehouseInoutCommonSearch
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, err := insWarehouseService.SundriesList(req)
	response.ResultErr(list, err, c)
}

// InventoryDetailsList 库存变更明细
// @Tags InsWarehouse
// @Summary 库存明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InventoryDetailsListReq true "库存变更明细"
// @Success 200 {object} response.Response{data=insbuyResp.InventoryDetailsListResp,msg=string}"
// @Router /insWarehouse/inventoryDetailsList [get]
func (InsWarehouseApi *InsWarehouseApi) InventoryDetailsList(c *gin.Context) {
	var req insbuyReq.InventoryDetailsListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if req.Export() {
		req.PageInfo.SetAllPage()
	}
	list, err := insWarehouseService.InventoryDetailsList(req)
	if req.Export() {
		_, e := insImportService.ExcelCommonList(c, insbuy.ETStorageChangeList.ToInt(), list.List)
		if e != nil {
			return
		}
		return
	}
	response.ResultErr(list, err, c)
}

// ExecuteProcess
// @Tags InsWarehouse
// @Summary 执行流程
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ExecuteProcessReq true "执行流程"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"执行成功"}"
// @Router /insWarehouse/executeProcess [post]
func (InsWarehouseApi *InsWarehouseApi) ExecuteProcess(c *gin.Context) {
	var req insbuyReq.ExecuteProcessReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.ExecuteProcess(req)
	response.Err(err, c)
}

// ExecuteInout 执行单据
// @Tags InsWarehouse
// @Summary 执行单据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ExecuteProcessReq true "执行单据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"执行成功"}"
// @Router /insWarehouse/executeInout [post]
func (InsWarehouseApi *InsWarehouseApi) ExecuteInout(c *gin.Context) {
	var req insbuyReq.ExecuteProcessReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.ExecuteInout(req)
	response.Err(err, c)
}

// WriteCustomInout 写入自定义单据
// @Tags InsWarehouse
// @Summary 写入自定义单据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.WriteCustomInoutReq true "写入自定义单据"
// @Success 200 {object} response.Response{data=insbuy.InsTransferExecution,msg=string}"
// @Router /insWarehouse/writeCustomInout [post]
func (InsWarehouseApi *InsWarehouseApi) WriteCustomInout(c *gin.Context) {
	var req insbuyReq.WriteCustomInoutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.WriteCustomInout(req)
	response.ResultErr(resp, err, c)
}

// ExecuteBaseDetails 执行单据基础详情
// @Tags InsWarehouse
// @Summary 执行单据基础详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ExecuteDetailsReq true "执行单据基础详情"
// @Success 200 {object} response.Response{data=insbuyResp.ExecuteBaseDetailsResp,msg=string}"
// @Router /insWarehouse/executeBaseDetails [get]
func (InsWarehouseApi *InsWarehouseApi) ExecuteBaseDetails(c *gin.Context) {
	var req insbuyReq.ExecuteDetailsReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.ExecuteBaseDetails(req)
	response.ResultErr(resp, err, c)
}

// ProcessList 流程列表
// @Tags InsWarehouse
// @Summary 流程列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ProcessListReq true "流程列表"
// @Success 200 {object} response.Response{data=insbuy.InsTransferProcess,msg=string}"
// @Router /insWarehouse/processList [get]
func (InsWarehouseApi *InsWarehouseApi) ProcessList(c *gin.Context) {
	var req insbuyReq.ProcessListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.ProcessList(req)
	response.ResultErr(resp, err, c)
}

// CustomInoutList 自定义单据列表
// @Tags InsWarehouse
// @Summary 自定义单据列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CustomInoutListReq true "自定义单据列表"
// @Success 200 {object} response.Response{data=insbuyResp.CustomInoutListResp,msg=string}"
// @Router /insWarehouse/customInoutList [get]
func (InsWarehouseApi *InsWarehouseApi) CustomInoutList(c *gin.Context) {
	var req insbuyReq.CustomInoutListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.CustomInoutList(req)
	response.ResultErr(resp, err, c)
}

// UpdateExecuteDetails 更新执行单据详情
// @Tags InsWarehouse
// @Summary 更新执行单据详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.WriteCustomInoutReq true "更新执行单据详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateExecuteDetails [put]
func (InsWarehouseApi *InsWarehouseApi) UpdateExecuteDetails(c *gin.Context) {
	var req insbuyReq.WriteCustomInoutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.UpdateExecuteDetails(req)
	response.Err(err, c)
}

// DelInout 删除快捷调拨
// @Tags InsWarehouse
// @Summary 删除快捷调拨
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DelInoutReq true "删除快捷调拨"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insWarehouse/delInout [delete]
func (InsWarehouseApi *InsWarehouseApi) DelInout(c *gin.Context) {
	var req insbuyReq.DelInoutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.DelInout(req)
	response.Err(err, c)
}
