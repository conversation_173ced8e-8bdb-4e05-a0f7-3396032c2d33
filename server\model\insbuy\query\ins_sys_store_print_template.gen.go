// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSysStorePrintTemplate(db *gorm.DB, opts ...gen.DOOption) insSysStorePrintTemplate {
	_insSysStorePrintTemplate := insSysStorePrintTemplate{}

	_insSysStorePrintTemplate.insSysStorePrintTemplateDo.UseDB(db, opts...)
	_insSysStorePrintTemplate.insSysStorePrintTemplateDo.UseModel(&insbuy.InsSysStorePrintTemplate{})

	tableName := _insSysStorePrintTemplate.insSysStorePrintTemplateDo.TableName()
	_insSysStorePrintTemplate.ALL = field.NewAsterisk(tableName)
	_insSysStorePrintTemplate.ID = field.NewUint(tableName, "id")
	_insSysStorePrintTemplate.CreatedAt = field.NewTime(tableName, "created_at")
	_insSysStorePrintTemplate.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSysStorePrintTemplate.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSysStorePrintTemplate.StoreId = field.NewUint(tableName, "store_id")
	_insSysStorePrintTemplate.EventType = field.NewInt(tableName, "event_type")
	_insSysStorePrintTemplate.Code = field.NewString(tableName, "code")
	_insSysStorePrintTemplate.TemplateCode = field.NewString(tableName, "template_code")
	_insSysStorePrintTemplate.LangCode = field.NewString(tableName, "lang_code")
	_insSysStorePrintTemplate.PrintTemplateId = field.NewUint(tableName, "print_template_id")

	_insSysStorePrintTemplate.fillFieldMap()

	return _insSysStorePrintTemplate
}

type insSysStorePrintTemplate struct {
	insSysStorePrintTemplateDo

	ALL             field.Asterisk
	ID              field.Uint
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	StoreId         field.Uint
	EventType       field.Int
	Code            field.String
	TemplateCode    field.String
	LangCode        field.String
	PrintTemplateId field.Uint

	fieldMap map[string]field.Expr
}

func (i insSysStorePrintTemplate) Table(newTableName string) *insSysStorePrintTemplate {
	i.insSysStorePrintTemplateDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSysStorePrintTemplate) As(alias string) *insSysStorePrintTemplate {
	i.insSysStorePrintTemplateDo.DO = *(i.insSysStorePrintTemplateDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSysStorePrintTemplate) updateTableName(table string) *insSysStorePrintTemplate {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.EventType = field.NewInt(table, "event_type")
	i.Code = field.NewString(table, "code")
	i.TemplateCode = field.NewString(table, "template_code")
	i.LangCode = field.NewString(table, "lang_code")
	i.PrintTemplateId = field.NewUint(table, "print_template_id")

	i.fillFieldMap()

	return i
}

func (i *insSysStorePrintTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSysStorePrintTemplate) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["event_type"] = i.EventType
	i.fieldMap["code"] = i.Code
	i.fieldMap["template_code"] = i.TemplateCode
	i.fieldMap["lang_code"] = i.LangCode
	i.fieldMap["print_template_id"] = i.PrintTemplateId
}

func (i insSysStorePrintTemplate) clone(db *gorm.DB) insSysStorePrintTemplate {
	i.insSysStorePrintTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSysStorePrintTemplate) replaceDB(db *gorm.DB) insSysStorePrintTemplate {
	i.insSysStorePrintTemplateDo.ReplaceDB(db)
	return i
}

type insSysStorePrintTemplateDo struct{ gen.DO }

func (i insSysStorePrintTemplateDo) Debug() *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Debug())
}

func (i insSysStorePrintTemplateDo) WithContext(ctx context.Context) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSysStorePrintTemplateDo) ReadDB() *insSysStorePrintTemplateDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSysStorePrintTemplateDo) WriteDB() *insSysStorePrintTemplateDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSysStorePrintTemplateDo) Session(config *gorm.Session) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSysStorePrintTemplateDo) Clauses(conds ...clause.Expression) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSysStorePrintTemplateDo) Returning(value interface{}, columns ...string) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSysStorePrintTemplateDo) Not(conds ...gen.Condition) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSysStorePrintTemplateDo) Or(conds ...gen.Condition) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSysStorePrintTemplateDo) Select(conds ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSysStorePrintTemplateDo) Where(conds ...gen.Condition) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSysStorePrintTemplateDo) Order(conds ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSysStorePrintTemplateDo) Distinct(cols ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSysStorePrintTemplateDo) Omit(cols ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSysStorePrintTemplateDo) Join(table schema.Tabler, on ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSysStorePrintTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSysStorePrintTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSysStorePrintTemplateDo) Group(cols ...field.Expr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSysStorePrintTemplateDo) Having(conds ...gen.Condition) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSysStorePrintTemplateDo) Limit(limit int) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSysStorePrintTemplateDo) Offset(offset int) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSysStorePrintTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSysStorePrintTemplateDo) Unscoped() *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSysStorePrintTemplateDo) Create(values ...*insbuy.InsSysStorePrintTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSysStorePrintTemplateDo) CreateInBatches(values []*insbuy.InsSysStorePrintTemplate, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSysStorePrintTemplateDo) Save(values ...*insbuy.InsSysStorePrintTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSysStorePrintTemplateDo) First() (*insbuy.InsSysStorePrintTemplate, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysStorePrintTemplate), nil
	}
}

func (i insSysStorePrintTemplateDo) Take() (*insbuy.InsSysStorePrintTemplate, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysStorePrintTemplate), nil
	}
}

func (i insSysStorePrintTemplateDo) Last() (*insbuy.InsSysStorePrintTemplate, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysStorePrintTemplate), nil
	}
}

func (i insSysStorePrintTemplateDo) Find() ([]*insbuy.InsSysStorePrintTemplate, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSysStorePrintTemplate), err
}

func (i insSysStorePrintTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSysStorePrintTemplate, err error) {
	buf := make([]*insbuy.InsSysStorePrintTemplate, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSysStorePrintTemplateDo) FindInBatches(result *[]*insbuy.InsSysStorePrintTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSysStorePrintTemplateDo) Attrs(attrs ...field.AssignExpr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSysStorePrintTemplateDo) Assign(attrs ...field.AssignExpr) *insSysStorePrintTemplateDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSysStorePrintTemplateDo) Joins(fields ...field.RelationField) *insSysStorePrintTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSysStorePrintTemplateDo) Preload(fields ...field.RelationField) *insSysStorePrintTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSysStorePrintTemplateDo) FirstOrInit() (*insbuy.InsSysStorePrintTemplate, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysStorePrintTemplate), nil
	}
}

func (i insSysStorePrintTemplateDo) FirstOrCreate() (*insbuy.InsSysStorePrintTemplate, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysStorePrintTemplate), nil
	}
}

func (i insSysStorePrintTemplateDo) FindByPage(offset int, limit int) (result []*insbuy.InsSysStorePrintTemplate, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSysStorePrintTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSysStorePrintTemplateDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSysStorePrintTemplateDo) Delete(models ...*insbuy.InsSysStorePrintTemplate) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSysStorePrintTemplateDo) withDO(do gen.Dao) *insSysStorePrintTemplateDo {
	i.DO = *do.(*gen.DO)
	return i
}
