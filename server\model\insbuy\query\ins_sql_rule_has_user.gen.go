// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSqlRuleHasUser(db *gorm.DB, opts ...gen.DOOption) insSqlRuleHasUser {
	_insSqlRuleHasUser := insSqlRuleHasUser{}

	_insSqlRuleHasUser.insSqlRuleHasUserDo.UseDB(db, opts...)
	_insSqlRuleHasUser.insSqlRuleHasUserDo.UseModel(&insbuy.InsSqlRuleHasUser{})

	tableName := _insSqlRuleHasUser.insSqlRuleHasUserDo.TableName()
	_insSqlRuleHasUser.ALL = field.NewAsterisk(tableName)
	_insSqlRuleHasUser.ID = field.NewUint(tableName, "id")
	_insSqlRuleHasUser.CreatedAt = field.NewTime(tableName, "created_at")
	_insSqlRuleHasUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSqlRuleHasUser.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSqlRuleHasUser.UserId = field.NewUint(tableName, "user_id")
	_insSqlRuleHasUser.RuleId = field.NewUint(tableName, "rule_id")

	_insSqlRuleHasUser.fillFieldMap()

	return _insSqlRuleHasUser
}

type insSqlRuleHasUser struct {
	insSqlRuleHasUserDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserId    field.Uint
	RuleId    field.Uint

	fieldMap map[string]field.Expr
}

func (i insSqlRuleHasUser) Table(newTableName string) *insSqlRuleHasUser {
	i.insSqlRuleHasUserDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSqlRuleHasUser) As(alias string) *insSqlRuleHasUser {
	i.insSqlRuleHasUserDo.DO = *(i.insSqlRuleHasUserDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSqlRuleHasUser) updateTableName(table string) *insSqlRuleHasUser {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserId = field.NewUint(table, "user_id")
	i.RuleId = field.NewUint(table, "rule_id")

	i.fillFieldMap()

	return i
}

func (i *insSqlRuleHasUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSqlRuleHasUser) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["rule_id"] = i.RuleId
}

func (i insSqlRuleHasUser) clone(db *gorm.DB) insSqlRuleHasUser {
	i.insSqlRuleHasUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSqlRuleHasUser) replaceDB(db *gorm.DB) insSqlRuleHasUser {
	i.insSqlRuleHasUserDo.ReplaceDB(db)
	return i
}

type insSqlRuleHasUserDo struct{ gen.DO }

func (i insSqlRuleHasUserDo) Debug() *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Debug())
}

func (i insSqlRuleHasUserDo) WithContext(ctx context.Context) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSqlRuleHasUserDo) ReadDB() *insSqlRuleHasUserDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSqlRuleHasUserDo) WriteDB() *insSqlRuleHasUserDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSqlRuleHasUserDo) Session(config *gorm.Session) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSqlRuleHasUserDo) Clauses(conds ...clause.Expression) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSqlRuleHasUserDo) Returning(value interface{}, columns ...string) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSqlRuleHasUserDo) Not(conds ...gen.Condition) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSqlRuleHasUserDo) Or(conds ...gen.Condition) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSqlRuleHasUserDo) Select(conds ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSqlRuleHasUserDo) Where(conds ...gen.Condition) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSqlRuleHasUserDo) Order(conds ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSqlRuleHasUserDo) Distinct(cols ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSqlRuleHasUserDo) Omit(cols ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSqlRuleHasUserDo) Join(table schema.Tabler, on ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSqlRuleHasUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSqlRuleHasUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSqlRuleHasUserDo) Group(cols ...field.Expr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSqlRuleHasUserDo) Having(conds ...gen.Condition) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSqlRuleHasUserDo) Limit(limit int) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSqlRuleHasUserDo) Offset(offset int) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSqlRuleHasUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSqlRuleHasUserDo) Unscoped() *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSqlRuleHasUserDo) Create(values ...*insbuy.InsSqlRuleHasUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSqlRuleHasUserDo) CreateInBatches(values []*insbuy.InsSqlRuleHasUser, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSqlRuleHasUserDo) Save(values ...*insbuy.InsSqlRuleHasUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSqlRuleHasUserDo) First() (*insbuy.InsSqlRuleHasUser, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasUser), nil
	}
}

func (i insSqlRuleHasUserDo) Take() (*insbuy.InsSqlRuleHasUser, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasUser), nil
	}
}

func (i insSqlRuleHasUserDo) Last() (*insbuy.InsSqlRuleHasUser, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasUser), nil
	}
}

func (i insSqlRuleHasUserDo) Find() ([]*insbuy.InsSqlRuleHasUser, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSqlRuleHasUser), err
}

func (i insSqlRuleHasUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSqlRuleHasUser, err error) {
	buf := make([]*insbuy.InsSqlRuleHasUser, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSqlRuleHasUserDo) FindInBatches(result *[]*insbuy.InsSqlRuleHasUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSqlRuleHasUserDo) Attrs(attrs ...field.AssignExpr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSqlRuleHasUserDo) Assign(attrs ...field.AssignExpr) *insSqlRuleHasUserDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSqlRuleHasUserDo) Joins(fields ...field.RelationField) *insSqlRuleHasUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSqlRuleHasUserDo) Preload(fields ...field.RelationField) *insSqlRuleHasUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSqlRuleHasUserDo) FirstOrInit() (*insbuy.InsSqlRuleHasUser, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasUser), nil
	}
}

func (i insSqlRuleHasUserDo) FirstOrCreate() (*insbuy.InsSqlRuleHasUser, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRuleHasUser), nil
	}
}

func (i insSqlRuleHasUserDo) FindByPage(offset int, limit int) (result []*insbuy.InsSqlRuleHasUser, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSqlRuleHasUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSqlRuleHasUserDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSqlRuleHasUserDo) Delete(models ...*insbuy.InsSqlRuleHasUser) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSqlRuleHasUserDo) withDO(do gen.Dao) *insSqlRuleHasUserDo {
	i.DO = *do.(*gen.DO)
	return i
}
