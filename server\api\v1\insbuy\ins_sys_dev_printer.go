package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSysDevPrinterApi struct {
}

// CreateInsSysDevPrinter 创建 InsSysDevPrinter 记录
// @Tags InsSysDevPrinter
// @Summary 创建 InsSysDevPrinter 记录
// @Description 创建 InsSysDevPrinter 记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuy.InsSysDevPrinter true "创建 InsSysDevPrinter 记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSysDevPrinter/createInsSysDevPrinter [post]
func (insSysDevPrinterApi *InsSysDevPrinterApi) CreateInsSysDevPrinter(c *gin.Context) {
	var req insbuyReq.CreateInsSysDevPrinterReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name": {utils.NotEmpty()},
	}
	if err := utils.Verify(req, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysDevPrinterService.CreateInsSysDevPrinter(c, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSysDevPrinter 删除 InsSysDevPrinter 记录
// @Tags InsSysDevPrinter
// @Summary 删除 InsSysDevPrinter 记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuy.InsSysDevPrinter true "InsSysDevPrinter 记录 ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSysDevPrinter/deleteInsSysDevPrinter [delete]
func (insSysDevPrinterApi *InsSysDevPrinterApi) DeleteInsSysDevPrinter(c *gin.Context) {
	var insSysDevPrinter insbuy.InsSysDevPrinter
	if err := GinMustBind(c, &insSysDevPrinter); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysDevPrinterService.DeleteInsSysDevPrinter(insSysDevPrinter); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSysDevPrinterByIds 批量删除 InsSysDevPrinter 记录
// @Tags InsSysDevPrinter
// @Summary 批量删除 InsSysDevPrinter 记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "InsSysDevPrinter 记录 IDs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insSysDevPrinter/deleteInsSysDevPrinterByIds [delete]
func (insSysDevPrinterApi *InsSysDevPrinterApi) DeleteInsSysDevPrinterByIds(c *gin.Context) {
	var IDS request.IdsReq
	if err := GinMustBind(c, &IDS); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysDevPrinterService.DeleteInsSysDevPrinterByIds(IDS.Ids); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSysDevPrinter 更新 InsSysDevPrinter 记录
// @Tags InsSysDevPrinter
// @Summary 更新 InsSysDevPrinter 记录
// @Description 更新 InsSysDevPrinter 记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuy.InsSysDevPrinter true "更新的 InsSysDevPrinter 记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSysDevPrinter/updateInsSysDevPrinter [put]
func (insSysDevPrinterApi *InsSysDevPrinterApi) UpdateInsSysDevPrinter(c *gin.Context) {
	var insSysDevPrinter insbuy.InsSysDevPrinter
	if err := GinMustBind(c, &insSysDevPrinter); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSysDevPrinterService.UpdateInsSysDevPrinter(insSysDevPrinter); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSysDevPrinter 根据ID获取 InsSysDevPrinter 记录
// @Tags InsSysDevPrinter
// @Summary 根据ID获取 InsSysDevPrinter 记录
// @Description 根据ID获取 InsSysDevPrinter 记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query insbuy.InsSysDevPrinter true "用id查询InsSysDevPrinter"
// @Success 200 {string} string "{"reInsSysDevPrinter":insbuy.InsSysDevPrinter}"
// @Router /insSysDevPrinter/findInsSysDevPrinter [get]
func (insSysDevPrinterApi *InsSysDevPrinterApi) FindInsSysDevPrinter(c *gin.Context) {
	var insSysDevPrinter insbuy.InsSysDevPrinter
	if err := GinMustBind(c, &insSysDevPrinter); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reInsSysDevPrinter, err := insSysDevPrinterService.GetInsSysDevPrinter(insSysDevPrinter.ID); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(gin.H{"reInsSysDevPrinter": reInsSysDevPrinter}, c)
	}
}

// GetInsSysDevPrinterList 分页获取InsSysDevPrinter列表
// @Tags InsSysDevPrinter
// @Summary 分页获取InsSysDevPrinter列表
// @Description 分页获取InsSysDevPrinter列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSysDevPrinterSearch true "分页获取InsSysDevPrinter列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSysDevPrinter/getInsSysDevPrinterList [get]
func (insSysDevPrinterApi *InsSysDevPrinterApi) GetInsSysDevPrinterList(c *gin.Context) {
	var req insbuyReq.InsSysDevPrinterSearch
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	req.FixPageSize(1, 10)
	if list, page, err := insSysDevPrinterService.GetInsSysDevPrinterInfoList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    int64(page.Total),
			Page:     page.Page,
			PageSize: page.PageSize,
		}, "获取成功", c)
	}
}

// PrintLog 打印日志
// @Tags InsSysDevPrinter
// @Summary 打印日志
// @Description 打印日志
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.PrintLogReq true "打印日志"
// @Success   200   {object}  response.Response{data=insbuyResp.PrintLogResp,msg=string}
// @Router /insSysDevPrinter/getPrintLog [get]
func (insSysDevPrinterApi *InsSysDevPrinterApi) PrintLog(c *gin.Context) {
	var req insbuyReq.PrintLogReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var (
		list  = make([]insbuyResp.PrintLogResp, 0)
		total int64
		err   error
	)
	if list, total, err = insSysDevPrinterService.PrintLog(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// SavePrinterConfig 保存打印机配置
// @Tags InsSysDevPrinter
// @Summary 保存打印机配置
// @Description 保存打印机配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.SavePrinterConfigReq true "保存打印机配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"保存成功"}"
// @Router /insSysDevPrinter/savePrinterConfig [post]
func (insSysDevPrinterApi *InsSysDevPrinterApi) SavePrinterConfig(c *gin.Context) {
	var req insbuyReq.SavePrinterConfigReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err := insSysDevPrinterService.SavePrinterConfig(req)
	response.Err(err, c)
}

// FindPrinterConfig 查看打印机配置
// @Tags InsSysDevPrinter
// @Summary 查看打印机配置
// @Description 查看打印机配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.WithStoreId true "查看打印机配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查看成功"}"
// @Router /insSysDevPrinter/findPrinterConfig [get]
func (insSysDevPrinterApi *InsSysDevPrinterApi) FindPrinterConfig(c *gin.Context) {
	var req insbuyReq.WithStoreId
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insSysDevPrinterService.FindPrinterConfig(req.GetUintStoreId())
	response.ResultErr(resp, err, c)
}
