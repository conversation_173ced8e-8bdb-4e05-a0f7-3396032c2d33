// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreConsumeSnapshot(db *gorm.DB, opts ...gen.DOOption) insStoreConsumeSnapshot {
	_insStoreConsumeSnapshot := insStoreConsumeSnapshot{}

	_insStoreConsumeSnapshot.insStoreConsumeSnapshotDo.UseDB(db, opts...)
	_insStoreConsumeSnapshot.insStoreConsumeSnapshotDo.UseModel(&insbuy.InsStoreConsumeSnapshot{})

	tableName := _insStoreConsumeSnapshot.insStoreConsumeSnapshotDo.TableName()
	_insStoreConsumeSnapshot.ALL = field.NewAsterisk(tableName)
	_insStoreConsumeSnapshot.ID = field.NewUint(tableName, "id")
	_insStoreConsumeSnapshot.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreConsumeSnapshot.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreConsumeSnapshot.StoreId = field.NewString(tableName, "store_id")
	_insStoreConsumeSnapshot.StoreName = field.NewString(tableName, "store_name")
	_insStoreConsumeSnapshot.StoreCategory = field.NewString(tableName, "store_category")
	_insStoreConsumeSnapshot.StoreCategoryText = field.NewString(tableName, "store_category_text")
	_insStoreConsumeSnapshot.ChargeTotal = field.NewFloat64(tableName, "charge_total")
	_insStoreConsumeSnapshot.LastChargeTotal = field.NewFloat64(tableName, "last_charge_total")
	_insStoreConsumeSnapshot.Progress = field.NewFloat64(tableName, "progress")
	_insStoreConsumeSnapshot.FactChargeTotal = field.NewFloat64(tableName, "fact_charge_total")
	_insStoreConsumeSnapshot.NotFactChargeTotal = field.NewFloat64(tableName, "not_fact_charge_total")
	_insStoreConsumeSnapshot.TicketTotal = field.NewFloat64(tableName, "ticket_total")
	_insStoreConsumeSnapshot.BoxTotal = field.NewFloat64(tableName, "box_total")
	_insStoreConsumeSnapshot.HallTotal = field.NewFloat64(tableName, "hall_total")
	_insStoreConsumeSnapshot.OrderCount = field.NewInt(tableName, "order_count")
	_insStoreConsumeSnapshot.ClearCharge = field.NewFloat64(tableName, "clear_charge")
	_insStoreConsumeSnapshot.BusinessDay = field.NewTime(tableName, "business_day")

	_insStoreConsumeSnapshot.fillFieldMap()

	return _insStoreConsumeSnapshot
}

type insStoreConsumeSnapshot struct {
	insStoreConsumeSnapshotDo

	ALL                field.Asterisk
	ID                 field.Uint
	CreatedAt          field.Time
	UpdatedAt          field.Time
	StoreId            field.String
	StoreName          field.String
	StoreCategory      field.String
	StoreCategoryText  field.String
	ChargeTotal        field.Float64
	LastChargeTotal    field.Float64
	Progress           field.Float64
	FactChargeTotal    field.Float64
	NotFactChargeTotal field.Float64
	TicketTotal        field.Float64
	BoxTotal           field.Float64
	HallTotal          field.Float64
	OrderCount         field.Int
	ClearCharge        field.Float64
	BusinessDay        field.Time

	fieldMap map[string]field.Expr
}

func (i insStoreConsumeSnapshot) Table(newTableName string) *insStoreConsumeSnapshot {
	i.insStoreConsumeSnapshotDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreConsumeSnapshot) As(alias string) *insStoreConsumeSnapshot {
	i.insStoreConsumeSnapshotDo.DO = *(i.insStoreConsumeSnapshotDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreConsumeSnapshot) updateTableName(table string) *insStoreConsumeSnapshot {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewString(table, "store_id")
	i.StoreName = field.NewString(table, "store_name")
	i.StoreCategory = field.NewString(table, "store_category")
	i.StoreCategoryText = field.NewString(table, "store_category_text")
	i.ChargeTotal = field.NewFloat64(table, "charge_total")
	i.LastChargeTotal = field.NewFloat64(table, "last_charge_total")
	i.Progress = field.NewFloat64(table, "progress")
	i.FactChargeTotal = field.NewFloat64(table, "fact_charge_total")
	i.NotFactChargeTotal = field.NewFloat64(table, "not_fact_charge_total")
	i.TicketTotal = field.NewFloat64(table, "ticket_total")
	i.BoxTotal = field.NewFloat64(table, "box_total")
	i.HallTotal = field.NewFloat64(table, "hall_total")
	i.OrderCount = field.NewInt(table, "order_count")
	i.ClearCharge = field.NewFloat64(table, "clear_charge")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insStoreConsumeSnapshot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreConsumeSnapshot) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 18)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["store_name"] = i.StoreName
	i.fieldMap["store_category"] = i.StoreCategory
	i.fieldMap["store_category_text"] = i.StoreCategoryText
	i.fieldMap["charge_total"] = i.ChargeTotal
	i.fieldMap["last_charge_total"] = i.LastChargeTotal
	i.fieldMap["progress"] = i.Progress
	i.fieldMap["fact_charge_total"] = i.FactChargeTotal
	i.fieldMap["not_fact_charge_total"] = i.NotFactChargeTotal
	i.fieldMap["ticket_total"] = i.TicketTotal
	i.fieldMap["box_total"] = i.BoxTotal
	i.fieldMap["hall_total"] = i.HallTotal
	i.fieldMap["order_count"] = i.OrderCount
	i.fieldMap["clear_charge"] = i.ClearCharge
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insStoreConsumeSnapshot) clone(db *gorm.DB) insStoreConsumeSnapshot {
	i.insStoreConsumeSnapshotDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreConsumeSnapshot) replaceDB(db *gorm.DB) insStoreConsumeSnapshot {
	i.insStoreConsumeSnapshotDo.ReplaceDB(db)
	return i
}

type insStoreConsumeSnapshotDo struct{ gen.DO }

func (i insStoreConsumeSnapshotDo) Debug() *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreConsumeSnapshotDo) WithContext(ctx context.Context) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreConsumeSnapshotDo) ReadDB() *insStoreConsumeSnapshotDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreConsumeSnapshotDo) WriteDB() *insStoreConsumeSnapshotDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreConsumeSnapshotDo) Session(config *gorm.Session) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreConsumeSnapshotDo) Clauses(conds ...clause.Expression) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreConsumeSnapshotDo) Returning(value interface{}, columns ...string) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreConsumeSnapshotDo) Not(conds ...gen.Condition) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreConsumeSnapshotDo) Or(conds ...gen.Condition) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreConsumeSnapshotDo) Select(conds ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreConsumeSnapshotDo) Where(conds ...gen.Condition) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreConsumeSnapshotDo) Order(conds ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreConsumeSnapshotDo) Distinct(cols ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreConsumeSnapshotDo) Omit(cols ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreConsumeSnapshotDo) Join(table schema.Tabler, on ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreConsumeSnapshotDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreConsumeSnapshotDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreConsumeSnapshotDo) Group(cols ...field.Expr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreConsumeSnapshotDo) Having(conds ...gen.Condition) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreConsumeSnapshotDo) Limit(limit int) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreConsumeSnapshotDo) Offset(offset int) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreConsumeSnapshotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreConsumeSnapshotDo) Unscoped() *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreConsumeSnapshotDo) Create(values ...*insbuy.InsStoreConsumeSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreConsumeSnapshotDo) CreateInBatches(values []*insbuy.InsStoreConsumeSnapshot, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreConsumeSnapshotDo) Save(values ...*insbuy.InsStoreConsumeSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreConsumeSnapshotDo) First() (*insbuy.InsStoreConsumeSnapshot, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreConsumeSnapshot), nil
	}
}

func (i insStoreConsumeSnapshotDo) Take() (*insbuy.InsStoreConsumeSnapshot, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreConsumeSnapshot), nil
	}
}

func (i insStoreConsumeSnapshotDo) Last() (*insbuy.InsStoreConsumeSnapshot, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreConsumeSnapshot), nil
	}
}

func (i insStoreConsumeSnapshotDo) Find() ([]*insbuy.InsStoreConsumeSnapshot, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreConsumeSnapshot), err
}

func (i insStoreConsumeSnapshotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreConsumeSnapshot, err error) {
	buf := make([]*insbuy.InsStoreConsumeSnapshot, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreConsumeSnapshotDo) FindInBatches(result *[]*insbuy.InsStoreConsumeSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreConsumeSnapshotDo) Attrs(attrs ...field.AssignExpr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreConsumeSnapshotDo) Assign(attrs ...field.AssignExpr) *insStoreConsumeSnapshotDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreConsumeSnapshotDo) Joins(fields ...field.RelationField) *insStoreConsumeSnapshotDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreConsumeSnapshotDo) Preload(fields ...field.RelationField) *insStoreConsumeSnapshotDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreConsumeSnapshotDo) FirstOrInit() (*insbuy.InsStoreConsumeSnapshot, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreConsumeSnapshot), nil
	}
}

func (i insStoreConsumeSnapshotDo) FirstOrCreate() (*insbuy.InsStoreConsumeSnapshot, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreConsumeSnapshot), nil
	}
}

func (i insStoreConsumeSnapshotDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreConsumeSnapshot, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreConsumeSnapshotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreConsumeSnapshotDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreConsumeSnapshotDo) Delete(models ...*insbuy.InsStoreConsumeSnapshot) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreConsumeSnapshotDo) withDO(do gen.Dao) *insStoreConsumeSnapshotDo {
	i.DO = *do.(*gen.DO)
	return i
}
