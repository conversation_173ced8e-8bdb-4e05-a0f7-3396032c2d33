// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOpenDeskSnapshot(db *gorm.DB, opts ...gen.DOOption) insOpenDeskSnapshot {
	_insOpenDeskSnapshot := insOpenDeskSnapshot{}

	_insOpenDeskSnapshot.insOpenDeskSnapshotDo.UseDB(db, opts...)
	_insOpenDeskSnapshot.insOpenDeskSnapshotDo.UseModel(&insbuy.InsOpenDeskSnapshot{})

	tableName := _insOpenDeskSnapshot.insOpenDeskSnapshotDo.TableName()
	_insOpenDeskSnapshot.ALL = field.NewAsterisk(tableName)
	_insOpenDeskSnapshot.ID = field.NewUint(tableName, "id")
	_insOpenDeskSnapshot.CreatedAt = field.NewTime(tableName, "created_at")
	_insOpenDeskSnapshot.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOpenDeskSnapshot.StoreId = field.NewUint(tableName, "store_id")
	_insOpenDeskSnapshot.StoreName = field.NewString(tableName, "store_name")
	_insOpenDeskSnapshot.BusinessDay = field.NewTime(tableName, "business_day")
	_insOpenDeskSnapshot.DeskId = field.NewUint(tableName, "desk_id")
	_insOpenDeskSnapshot.DeskName = field.NewString(tableName, "desk_name")
	_insOpenDeskSnapshot.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insOpenDeskSnapshot.IncomeAmount = field.NewFloat64(tableName, "income_amount")
	_insOpenDeskSnapshot.CountableIncome = field.NewFloat64(tableName, "countable_income")
	_insOpenDeskSnapshot.RealIncome = field.NewFloat64(tableName, "real_income")
	_insOpenDeskSnapshot.CharityFund = field.NewFloat64(tableName, "charity_fund")

	_insOpenDeskSnapshot.fillFieldMap()

	return _insOpenDeskSnapshot
}

type insOpenDeskSnapshot struct {
	insOpenDeskSnapshotDo

	ALL             field.Asterisk
	ID              field.Uint
	CreatedAt       field.Time
	UpdatedAt       field.Time
	StoreId         field.Uint
	StoreName       field.String
	BusinessDay     field.Time
	DeskId          field.Uint
	DeskName        field.String
	OpenDeskId      field.Uint
	IncomeAmount    field.Float64
	CountableIncome field.Float64
	RealIncome      field.Float64
	CharityFund     field.Float64

	fieldMap map[string]field.Expr
}

func (i insOpenDeskSnapshot) Table(newTableName string) *insOpenDeskSnapshot {
	i.insOpenDeskSnapshotDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOpenDeskSnapshot) As(alias string) *insOpenDeskSnapshot {
	i.insOpenDeskSnapshotDo.DO = *(i.insOpenDeskSnapshotDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOpenDeskSnapshot) updateTableName(table string) *insOpenDeskSnapshot {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.StoreName = field.NewString(table, "store_name")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.DeskId = field.NewUint(table, "desk_id")
	i.DeskName = field.NewString(table, "desk_name")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.IncomeAmount = field.NewFloat64(table, "income_amount")
	i.CountableIncome = field.NewFloat64(table, "countable_income")
	i.RealIncome = field.NewFloat64(table, "real_income")
	i.CharityFund = field.NewFloat64(table, "charity_fund")

	i.fillFieldMap()

	return i
}

func (i *insOpenDeskSnapshot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOpenDeskSnapshot) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["store_name"] = i.StoreName
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["desk_name"] = i.DeskName
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["income_amount"] = i.IncomeAmount
	i.fieldMap["countable_income"] = i.CountableIncome
	i.fieldMap["real_income"] = i.RealIncome
	i.fieldMap["charity_fund"] = i.CharityFund
}

func (i insOpenDeskSnapshot) clone(db *gorm.DB) insOpenDeskSnapshot {
	i.insOpenDeskSnapshotDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOpenDeskSnapshot) replaceDB(db *gorm.DB) insOpenDeskSnapshot {
	i.insOpenDeskSnapshotDo.ReplaceDB(db)
	return i
}

type insOpenDeskSnapshotDo struct{ gen.DO }

func (i insOpenDeskSnapshotDo) Debug() *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Debug())
}

func (i insOpenDeskSnapshotDo) WithContext(ctx context.Context) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOpenDeskSnapshotDo) ReadDB() *insOpenDeskSnapshotDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOpenDeskSnapshotDo) WriteDB() *insOpenDeskSnapshotDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOpenDeskSnapshotDo) Session(config *gorm.Session) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOpenDeskSnapshotDo) Clauses(conds ...clause.Expression) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOpenDeskSnapshotDo) Returning(value interface{}, columns ...string) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOpenDeskSnapshotDo) Not(conds ...gen.Condition) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOpenDeskSnapshotDo) Or(conds ...gen.Condition) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOpenDeskSnapshotDo) Select(conds ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOpenDeskSnapshotDo) Where(conds ...gen.Condition) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOpenDeskSnapshotDo) Order(conds ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOpenDeskSnapshotDo) Distinct(cols ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOpenDeskSnapshotDo) Omit(cols ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOpenDeskSnapshotDo) Join(table schema.Tabler, on ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOpenDeskSnapshotDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOpenDeskSnapshotDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOpenDeskSnapshotDo) Group(cols ...field.Expr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOpenDeskSnapshotDo) Having(conds ...gen.Condition) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOpenDeskSnapshotDo) Limit(limit int) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOpenDeskSnapshotDo) Offset(offset int) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOpenDeskSnapshotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOpenDeskSnapshotDo) Unscoped() *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOpenDeskSnapshotDo) Create(values ...*insbuy.InsOpenDeskSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOpenDeskSnapshotDo) CreateInBatches(values []*insbuy.InsOpenDeskSnapshot, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOpenDeskSnapshotDo) Save(values ...*insbuy.InsOpenDeskSnapshot) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOpenDeskSnapshotDo) First() (*insbuy.InsOpenDeskSnapshot, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOpenDeskSnapshot), nil
	}
}

func (i insOpenDeskSnapshotDo) Take() (*insbuy.InsOpenDeskSnapshot, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOpenDeskSnapshot), nil
	}
}

func (i insOpenDeskSnapshotDo) Last() (*insbuy.InsOpenDeskSnapshot, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOpenDeskSnapshot), nil
	}
}

func (i insOpenDeskSnapshotDo) Find() ([]*insbuy.InsOpenDeskSnapshot, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOpenDeskSnapshot), err
}

func (i insOpenDeskSnapshotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOpenDeskSnapshot, err error) {
	buf := make([]*insbuy.InsOpenDeskSnapshot, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOpenDeskSnapshotDo) FindInBatches(result *[]*insbuy.InsOpenDeskSnapshot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOpenDeskSnapshotDo) Attrs(attrs ...field.AssignExpr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOpenDeskSnapshotDo) Assign(attrs ...field.AssignExpr) *insOpenDeskSnapshotDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOpenDeskSnapshotDo) Joins(fields ...field.RelationField) *insOpenDeskSnapshotDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOpenDeskSnapshotDo) Preload(fields ...field.RelationField) *insOpenDeskSnapshotDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOpenDeskSnapshotDo) FirstOrInit() (*insbuy.InsOpenDeskSnapshot, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOpenDeskSnapshot), nil
	}
}

func (i insOpenDeskSnapshotDo) FirstOrCreate() (*insbuy.InsOpenDeskSnapshot, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOpenDeskSnapshot), nil
	}
}

func (i insOpenDeskSnapshotDo) FindByPage(offset int, limit int) (result []*insbuy.InsOpenDeskSnapshot, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOpenDeskSnapshotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOpenDeskSnapshotDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOpenDeskSnapshotDo) Delete(models ...*insbuy.InsOpenDeskSnapshot) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOpenDeskSnapshotDo) withDO(do gen.Dao) *insOpenDeskSnapshotDo {
	i.DO = *do.(*gen.DO)
	return i
}
