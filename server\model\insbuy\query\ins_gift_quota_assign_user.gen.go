// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftQuotaAssignUser(db *gorm.DB, opts ...gen.DOOption) insGiftQuotaAssignUser {
	_insGiftQuotaAssignUser := insGiftQuotaAssignUser{}

	_insGiftQuotaAssignUser.insGiftQuotaAssignUserDo.UseDB(db, opts...)
	_insGiftQuotaAssignUser.insGiftQuotaAssignUserDo.UseModel(&insbuy.InsGiftQuotaAssignUser{})

	tableName := _insGiftQuotaAssignUser.insGiftQuotaAssignUserDo.TableName()
	_insGiftQuotaAssignUser.ALL = field.NewAsterisk(tableName)
	_insGiftQuotaAssignUser.ID = field.NewUint(tableName, "id")
	_insGiftQuotaAssignUser.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftQuotaAssignUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftQuotaAssignUser.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftQuotaAssignUser.UserId = field.NewUint(tableName, "user_id")
	_insGiftQuotaAssignUser.Quota = field.NewFloat64(tableName, "quota")
	_insGiftQuotaAssignUser.InitQuota = field.NewFloat64(tableName, "init_quota")
	_insGiftQuotaAssignUser.StoreId = field.NewUint(tableName, "store_id")
	_insGiftQuotaAssignUser.Status = field.NewInt(tableName, "status")

	_insGiftQuotaAssignUser.fillFieldMap()

	return _insGiftQuotaAssignUser
}

type insGiftQuotaAssignUser struct {
	insGiftQuotaAssignUserDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserId    field.Uint
	Quota     field.Float64
	InitQuota field.Float64
	StoreId   field.Uint
	Status    field.Int

	fieldMap map[string]field.Expr
}

func (i insGiftQuotaAssignUser) Table(newTableName string) *insGiftQuotaAssignUser {
	i.insGiftQuotaAssignUserDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftQuotaAssignUser) As(alias string) *insGiftQuotaAssignUser {
	i.insGiftQuotaAssignUserDo.DO = *(i.insGiftQuotaAssignUserDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftQuotaAssignUser) updateTableName(table string) *insGiftQuotaAssignUser {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserId = field.NewUint(table, "user_id")
	i.Quota = field.NewFloat64(table, "quota")
	i.InitQuota = field.NewFloat64(table, "init_quota")
	i.StoreId = field.NewUint(table, "store_id")
	i.Status = field.NewInt(table, "status")

	i.fillFieldMap()

	return i
}

func (i *insGiftQuotaAssignUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftQuotaAssignUser) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["quota"] = i.Quota
	i.fieldMap["init_quota"] = i.InitQuota
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["status"] = i.Status
}

func (i insGiftQuotaAssignUser) clone(db *gorm.DB) insGiftQuotaAssignUser {
	i.insGiftQuotaAssignUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftQuotaAssignUser) replaceDB(db *gorm.DB) insGiftQuotaAssignUser {
	i.insGiftQuotaAssignUserDo.ReplaceDB(db)
	return i
}

type insGiftQuotaAssignUserDo struct{ gen.DO }

func (i insGiftQuotaAssignUserDo) Debug() *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftQuotaAssignUserDo) WithContext(ctx context.Context) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftQuotaAssignUserDo) ReadDB() *insGiftQuotaAssignUserDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftQuotaAssignUserDo) WriteDB() *insGiftQuotaAssignUserDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftQuotaAssignUserDo) Session(config *gorm.Session) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftQuotaAssignUserDo) Clauses(conds ...clause.Expression) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftQuotaAssignUserDo) Returning(value interface{}, columns ...string) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftQuotaAssignUserDo) Not(conds ...gen.Condition) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftQuotaAssignUserDo) Or(conds ...gen.Condition) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftQuotaAssignUserDo) Select(conds ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftQuotaAssignUserDo) Where(conds ...gen.Condition) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftQuotaAssignUserDo) Order(conds ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftQuotaAssignUserDo) Distinct(cols ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftQuotaAssignUserDo) Omit(cols ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftQuotaAssignUserDo) Join(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftQuotaAssignUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftQuotaAssignUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftQuotaAssignUserDo) Group(cols ...field.Expr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftQuotaAssignUserDo) Having(conds ...gen.Condition) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftQuotaAssignUserDo) Limit(limit int) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftQuotaAssignUserDo) Offset(offset int) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftQuotaAssignUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftQuotaAssignUserDo) Unscoped() *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftQuotaAssignUserDo) Create(values ...*insbuy.InsGiftQuotaAssignUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftQuotaAssignUserDo) CreateInBatches(values []*insbuy.InsGiftQuotaAssignUser, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftQuotaAssignUserDo) Save(values ...*insbuy.InsGiftQuotaAssignUser) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftQuotaAssignUserDo) First() (*insbuy.InsGiftQuotaAssignUser, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignUser), nil
	}
}

func (i insGiftQuotaAssignUserDo) Take() (*insbuy.InsGiftQuotaAssignUser, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignUser), nil
	}
}

func (i insGiftQuotaAssignUserDo) Last() (*insbuy.InsGiftQuotaAssignUser, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignUser), nil
	}
}

func (i insGiftQuotaAssignUserDo) Find() ([]*insbuy.InsGiftQuotaAssignUser, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftQuotaAssignUser), err
}

func (i insGiftQuotaAssignUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftQuotaAssignUser, err error) {
	buf := make([]*insbuy.InsGiftQuotaAssignUser, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftQuotaAssignUserDo) FindInBatches(result *[]*insbuy.InsGiftQuotaAssignUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftQuotaAssignUserDo) Attrs(attrs ...field.AssignExpr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftQuotaAssignUserDo) Assign(attrs ...field.AssignExpr) *insGiftQuotaAssignUserDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftQuotaAssignUserDo) Joins(fields ...field.RelationField) *insGiftQuotaAssignUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftQuotaAssignUserDo) Preload(fields ...field.RelationField) *insGiftQuotaAssignUserDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftQuotaAssignUserDo) FirstOrInit() (*insbuy.InsGiftQuotaAssignUser, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignUser), nil
	}
}

func (i insGiftQuotaAssignUserDo) FirstOrCreate() (*insbuy.InsGiftQuotaAssignUser, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignUser), nil
	}
}

func (i insGiftQuotaAssignUserDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftQuotaAssignUser, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftQuotaAssignUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftQuotaAssignUserDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftQuotaAssignUserDo) Delete(models ...*insbuy.InsGiftQuotaAssignUser) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftQuotaAssignUserDo) withDO(do gen.Dao) *insGiftQuotaAssignUserDo {
	i.DO = *do.(*gen.DO)
	return i
}
