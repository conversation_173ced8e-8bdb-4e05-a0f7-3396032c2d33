// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderPayDetail(db *gorm.DB, opts ...gen.DOOption) insOrderPayDetail {
	_insOrderPayDetail := insOrderPayDetail{}

	_insOrderPayDetail.insOrderPayDetailDo.UseDB(db, opts...)
	_insOrderPayDetail.insOrderPayDetailDo.UseModel(&insbuy.InsOrderPayDetail{})

	tableName := _insOrderPayDetail.insOrderPayDetailDo.TableName()
	_insOrderPayDetail.ALL = field.NewAsterisk(tableName)
	_insOrderPayDetail.ID = field.NewUint(tableName, "id")
	_insOrderPayDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderPayDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderPayDetail.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderPayDetail.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insOrderPayDetail.OrderBill = field.NewInt(tableName, "order_bill")
	_insOrderPayDetail.TradeId = field.NewUint64(tableName, "trade_id")

	_insOrderPayDetail.fillFieldMap()

	return _insOrderPayDetail
}

type insOrderPayDetail struct {
	insOrderPayDetailDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	OrderId        field.Uint64
	OrderDetailsId field.Uint64
	OrderBill      field.Int
	TradeId        field.Uint64

	fieldMap map[string]field.Expr
}

func (i insOrderPayDetail) Table(newTableName string) *insOrderPayDetail {
	i.insOrderPayDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderPayDetail) As(alias string) *insOrderPayDetail {
	i.insOrderPayDetailDo.DO = *(i.insOrderPayDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderPayDetail) updateTableName(table string) *insOrderPayDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.OrderBill = field.NewInt(table, "order_bill")
	i.TradeId = field.NewUint64(table, "trade_id")

	i.fillFieldMap()

	return i
}

func (i *insOrderPayDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderPayDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["order_bill"] = i.OrderBill
	i.fieldMap["trade_id"] = i.TradeId
}

func (i insOrderPayDetail) clone(db *gorm.DB) insOrderPayDetail {
	i.insOrderPayDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderPayDetail) replaceDB(db *gorm.DB) insOrderPayDetail {
	i.insOrderPayDetailDo.ReplaceDB(db)
	return i
}

type insOrderPayDetailDo struct{ gen.DO }

func (i insOrderPayDetailDo) Debug() *insOrderPayDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderPayDetailDo) WithContext(ctx context.Context) *insOrderPayDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderPayDetailDo) ReadDB() *insOrderPayDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderPayDetailDo) WriteDB() *insOrderPayDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderPayDetailDo) Session(config *gorm.Session) *insOrderPayDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderPayDetailDo) Clauses(conds ...clause.Expression) *insOrderPayDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderPayDetailDo) Returning(value interface{}, columns ...string) *insOrderPayDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderPayDetailDo) Not(conds ...gen.Condition) *insOrderPayDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderPayDetailDo) Or(conds ...gen.Condition) *insOrderPayDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderPayDetailDo) Select(conds ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderPayDetailDo) Where(conds ...gen.Condition) *insOrderPayDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderPayDetailDo) Order(conds ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderPayDetailDo) Distinct(cols ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderPayDetailDo) Omit(cols ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderPayDetailDo) Join(table schema.Tabler, on ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderPayDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderPayDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderPayDetailDo) Group(cols ...field.Expr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderPayDetailDo) Having(conds ...gen.Condition) *insOrderPayDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderPayDetailDo) Limit(limit int) *insOrderPayDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderPayDetailDo) Offset(offset int) *insOrderPayDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderPayDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderPayDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderPayDetailDo) Unscoped() *insOrderPayDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderPayDetailDo) Create(values ...*insbuy.InsOrderPayDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderPayDetailDo) CreateInBatches(values []*insbuy.InsOrderPayDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderPayDetailDo) Save(values ...*insbuy.InsOrderPayDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderPayDetailDo) First() (*insbuy.InsOrderPayDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderPayDetail), nil
	}
}

func (i insOrderPayDetailDo) Take() (*insbuy.InsOrderPayDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderPayDetail), nil
	}
}

func (i insOrderPayDetailDo) Last() (*insbuy.InsOrderPayDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderPayDetail), nil
	}
}

func (i insOrderPayDetailDo) Find() ([]*insbuy.InsOrderPayDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderPayDetail), err
}

func (i insOrderPayDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderPayDetail, err error) {
	buf := make([]*insbuy.InsOrderPayDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderPayDetailDo) FindInBatches(result *[]*insbuy.InsOrderPayDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderPayDetailDo) Attrs(attrs ...field.AssignExpr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderPayDetailDo) Assign(attrs ...field.AssignExpr) *insOrderPayDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderPayDetailDo) Joins(fields ...field.RelationField) *insOrderPayDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderPayDetailDo) Preload(fields ...field.RelationField) *insOrderPayDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderPayDetailDo) FirstOrInit() (*insbuy.InsOrderPayDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderPayDetail), nil
	}
}

func (i insOrderPayDetailDo) FirstOrCreate() (*insbuy.InsOrderPayDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderPayDetail), nil
	}
}

func (i insOrderPayDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderPayDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderPayDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderPayDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderPayDetailDo) Delete(models ...*insbuy.InsOrderPayDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderPayDetailDo) withDO(do gen.Dao) *insOrderPayDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
