// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReport(db *gorm.DB, opts ...gen.DOOption) insReport {
	_insReport := insReport{}

	_insReport.insReportDo.UseDB(db, opts...)
	_insReport.insReportDo.UseModel(&insbuy.InsReport{})

	tableName := _insReport.insReportDo.TableName()
	_insReport.ALL = field.NewAsterisk(tableName)
	_insReport.ID = field.NewUint(tableName, "id")
	_insReport.CreatedAt = field.NewTime(tableName, "created_at")
	_insReport.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReport.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReport.StoreId = field.NewUint(tableName, "store_id")
	_insReport.Code = field.NewInt(tableName, "code")
	_insReport.CategoryID = field.NewUint(tableName, "category_id")
	_insReport.Sort = field.NewInt(tableName, "sort")
	_insReport.Name = field.NewString(tableName, "name")
	_insReport.Description = field.NewString(tableName, "description")
	_insReport.BaseTable = field.NewString(tableName, "base_table")
	_insReport.JoinConditions = field.NewField(tableName, "join_conditions")
	_insReport.Filters = field.NewField(tableName, "filters")
	_insReport.OutputColumns = field.NewField(tableName, "output_columns")
	_insReport.Ext = field.NewField(tableName, "ext")
	_insReport.Fields = insReportHasManyFields{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Fields", "insbuy.InsReportField"),
	}

	_insReport.fillFieldMap()

	return _insReport
}

type insReport struct {
	insReportDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	StoreId        field.Uint
	Code           field.Int
	CategoryID     field.Uint
	Sort           field.Int
	Name           field.String
	Description    field.String
	BaseTable      field.String
	JoinConditions field.Field
	Filters        field.Field
	OutputColumns  field.Field
	Ext            field.Field
	Fields         insReportHasManyFields

	fieldMap map[string]field.Expr
}

func (i insReport) Table(newTableName string) *insReport {
	i.insReportDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReport) As(alias string) *insReport {
	i.insReportDo.DO = *(i.insReportDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReport) updateTableName(table string) *insReport {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Code = field.NewInt(table, "code")
	i.CategoryID = field.NewUint(table, "category_id")
	i.Sort = field.NewInt(table, "sort")
	i.Name = field.NewString(table, "name")
	i.Description = field.NewString(table, "description")
	i.BaseTable = field.NewString(table, "base_table")
	i.JoinConditions = field.NewField(table, "join_conditions")
	i.Filters = field.NewField(table, "filters")
	i.OutputColumns = field.NewField(table, "output_columns")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insReport) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReport) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["code"] = i.Code
	i.fieldMap["category_id"] = i.CategoryID
	i.fieldMap["sort"] = i.Sort
	i.fieldMap["name"] = i.Name
	i.fieldMap["description"] = i.Description
	i.fieldMap["base_table"] = i.BaseTable
	i.fieldMap["join_conditions"] = i.JoinConditions
	i.fieldMap["filters"] = i.Filters
	i.fieldMap["output_columns"] = i.OutputColumns
	i.fieldMap["ext"] = i.Ext

}

func (i insReport) clone(db *gorm.DB) insReport {
	i.insReportDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReport) replaceDB(db *gorm.DB) insReport {
	i.insReportDo.ReplaceDB(db)
	return i
}

type insReportHasManyFields struct {
	db *gorm.DB

	field.RelationField
}

func (a insReportHasManyFields) Where(conds ...field.Expr) *insReportHasManyFields {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insReportHasManyFields) WithContext(ctx context.Context) *insReportHasManyFields {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insReportHasManyFields) Session(session *gorm.Session) *insReportHasManyFields {
	a.db = a.db.Session(session)
	return &a
}

func (a insReportHasManyFields) Model(m *insbuy.InsReport) *insReportHasManyFieldsTx {
	return &insReportHasManyFieldsTx{a.db.Model(m).Association(a.Name())}
}

type insReportHasManyFieldsTx struct{ tx *gorm.Association }

func (a insReportHasManyFieldsTx) Find() (result []*insbuy.InsReportField, err error) {
	return result, a.tx.Find(&result)
}

func (a insReportHasManyFieldsTx) Append(values ...*insbuy.InsReportField) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insReportHasManyFieldsTx) Replace(values ...*insbuy.InsReportField) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insReportHasManyFieldsTx) Delete(values ...*insbuy.InsReportField) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insReportHasManyFieldsTx) Clear() error {
	return a.tx.Clear()
}

func (a insReportHasManyFieldsTx) Count() int64 {
	return a.tx.Count()
}

type insReportDo struct{ gen.DO }

func (i insReportDo) Debug() *insReportDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportDo) WithContext(ctx context.Context) *insReportDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportDo) ReadDB() *insReportDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportDo) WriteDB() *insReportDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportDo) Session(config *gorm.Session) *insReportDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportDo) Clauses(conds ...clause.Expression) *insReportDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportDo) Returning(value interface{}, columns ...string) *insReportDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportDo) Not(conds ...gen.Condition) *insReportDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportDo) Or(conds ...gen.Condition) *insReportDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportDo) Select(conds ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportDo) Where(conds ...gen.Condition) *insReportDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportDo) Order(conds ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportDo) Distinct(cols ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportDo) Omit(cols ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportDo) Join(table schema.Tabler, on ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportDo) Group(cols ...field.Expr) *insReportDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportDo) Having(conds ...gen.Condition) *insReportDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportDo) Limit(limit int) *insReportDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportDo) Offset(offset int) *insReportDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportDo) Unscoped() *insReportDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportDo) Create(values ...*insbuy.InsReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportDo) CreateInBatches(values []*insbuy.InsReport, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportDo) Save(values ...*insbuy.InsReport) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportDo) First() (*insbuy.InsReport, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReport), nil
	}
}

func (i insReportDo) Take() (*insbuy.InsReport, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReport), nil
	}
}

func (i insReportDo) Last() (*insbuy.InsReport, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReport), nil
	}
}

func (i insReportDo) Find() ([]*insbuy.InsReport, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReport), err
}

func (i insReportDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReport, err error) {
	buf := make([]*insbuy.InsReport, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportDo) FindInBatches(result *[]*insbuy.InsReport, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportDo) Attrs(attrs ...field.AssignExpr) *insReportDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportDo) Assign(attrs ...field.AssignExpr) *insReportDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportDo) Joins(fields ...field.RelationField) *insReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportDo) Preload(fields ...field.RelationField) *insReportDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportDo) FirstOrInit() (*insbuy.InsReport, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReport), nil
	}
}

func (i insReportDo) FirstOrCreate() (*insbuy.InsReport, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReport), nil
	}
}

func (i insReportDo) FindByPage(offset int, limit int) (result []*insbuy.InsReport, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportDo) Delete(models ...*insbuy.InsReport) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportDo) withDO(do gen.Dao) *insReportDo {
	i.DO = *do.(*gen.DO)
	return i
}
