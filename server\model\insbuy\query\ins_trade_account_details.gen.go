// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeAccountDetails(db *gorm.DB, opts ...gen.DOOption) insTradeAccountDetails {
	_insTradeAccountDetails := insTradeAccountDetails{}

	_insTradeAccountDetails.insTradeAccountDetailsDo.UseDB(db, opts...)
	_insTradeAccountDetails.insTradeAccountDetailsDo.UseModel(&insbuy.InsTradeAccountDetails{})

	tableName := _insTradeAccountDetails.insTradeAccountDetailsDo.TableName()
	_insTradeAccountDetails.ALL = field.NewAsterisk(tableName)
	_insTradeAccountDetails.ID = field.NewUint(tableName, "id")
	_insTradeAccountDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeAccountDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeAccountDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTradeAccountDetails.AmountId = field.NewUint(tableName, "amount_id")
	_insTradeAccountDetails.ChangeType = field.NewInt(tableName, "change_type")
	_insTradeAccountDetails.ChangeAmount = field.NewFloat64(tableName, "change_amount")
	_insTradeAccountDetails.SourceType = field.NewInt(tableName, "source_type")
	_insTradeAccountDetails.BeforeAmount = field.NewFloat64(tableName, "before_amount")
	_insTradeAccountDetails.AfterAmount = field.NewFloat64(tableName, "after_amount")
	_insTradeAccountDetails.Version = field.NewUint(tableName, "version")
	_insTradeAccountDetails.Status = field.NewInt(tableName, "status")
	_insTradeAccountDetails.RepayUserId = field.NewUint(tableName, "repay_user_id")
	_insTradeAccountDetails.TradeId = field.NewUint64(tableName, "trade_id")
	_insTradeAccountDetails.RemarkExt = field.NewField(tableName, "remark_ext")

	_insTradeAccountDetails.fillFieldMap()

	return _insTradeAccountDetails
}

type insTradeAccountDetails struct {
	insTradeAccountDetailsDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	AmountId     field.Uint
	ChangeType   field.Int
	ChangeAmount field.Float64
	SourceType   field.Int
	BeforeAmount field.Float64
	AfterAmount  field.Float64
	Version      field.Uint
	Status       field.Int
	RepayUserId  field.Uint
	TradeId      field.Uint64
	RemarkExt    field.Field

	fieldMap map[string]field.Expr
}

func (i insTradeAccountDetails) Table(newTableName string) *insTradeAccountDetails {
	i.insTradeAccountDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeAccountDetails) As(alias string) *insTradeAccountDetails {
	i.insTradeAccountDetailsDo.DO = *(i.insTradeAccountDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeAccountDetails) updateTableName(table string) *insTradeAccountDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.AmountId = field.NewUint(table, "amount_id")
	i.ChangeType = field.NewInt(table, "change_type")
	i.ChangeAmount = field.NewFloat64(table, "change_amount")
	i.SourceType = field.NewInt(table, "source_type")
	i.BeforeAmount = field.NewFloat64(table, "before_amount")
	i.AfterAmount = field.NewFloat64(table, "after_amount")
	i.Version = field.NewUint(table, "version")
	i.Status = field.NewInt(table, "status")
	i.RepayUserId = field.NewUint(table, "repay_user_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.RemarkExt = field.NewField(table, "remark_ext")

	i.fillFieldMap()

	return i
}

func (i *insTradeAccountDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeAccountDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["amount_id"] = i.AmountId
	i.fieldMap["change_type"] = i.ChangeType
	i.fieldMap["change_amount"] = i.ChangeAmount
	i.fieldMap["source_type"] = i.SourceType
	i.fieldMap["before_amount"] = i.BeforeAmount
	i.fieldMap["after_amount"] = i.AfterAmount
	i.fieldMap["version"] = i.Version
	i.fieldMap["status"] = i.Status
	i.fieldMap["repay_user_id"] = i.RepayUserId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["remark_ext"] = i.RemarkExt
}

func (i insTradeAccountDetails) clone(db *gorm.DB) insTradeAccountDetails {
	i.insTradeAccountDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeAccountDetails) replaceDB(db *gorm.DB) insTradeAccountDetails {
	i.insTradeAccountDetailsDo.ReplaceDB(db)
	return i
}

type insTradeAccountDetailsDo struct{ gen.DO }

func (i insTradeAccountDetailsDo) Debug() *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeAccountDetailsDo) WithContext(ctx context.Context) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeAccountDetailsDo) ReadDB() *insTradeAccountDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeAccountDetailsDo) WriteDB() *insTradeAccountDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeAccountDetailsDo) Session(config *gorm.Session) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeAccountDetailsDo) Clauses(conds ...clause.Expression) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeAccountDetailsDo) Returning(value interface{}, columns ...string) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeAccountDetailsDo) Not(conds ...gen.Condition) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeAccountDetailsDo) Or(conds ...gen.Condition) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeAccountDetailsDo) Select(conds ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeAccountDetailsDo) Where(conds ...gen.Condition) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeAccountDetailsDo) Order(conds ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeAccountDetailsDo) Distinct(cols ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeAccountDetailsDo) Omit(cols ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeAccountDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeAccountDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeAccountDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeAccountDetailsDo) Group(cols ...field.Expr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeAccountDetailsDo) Having(conds ...gen.Condition) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeAccountDetailsDo) Limit(limit int) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeAccountDetailsDo) Offset(offset int) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeAccountDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeAccountDetailsDo) Unscoped() *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeAccountDetailsDo) Create(values ...*insbuy.InsTradeAccountDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeAccountDetailsDo) CreateInBatches(values []*insbuy.InsTradeAccountDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeAccountDetailsDo) Save(values ...*insbuy.InsTradeAccountDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeAccountDetailsDo) First() (*insbuy.InsTradeAccountDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountDetails), nil
	}
}

func (i insTradeAccountDetailsDo) Take() (*insbuy.InsTradeAccountDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountDetails), nil
	}
}

func (i insTradeAccountDetailsDo) Last() (*insbuy.InsTradeAccountDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountDetails), nil
	}
}

func (i insTradeAccountDetailsDo) Find() ([]*insbuy.InsTradeAccountDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeAccountDetails), err
}

func (i insTradeAccountDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeAccountDetails, err error) {
	buf := make([]*insbuy.InsTradeAccountDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeAccountDetailsDo) FindInBatches(result *[]*insbuy.InsTradeAccountDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeAccountDetailsDo) Attrs(attrs ...field.AssignExpr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeAccountDetailsDo) Assign(attrs ...field.AssignExpr) *insTradeAccountDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeAccountDetailsDo) Joins(fields ...field.RelationField) *insTradeAccountDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeAccountDetailsDo) Preload(fields ...field.RelationField) *insTradeAccountDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeAccountDetailsDo) FirstOrInit() (*insbuy.InsTradeAccountDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountDetails), nil
	}
}

func (i insTradeAccountDetailsDo) FirstOrCreate() (*insbuy.InsTradeAccountDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeAccountDetails), nil
	}
}

func (i insTradeAccountDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeAccountDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeAccountDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeAccountDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeAccountDetailsDo) Delete(models ...*insbuy.InsTradeAccountDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeAccountDetailsDo) withDO(do gen.Dao) *insTradeAccountDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
