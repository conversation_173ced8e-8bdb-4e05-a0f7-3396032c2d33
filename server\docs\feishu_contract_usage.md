# 飞书合同数据拉取功能使用指南

## 概述

本文档介绍了重构后的飞书合同数据拉取功能的使用方法。重构后的代码提供了更加结构化、易于使用和维护的接口。

## 主要改进

1. **结构化参数**: 使用专门的请求结构体，支持参数验证和默认值设置
2. **统一错误处理**: 提供明确的错误信息和状态码
3. **批量处理**: 支持批量获取合同详情，自动处理API限流
4. **向后兼容**: 保留原有函数接口，确保现有代码不受影响
5. **便捷方法**: 提供多种便捷方法满足不同使用场景

## 核心接口

### 1. FeishuContractService

主要的服务类，封装了所有飞书合同相关的操作。

```go
// 创建服务实例
service := insfinance.NewFeishuContractService()
```

### 2. 获取合同列表

```go
// 构建请求参数
req := insfinance.ContractListRequest{
    ApprovalCode: "your-approval-code",     // 必填：审批定义Code
    StartTime:    time.Now().AddDate(0, 0, -30), // 开始时间
    EndTime:      time.Now(),               // 结束时间
    PageSize:     100,                      // 页面大小，默认100，最大200
    PageToken:    "",                       // 分页标记，首次请求为空
}

// 调用接口
ctx := context.Background()
resp, err := service.GetContractList(ctx, req)
if err != nil {
    // 处理错误
    log.Printf("获取合同列表失败: %v", err)
    return
}

// 处理响应
if resp.Success {
    fmt.Printf("获取到 %d 个合同实例代码\n", resp.Total)
    fmt.Printf("是否还有更多数据: %v\n", resp.HasMore)
    
    for _, instanceCode := range resp.InstanceCodeList {
        fmt.Printf("实例代码: %s\n", instanceCode)
    }
    
    // 如果还有更多数据，可以使用 resp.PageToken 继续获取
    if resp.HasMore {
        req.PageToken = resp.PageToken
        // 继续调用 GetContractList...
    }
}
```

### 3. 获取合同详情

```go
// 构建请求参数
req := insfinance.ContractDetailRequest{
    InstanceCodes: []string{
        "instance-code-1",
        "instance-code-2",
        // ... 最多50个
    },
}

// 调用接口
resp, err := service.GetContractDetails(ctx, req)
if err != nil {
    log.Printf("获取合同详情失败: %v", err)
    return
}

// 处理响应
fmt.Printf("成功获取 %d 个合同详情\n", resp.Total)
fmt.Printf("失败 %d 个合同\n", resp.FailedCount)

// 处理成功的合同
for _, contract := range resp.Contracts {
    fmt.Printf("合同: %s, 状态: %s\n", contract.ApprovalName, contract.Status)
    
    // 访问评论
    for _, comment := range contract.CommentList {
        fmt.Printf("  评论: %s\n", comment.Comment)
    }
    
    // 访问任务
    for _, task := range contract.TaskList {
        fmt.Printf("  任务: %s, 状态: %s\n", task.NodeName, task.Status)
    }
}

// 处理失败的合同
for _, err := range resp.Errors {
    fmt.Printf("合同 %s 获取失败: %s\n", err.InstanceCode, err.ErrorMsg)
}
```

## 便捷方法

### 1. 使用默认参数获取合同列表

```go
resp, err := insfinance.GetContractListWithDefaults("your-approval-code")
if err != nil {
    log.Printf("获取失败: %v", err)
    return
}
// 自动使用最近30天的数据，页面大小100
```

### 2. 获取所有分页数据

```go
startTime := time.Now().AddDate(0, 0, -7) // 最近7天
endTime := time.Now()

allInstanceCodes, err := insfinance.GetAllContractPages("your-approval-code", startTime, endTime)
if err != nil {
    log.Printf("获取失败: %v", err)
    return
}

fmt.Printf("总共获取到 %d 个合同实例代码\n", len(allInstanceCodes))
```

### 3. 批量获取合同详情（自动分批）

```go
// 假设有很多实例代码
instanceCodes := []string{
    // ... 100个实例代码
}

// 自动分批处理，每批20个
resp, err := insfinance.GetContractDetailsBatch(instanceCodes, 20)
if err != nil {
    log.Printf("批量获取失败: %v", err)
    return
}

fmt.Printf("批量处理完成: 成功 %d, 失败 %d\n", resp.Total, resp.FailedCount)
```

## 向后兼容

原有的函数仍然可以使用，它们内部会调用新的结构化接口：

```go
// 原有函数，保持兼容
insfinance.InstanceCodeList()  // 获取合同列表
insfinance.InstancesDetails()  // 获取合同详情
```

## 错误处理

重构后的接口提供了更好的错误处理机制：

```go
resp, err := service.GetContractList(ctx, req)
if err != nil {
    // 网络错误或参数错误
    log.Printf("请求失败: %v", err)
    return
}

if !resp.Success {
    // 飞书API返回的业务错误
    log.Printf("业务错误: %s", resp.ErrorMsg)
    return
}

// 成功处理数据
```

## 参数验证

新接口会自动验证参数并设置默认值：

```go
req := insfinance.ContractListRequest{
    ApprovalCode: "",  // 会报错：审批定义Code不能为空
    PageSize:     0,   // 会自动设置为100
    PageSize:     300, // 会自动限制为200（飞书API限制）
}
```

## 日志记录

所有操作都会记录详细的日志，便于调试和监控：

```
[INFO] 开始获取飞书合同列表 approval_code=xxx page_size=100
[INFO] 成功获取飞书合同列表 request_id=xxx total=50 has_more=true
[INFO] 开始获取飞书合同详情 instance_codes=[...] count=10
[INFO] 批量获取飞书合同详情完成 total_success=8 total_failed=2
```

## 性能优化

1. **API限流处理**: 自动添加延迟避免触发飞书API限流
2. **批量处理**: 支持批量获取详情，提高效率
3. **分页处理**: 自动处理分页，获取所有数据
4. **错误恢复**: 部分失败不影响整体处理

## 配置要求

确保在配置文件中正确设置飞书应用信息：

```yaml
feishu-app:
  appid: "your-app-id"
  app-secret: "your-app-secret"
```

## 注意事项

1. **审批代码**: 需要使用正确的审批定义Code，可以从飞书管理后台获取
2. **权限**: 确保飞书应用有足够的权限访问审批数据
3. **限流**: 飞书API有调用频率限制，建议合理控制调用频率
4. **数据量**: 大量数据获取时建议使用批量处理方法
5. **错误处理**: 务必处理可能的网络错误和业务错误

## 示例代码

完整的使用示例可以参考 `ExampleUsage()` 函数，或者运行：

```go
insfinance.ExampleUsage()
```
