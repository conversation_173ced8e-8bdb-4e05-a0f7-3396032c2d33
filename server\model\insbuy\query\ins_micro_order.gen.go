// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMicroOrder(db *gorm.DB, opts ...gen.DOOption) insMicroOrder {
	_insMicroOrder := insMicroOrder{}

	_insMicroOrder.insMicroOrderDo.UseDB(db, opts...)
	_insMicroOrder.insMicroOrderDo.UseModel(&insbuy.InsMicroOrder{})

	tableName := _insMicroOrder.insMicroOrderDo.TableName()
	_insMicroOrder.ALL = field.NewAsterisk(tableName)
	_insMicroOrder.ID = field.NewUint(tableName, "id")
	_insMicroOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_insMicroOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMicroOrder.DeletedAt = field.NewField(tableName, "deleted_at")
	_insMicroOrder.OrderSN = field.NewString(tableName, "order_sn")
	_insMicroOrder.OrderStatus = field.NewUint8(tableName, "order_status")
	_insMicroOrder.PayStatus = field.NewUint8(tableName, "pay_status")
	_insMicroOrder.PayName = field.NewString(tableName, "pay_name")
	_insMicroOrder.GoodsAmount = field.NewFloat64(tableName, "goods_amount")
	_insMicroOrder.PayFee = field.NewFloat64(tableName, "pay_fee")
	_insMicroOrder.OrderAmount = field.NewFloat64(tableName, "order_amount")
	_insMicroOrder.PayAmount = field.NewFloat64(tableName, "pay_amount")
	_insMicroOrder.OrderTime = field.NewTime(tableName, "order_time")
	_insMicroOrder.PayTime = field.NewTime(tableName, "pay_time")
	_insMicroOrder.CallbackStatus = field.NewString(tableName, "callback_status")

	_insMicroOrder.fillFieldMap()

	return _insMicroOrder
}

type insMicroOrder struct {
	insMicroOrderDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	OrderSN        field.String
	OrderStatus    field.Uint8
	PayStatus      field.Uint8
	PayName        field.String
	GoodsAmount    field.Float64
	PayFee         field.Float64
	OrderAmount    field.Float64
	PayAmount      field.Float64
	OrderTime      field.Time
	PayTime        field.Time
	CallbackStatus field.String

	fieldMap map[string]field.Expr
}

func (i insMicroOrder) Table(newTableName string) *insMicroOrder {
	i.insMicroOrderDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMicroOrder) As(alias string) *insMicroOrder {
	i.insMicroOrderDo.DO = *(i.insMicroOrderDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMicroOrder) updateTableName(table string) *insMicroOrder {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.OrderSN = field.NewString(table, "order_sn")
	i.OrderStatus = field.NewUint8(table, "order_status")
	i.PayStatus = field.NewUint8(table, "pay_status")
	i.PayName = field.NewString(table, "pay_name")
	i.GoodsAmount = field.NewFloat64(table, "goods_amount")
	i.PayFee = field.NewFloat64(table, "pay_fee")
	i.OrderAmount = field.NewFloat64(table, "order_amount")
	i.PayAmount = field.NewFloat64(table, "pay_amount")
	i.OrderTime = field.NewTime(table, "order_time")
	i.PayTime = field.NewTime(table, "pay_time")
	i.CallbackStatus = field.NewString(table, "callback_status")

	i.fillFieldMap()

	return i
}

func (i *insMicroOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMicroOrder) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["order_sn"] = i.OrderSN
	i.fieldMap["order_status"] = i.OrderStatus
	i.fieldMap["pay_status"] = i.PayStatus
	i.fieldMap["pay_name"] = i.PayName
	i.fieldMap["goods_amount"] = i.GoodsAmount
	i.fieldMap["pay_fee"] = i.PayFee
	i.fieldMap["order_amount"] = i.OrderAmount
	i.fieldMap["pay_amount"] = i.PayAmount
	i.fieldMap["order_time"] = i.OrderTime
	i.fieldMap["pay_time"] = i.PayTime
	i.fieldMap["callback_status"] = i.CallbackStatus
}

func (i insMicroOrder) clone(db *gorm.DB) insMicroOrder {
	i.insMicroOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMicroOrder) replaceDB(db *gorm.DB) insMicroOrder {
	i.insMicroOrderDo.ReplaceDB(db)
	return i
}

type insMicroOrderDo struct{ gen.DO }

func (i insMicroOrderDo) Debug() *insMicroOrderDo {
	return i.withDO(i.DO.Debug())
}

func (i insMicroOrderDo) WithContext(ctx context.Context) *insMicroOrderDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMicroOrderDo) ReadDB() *insMicroOrderDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMicroOrderDo) WriteDB() *insMicroOrderDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMicroOrderDo) Session(config *gorm.Session) *insMicroOrderDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMicroOrderDo) Clauses(conds ...clause.Expression) *insMicroOrderDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMicroOrderDo) Returning(value interface{}, columns ...string) *insMicroOrderDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMicroOrderDo) Not(conds ...gen.Condition) *insMicroOrderDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMicroOrderDo) Or(conds ...gen.Condition) *insMicroOrderDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMicroOrderDo) Select(conds ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMicroOrderDo) Where(conds ...gen.Condition) *insMicroOrderDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMicroOrderDo) Order(conds ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMicroOrderDo) Distinct(cols ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMicroOrderDo) Omit(cols ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMicroOrderDo) Join(table schema.Tabler, on ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMicroOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMicroOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMicroOrderDo) Group(cols ...field.Expr) *insMicroOrderDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMicroOrderDo) Having(conds ...gen.Condition) *insMicroOrderDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMicroOrderDo) Limit(limit int) *insMicroOrderDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMicroOrderDo) Offset(offset int) *insMicroOrderDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMicroOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMicroOrderDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMicroOrderDo) Unscoped() *insMicroOrderDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMicroOrderDo) Create(values ...*insbuy.InsMicroOrder) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMicroOrderDo) CreateInBatches(values []*insbuy.InsMicroOrder, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMicroOrderDo) Save(values ...*insbuy.InsMicroOrder) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMicroOrderDo) First() (*insbuy.InsMicroOrder, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMicroOrder), nil
	}
}

func (i insMicroOrderDo) Take() (*insbuy.InsMicroOrder, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMicroOrder), nil
	}
}

func (i insMicroOrderDo) Last() (*insbuy.InsMicroOrder, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMicroOrder), nil
	}
}

func (i insMicroOrderDo) Find() ([]*insbuy.InsMicroOrder, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMicroOrder), err
}

func (i insMicroOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMicroOrder, err error) {
	buf := make([]*insbuy.InsMicroOrder, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMicroOrderDo) FindInBatches(result *[]*insbuy.InsMicroOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMicroOrderDo) Attrs(attrs ...field.AssignExpr) *insMicroOrderDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMicroOrderDo) Assign(attrs ...field.AssignExpr) *insMicroOrderDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMicroOrderDo) Joins(fields ...field.RelationField) *insMicroOrderDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMicroOrderDo) Preload(fields ...field.RelationField) *insMicroOrderDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMicroOrderDo) FirstOrInit() (*insbuy.InsMicroOrder, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMicroOrder), nil
	}
}

func (i insMicroOrderDo) FirstOrCreate() (*insbuy.InsMicroOrder, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMicroOrder), nil
	}
}

func (i insMicroOrderDo) FindByPage(offset int, limit int) (result []*insbuy.InsMicroOrder, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMicroOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMicroOrderDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMicroOrderDo) Delete(models ...*insbuy.InsMicroOrder) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMicroOrderDo) withDO(do gen.Dao) *insMicroOrderDo {
	i.DO = *do.(*gen.DO)
	return i
}
