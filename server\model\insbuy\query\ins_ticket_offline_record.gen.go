// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTicketOfflineRecord(db *gorm.DB, opts ...gen.DOOption) insTicketOfflineRecord {
	_insTicketOfflineRecord := insTicketOfflineRecord{}

	_insTicketOfflineRecord.insTicketOfflineRecordDo.UseDB(db, opts...)
	_insTicketOfflineRecord.insTicketOfflineRecordDo.UseModel(&insbuy.InsTicketOfflineRecord{})

	tableName := _insTicketOfflineRecord.insTicketOfflineRecordDo.TableName()
	_insTicketOfflineRecord.ALL = field.NewAsterisk(tableName)
	_insTicketOfflineRecord.ID = field.NewUint(tableName, "id")
	_insTicketOfflineRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_insTicketOfflineRecord.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTicketOfflineRecord.StoreId = field.NewUint(tableName, "store_id")
	_insTicketOfflineRecord.FileName = field.NewString(tableName, "file_name")
	_insTicketOfflineRecord.ParamsMd5 = field.NewString(tableName, "params_md5")
	_insTicketOfflineRecord.Ext = field.NewField(tableName, "ext")

	_insTicketOfflineRecord.fillFieldMap()

	return _insTicketOfflineRecord
}

type insTicketOfflineRecord struct {
	insTicketOfflineRecordDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	StoreId   field.Uint
	FileName  field.String
	ParamsMd5 field.String
	Ext       field.Field

	fieldMap map[string]field.Expr
}

func (i insTicketOfflineRecord) Table(newTableName string) *insTicketOfflineRecord {
	i.insTicketOfflineRecordDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTicketOfflineRecord) As(alias string) *insTicketOfflineRecord {
	i.insTicketOfflineRecordDo.DO = *(i.insTicketOfflineRecordDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTicketOfflineRecord) updateTableName(table string) *insTicketOfflineRecord {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.FileName = field.NewString(table, "file_name")
	i.ParamsMd5 = field.NewString(table, "params_md5")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insTicketOfflineRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTicketOfflineRecord) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["file_name"] = i.FileName
	i.fieldMap["params_md5"] = i.ParamsMd5
	i.fieldMap["ext"] = i.Ext
}

func (i insTicketOfflineRecord) clone(db *gorm.DB) insTicketOfflineRecord {
	i.insTicketOfflineRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTicketOfflineRecord) replaceDB(db *gorm.DB) insTicketOfflineRecord {
	i.insTicketOfflineRecordDo.ReplaceDB(db)
	return i
}

type insTicketOfflineRecordDo struct{ gen.DO }

func (i insTicketOfflineRecordDo) Debug() *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Debug())
}

func (i insTicketOfflineRecordDo) WithContext(ctx context.Context) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTicketOfflineRecordDo) ReadDB() *insTicketOfflineRecordDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTicketOfflineRecordDo) WriteDB() *insTicketOfflineRecordDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTicketOfflineRecordDo) Session(config *gorm.Session) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTicketOfflineRecordDo) Clauses(conds ...clause.Expression) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTicketOfflineRecordDo) Returning(value interface{}, columns ...string) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTicketOfflineRecordDo) Not(conds ...gen.Condition) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTicketOfflineRecordDo) Or(conds ...gen.Condition) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTicketOfflineRecordDo) Select(conds ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTicketOfflineRecordDo) Where(conds ...gen.Condition) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTicketOfflineRecordDo) Order(conds ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTicketOfflineRecordDo) Distinct(cols ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTicketOfflineRecordDo) Omit(cols ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTicketOfflineRecordDo) Join(table schema.Tabler, on ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTicketOfflineRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTicketOfflineRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTicketOfflineRecordDo) Group(cols ...field.Expr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTicketOfflineRecordDo) Having(conds ...gen.Condition) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTicketOfflineRecordDo) Limit(limit int) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTicketOfflineRecordDo) Offset(offset int) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTicketOfflineRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTicketOfflineRecordDo) Unscoped() *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTicketOfflineRecordDo) Create(values ...*insbuy.InsTicketOfflineRecord) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTicketOfflineRecordDo) CreateInBatches(values []*insbuy.InsTicketOfflineRecord, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTicketOfflineRecordDo) Save(values ...*insbuy.InsTicketOfflineRecord) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTicketOfflineRecordDo) First() (*insbuy.InsTicketOfflineRecord, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTicketOfflineRecord), nil
	}
}

func (i insTicketOfflineRecordDo) Take() (*insbuy.InsTicketOfflineRecord, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTicketOfflineRecord), nil
	}
}

func (i insTicketOfflineRecordDo) Last() (*insbuy.InsTicketOfflineRecord, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTicketOfflineRecord), nil
	}
}

func (i insTicketOfflineRecordDo) Find() ([]*insbuy.InsTicketOfflineRecord, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTicketOfflineRecord), err
}

func (i insTicketOfflineRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTicketOfflineRecord, err error) {
	buf := make([]*insbuy.InsTicketOfflineRecord, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTicketOfflineRecordDo) FindInBatches(result *[]*insbuy.InsTicketOfflineRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTicketOfflineRecordDo) Attrs(attrs ...field.AssignExpr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTicketOfflineRecordDo) Assign(attrs ...field.AssignExpr) *insTicketOfflineRecordDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTicketOfflineRecordDo) Joins(fields ...field.RelationField) *insTicketOfflineRecordDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTicketOfflineRecordDo) Preload(fields ...field.RelationField) *insTicketOfflineRecordDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTicketOfflineRecordDo) FirstOrInit() (*insbuy.InsTicketOfflineRecord, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTicketOfflineRecord), nil
	}
}

func (i insTicketOfflineRecordDo) FirstOrCreate() (*insbuy.InsTicketOfflineRecord, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTicketOfflineRecord), nil
	}
}

func (i insTicketOfflineRecordDo) FindByPage(offset int, limit int) (result []*insbuy.InsTicketOfflineRecord, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTicketOfflineRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTicketOfflineRecordDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTicketOfflineRecordDo) Delete(models ...*insbuy.InsTicketOfflineRecord) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTicketOfflineRecordDo) withDO(do gen.Dao) *insTicketOfflineRecordDo {
	i.DO = *do.(*gen.DO)
	return i
}
