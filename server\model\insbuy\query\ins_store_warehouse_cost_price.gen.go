// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreWarehouseCostPrice(db *gorm.DB, opts ...gen.DOOption) insStoreWarehouseCostPrice {
	_insStoreWarehouseCostPrice := insStoreWarehouseCostPrice{}

	_insStoreWarehouseCostPrice.insStoreWarehouseCostPriceDo.UseDB(db, opts...)
	_insStoreWarehouseCostPrice.insStoreWarehouseCostPriceDo.UseModel(&insbuy.InsStoreWarehouseCostPrice{})

	tableName := _insStoreWarehouseCostPrice.insStoreWarehouseCostPriceDo.TableName()
	_insStoreWarehouseCostPrice.ALL = field.NewAsterisk(tableName)
	_insStoreWarehouseCostPrice.ID = field.NewUint(tableName, "id")
	_insStoreWarehouseCostPrice.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreWarehouseCostPrice.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreWarehouseCostPrice.StoreId = field.NewUint(tableName, "store_id")
	_insStoreWarehouseCostPrice.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insStoreWarehouseCostPrice.MaterialId = field.NewUint(tableName, "material_id")
	_insStoreWarehouseCostPrice.Num = field.NewUint(tableName, "num")
	_insStoreWarehouseCostPrice.Price = field.NewFloat64(tableName, "price")
	_insStoreWarehouseCostPrice.PriceTotal = field.NewFloat64(tableName, "price_total")
	_insStoreWarehouseCostPrice.Ext = field.NewField(tableName, "ext")
	_insStoreWarehouseCostPrice.BusinessDay = field.NewTime(tableName, "business_day")

	_insStoreWarehouseCostPrice.fillFieldMap()

	return _insStoreWarehouseCostPrice
}

type insStoreWarehouseCostPrice struct {
	insStoreWarehouseCostPriceDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	StoreId     field.Uint
	WarehouseId field.Uint
	MaterialId  field.Uint
	Num         field.Uint
	Price       field.Float64
	PriceTotal  field.Float64
	Ext         field.Field
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insStoreWarehouseCostPrice) Table(newTableName string) *insStoreWarehouseCostPrice {
	i.insStoreWarehouseCostPriceDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreWarehouseCostPrice) As(alias string) *insStoreWarehouseCostPrice {
	i.insStoreWarehouseCostPriceDo.DO = *(i.insStoreWarehouseCostPriceDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreWarehouseCostPrice) updateTableName(table string) *insStoreWarehouseCostPrice {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.MaterialId = field.NewUint(table, "material_id")
	i.Num = field.NewUint(table, "num")
	i.Price = field.NewFloat64(table, "price")
	i.PriceTotal = field.NewFloat64(table, "price_total")
	i.Ext = field.NewField(table, "ext")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insStoreWarehouseCostPrice) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreWarehouseCostPrice) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 11)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["num"] = i.Num
	i.fieldMap["price"] = i.Price
	i.fieldMap["price_total"] = i.PriceTotal
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insStoreWarehouseCostPrice) clone(db *gorm.DB) insStoreWarehouseCostPrice {
	i.insStoreWarehouseCostPriceDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreWarehouseCostPrice) replaceDB(db *gorm.DB) insStoreWarehouseCostPrice {
	i.insStoreWarehouseCostPriceDo.ReplaceDB(db)
	return i
}

type insStoreWarehouseCostPriceDo struct{ gen.DO }

func (i insStoreWarehouseCostPriceDo) Debug() *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreWarehouseCostPriceDo) WithContext(ctx context.Context) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreWarehouseCostPriceDo) ReadDB() *insStoreWarehouseCostPriceDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreWarehouseCostPriceDo) WriteDB() *insStoreWarehouseCostPriceDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreWarehouseCostPriceDo) Session(config *gorm.Session) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreWarehouseCostPriceDo) Clauses(conds ...clause.Expression) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreWarehouseCostPriceDo) Returning(value interface{}, columns ...string) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreWarehouseCostPriceDo) Not(conds ...gen.Condition) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreWarehouseCostPriceDo) Or(conds ...gen.Condition) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreWarehouseCostPriceDo) Select(conds ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreWarehouseCostPriceDo) Where(conds ...gen.Condition) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreWarehouseCostPriceDo) Order(conds ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreWarehouseCostPriceDo) Distinct(cols ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreWarehouseCostPriceDo) Omit(cols ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreWarehouseCostPriceDo) Join(table schema.Tabler, on ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreWarehouseCostPriceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreWarehouseCostPriceDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreWarehouseCostPriceDo) Group(cols ...field.Expr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreWarehouseCostPriceDo) Having(conds ...gen.Condition) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreWarehouseCostPriceDo) Limit(limit int) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreWarehouseCostPriceDo) Offset(offset int) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreWarehouseCostPriceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreWarehouseCostPriceDo) Unscoped() *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreWarehouseCostPriceDo) Create(values ...*insbuy.InsStoreWarehouseCostPrice) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreWarehouseCostPriceDo) CreateInBatches(values []*insbuy.InsStoreWarehouseCostPrice, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreWarehouseCostPriceDo) Save(values ...*insbuy.InsStoreWarehouseCostPrice) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreWarehouseCostPriceDo) First() (*insbuy.InsStoreWarehouseCostPrice, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreWarehouseCostPrice), nil
	}
}

func (i insStoreWarehouseCostPriceDo) Take() (*insbuy.InsStoreWarehouseCostPrice, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreWarehouseCostPrice), nil
	}
}

func (i insStoreWarehouseCostPriceDo) Last() (*insbuy.InsStoreWarehouseCostPrice, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreWarehouseCostPrice), nil
	}
}

func (i insStoreWarehouseCostPriceDo) Find() ([]*insbuy.InsStoreWarehouseCostPrice, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreWarehouseCostPrice), err
}

func (i insStoreWarehouseCostPriceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreWarehouseCostPrice, err error) {
	buf := make([]*insbuy.InsStoreWarehouseCostPrice, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreWarehouseCostPriceDo) FindInBatches(result *[]*insbuy.InsStoreWarehouseCostPrice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreWarehouseCostPriceDo) Attrs(attrs ...field.AssignExpr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreWarehouseCostPriceDo) Assign(attrs ...field.AssignExpr) *insStoreWarehouseCostPriceDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreWarehouseCostPriceDo) Joins(fields ...field.RelationField) *insStoreWarehouseCostPriceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreWarehouseCostPriceDo) Preload(fields ...field.RelationField) *insStoreWarehouseCostPriceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreWarehouseCostPriceDo) FirstOrInit() (*insbuy.InsStoreWarehouseCostPrice, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreWarehouseCostPrice), nil
	}
}

func (i insStoreWarehouseCostPriceDo) FirstOrCreate() (*insbuy.InsStoreWarehouseCostPrice, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreWarehouseCostPrice), nil
	}
}

func (i insStoreWarehouseCostPriceDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreWarehouseCostPrice, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreWarehouseCostPriceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreWarehouseCostPriceDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreWarehouseCostPriceDo) Delete(models ...*insbuy.InsStoreWarehouseCostPrice) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreWarehouseCostPriceDo) withDO(do gen.Dao) *insStoreWarehouseCostPriceDo {
	i.DO = *do.(*gen.DO)
	return i
}
