# 费用报销审批配置文件
# 用于将飞书审批表单数据转换为标准化的费用报销数据
# 特别处理 fieldList 类型字段，将费用明细列表拆分为独立记录

EXPENSE_REIMBURSEMENT_APPROVAL:
  approval_code: "EXPENSE_REIMBURSEMENT_APPROVAL"
  approval_name: "费用报销审批"
  description: "费用报销审批流程的数据映射配置，支持费用明细列表处理"
  
  # 字段映射配置
  field_mappings:
    # 基础信息
    application_number:
      source_path: "basic.instance_code"
      target_field: "application_number"
      data_type: "string"
      required: true
      description: "申请编号"
    
    title:
      source_path: "basic.approval_name"
      target_field: "title"
      data_type: "string"
      required: true
      description: "申请标题"
    
    application_status:
      source_path: "basic.status"
      target_field: "application_status"
      data_type: "string"
      required: true
      description: "申请状态"
    
    initiate_time:
      source_path: "basic.start_time"
      target_field: "initiate_time"
      data_type: "time"
      required: true
      description: "发起时间"
    
    complete_time:
      source_path: "basic.end_time"
      target_field: "complete_time"
      data_type: "time"
      required: false
      description: "完成时间"
    
    # 人员信息
    initiator_user_id:
      source_path: "basic.user_id"
      target_field: "initiator_user_id"
      data_type: "string"
      required: true
      description: "发起人User ID"
    
    initiator_department_id:
      source_path: "basic.department_id"
      target_field: "initiator_department_id"
      data_type: "string"
      required: false
      description: "发起人部门ID"
    
    serial_number:
      source_path: "form.widget_serial_number.value"
      target_field: "serial_number"
      data_type: "string"
      required: false
      description: "流水号"
    
    # 报销基本信息
    reimbursement_reason:
      source_path: "form.widget_reason.value"
      target_field: "reimbursement_reason"
      data_type: "string"
      required: true
      description: "报销事由"
    
    reimbursement_entity:
      source_path: "form.widget_entity.value"
      target_field: "reimbursement_entity"
      data_type: "string"
      required: true
      description: "报销主体"
    
    business_type:
      source_path: "form.widget_business_type.value"
      target_field: "business_type"
      data_type: "string"
      required: false
      description: "业务类型"
    
    currency:
      source_path: "form.widget_currency.value"
      target_field: "currency"
      data_type: "string"
      required: true
      description: "币种"
    
    # 费用明细列表处理 - 特殊字段类型 fieldList
    expense_details:
      source_path: "form.widget_expense_list.value"
      target_field: "expense_details"
      data_type: "fieldList"
      required: true
      description: "费用明细列表"
      field_list_config:
        # fieldList 内部字段映射
        expense_type:
          source_path: "widget16710060735460001.value"
          target_field: "expense_type"
          data_type: "string"
          required: true
          description: "费用类型"
        
        location:
          source_path: "widget16732469773550001.value"
          target_field: "location"
          data_type: "string"
          required: false
          description: "地点"
        
        date_range:
          source_path: "widget16710061214060001.value"
          target_field: "date_range"
          data_type: "string"
          required: true
          description: "日期区间"
        
        start_date:
          source_path: "widget16710061214060001.value.start_date"
          target_field: "start_date"
          data_type: "time"
          required: false
          description: "开始日期"
        
        end_date:
          source_path: "widget16710061214060001.value.end_date"
          target_field: "end_date"
          data_type: "time"
          required: false
          description: "结束日期"
        
        vat_invoice_type:
          source_path: "widget17455770530670001.value"
          target_field: "vat_invoice_type"
          data_type: "string"
          required: false
          description: "增值税发票类型"
        
        amount:
          source_path: "widget16710062249940001.value"
          target_field: "amount"
          data_type: "float"
          required: true
          description: "金额"
        
        amount_currency:
          source_path: "widget16710062249940001.ext.currency"
          target_field: "amount_currency"
          data_type: "string"
          required: false
          description: "金额币种"
        
        amount_capital:
          source_path: "widget16710062249940001.ext.capitalValue"
          target_field: "amount_capital"
          data_type: "string"
          required: false
          description: "金额大写"
    
    # 汇总金额信息
    total_amount:
      source_path: "form.widget_expense_list.ext.sumItems.widget16710062249940001"
      target_field: "total_amount"
      data_type: "float"
      required: true
      description: "费用总金额"
    
    # 银行账户信息
    account_holder:
      source_path: "form.widget_account.value.widgetAccountName"
      target_field: "account_holder"
      data_type: "string"
      required: true
      description: "收款方户名"
    
    account_type:
      source_path: "form.widget_account.value.widgetAccountType.text"
      target_field: "account_type"
      data_type: "string"
      required: false
      description: "账户类型"
    
    account_number:
      source_path: "form.widget_account.value.widgetAccountNumber"
      target_field: "account_number"
      data_type: "string"
      required: true
      description: "账户号码"
    
    bank_name:
      source_path: "form.widget_account.value.widgetAccountBankName"
      target_field: "bank_name"
      data_type: "string"
      required: true
      description: "银行名称"
      transform: "extractBankName"
    
    bank_branch:
      source_path: "form.widget_account.value.widgetAccountBankBranch"
      target_field: "bank_branch"
      data_type: "string"
      required: false
      description: "银行支行"
      transform: "extractBankBranch"
    
    bank_region:
      source_path: "form.widget_account.value.widgetAccountBankArea"
      target_field: "bank_region"
      data_type: "string"
      required: false
      description: "银行地区"
      transform: "extractBankRegion"
    
    # 其他信息
    expected_payment_date:
      source_path: "form.widget_payment_date.value"
      target_field: "expected_payment_date"
      data_type: "time"
      required: false
      description: "期望付款日期"
    
    attachments:
      source_path: "form.widget_attachments.value"
      target_field: "attachments"
      data_type: "array"
      required: false
      description: "附件"
      transform: "parseAttachmentsWithUrls"
    
    remarks:
      source_path: "form.widget_remarks.value"
      target_field: "remarks"
      data_type: "string"
      required: false
      description: "备注说明"
  
  # 默认值配置
  default_values:
    business_type: "费用报销"
    data_version: "1.0"
    currency: "人民币"
    record_type: "expense_detail"  # 标识这是费用明细记录
  
  # 必填字段列表
  required_fields:
    - "application_number"
    - "title"
    - "reimbursement_reason"
    - "reimbursement_entity"
    - "total_amount"
    - "account_holder"
    - "account_number"
    - "bank_name"
    - "initiator_user_id"
    - "expense_details"  # 费用明细列表必填
  
  # 验证规则
  validation_rules:
    - field: "application_number"
      rule: "required"
      parameter: ""
      message: "申请编号不能为空"
    
    - field: "reimbursement_reason"
      rule: "required"
      parameter: ""
      message: "报销事由不能为空"
    
    - field: "total_amount"
      rule: "range"
      parameter: "0.01,*********"
      message: "费用总金额必须大于0"
    
    - field: "expense_details"
      rule: "required"
      parameter: ""
      message: "费用明细不能为空"
    
    - field: "account_number"
      rule: "format"
      parameter: "^[0-9]{10,30}$"
      message: "账户号码格式不正确"
  
  # fieldList 特殊处理配置
  field_list_processing:
    enabled: true
    split_records: true  # 是否将列表拆分为独立记录
    preserve_parent_data: true  # 是否保留父级数据
    record_prefix: "expense_detail"  # 记录前缀
    
    # 拆分规则
    split_rules:
      - source_field: "expense_details"
        target_record_type: "expense_detail_record"
        merge_parent_fields:
          - "application_number"
          - "title"
          - "initiator_user_id"
          - "reimbursement_reason"
          - "reimbursement_entity"
          - "account_holder"
          - "account_number"
          - "bank_name"
