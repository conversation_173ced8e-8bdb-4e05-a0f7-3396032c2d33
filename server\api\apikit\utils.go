package apikit

import (
	"bytes"
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	jsoniter "github.com/json-iterator/go"
	"github.com/json-iterator/go/extra"
	"io"
	"net/http"
	"reflect"
	"time"
)

func init() {
	// 启用 json 兼容处理 ，
	// 还需要在编译参数加上:  -tags jsoniter
	//  如 go run -tags "jsoniter" main.go
	extra.RegisterFuzzyDecoders()
}

type tWithNow interface {
	SetNow(now time.Time)
}
type tWithIP interface {
	SetIP(string)
}
type tWithCtx interface {
	SetCtx(ctx context.Context)
	SetCtxValue(k, v interface{})
}
type tWithStoreId interface {
	SetStoreId(storeId string)
}
type tWithAuthUser interface {
	SetAuthUser(info request.IUserInfoProvider)
}
type tWithGinContext interface {
	SetGinContext(c *gin.Context)
}

type tWithInsStoreCode interface {
	SetInsStoreCode(insStoreCode string)
}

// GinMustBind 示例：解析请求参数
func GinMustBind(c *gin.Context, obj interface{}) error {
	b := binding.Default(c.Request.Method, c.ContentType())
	if b == binding.JSON {
		b = BindingJSON
	}
	err := c.MustBindWith(obj, b)
	return GinBindAfter(c, obj, err)
}

// GinBindAfter 参数事后处理
func GinBindAfter(c *gin.Context, obj interface{}, err error) error {
	if err != nil {
		return err
	}
	if m, ok := obj.(tWithNow); ok {
		m.SetNow(time.Now())
	}
	if m, ok := obj.(tWithGinContext); ok {
		m.SetGinContext(c)
	}
	if m, ok := obj.(tWithCtx); ok {
		m.SetCtx(c.Request.Context())
	}
	if m, ok := obj.(tWithIP); ok {
		m.SetIP(c.ClientIP())
	}
	if m, ok := obj.(tWithStoreId); ok {
		if id := utils.GetHeaderStoreIdString(c); id != "" {
			m.SetStoreId(id)
		}
	}
	if m, ok := obj.(tWithAuthUser); ok {
		m.SetAuthUser(utils.GetUserInfo(c))
	}

	if m, ok := obj.(tWithInsStoreCode); ok {
		if code := utils.GetHeaderStoreCode(c); code != "" {
			m.SetInsStoreCode(code)
		}
	}

	return nil
}

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
	// Marshal is exported by gin/json package.
	Marshal = json.Marshal
	// Unmarshal is exported by gin/json package.
	Unmarshal = json.Unmarshal
	// MarshalIndent is exported by gin/json package.
	MarshalIndent = json.MarshalIndent
	// NewDecoder is exported by gin/json package.
	NewDecoder = json.NewDecoder
	// NewEncoder is exported by gin/json package.
	NewEncoder = json.NewEncoder
)

// BindingJSON 替换Gin默认的binding，支持更丰富JSON功能
var BindingJSON = jsonBinding{}

type jsonBinding struct{}

func (jsonBinding) Name() string {
	return "json"
}

func (jsonBinding) Bind(req *http.Request, obj interface{}) error {
	if req == nil || req.Body == nil {
		return fmt.Errorf("invalid request")
	}
	if req.GetBody != nil {
		body, err := req.GetBody()
		if err != nil {
			return err
		}
		return decodeJSON(body, obj)
	}
	return decodeJSON(req.Body, obj)
}

func (jsonBinding) BindBody(body []byte, obj interface{}) error {
	return decodeJSON(bytes.NewReader(body), obj)
}

func decodeJSON(r io.Reader, obj interface{}) error {
	decoder := NewDecoder(r)
	if binding.EnableDecoderUseNumber {
		decoder.UseNumber()
	}
	if binding.EnableDecoderDisallowUnknownFields {
		decoder.DisallowUnknownFields()
	}
	if err := decoder.Decode(obj); err != nil {
		return err
	}
	return validate(obj)
}

func validate(obj interface{}) error {
	if binding.Validator == nil {
		return nil
	}
	return binding.Validator.ValidateStruct(obj)
}

var (
	ctxType = reflect.TypeOf((*context.Context)(nil)).Elem()
	ginType = reflect.TypeOf((*gin.Context)(nil)).Elem()
	errType = reflect.TypeOf((*error)(nil)).Elem()
)

// WrapperApi0 包装器, 用于将 gin 的请求参数绑定到结构体上，
//
// 特殊参数: context.Context, *gin.Context
//
// 不列出需要解析的参数的话，则只有第一个非 context 的参数用于接收请求参数
//
// jason.liao 2023.12.12
func WrapperApi0(callbackFunc any, reqSample ...any) gin.HandlerFunc {
	fnType := reflect.TypeOf(callbackFunc)
	if fnType.Kind() != reflect.Func {
		panic("callbackFunc must be func")
	}
	fnVal := reflect.ValueOf(callbackFunc)
	reqTypes := make(map[reflect.Type]reflect.Value)
	for _, v := range reqSample {
		rt1, rv1 := reflect.TypeOf(v), reflect.ValueOf(v)
		if rt1.Kind() == reflect.Ptr {
			rt1 = rt1.Elem()
			rv1 = rv1.Elem()
		}
		reqTypes[rt1] = rv1
	}
	skipReqType := len(reqTypes) > 0
	return func(c *gin.Context) {
		var args = make([]reflect.Value, fnType.NumIn())
		for i := 0; i < fnType.NumIn(); i++ {
			var rt1 = fnType.In(i)
			var rt2 = rt1
			for rt2.Kind() == reflect.Ptr {
				rt2 = rt2.Elem()
			}
			if rt1 == ctxType || rt2 == ctxType {
				args[i] = reflect.ValueOf(c.Request.Context())
				continue
			}
			if rt1 == ginType || rt2 == ginType {
				args[i] = reflect.ValueOf(c)
				continue
			}
			if rt2.Kind() != reflect.Struct {
				if rt2.Kind() == reflect.Interface {
					//if rt2.Implements(ctxType) {
					//	args[i] = reflect.ValueOf(c.Request.Context())
					//} else if rt2.Implements(ginType) {
					//	args[i] = reflect.ValueOf(c)
					//} else {
					args[i] = reflect.New(rt1).Elem()
					//}
				}
				continue
			}
			var a1 reflect.Value
			if rt1.Kind() == reflect.Ptr {
				rt2 = rt1.Elem()
				a1 = reflect.New(rt1.Elem())
			} else {
				a1 = reflect.New(rt1)
			}
			if skipReqType {
				//if _, ok := reqTypes[rt2]; !ok {
				//	continue
				//}
			}
			var p1 = a1.Interface()
			if err := GinMustBind(c, p1); err != nil {
				response.Err(err, c)
				return
			}
			if rt1.Kind() == reflect.Ptr {
				args[i] = a1
			} else {
				args[i] = a1.Elem()
			}
		}
		var resp = fnVal.Call(args)
		var data interface{}
		var err error
		if len(resp) > 0 {
			for _, v := range resp {
				if v.Type().Implements(reflect.TypeOf((*error)(nil)).Elem()) {
					if !v.IsNil() {
						err = v.Interface().(error)
					}
				} else {
					data = v.Interface()
				}
			}
		}
		response.ResultErr(data, err, c)
	}
}
