package insbuy

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseEmptiesApi struct {
}

// CreateInsWarehouseEmpties 创建空瓶回收记录
// @Tags InsWarehouseEmpties
// @Summary 创建空瓶回收记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsWarehouseEmptiesCreate true "创建空瓶回收记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/createInsWarehouseEmpties [post]
func (p *InsWarehouseEmptiesApi) CreateInsWarehouseEmpties(c *gin.Context) {
	var insWarehouseEmpties insbuyReq.InsWarehouseEmptiesCreate
	err := GinMustBind(c, &insWarehouseEmpties)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insWarehouseEmptiesService.CreateInsWarehouseEmpties(insWarehouseEmpties); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateInsWarehouseEmpties 更新空瓶回收
// @Tags InsWarehouseEmpties
// @Summary 更新空瓶回收
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsWarehouseEmpties true "更新空瓶回收"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insWarehouse/updateInsWarehouseEmpties [put]
func (p *InsWarehouseEmptiesApi) UpdateInsWarehouseEmpties(c *gin.Context) {
	var insWarehouseEmpties insbuy.InsWarehouseEmpties
	err := c.ShouldBindJSON(&insWarehouseEmpties)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseEmptiesService.UpdateInsWarehouseEmpties(insWarehouseEmpties); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsWarehouseEmpties 用id查询空瓶回收
// @Tags InsWarehouseEmpties
// @Summary 用id查询空瓶回收
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsWarehouseEmpties true "用id查询空瓶回收"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insWarehouse/findInsWarehouseEmpties [get]
func (p *InsWarehouseEmptiesApi) FindInsWarehouseEmpties(c *gin.Context) {
	var insWarehouseEmpties insbuy.InsWarehouseEmpties
	err := c.ShouldBindQuery(&insWarehouseEmpties)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	fmt.Printf("%+v", insWarehouseEmpties)
	if reinsWarehouse, err := insWarehouseEmptiesService.GetInsWarehouseEmpties(insWarehouseEmpties.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reins": reinsWarehouse}, c)
	}
}

// GetInsWarehouseEmpties 分页获取空瓶回收列表
// @Tags InsWarehouseEmpties
// @Summary 分页获取空瓶回收列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsWarehouseEmptiesSearch true "分页获取空瓶回收列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getInsWarehouseEmpties [get]
func (p *InsWarehouseEmptiesApi) GetInsWarehouseEmpties(c *gin.Context) {
	var pageInfo insbuyReq.InsWarehouseEmptiesSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseEmptiesService.GetInsWarehouseEmptiesInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETEmptyList.ToInt(), list)
			if e != nil {
				response.FailWithMessage("导出失败", c)
				return
			}
			c.Abort()
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
