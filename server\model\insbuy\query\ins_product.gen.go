// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProduct(db *gorm.DB, opts ...gen.DOOption) insProduct {
	_insProduct := insProduct{}

	_insProduct.insProductDo.UseDB(db, opts...)
	_insProduct.insProductDo.UseModel(&insbuy.InsProduct{})

	tableName := _insProduct.insProductDo.TableName()
	_insProduct.ALL = field.NewAsterisk(tableName)
	_insProduct.ID = field.NewUint(tableName, "id")
	_insProduct.ProductSn = field.NewString(tableName, "product_sn")
	_insProduct.CategoryId = field.NewInt(tableName, "category_id")
	_insProduct.BrandId = field.NewInt(tableName, "brand_id")
	_insProduct.ProductName = field.NewString(tableName, "product_name")
	_insProduct.EnName = field.NewString(tableName, "en_name")
	_insProduct.MarketPrice = field.NewFloat64(tableName, "market_price")
	_insProduct.ShopPrice = field.NewFloat64(tableName, "shop_price")
	_insProduct.Unit = field.NewInt(tableName, "unit")
	_insProduct.Stock = field.NewInt(tableName, "stock")
	_insProduct.Keywords = field.NewString(tableName, "keywords")
	_insProduct.BarCode = field.NewString(tableName, "bar_code")
	_insProduct.QrCode = field.NewString(tableName, "qr_code")
	_insProduct.ProductThumb = field.NewString(tableName, "product_thumb")
	_insProduct.IsReal = field.NewInt(tableName, "is_real")
	_insProduct.IsOnSale = field.NewInt(tableName, "is_on_sale")
	_insProduct.IsDelete = field.NewInt(tableName, "is_delete")
	_insProduct.IsPromote = field.NewInt(tableName, "is_promote")
	_insProduct.SortOrder = field.NewInt(tableName, "sort_order")
	_insProduct.Remark = field.NewString(tableName, "remark")
	_insProduct.IsUnique = field.NewInt(tableName, "is_unique")
	_insProduct.ExpireDay = field.NewInt(tableName, "expire_day")
	_insProduct.IsPackage = field.NewInt(tableName, "is_package")
	_insProduct.IsDeposit = field.NewInt(tableName, "is_deposit")
	_insProduct.IsGive = field.NewInt(tableName, "is_give")
	_insProduct.IsRecycle = field.NewInt(tableName, "is_recycle")
	_insProduct.IsWine = field.NewInt(tableName, "is_wine")
	_insProduct.IsEntertain = field.NewInt(tableName, "is_entertain")
	_insProduct.IsCostCard = field.NewInt(tableName, "is_cost_card")
	_insProduct.IsScanCode = field.NewInt(tableName, "is_scan_code")
	_insProduct.IsTips = field.NewInt(tableName, "is_tips")
	_insProduct.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insProduct.StoreId = field.NewUint(tableName, "store_id")
	_insProduct.CreatedAt = field.NewTime(tableName, "created_at")
	_insProduct.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProduct.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProduct.Details = insProductHasOneDetails{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Details", "insbuy.InsProductDetails"),
	}

	_insProduct.fillFieldMap()

	return _insProduct
}

type insProduct struct {
	insProductDo

	ALL          field.Asterisk
	ID           field.Uint
	ProductSn    field.String
	CategoryId   field.Int
	BrandId      field.Int
	ProductName  field.String
	EnName       field.String
	MarketPrice  field.Float64
	ShopPrice    field.Float64
	Unit         field.Int
	Stock        field.Int
	Keywords     field.String
	BarCode      field.String
	QrCode       field.String
	ProductThumb field.String
	IsReal       field.Int
	IsOnSale     field.Int
	IsDelete     field.Int
	IsPromote    field.Int
	SortOrder    field.Int
	Remark       field.String
	IsUnique     field.Int
	ExpireDay    field.Int
	IsPackage    field.Int
	IsDeposit    field.Int
	IsGive       field.Int
	IsRecycle    field.Int
	IsWine       field.Int
	IsEntertain  field.Int
	IsCostCard   field.Int
	IsScanCode   field.Int
	IsTips       field.Int
	WarehouseId  field.Int
	StoreId      field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Details      insProductHasOneDetails

	fieldMap map[string]field.Expr
}

func (i insProduct) Table(newTableName string) *insProduct {
	i.insProductDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProduct) As(alias string) *insProduct {
	i.insProductDo.DO = *(i.insProductDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProduct) updateTableName(table string) *insProduct {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.ProductSn = field.NewString(table, "product_sn")
	i.CategoryId = field.NewInt(table, "category_id")
	i.BrandId = field.NewInt(table, "brand_id")
	i.ProductName = field.NewString(table, "product_name")
	i.EnName = field.NewString(table, "en_name")
	i.MarketPrice = field.NewFloat64(table, "market_price")
	i.ShopPrice = field.NewFloat64(table, "shop_price")
	i.Unit = field.NewInt(table, "unit")
	i.Stock = field.NewInt(table, "stock")
	i.Keywords = field.NewString(table, "keywords")
	i.BarCode = field.NewString(table, "bar_code")
	i.QrCode = field.NewString(table, "qr_code")
	i.ProductThumb = field.NewString(table, "product_thumb")
	i.IsReal = field.NewInt(table, "is_real")
	i.IsOnSale = field.NewInt(table, "is_on_sale")
	i.IsDelete = field.NewInt(table, "is_delete")
	i.IsPromote = field.NewInt(table, "is_promote")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.Remark = field.NewString(table, "remark")
	i.IsUnique = field.NewInt(table, "is_unique")
	i.ExpireDay = field.NewInt(table, "expire_day")
	i.IsPackage = field.NewInt(table, "is_package")
	i.IsDeposit = field.NewInt(table, "is_deposit")
	i.IsGive = field.NewInt(table, "is_give")
	i.IsRecycle = field.NewInt(table, "is_recycle")
	i.IsWine = field.NewInt(table, "is_wine")
	i.IsEntertain = field.NewInt(table, "is_entertain")
	i.IsCostCard = field.NewInt(table, "is_cost_card")
	i.IsScanCode = field.NewInt(table, "is_scan_code")
	i.IsTips = field.NewInt(table, "is_tips")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")

	i.fillFieldMap()

	return i
}

func (i *insProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProduct) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 37)
	i.fieldMap["id"] = i.ID
	i.fieldMap["product_sn"] = i.ProductSn
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["brand_id"] = i.BrandId
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["en_name"] = i.EnName
	i.fieldMap["market_price"] = i.MarketPrice
	i.fieldMap["shop_price"] = i.ShopPrice
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["stock"] = i.Stock
	i.fieldMap["keywords"] = i.Keywords
	i.fieldMap["bar_code"] = i.BarCode
	i.fieldMap["qr_code"] = i.QrCode
	i.fieldMap["product_thumb"] = i.ProductThumb
	i.fieldMap["is_real"] = i.IsReal
	i.fieldMap["is_on_sale"] = i.IsOnSale
	i.fieldMap["is_delete"] = i.IsDelete
	i.fieldMap["is_promote"] = i.IsPromote
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["is_unique"] = i.IsUnique
	i.fieldMap["expire_day"] = i.ExpireDay
	i.fieldMap["is_package"] = i.IsPackage
	i.fieldMap["is_deposit"] = i.IsDeposit
	i.fieldMap["is_give"] = i.IsGive
	i.fieldMap["is_recycle"] = i.IsRecycle
	i.fieldMap["is_wine"] = i.IsWine
	i.fieldMap["is_entertain"] = i.IsEntertain
	i.fieldMap["is_cost_card"] = i.IsCostCard
	i.fieldMap["is_scan_code"] = i.IsScanCode
	i.fieldMap["is_tips"] = i.IsTips
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt

}

func (i insProduct) clone(db *gorm.DB) insProduct {
	i.insProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProduct) replaceDB(db *gorm.DB) insProduct {
	i.insProductDo.ReplaceDB(db)
	return i
}

type insProductHasOneDetails struct {
	db *gorm.DB

	field.RelationField
}

func (a insProductHasOneDetails) Where(conds ...field.Expr) *insProductHasOneDetails {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insProductHasOneDetails) WithContext(ctx context.Context) *insProductHasOneDetails {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insProductHasOneDetails) Session(session *gorm.Session) *insProductHasOneDetails {
	a.db = a.db.Session(session)
	return &a
}

func (a insProductHasOneDetails) Model(m *insbuy.InsProduct) *insProductHasOneDetailsTx {
	return &insProductHasOneDetailsTx{a.db.Model(m).Association(a.Name())}
}

type insProductHasOneDetailsTx struct{ tx *gorm.Association }

func (a insProductHasOneDetailsTx) Find() (result *insbuy.InsProductDetails, err error) {
	return result, a.tx.Find(&result)
}

func (a insProductHasOneDetailsTx) Append(values ...*insbuy.InsProductDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insProductHasOneDetailsTx) Replace(values ...*insbuy.InsProductDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insProductHasOneDetailsTx) Delete(values ...*insbuy.InsProductDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insProductHasOneDetailsTx) Clear() error {
	return a.tx.Clear()
}

func (a insProductHasOneDetailsTx) Count() int64 {
	return a.tx.Count()
}

type insProductDo struct{ gen.DO }

func (i insProductDo) Debug() *insProductDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductDo) WithContext(ctx context.Context) *insProductDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductDo) ReadDB() *insProductDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductDo) WriteDB() *insProductDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductDo) Session(config *gorm.Session) *insProductDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductDo) Clauses(conds ...clause.Expression) *insProductDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductDo) Returning(value interface{}, columns ...string) *insProductDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductDo) Not(conds ...gen.Condition) *insProductDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductDo) Or(conds ...gen.Condition) *insProductDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductDo) Select(conds ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductDo) Where(conds ...gen.Condition) *insProductDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductDo) Order(conds ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductDo) Distinct(cols ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductDo) Omit(cols ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductDo) Join(table schema.Tabler, on ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductDo) Group(cols ...field.Expr) *insProductDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductDo) Having(conds ...gen.Condition) *insProductDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductDo) Limit(limit int) *insProductDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductDo) Offset(offset int) *insProductDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductDo) Unscoped() *insProductDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductDo) Create(values ...*insbuy.InsProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductDo) CreateInBatches(values []*insbuy.InsProduct, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductDo) Save(values ...*insbuy.InsProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductDo) First() (*insbuy.InsProduct, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProduct), nil
	}
}

func (i insProductDo) Take() (*insbuy.InsProduct, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProduct), nil
	}
}

func (i insProductDo) Last() (*insbuy.InsProduct, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProduct), nil
	}
}

func (i insProductDo) Find() ([]*insbuy.InsProduct, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProduct), err
}

func (i insProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProduct, err error) {
	buf := make([]*insbuy.InsProduct, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductDo) FindInBatches(result *[]*insbuy.InsProduct, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductDo) Attrs(attrs ...field.AssignExpr) *insProductDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductDo) Assign(attrs ...field.AssignExpr) *insProductDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductDo) Joins(fields ...field.RelationField) *insProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductDo) Preload(fields ...field.RelationField) *insProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductDo) FirstOrInit() (*insbuy.InsProduct, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProduct), nil
	}
}

func (i insProductDo) FirstOrCreate() (*insbuy.InsProduct, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProduct), nil
	}
}

func (i insProductDo) FindByPage(offset int, limit int) (result []*insbuy.InsProduct, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductDo) Delete(models ...*insbuy.InsProduct) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductDo) withDO(do gen.Dao) *insProductDo {
	i.DO = *do.(*gen.DO)
	return i
}
