# 自动盘点定时任务

## 功能概述

自动盘点定时任务是一个每日自动执行的后台任务，用于自动完成各店铺的库存盘点操作。该任务会在每天早上9点整执行，自动为所有店铺创建并完成前一营业日的盘点单。

## 功能特性

### 1. 定时执行
- **执行时间**: 每日早上9点整（09:00:00）
- **CRON表达式**: `0 9 * * *`
- **支持配置**: 可在配置文件中启用/禁用或修改执行时间

### 2. 营业日计算
- **计算规则**: 当前时间9点之前算前一天，9点及之后算当天
- **示例**:
  - 2025-7-17 08:59:59 → 营业日为 2025-7-16
  - 2025-7-17 09:00:00 → 营业日为 2025-7-17
- **盘点目标**: 自动盘点昨日营业日的库存数据

### 3. 店铺处理
- **范围**: 处理所有有效店铺（排除总店ID=0）
- **独立性**: 每个店铺独立处理，单个店铺失败不影响其他店铺
- **事务保证**: 每个店铺的盘点操作在独立事务中执行

### 4. 业务逻辑
1. **检查重复**: 检查是否已存在该营业日的盘点单，避免重复创建
2. **创建盘点单**: 自动创建日常盘点单，包含完整的盘点信息
3. **生成明细**: 查询该营业日的库存数据，自动生成盘点明细
4. **完成盘点**: 调用现有的 `FinishCheck` 方法完成盘点操作

## 配置说明

### 生产环境配置 (config.srv.yaml)
```yaml
Timer:
  start: true
  insTask:
    - taskName: "AutoInventoryCheck"
      spec: "0 9 * * *"  # 每天早上9点执行自动盘点
      withSeconds: false
```

### 开发环境配置 (config.yaml)
```yaml
Timer:
  start: true
  insTask:
    # - taskName: "AutoInventoryCheck"
    #   spec: "0 9 * * *"  # 每天早上9点执行自动盘点
    #   withSeconds: false
```
*注意: 开发环境中默认注释掉，避免频繁执行*

## 数据结构

### 盘点单 (InsWarehouseCheckDay)
- **名称**: "自动盘点_YYYY-MM-DD"
- **编号**: "AUTO_{店铺ID}_{YYYYMMDD}"
- **类型**: 日常盘点 (CheckTypeDaily = 1)
- **范围**: 整店盘点 (CheckRangeAll = 1)
- **操作人**: 系统管理员 (ID = 1)

### 盘点明细 (InsWarehouseCheckDayDetail)
- **数据来源**: 该营业日的库存明细表
- **盘点逻辑**: 设置本次盘点数量等于实际库存，差异为0
- **备注**: "系统自动盘点"

## 错误处理

### 1. 日志记录
- 任务开始/结束日志
- 每个店铺的处理结果
- 详细的错误信息

### 2. 容错机制
- 单个店铺失败不影响其他店铺
- 重复执行检查，避免数据重复
- 数据库事务保证数据一致性

### 3. 常见错误
- **数据库连接失败**: 检查数据库配置和连接状态
- **店铺数据异常**: 检查店铺配置和权限
- **库存数据缺失**: 检查该营业日是否有库存记录

## 监控和维护

### 1. 日志查看
```bash
# 查看任务执行日志
tail -f log/$(date +%Y-%m-%d)/server.log | grep "自动盘点"
```

### 2. 手动执行
```go
// 在代码中手动触发
job := &task.InsbuyJob{}
err := job.AutoInventoryCheck()
```

### 3. 数据验证
- 检查盘点单是否正确创建
- 验证盘点明细数据完整性
- 确认盘点状态为已完成

## 注意事项

1. **执行时间**: 确保在营业结束后执行，避免影响正常业务
2. **数据备份**: 建议在执行前进行数据备份
3. **性能影响**: 大量数据时可能影响数据库性能，建议在低峰期执行
4. **权限配置**: 确保系统管理员账户有足够权限执行盘点操作
5. **监控告警**: 建议配置监控告警，及时发现执行异常

## 版本历史

- **v1.0**: 初始版本，支持基本的自动盘点功能
- 创建时间: 2025-07-17
- 作者: Augment Agent
