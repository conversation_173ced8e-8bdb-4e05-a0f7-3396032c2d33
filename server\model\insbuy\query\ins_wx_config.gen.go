// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWxConfig(db *gorm.DB, opts ...gen.DOOption) insWxConfig {
	_insWxConfig := insWxConfig{}

	_insWxConfig.insWxConfigDo.UseDB(db, opts...)
	_insWxConfig.insWxConfigDo.UseModel(&insbuy.InsWxConfig{})

	tableName := _insWxConfig.insWxConfigDo.TableName()
	_insWxConfig.ALL = field.NewAsterisk(tableName)
	_insWxConfig.ID = field.NewUint(tableName, "id")
	_insWxConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_insWxConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWxConfig.StoreId = field.NewUint(tableName, "store_id")
	_insWxConfig.Status = field.NewInt(tableName, "status")
	_insWxConfig.AppID = field.NewString(tableName, "app_id")
	_insWxConfig.AppSecret = field.NewString(tableName, "app_secret")
	_insWxConfig.Token = field.NewString(tableName, "token")
	_insWxConfig.EncodingAESKey = field.NewString(tableName, "encoding_aes_key")

	_insWxConfig.fillFieldMap()

	return _insWxConfig
}

type insWxConfig struct {
	insWxConfigDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	StoreId        field.Uint
	Status         field.Int
	AppID          field.String
	AppSecret      field.String
	Token          field.String
	EncodingAESKey field.String

	fieldMap map[string]field.Expr
}

func (i insWxConfig) Table(newTableName string) *insWxConfig {
	i.insWxConfigDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWxConfig) As(alias string) *insWxConfig {
	i.insWxConfigDo.DO = *(i.insWxConfigDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWxConfig) updateTableName(table string) *insWxConfig {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Status = field.NewInt(table, "status")
	i.AppID = field.NewString(table, "app_id")
	i.AppSecret = field.NewString(table, "app_secret")
	i.Token = field.NewString(table, "token")
	i.EncodingAESKey = field.NewString(table, "encoding_aes_key")

	i.fillFieldMap()

	return i
}

func (i *insWxConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWxConfig) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["status"] = i.Status
	i.fieldMap["app_id"] = i.AppID
	i.fieldMap["app_secret"] = i.AppSecret
	i.fieldMap["token"] = i.Token
	i.fieldMap["encoding_aes_key"] = i.EncodingAESKey
}

func (i insWxConfig) clone(db *gorm.DB) insWxConfig {
	i.insWxConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWxConfig) replaceDB(db *gorm.DB) insWxConfig {
	i.insWxConfigDo.ReplaceDB(db)
	return i
}

type insWxConfigDo struct{ gen.DO }

func (i insWxConfigDo) Debug() *insWxConfigDo {
	return i.withDO(i.DO.Debug())
}

func (i insWxConfigDo) WithContext(ctx context.Context) *insWxConfigDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWxConfigDo) ReadDB() *insWxConfigDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWxConfigDo) WriteDB() *insWxConfigDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWxConfigDo) Session(config *gorm.Session) *insWxConfigDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWxConfigDo) Clauses(conds ...clause.Expression) *insWxConfigDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWxConfigDo) Returning(value interface{}, columns ...string) *insWxConfigDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWxConfigDo) Not(conds ...gen.Condition) *insWxConfigDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWxConfigDo) Or(conds ...gen.Condition) *insWxConfigDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWxConfigDo) Select(conds ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWxConfigDo) Where(conds ...gen.Condition) *insWxConfigDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWxConfigDo) Order(conds ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWxConfigDo) Distinct(cols ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWxConfigDo) Omit(cols ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWxConfigDo) Join(table schema.Tabler, on ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWxConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWxConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWxConfigDo) Group(cols ...field.Expr) *insWxConfigDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWxConfigDo) Having(conds ...gen.Condition) *insWxConfigDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWxConfigDo) Limit(limit int) *insWxConfigDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWxConfigDo) Offset(offset int) *insWxConfigDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWxConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWxConfigDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWxConfigDo) Unscoped() *insWxConfigDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWxConfigDo) Create(values ...*insbuy.InsWxConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWxConfigDo) CreateInBatches(values []*insbuy.InsWxConfig, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWxConfigDo) Save(values ...*insbuy.InsWxConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWxConfigDo) First() (*insbuy.InsWxConfig, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWxConfig), nil
	}
}

func (i insWxConfigDo) Take() (*insbuy.InsWxConfig, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWxConfig), nil
	}
}

func (i insWxConfigDo) Last() (*insbuy.InsWxConfig, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWxConfig), nil
	}
}

func (i insWxConfigDo) Find() ([]*insbuy.InsWxConfig, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWxConfig), err
}

func (i insWxConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWxConfig, err error) {
	buf := make([]*insbuy.InsWxConfig, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWxConfigDo) FindInBatches(result *[]*insbuy.InsWxConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWxConfigDo) Attrs(attrs ...field.AssignExpr) *insWxConfigDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWxConfigDo) Assign(attrs ...field.AssignExpr) *insWxConfigDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWxConfigDo) Joins(fields ...field.RelationField) *insWxConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWxConfigDo) Preload(fields ...field.RelationField) *insWxConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWxConfigDo) FirstOrInit() (*insbuy.InsWxConfig, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWxConfig), nil
	}
}

func (i insWxConfigDo) FirstOrCreate() (*insbuy.InsWxConfig, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWxConfig), nil
	}
}

func (i insWxConfigDo) FindByPage(offset int, limit int) (result []*insbuy.InsWxConfig, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWxConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWxConfigDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWxConfigDo) Delete(models ...*insbuy.InsWxConfig) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWxConfigDo) withDO(do gen.Dao) *insWxConfigDo {
	i.DO = *do.(*gen.DO)
	return i
}
