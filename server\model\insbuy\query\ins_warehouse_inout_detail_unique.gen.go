// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutDetailUnique(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutDetailUnique {
	_insWarehouseInoutDetailUnique := insWarehouseInoutDetailUnique{}

	_insWarehouseInoutDetailUnique.insWarehouseInoutDetailUniqueDo.UseDB(db, opts...)
	_insWarehouseInoutDetailUnique.insWarehouseInoutDetailUniqueDo.UseModel(&insbuy.InsWarehouseInoutDetailUnique{})

	tableName := _insWarehouseInoutDetailUnique.insWarehouseInoutDetailUniqueDo.TableName()
	_insWarehouseInoutDetailUnique.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutDetailUnique.ID = field.NewUint(tableName, "id")
	_insWarehouseInoutDetailUnique.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutDetailUnique.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutDetailUnique.InoutId = field.NewInt(tableName, "inout_id")
	_insWarehouseInoutDetailUnique.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseInoutDetailUnique.UniqueQrCode = field.NewString(tableName, "unique_qr_code")
	_insWarehouseInoutDetailUnique.UniqueSupplierCode = field.NewString(tableName, "unique_supplier_code")
	_insWarehouseInoutDetailUnique.InoutDetailsId = field.NewInt(tableName, "inout_details_id")
	_insWarehouseInoutDetailUnique.SourceType = field.NewInt(tableName, "source_type")
	_insWarehouseInoutDetailUnique.InoutType = field.NewInt(tableName, "inout_type")

	_insWarehouseInoutDetailUnique.fillFieldMap()

	return _insWarehouseInoutDetailUnique
}

type insWarehouseInoutDetailUnique struct {
	insWarehouseInoutDetailUniqueDo

	ALL                field.Asterisk
	ID                 field.Uint
	CreatedAt          field.Time
	UpdatedAt          field.Time
	InoutId            field.Int
	MaterialId         field.Int
	UniqueQrCode       field.String
	UniqueSupplierCode field.String
	InoutDetailsId     field.Int
	SourceType         field.Int
	InoutType          field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutDetailUnique) Table(newTableName string) *insWarehouseInoutDetailUnique {
	i.insWarehouseInoutDetailUniqueDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutDetailUnique) As(alias string) *insWarehouseInoutDetailUnique {
	i.insWarehouseInoutDetailUniqueDo.DO = *(i.insWarehouseInoutDetailUniqueDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutDetailUnique) updateTableName(table string) *insWarehouseInoutDetailUnique {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.InoutId = field.NewInt(table, "inout_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.UniqueQrCode = field.NewString(table, "unique_qr_code")
	i.UniqueSupplierCode = field.NewString(table, "unique_supplier_code")
	i.InoutDetailsId = field.NewInt(table, "inout_details_id")
	i.SourceType = field.NewInt(table, "source_type")
	i.InoutType = field.NewInt(table, "inout_type")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutDetailUnique) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutDetailUnique) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["inout_id"] = i.InoutId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["unique_qr_code"] = i.UniqueQrCode
	i.fieldMap["unique_supplier_code"] = i.UniqueSupplierCode
	i.fieldMap["inout_details_id"] = i.InoutDetailsId
	i.fieldMap["source_type"] = i.SourceType
	i.fieldMap["inout_type"] = i.InoutType
}

func (i insWarehouseInoutDetailUnique) clone(db *gorm.DB) insWarehouseInoutDetailUnique {
	i.insWarehouseInoutDetailUniqueDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutDetailUnique) replaceDB(db *gorm.DB) insWarehouseInoutDetailUnique {
	i.insWarehouseInoutDetailUniqueDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutDetailUniqueDo struct{ gen.DO }

func (i insWarehouseInoutDetailUniqueDo) Debug() *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutDetailUniqueDo) WithContext(ctx context.Context) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutDetailUniqueDo) ReadDB() *insWarehouseInoutDetailUniqueDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutDetailUniqueDo) WriteDB() *insWarehouseInoutDetailUniqueDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutDetailUniqueDo) Session(config *gorm.Session) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutDetailUniqueDo) Clauses(conds ...clause.Expression) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Returning(value interface{}, columns ...string) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutDetailUniqueDo) Not(conds ...gen.Condition) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Or(conds ...gen.Condition) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Select(conds ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Where(conds ...gen.Condition) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Order(conds ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Distinct(cols ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutDetailUniqueDo) Omit(cols ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutDetailUniqueDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutDetailUniqueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutDetailUniqueDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutDetailUniqueDo) Group(cols ...field.Expr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutDetailUniqueDo) Having(conds ...gen.Condition) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutDetailUniqueDo) Limit(limit int) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutDetailUniqueDo) Offset(offset int) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutDetailUniqueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutDetailUniqueDo) Unscoped() *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutDetailUniqueDo) Create(values ...*insbuy.InsWarehouseInoutDetailUnique) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutDetailUniqueDo) CreateInBatches(values []*insbuy.InsWarehouseInoutDetailUnique, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutDetailUniqueDo) Save(values ...*insbuy.InsWarehouseInoutDetailUnique) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutDetailUniqueDo) First() (*insbuy.InsWarehouseInoutDetailUnique, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetailUnique), nil
	}
}

func (i insWarehouseInoutDetailUniqueDo) Take() (*insbuy.InsWarehouseInoutDetailUnique, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetailUnique), nil
	}
}

func (i insWarehouseInoutDetailUniqueDo) Last() (*insbuy.InsWarehouseInoutDetailUnique, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetailUnique), nil
	}
}

func (i insWarehouseInoutDetailUniqueDo) Find() ([]*insbuy.InsWarehouseInoutDetailUnique, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutDetailUnique), err
}

func (i insWarehouseInoutDetailUniqueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutDetailUnique, err error) {
	buf := make([]*insbuy.InsWarehouseInoutDetailUnique, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutDetailUniqueDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutDetailUnique, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutDetailUniqueDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutDetailUniqueDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutDetailUniqueDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutDetailUniqueDo) Joins(fields ...field.RelationField) *insWarehouseInoutDetailUniqueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutDetailUniqueDo) Preload(fields ...field.RelationField) *insWarehouseInoutDetailUniqueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutDetailUniqueDo) FirstOrInit() (*insbuy.InsWarehouseInoutDetailUnique, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetailUnique), nil
	}
}

func (i insWarehouseInoutDetailUniqueDo) FirstOrCreate() (*insbuy.InsWarehouseInoutDetailUnique, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutDetailUnique), nil
	}
}

func (i insWarehouseInoutDetailUniqueDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutDetailUnique, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutDetailUniqueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutDetailUniqueDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutDetailUniqueDo) Delete(models ...*insbuy.InsWarehouseInoutDetailUnique) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutDetailUniqueDo) withDO(do gen.Dao) *insWarehouseInoutDetailUniqueDo {
	i.DO = *do.(*gen.DO)
	return i
}
