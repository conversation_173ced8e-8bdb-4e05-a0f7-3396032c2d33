package test

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport/sqlreport"
	"testing"
)

func TestQuery(t *testing.T) {
	prepare()
	sql := `SELECT * FROM ins_vip_member 
WHERE {created_at between [conditions.start_date,'Y-m-dH:i:s']} 
AND {status in [conditions.status_list,string]} 
AND {user_name like [conditions.user_name,string]}
AND {age = [conditions.age,int]}`
	condition := sqlreport.SearchTgaCondition{
		Conditions: map[string][]string{
			"start_date":  {"2024-01-01", "2024-07-30"},
			"status_list": {""}, //暂时必须传入空值
			"user_name":   {"1"},
			"age":         {""},
		},
	}
	tool, err := sqlreport.NewSqlTool(sql, condition, sqlreport.DtMysql, sqlreport.Local)
	_, err = tool.Query()
	if err != nil {
		t.<PERSON><PERSON>r(err)
	}
}
