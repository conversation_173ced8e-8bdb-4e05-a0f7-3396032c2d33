package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsMaterialApi struct {
}

// CreateInsMaterial 创建原料
// @Tags InsMaterial
// @Summary 创建原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.MaterialReq true "创建原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/createInsMaterial [post]
func (m *InsMaterialApi) CreateInsMaterial(c *gin.Context) {
	var insMaterial insbuyReq.MaterialReq
	err := GinMustBind(c, &insMaterial)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.CreateInsMaterial(c, insMaterial); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsMaterial 删除原料
// @Tags InsMaterial
// @Summary 删除原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsMaterial true "删除原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insMaterial/deleteInsMaterial [delete]
func (m *InsMaterialApi) DeleteInsMaterial(c *gin.Context) {
	var insMaterial insbuy.InsMaterial
	err := c.ShouldBindJSON(&insMaterial)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.DeleteInsMaterial(insMaterial); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// CreateInsSundriesMaterial 创建日杂原料
// @Tags InsMaterial
// @Summary 创建日杂原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SundriesMaterialSaveReq true "创建日杂原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/createInsSundriesMaterial [post]
func (m *InsMaterialApi) CreateInsSundriesMaterial(c *gin.Context) {
	var req insbuyReq.SundriesMaterialSaveReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.CreateInsSundriesMaterial(c, req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateInsSundriesMaterial 更新日杂原料
// @Tags InsMaterial
// @Summary 更新日杂原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SundriesMaterialSaveReq true "更新日杂原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/updateInsSundriesMaterial [put]
func (m *InsMaterialApi) UpdateInsSundriesMaterial(c *gin.Context) {
	var req insbuyReq.SundriesMaterialSaveReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.UpdateInsSundriesMaterial(c, req); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败"+err.Error(), c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// DeleteInsSundriesMaterial 删除日杂原料
// @Tags InsMaterial
// @Summary 删除日杂原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.GetById true "删除日杂原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/deleteInsSundriesMaterial [delete]
func (m *InsMaterialApi) DeleteInsSundriesMaterial(c *gin.Context) {
	var req request.GetById
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.DeleteInsSundriesMaterial(req.Uint()); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsMaterialByIds 批量删除原料
// @Tags InsMaterial
// @Summary 批量删除原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insMaterial/deleteInsMaterialByIds [delete]
func (m *InsMaterialApi) DeleteInsMaterialByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.DeleteInsMaterialByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsMaterial 更新原料
// @Tags InsMaterial
// @Summary 更新原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.MaterialReq true "更新原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insMaterial/updateInsMaterial [put]
func (m *InsMaterialApi) UpdateInsMaterial(c *gin.Context) {
	var insMaterial insbuyReq.MaterialReq
	err := GinMustBind(c, &insMaterial)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"MaterialId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insMaterial, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insMaterialService.UpdateInsMaterial(insMaterial); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsMaterial 用id查询原料
// @Tags InsMaterial
// @Summary 用id查询原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsMaterial true "用id查询原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insMaterial/findInsMaterial [get]
func (m *InsMaterialApi) FindInsMaterial(c *gin.Context) {
	var insMaterial insbuy.InsMaterial
	err := c.ShouldBindQuery(&insMaterial)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsMaterial, err := insMaterialService.GetInsMaterial(insMaterial.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsMaterial": reinsMaterial}, c)
	}
}

// GetInsMaterialList 分页获取原料列表
// @Tags InsMaterial
// @Summary 分页获取原料列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsMaterialSearch true "分页获取原料列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/getInsMaterialList [get]
func (m *InsMaterialApi) GetInsMaterialList(c *gin.Context) {
	var pageInfo insbuyReq.InsMaterialSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insMaterialService.GetInsMaterialInfoList(c, pageInfo); err != nil {
		if pageInfo.IsExport == 1 {
			return
		}
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETMaterial.ToInt(), list)
			if e != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// MaterialWithStockList 分页获取原料列表（支持店铺和仓库库存）
// @Tags InsMaterial
// @Summary 分页获取原料列表（支持店铺和仓库库存）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.MaterialWithStockSearch true "分页获取原料列表（支持店铺和仓库库存）"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/materialWithStockList [get]
func (m *InsMaterialApi) MaterialWithStockList(c *gin.Context) {
	var pageInfo insbuyReq.MaterialWithStockSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insMaterialService.MaterialWithStockList(c, pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// MaterialAuditList 原料审核列表
// @Tags InsMaterial
// @Summary 原料审核列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.MaterialAuditListReq true "原料审核列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/materialAuditList [get]
func (m *InsMaterialApi) MaterialAuditList(c *gin.Context) {
	var req insbuyReq.MaterialAuditListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insMaterialService.MaterialAuditList(c.Request.Context(), req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// MaterialAudit 原料审核
// @Tags InsMaterial
// @Summary 原料审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.MaterialAuditReq true "原料审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/materialAudit [put]
func (m *InsMaterialApi) MaterialAudit(c *gin.Context) {
	var req insbuyReq.MaterialAuditReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insMaterialService.MaterialAudit(c, req)
	response.Err(err, c)
}

// SetMaterialStatus 设置原料状态
// @Tags InsMaterial
// @Summary 设置原料状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SetMaterialStatusReq true "设置原料状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/setMaterialStatus [put]
func (m *InsMaterialApi) SetMaterialStatus(c *gin.Context) {
	var req insbuyReq.SetMaterialStatusReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insMaterialService.SetMaterialStatus(req)
	response.Err(err, c)
}

// CreateInsMaterialCategory 创建原料分类
// @Tags InsMaterialCategory
// @Summary 创建原料分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsMaterialCategory true "创建原料分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/createInsMaterialCategory [post]
func (m *InsMaterialApi) CreateInsMaterialCategory(c *gin.Context) {
	var insMaterialCategory insbuy.InsMaterialCategory
	err := c.ShouldBindJSON(&insMaterialCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.CreateInsMaterialCategory(&insMaterialCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsMaterialCategory 删除原料分类
// @Tags InsMaterialCategory
// @Summary 删除原料分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsMaterialCategory true "删除原料分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insMaterial/deleteInsMaterialCategory [delete]
func (m *InsMaterialApi) DeleteInsMaterialCategory(c *gin.Context) {
	var insMaterialCategory insbuy.InsMaterialCategory
	err := c.ShouldBindJSON(&insMaterialCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.DeleteInsMaterialCategory(insMaterialCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsMaterialCategoryByIds 批量删除原料分类
// @Tags InsMaterialCategory
// @Summary 批量删除原料分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除原料分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insMaterial/deleteInsMaterialCategoryByIds [delete]
func (m *InsMaterialApi) DeleteInsMaterialCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.DeleteInsMaterialCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsMaterialCategory 更新原料分类
// @Tags InsMaterialCategory
// @Summary 更新原料分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsMaterialCategory true "更新原料分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insMaterial/updateInsMaterialCategory [put]
func (m *InsMaterialApi) UpdateInsMaterialCategory(c *gin.Context) {
	var insMaterialCategory insbuy.InsMaterialCategory
	err := c.ShouldBindJSON(&insMaterialCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insMaterialService.UpdateInsMaterialCategory(insMaterialCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsMaterialCategory 用id查询原料分类
// @Tags InsMaterialCategory
// @Summary 用id查询原料分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsMaterialCategory true "用id查询原料分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insMaterial/findInsMaterialCategory [get]
func (m *InsMaterialApi) FindInsMaterialCategory(c *gin.Context) {
	var insMaterialCategory insbuy.InsMaterialCategory
	err := c.ShouldBindQuery(&insMaterialCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsMaterialCategory, err := insMaterialService.GetInsMaterialCategory(insMaterialCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsMaterialCategory": reinsMaterialCategory}, c)
	}
}

// GetInsMaterialCategoryList 分页获取原料分类列表
// @Tags InsMaterialCategory
// @Summary 分页获取原料分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsMaterialCategorySearch true "分页获取原料分类列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insMaterial/getInsMaterialCategoryList [get]
func (m *InsMaterialApi) GetInsMaterialCategoryList(c *gin.Context) {
	var pageInfo insbuyReq.InsMaterialCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insMaterialService.GetInsMaterialCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// MaterialCateTree 获取原料分类树
// @Tags InsMaterial
// @Summary 获取原料分类树
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success   200   {object}  response.Response{data=insbuyResp.CategoryTreeResp,msg=string}  "分页获取InsOrderInfo列表"
// @Router /insMaterial/materialCateTree [get]
func (m *InsMaterialApi) MaterialCateTree(c *gin.Context) {
	list := make([]*insbuyResp.CategoryTreeResp, 0)
	list, err := insMaterialService.MaterialCateTree()
	response.ResultErr(list, err, c)
}

// MaterialPriceHistory 获取原料价格历史
// @Tags InsMaterial
// @Summary 获取原料价格历史
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.MaterialPriceHistoryReq true "获取原料价格历史"
// @Success   200   {object}  response.Response{data=insbuy.InsMaterialHistory,msg=string}  "分页获取InsOrderInfo列表"
// @Router /insMaterial/materialPriceHistory [get]
func (m *InsMaterialApi) MaterialPriceHistory(c *gin.Context) {
	var req insbuyReq.MaterialPriceHistoryReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := insMaterialService.MaterialPriceHistory(c.Request.Context(), req)
	response.ResultErr(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, err, c)
}

// SundriesPriceHistory 获取日杂价格历史
// @Tags InsMaterial
// @Summary 获取日杂价格历史
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.MaterialPriceHistoryReq true "获取耗材价格历史"
// @Success   200   {object}  response.Response{data=insbuy.InsSundriesMaterialHistory,msg=string}  "分页获取InsOrderInfo列表"
// @Router /insMaterial/sundriesPriceHistory [get]
func (m *InsMaterialApi) SundriesPriceHistory(c *gin.Context) {
	var req insbuyReq.MaterialPriceHistoryReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := insMaterialService.SundriesMaterialPriceHistory(c.Request.Context(), req)
	response.ResultErr(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, err, c)
}
