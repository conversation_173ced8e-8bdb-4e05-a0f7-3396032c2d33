[insbuy]2025/07/23 - 13:29:35.508	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 13:29:35.647	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 13:29:43.909	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 13:29:44.027	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 13:29:44.223	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 13:29:44.235	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 13:29:44.241	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 13:38:58.212	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 13:38:58.371	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 13:39:06.615	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 13:39:06.698	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 13:39:06.838	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 13:39:06.854	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 13:39:06.861	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 13:36:04.870	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 13:36:05.032	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 13:36:13.311	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 13:36:13.387	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 13:36:13.509	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 13:36:13.514	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 13:36:13.520	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 13:41:04.044	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 13:41:04.168	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 13:41:12.470	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 13:41:12.596	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 13:41:12.728	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 13:41:12.739	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 13:41:12.746	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 14:06:20.445	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 14:06:20.561	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 14:06:28.858	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 14:06:28.952	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 14:06:29.113	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 14:06:29.123	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 14:06:29.130	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 14:06:35.617	[34minfo[0m	insbuy/insact_bookdesk.go:4571	门票报表查询完成	{"recordCount": 0}
[insbuy]2025/07/23 - 14:07:04.924	[34minfo[0m	insbuy/insact_bookdesk.go:4571	门票报表查询完成	{"recordCount": 0}
[insbuy]2025/07/23 - 14:08:20.414	[34minfo[0m	insbuy/insact_bookdesk.go:4571	门票报表查询完成	{"recordCount": 0}
[insbuy]2025/07/23 - 14:08:41.232	[34minfo[0m	insbuy/insact_bookdesk.go:4571	门票报表查询完成	{"recordCount": 0}
[insbuy]2025/07/23 - 14:09:48.381	[34minfo[0m	insbuy/insact_bookdesk.go:4571	门票报表查询完成	{"recordCount": 0}
[insbuy]2025/07/23 - 14:34:47.455	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 14:34:47.585	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 14:34:55.889	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 14:34:56.003	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 14:34:56.136	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 14:34:56.150	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 14:34:56.155	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 14:35:00.947	[34minfo[0m	insbuy/insact_bookdesk.go:4600	门票报表查询完成	{"recordCount": 1}
[insbuy]2025/07/23 - 14:35:26.910	[34minfo[0m	insbuy/insact_bookdesk.go:4600	门票报表查询完成	{"recordCount": 2}
[insbuy]2025/07/23 - 14:38:56.432	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 14:38:56.555	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 14:39:04.884	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 14:39:04.956	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 14:39:05.106	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 14:39:05.111	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 14:39:05.116	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 14:39:43.103	[34minfo[0m	insbuy/insact_bookdesk.go:4601	门票报表查询完成	{"recordCount": 2}
[insbuy]2025/07/23 - 17:03:36.049	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 17:03:36.240	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 17:03:44.577	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 17:03:44.681	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 17:03:44.928	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 17:03:44.929	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 17:03:44.942	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 17:48:49.789	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 17:48:50.678	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 17:48:59.009	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 17:48:59.142	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 17:48:59.352	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 17:48:59.367	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 17:48:59.373	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 18:33:23.396	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 18:33:23.535	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 18:33:31.890	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 18:33:32.015	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 18:33:32.136	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 18:33:32.149	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 18:33:32.155	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
[insbuy]2025/07/23 - 18:38:03.361	[34minfo[0m	initialize/gorm.go:43	register table struct disabled
[insbuy]2025/07/23 - 18:38:03.474	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/07/23 - 18:38:11.845	[34minfo[0m	utils/check.go:32	register table struct disabled
[insbuy]2025/07/23 - 18:38:11.984	[34minfo[0m	initialize/router.go:67	register swagger handler
[insbuy]2025/07/23 - 18:38:12.096	[34minfo[0m	initialize/router.go:163	router register success
[insbuy]2025/07/23 - 18:38:12.101	[34minfo[0m	core/server.go:40	server run success on 	{"address": ":8888"}
[insbuy]2025/07/23 - 18:38:12.107	[34minfo[0m	core/grpc.go:57	start grpc server on: :8889
