// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseCheckDay(db *gorm.DB, opts ...gen.DOOption) insWarehouseCheckDay {
	_insWarehouseCheckDay := insWarehouseCheckDay{}

	_insWarehouseCheckDay.insWarehouseCheckDayDo.UseDB(db, opts...)
	_insWarehouseCheckDay.insWarehouseCheckDayDo.UseModel(&insbuy.InsWarehouseCheckDay{})

	tableName := _insWarehouseCheckDay.insWarehouseCheckDayDo.TableName()
	_insWarehouseCheckDay.ALL = field.NewAsterisk(tableName)
	_insWarehouseCheckDay.ID = field.NewUint(tableName, "id")
	_insWarehouseCheckDay.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseCheckDay.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseCheckDay.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseCheckDay.Name = field.NewString(tableName, "name")
	_insWarehouseCheckDay.CheckSn = field.NewString(tableName, "check_sn")
	_insWarehouseCheckDay.Day = field.NewTime(tableName, "day")
	_insWarehouseCheckDay.Status = field.NewInt(tableName, "status")
	_insWarehouseCheckDay.StoreId = field.NewUint(tableName, "store_id")
	_insWarehouseCheckDay.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insWarehouseCheckDay.CheckStoreId = field.NewUint(tableName, "check_store_id")
	_insWarehouseCheckDay.CheckType = field.NewInt(tableName, "check_type")
	_insWarehouseCheckDay.CheckRange = field.NewInt(tableName, "check_range")
	_insWarehouseCheckDay.OperatorId = field.NewUint(tableName, "operator_id")
	_insWarehouseCheckDay.Remark = field.NewString(tableName, "remark")
	_insWarehouseCheckDay.Ext = field.NewField(tableName, "ext")

	_insWarehouseCheckDay.fillFieldMap()

	return _insWarehouseCheckDay
}

type insWarehouseCheckDay struct {
	insWarehouseCheckDayDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Name         field.String
	CheckSn      field.String
	Day          field.Time
	Status       field.Int
	StoreId      field.Uint
	WarehouseId  field.Uint
	CheckStoreId field.Uint
	CheckType    field.Int
	CheckRange   field.Int
	OperatorId   field.Uint
	Remark       field.String
	Ext          field.Field

	fieldMap map[string]field.Expr
}

func (i insWarehouseCheckDay) Table(newTableName string) *insWarehouseCheckDay {
	i.insWarehouseCheckDayDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseCheckDay) As(alias string) *insWarehouseCheckDay {
	i.insWarehouseCheckDayDo.DO = *(i.insWarehouseCheckDayDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseCheckDay) updateTableName(table string) *insWarehouseCheckDay {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Name = field.NewString(table, "name")
	i.CheckSn = field.NewString(table, "check_sn")
	i.Day = field.NewTime(table, "day")
	i.Status = field.NewInt(table, "status")
	i.StoreId = field.NewUint(table, "store_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.CheckStoreId = field.NewUint(table, "check_store_id")
	i.CheckType = field.NewInt(table, "check_type")
	i.CheckRange = field.NewInt(table, "check_range")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.Remark = field.NewString(table, "remark")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseCheckDay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseCheckDay) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["check_sn"] = i.CheckSn
	i.fieldMap["day"] = i.Day
	i.fieldMap["status"] = i.Status
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["check_store_id"] = i.CheckStoreId
	i.fieldMap["check_type"] = i.CheckType
	i.fieldMap["check_range"] = i.CheckRange
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["ext"] = i.Ext
}

func (i insWarehouseCheckDay) clone(db *gorm.DB) insWarehouseCheckDay {
	i.insWarehouseCheckDayDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseCheckDay) replaceDB(db *gorm.DB) insWarehouseCheckDay {
	i.insWarehouseCheckDayDo.ReplaceDB(db)
	return i
}

type insWarehouseCheckDayDo struct{ gen.DO }

func (i insWarehouseCheckDayDo) Debug() *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseCheckDayDo) WithContext(ctx context.Context) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseCheckDayDo) ReadDB() *insWarehouseCheckDayDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseCheckDayDo) WriteDB() *insWarehouseCheckDayDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseCheckDayDo) Session(config *gorm.Session) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseCheckDayDo) Clauses(conds ...clause.Expression) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseCheckDayDo) Returning(value interface{}, columns ...string) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseCheckDayDo) Not(conds ...gen.Condition) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseCheckDayDo) Or(conds ...gen.Condition) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseCheckDayDo) Select(conds ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseCheckDayDo) Where(conds ...gen.Condition) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseCheckDayDo) Order(conds ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseCheckDayDo) Distinct(cols ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseCheckDayDo) Omit(cols ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseCheckDayDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseCheckDayDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseCheckDayDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseCheckDayDo) Group(cols ...field.Expr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseCheckDayDo) Having(conds ...gen.Condition) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseCheckDayDo) Limit(limit int) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseCheckDayDo) Offset(offset int) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseCheckDayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseCheckDayDo) Unscoped() *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseCheckDayDo) Create(values ...*insbuy.InsWarehouseCheckDay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseCheckDayDo) CreateInBatches(values []*insbuy.InsWarehouseCheckDay, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseCheckDayDo) Save(values ...*insbuy.InsWarehouseCheckDay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseCheckDayDo) First() (*insbuy.InsWarehouseCheckDay, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDay), nil
	}
}

func (i insWarehouseCheckDayDo) Take() (*insbuy.InsWarehouseCheckDay, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDay), nil
	}
}

func (i insWarehouseCheckDayDo) Last() (*insbuy.InsWarehouseCheckDay, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDay), nil
	}
}

func (i insWarehouseCheckDayDo) Find() ([]*insbuy.InsWarehouseCheckDay, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseCheckDay), err
}

func (i insWarehouseCheckDayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseCheckDay, err error) {
	buf := make([]*insbuy.InsWarehouseCheckDay, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseCheckDayDo) FindInBatches(result *[]*insbuy.InsWarehouseCheckDay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseCheckDayDo) Attrs(attrs ...field.AssignExpr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseCheckDayDo) Assign(attrs ...field.AssignExpr) *insWarehouseCheckDayDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseCheckDayDo) Joins(fields ...field.RelationField) *insWarehouseCheckDayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseCheckDayDo) Preload(fields ...field.RelationField) *insWarehouseCheckDayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseCheckDayDo) FirstOrInit() (*insbuy.InsWarehouseCheckDay, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDay), nil
	}
}

func (i insWarehouseCheckDayDo) FirstOrCreate() (*insbuy.InsWarehouseCheckDay, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseCheckDay), nil
	}
}

func (i insWarehouseCheckDayDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseCheckDay, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseCheckDayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseCheckDayDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseCheckDayDo) Delete(models ...*insbuy.InsWarehouseCheckDay) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseCheckDayDo) withDO(do gen.Dao) *insWarehouseCheckDayDo {
	i.DO = *do.(*gen.DO)
	return i
}
