package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsVipMemberApi struct {
}

var (
	insVipMemberVerify = utils.Rules{
		"StoreId":  {utils.NotEmpty()},
		"Phone":    {utils.NotEmpty()},
		"UserName": {utils.NotEmpty()},
	}
)

// CreateInsVipMember 创建InsVipMember
// @Tags InsVipMember
// @Summary 创建InsVipMember
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsVipMember true "创建InsVipMember"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insVipMember/createInsVipMember [post]
func (insVipMemberApi *InsVipMemberApi) CreateInsVipMember(c *gin.Context) {
	var insVipMember insbuy.InsVipMember
	err := c.ShouldBindJSON(&insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	insVipMember.StoreId = utils.GetHeaderStoreIdUint(c)
	//验证邮箱地址和手机号格式是否正确
	if insVipMember.Email != "" {
		insVipMemberVerify["Email"] = []string{utils.IsEmail()}
	}
	if insVipMember.Phone != "" {
		insVipMemberVerify["Phone"] = []string{utils.IsMobile()}
	}
	if err = utils.Verify(insVipMember, insVipMemberVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if vipMemberId, err := insVipMemberService.CreateInsVipMember(c, &insVipMember); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithData(gin.H{"vipMemberId": vipMemberId}, c)
	}
}

// DeleteInsVipMember 删除InsVipMember
// @Tags InsVipMember
// @Summary 删除InsVipMember
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsVipMember true "删除InsVipMember"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insVipMember/deleteInsVipMember [delete]
func (insVipMemberApi *InsVipMemberApi) DeleteInsVipMember(c *gin.Context) {
	var insVipMember insbuy.InsVipMember
	err := c.ShouldBindJSON(&insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insVipMemberService.DeleteInsVipMember(insVipMember); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsVipMemberByIds 批量删除InsVipMember
// @Tags InsVipMember
// @Summary 批量删除InsVipMember
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsVipMember"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insVipMember/deleteInsVipMemberByIds [delete]
func (insVipMemberApi *InsVipMemberApi) DeleteInsVipMemberByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insVipMemberService.DeleteInsVipMemberByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsVipMember 更新InsVipMember
// @Tags InsVipMember
// @Summary 更新InsVipMember
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsVipMember true "更新InsVipMember"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insVipMember/updateInsVipMember [put]
func (insVipMemberApi *InsVipMemberApi) UpdateInsVipMember(c *gin.Context) {
	var insVipMember insbuy.InsVipMember
	err := c.ShouldBindJSON(&insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	//验证邮箱地址和手机号格式是否正确
	if insVipMember.Email != "" {
		insVipMemberVerify["Email"] = []string{utils.IsEmail()}
	}
	if insVipMember.Phone != "" {
		insVipMemberVerify["Phone"] = []string{utils.IsMobile()}
	}
	if err = utils.Verify(insVipMember, insVipMemberVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insVipMemberService.UpdateInsVipMember(insVipMember); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsVipMember 用id查询InsVipMember
// @Tags InsVipMember
// @Summary 用id查询InsVipMember
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsVipMember true "用id查询InsVipMember"
// @Success 200 {object}  response.Response{data=insbuyResp.GetInsVipMemberResp,msg=string}  "用id查询InsVipMember"
// @Router /insVipMember/findInsVipMember [get]
func (insVipMemberApi *InsVipMemberApi) FindInsVipMember(c *gin.Context) {
	var insVipMember insbuy.InsVipMember
	err := c.ShouldBindQuery(&insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsVipMember, err := insVipMemberService.GetInsVipMember(insVipMember.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsVipMember": reinsVipMember}, c)
	}
}

// FindHideVipMember 用id查询会员信息含脱敏
// @Tags InsVipMember
// @Summary 用id查询会员信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsVipMemberReq true "用id查询会员信息含脱敏"
// @Success 200 {object}  response.Response{data=insbuyResp.GetInsVipMemberResp,msg=string}  "用id查询InsVipMember"
// @Router /insVipMember/findHideVipMember [get]
func (insVipMemberApi *InsVipMemberApi) FindHideVipMember(c *gin.Context) {
	var insVipMember insbuyReq.FindInsVipMemberReq
	err := c.ShouldBindQuery(&insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsVipMember, err := insVipMemberService.GetHideVipMember(insVipMember.Id); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsVipMember": reinsVipMember}, c)
	}
}

// GetInsVipMemberList 分页获取InsVipMember列表
// @Tags InsVipMember
// @Summary 分页获取InsVipMember列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberSearch true "分页获取InsVipMember列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insVipMember/getInsVipMemberList [get]
func (insVipMemberApi *InsVipMemberApi) GetInsVipMemberList(c *gin.Context) {
	var pageInfo insbuyReq.InsVipMemberSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insVipMemberService.GetInsVipMemberInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ClientMemberRecharge 会员储值下单
// @Tags InsVipMember
// @Summary 会员储值下单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsVipMemberRechargeReq true "会员储值下单"
// @Success 200 {object}  response.Response{data=insbuyResp.ClientMemberRechargeResp,msg=string}  "会员储值下单"
// @Router /insVipMember/clientMemberRecharge [post]
func (insVipMemberApi *InsVipMemberApi) ClientMemberRecharge(c *gin.Context) {
	var req insbuyReq.InsVipMemberRechargeReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp := insbuyResp.ClientMemberRechargeResp{}
	resp, err := insVipMemberService.ClientMemberRecharge(req)
	if err != nil {
		global.GVA_LOG.Error("充值失败!", zap.Error(err))
		response.FailWithMessage("充值失败:"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GetRechargeLogList 分页获取充值日志
// @Tags InsVipMember
// @Summary 分页获取充值日志
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsRechargeLogSearchReq true "分页获取充值日志"
// @Success   200   {object}  response.Response{data=insbuyResp.RechargeLogItemResp,msg=string}  "分页获取充值日志"
// @Router /insVipMember/getRechargeLogList [get]
func (insVipMemberApi *InsVipMemberApi) GetRechargeLogList(c *gin.Context) {
	var pageInfo insbuyReq.InsRechargeLogSearchReq
	if err := GinMustBind(c, &pageInfo); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if list, total, err := insVipMemberService.GetRechargeLogList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// VerifyBySmsSend 通过短信验证会员账号
// @Tags InsVipMember
// @Summary 通过短信验证会员账号
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberVerifyBySmsSendReq true "通过短信验证会员账号"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberVerifyBySmsSendResp} "通过短信验证会员账号"
// @Router /insVipMember/verifyBySmsSend [post]
func (A *InsVipMemberApi) VerifyBySmsSend(c *gin.Context) {
	var req insbuyReq.InsVipMemberVerifyBySmsSendReq
	var resp *insbuyResp.InsVipMemberVerifyBySmsSendResp
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insVipMemberService.VerifyBySmsSend(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// VerifyBySms 校验短信验证码
// @Tags InsVipMember
// @Summary 校验短信验证码
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberVerifyBySmsReq true "校验短信验证码"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberVerifyBySmsResp} "校验短信验证码"
// @Router /insVipMember/verifyBySms [post]
func (A *InsVipMemberApi) VerifyBySms(c *gin.Context) {
	var req insbuyReq.InsVipMemberVerifyBySmsReq
	var resp *insbuyResp.InsVipMemberVerifyBySmsResp
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insVipMemberService.VerifyBySms(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// GetVipBalance 获取会员余额
// @Tags InsVipMember
// @Summary 获取会员余额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberBalanceReq true "获取会员余额"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberBalanceResp} "获取会员余额"
// @Router /insVipMember/getVipBalance [get]
func (A *InsVipMemberApi) GetVipBalance(c *gin.Context) {
	var req insbuyReq.InsVipMemberBalanceReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insVipMemberService.GetVipBalance(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// GetVipMemberStatisticsList 获取会员统计列表
// @Tags InsVipMember
// @Summary 获取会员统计列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberStatisticsListReq true "获取会员统计列表"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberStatisticsListResp} "获取会员统计列表"
// @Router /insVipMember/getVipMemberStatisticsList [get]
func (A *InsVipMemberApi) GetVipMemberStatisticsList(c *gin.Context) {
	var req insbuyReq.InsVipMemberStatisticsListReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	list, total, err := insVipMemberService.GetVipMemberStatisticsList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetVipMemberConsumeList 获取会员消费列表
// @Tags InsVipMember
// @Summary 获取会员消费列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberConsumeListReq true "获取会员消费列表"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberConsumeListResp} "获取会员消费列表"
// @Router /insVipMember/getVipMemberConsumeList [get]
func (A *InsVipMemberApi) GetVipMemberConsumeList(c *gin.Context) {
	var req insbuyReq.InsVipMemberConsumeListReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	list, total, err := insVipMemberService.GetVipMemberConsumeList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.Export() {
			_, err = insImportService.ExcelCommonList(c, insbuy.ETMemberCardConsumed.ToInt(), list)
			if err != nil {
				response.FailWithMessage("导出失败", c)
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetVipMemberRechargeList 获取会员充值列表
// @Tags InsVipMember
// @Summary 获取会员充值列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberRechargeListReq true "获取会员充值列表"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberRechargeListResp} "获取会员充值列表"
// @Router /insVipMember/getVipMemberRechargeList [get]
func (A *InsVipMemberApi) GetVipMemberRechargeList(c *gin.Context) {
	var req insbuyReq.InsVipMemberRechargeListReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	list, total, err := insVipMemberService.GetVipMemberRechargeList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.Export() {
			_, err = insImportService.ExcelCommonList(c, insbuy.ETMemberCardRecharge.ToInt(), list)
			if err != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// RegisterBySmsSend 通过手机号注册会员卡
// @Tags InsVipMember
// @Summary 通过手机号注册会员卡
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsVipMemberRegisterBySmsSendReq true "通过短信验证会员账号"
// @Success 200 {object}  response.Response{data=insbuyResp.InsVipMemberVerifyBySmsSendResp} "通过短信验证会员账号"
// @Router /insVipMember/registerBySms [post]
func (A *InsVipMemberApi) RegisterBySmsSend(c *gin.Context) {
	var req insbuyReq.InsVipMemberRegisterBySmsSendReq
	var resp *insbuyResp.InsVipMemberVerifyBySmsSendResp
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insVipMemberService.RegisterBySmsSend(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// RegisterVipMember 通过手机号或服务码创建会员
// @Tags InsVipMember
// @Summary 通过手机号或服务码创建会员
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.RegisterInsVipMemberReq true "会员开卡"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"会员开卡成功"}"
// @Router /insVipMember/registerVipMember [post]
func (v *InsVipMemberApi) RegisterVipMember(c *gin.Context) {
	var insVipMember insbuyReq.RegisterInsVipMemberReq
	err := GinMustBind(c, &insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if insVipMember.Source == insbuy.VIPSourcePhone {
		insVipMemberVerify["Phone"] = []string{utils.IsMobile()}
	}
	if err = utils.Verify(insVipMember, insVipMemberVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if vipMemberId, err := insVipMemberService.RegisterVipMember(insVipMember); err != nil {
		global.GVA_LOG.Error("会员开卡失败!", zap.Error(err))
		response.FailWithMessage("会员开卡失败"+err.Error(), c)
	} else {
		response.OkWithData(gin.H{"vipMemberId": vipMemberId}, c)
	}
}

// LoginVipMember 通过手机号或服务码登录
// @Tags InsVipMember
// @Summary 通过手机号或服务码登录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.LoginInsVipMemberReq true "会员登录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"会员登录成功"}"
// @Router /insVipMember/loginVipMember [post]
func (v *InsVipMemberApi) LoginVipMember(c *gin.Context) {
	var insVipMember insbuyReq.LoginInsVipMemberReq
	err := GinMustBind(c, &insVipMember)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if insVipMember.Source == insbuy.VIPSourcePhone {
		insVipMemberVerify["Phone"] = []string{utils.IsMobile()}
	}
	if err = utils.Verify(insVipMember, insVipMemberVerify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insVipMemberService.LoginVipMember(insVipMember); err != nil {
		global.GVA_LOG.Error("会员登录失败", zap.Error(err))
		response.FailWithMessage("会员登录失败"+err.Error(), c)
	} else {
		response.ResultErr(resp, err, c)
	}
}
