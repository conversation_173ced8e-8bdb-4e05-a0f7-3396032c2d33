// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportList(db *gorm.DB, opts ...gen.DOOption) insReportList {
	_insReportList := insReportList{}

	_insReportList.insReportListDo.UseDB(db, opts...)
	_insReportList.insReportListDo.UseModel(&insbuy.InsReportList{})

	tableName := _insReportList.insReportListDo.TableName()
	_insReportList.ALL = field.NewAsterisk(tableName)
	_insReportList.ID = field.NewUint(tableName, "id")
	_insReportList.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportList.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportList.StoreId = field.NewUint(tableName, "store_id")
	_insReportList.CategoryID = field.NewUint(tableName, "category_id")
	_insReportList.Name = field.NewString(tableName, "name")
	_insReportList.Icon = field.NewString(tableName, "icon")
	_insReportList.Desc = field.NewString(tableName, "desc")
	_insReportList.Ext = field.NewString(tableName, "ext")
	_insReportList.Sort = field.NewInt(tableName, "sort")

	_insReportList.fillFieldMap()

	return _insReportList
}

type insReportList struct {
	insReportListDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	StoreId    field.Uint
	CategoryID field.Uint
	Name       field.String
	Icon       field.String
	Desc       field.String
	Ext        field.String
	Sort       field.Int

	fieldMap map[string]field.Expr
}

func (i insReportList) Table(newTableName string) *insReportList {
	i.insReportListDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportList) As(alias string) *insReportList {
	i.insReportListDo.DO = *(i.insReportListDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportList) updateTableName(table string) *insReportList {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.CategoryID = field.NewUint(table, "category_id")
	i.Name = field.NewString(table, "name")
	i.Icon = field.NewString(table, "icon")
	i.Desc = field.NewString(table, "desc")
	i.Ext = field.NewString(table, "ext")
	i.Sort = field.NewInt(table, "sort")

	i.fillFieldMap()

	return i
}

func (i *insReportList) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportList) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["category_id"] = i.CategoryID
	i.fieldMap["name"] = i.Name
	i.fieldMap["icon"] = i.Icon
	i.fieldMap["desc"] = i.Desc
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["sort"] = i.Sort
}

func (i insReportList) clone(db *gorm.DB) insReportList {
	i.insReportListDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportList) replaceDB(db *gorm.DB) insReportList {
	i.insReportListDo.ReplaceDB(db)
	return i
}

type insReportListDo struct{ gen.DO }

func (i insReportListDo) Debug() *insReportListDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportListDo) WithContext(ctx context.Context) *insReportListDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportListDo) ReadDB() *insReportListDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportListDo) WriteDB() *insReportListDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportListDo) Session(config *gorm.Session) *insReportListDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportListDo) Clauses(conds ...clause.Expression) *insReportListDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportListDo) Returning(value interface{}, columns ...string) *insReportListDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportListDo) Not(conds ...gen.Condition) *insReportListDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportListDo) Or(conds ...gen.Condition) *insReportListDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportListDo) Select(conds ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportListDo) Where(conds ...gen.Condition) *insReportListDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportListDo) Order(conds ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportListDo) Distinct(cols ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportListDo) Omit(cols ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportListDo) Join(table schema.Tabler, on ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportListDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportListDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportListDo) Group(cols ...field.Expr) *insReportListDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportListDo) Having(conds ...gen.Condition) *insReportListDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportListDo) Limit(limit int) *insReportListDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportListDo) Offset(offset int) *insReportListDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportListDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportListDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportListDo) Unscoped() *insReportListDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportListDo) Create(values ...*insbuy.InsReportList) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportListDo) CreateInBatches(values []*insbuy.InsReportList, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportListDo) Save(values ...*insbuy.InsReportList) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportListDo) First() (*insbuy.InsReportList, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportList), nil
	}
}

func (i insReportListDo) Take() (*insbuy.InsReportList, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportList), nil
	}
}

func (i insReportListDo) Last() (*insbuy.InsReportList, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportList), nil
	}
}

func (i insReportListDo) Find() ([]*insbuy.InsReportList, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportList), err
}

func (i insReportListDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportList, err error) {
	buf := make([]*insbuy.InsReportList, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportListDo) FindInBatches(result *[]*insbuy.InsReportList, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportListDo) Attrs(attrs ...field.AssignExpr) *insReportListDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportListDo) Assign(attrs ...field.AssignExpr) *insReportListDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportListDo) Joins(fields ...field.RelationField) *insReportListDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportListDo) Preload(fields ...field.RelationField) *insReportListDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportListDo) FirstOrInit() (*insbuy.InsReportList, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportList), nil
	}
}

func (i insReportListDo) FirstOrCreate() (*insbuy.InsReportList, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportList), nil
	}
}

func (i insReportListDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportList, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportListDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportListDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportListDo) Delete(models ...*insbuy.InsReportList) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportListDo) withDO(do gen.Dao) *insReportListDo {
	i.DO = *do.(*gen.DO)
	return i
}
