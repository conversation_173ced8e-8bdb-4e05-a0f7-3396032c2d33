package insbuy

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseInApi struct {
}

// StartIn 发起入库
// @Tags InsWarehouseIn
// @Summary 发起入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartInReq true "发起入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发起入库成功"}"
// @Router /insWarehouse/startIn [post]
func (InsWarehouseInApi *InsWarehouseInApi) StartIn(c *gin.Context) {
	var req insbuyReq.StartInReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseInService.StartIn(req); err != nil {
		global.GVA_LOG.Error("发起入库失败!", zap.Error(err))
		response.FailWithMessage("发起入库失败"+err.Error(), c)
	} else {
		response.OkWithMessage("发起入库成功", c)
	}
}

// ConfirmReturn 确认入库
// @Tags InsWarehouseIn
// @Summary 确认入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfirmInWarehouseReq true "确认入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"确认入库成功"}"
// @Router /insWarehouse/confirmReturn [post]
func (InsWarehouseInApi *InsWarehouseInApi) ConfirmReturn(c *gin.Context) {
	var req insbuyReq.ConfirmInWarehouseReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseInService.ConfirmReturn(req); err != nil {
		global.GVA_LOG.Error("确认入库失败!", zap.Error(err))
		response.FailWithMessage("确认入库失败"+err.Error(), c)
	} else {
		response.OkWithMessage("确认入库成功", c)
	}
}

// GetDepositInventoryList 获取存酒库库存列表
// @Tags InsWarehouseIn
// @Summary 获取存酒库库存列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DepositInventoryListReq true "获取存酒库库存列表"
// @Success 200 {object} response.Response{data=insbuyResp.DepositInventoryItem,msg=string} "获取存酒库库存列表"
// @Router /insWarehouse/getDepositInventoryList [get]
func (InsWarehouseInApi *InsWarehouseInApi) GetDepositInventoryList(c *gin.Context) {
	var req insbuyReq.DepositInventoryListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	var list []insbuyResp.DepositInventoryItem
	var total int64
	if list, total, err = insWarehouseInService.GetDepositInventoryList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

func (InsWarehouseInApi *InsWarehouseInApi) TestR(c *gin.Context) {
	insWarehouseInService.TestR(c)
}
func (InsWarehouseInApi *InsWarehouseInApi) TestR2(c *gin.Context) {
	var req insbuyReq.TestR2
	var req2 insbuyReq.TestR3
	_ = GinMustBind(c, &req)
	_ = GinMustBind(c, &req2)
	fmt.Println(req2)
	insWarehouseInService.TestR2(req, req2)
}
