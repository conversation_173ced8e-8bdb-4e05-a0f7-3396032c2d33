package global

import (
	"context"
	"encoding/hex"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jrand"
	"go.uber.org/zap"
)

// 日志调试
//
// jason.liao 2024.01.31

type __logger4task struct{}
type __logger4trace struct{}
type __logger4attach struct{}

// LogAttachCtx 附加日志到 context
func LogAttachCtx(ctx context.Context, logger *zap.Logger, fields ...zap.Field) context.Context {
	if len(fields) > 0 {
		logger = logger.With(fields...)
	}
	return context.WithValue(ctx, __logger4attach{}, logger)
}

// LogSetTraceId 附加 traceId 到 context
func LogSetTraceId(ctx context.Context, traceId string) context.Context {
	return context.WithValue(ctx, __logger4trace{}, traceId)
}

// LogByCtx 从 context 中获取 日志器
func LogByCtx(ctx context.Context) *zap.Logger {
	logger, _ := ctx.Value(__logger4attach{}).(*zap.Logger)
	if logger == nil {
		logger = GVA_LOG
	}
	return logger
}

// Log4Task 用于任务的日志器，会自动附加 traceId 和 taskName
func Log4Task(ctx context.Context, taskName string, fields ...zap.Field) (context.Context, *zap.Logger) {
	lastTrace, _ := ctx.Value(__logger4trace{}).(string)
	lastLogger, _ := ctx.Value(__logger4attach{}).(*zap.Logger)
	lastName, _ := ctx.Value(__logger4task{}).(string)

	namepath := lastName
	if taskName != "" {
		if namepath != "" {
			namepath = namepath + "/" + taskName
		} else {
			namepath = taskName
		}
	}
	traceId := lastTrace
	if traceId == "" {
		traceId = hex.EncodeToString(jrand.RandomBytes(16))
		ctx = context.WithValue(ctx, __logger4trace{}, traceId)
	}
	if lastLogger == nil {
		lastLogger = GVA_LOG
	}
	logger := lastLogger.With(zap.String("traceId", traceId), zap.String("task", namepath))
	if len(fields) > 0 {
		logger = logger.With(fields...)
	}
	if namepath != "" {
		//logger = logger.Named(taskName)
		ctx = context.WithValue(ctx, __logger4task{}, namepath)
	}
	ctx = context.WithValue(ctx, __logger4attach{}, logger)
	return ctx, logger
}
