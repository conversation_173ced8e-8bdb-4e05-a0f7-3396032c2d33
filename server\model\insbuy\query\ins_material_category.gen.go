// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterialCategory(db *gorm.DB, opts ...gen.DOOption) insMaterialCategory {
	_insMaterialCategory := insMaterialCategory{}

	_insMaterialCategory.insMaterialCategoryDo.UseDB(db, opts...)
	_insMaterialCategory.insMaterialCategoryDo.UseModel(&insbuy.InsMaterialCategory{})

	tableName := _insMaterialCategory.insMaterialCategoryDo.TableName()
	_insMaterialCategory.ALL = field.NewAsterisk(tableName)
	_insMaterialCategory.ID = field.NewUint(tableName, "id")
	_insMaterialCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterialCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterialCategory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insMaterialCategory.CatName = field.NewString(tableName, "cat_name")
	_insMaterialCategory.CatDesc = field.NewString(tableName, "cat_desc")
	_insMaterialCategory.ParentId = field.NewInt(tableName, "parent_id")
	_insMaterialCategory.SortOrder = field.NewInt(tableName, "sort_order")
	_insMaterialCategory.IsShow = field.NewInt(tableName, "is_show")

	_insMaterialCategory.fillFieldMap()

	return _insMaterialCategory
}

type insMaterialCategory struct {
	insMaterialCategoryDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	CatName   field.String
	CatDesc   field.String
	ParentId  field.Int
	SortOrder field.Int
	IsShow    field.Int

	fieldMap map[string]field.Expr
}

func (i insMaterialCategory) Table(newTableName string) *insMaterialCategory {
	i.insMaterialCategoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterialCategory) As(alias string) *insMaterialCategory {
	i.insMaterialCategoryDo.DO = *(i.insMaterialCategoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterialCategory) updateTableName(table string) *insMaterialCategory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CatName = field.NewString(table, "cat_name")
	i.CatDesc = field.NewString(table, "cat_desc")
	i.ParentId = field.NewInt(table, "parent_id")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.IsShow = field.NewInt(table, "is_show")

	i.fillFieldMap()

	return i
}

func (i *insMaterialCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterialCategory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["cat_name"] = i.CatName
	i.fieldMap["cat_desc"] = i.CatDesc
	i.fieldMap["parent_id"] = i.ParentId
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["is_show"] = i.IsShow
}

func (i insMaterialCategory) clone(db *gorm.DB) insMaterialCategory {
	i.insMaterialCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterialCategory) replaceDB(db *gorm.DB) insMaterialCategory {
	i.insMaterialCategoryDo.ReplaceDB(db)
	return i
}

type insMaterialCategoryDo struct{ gen.DO }

func (i insMaterialCategoryDo) Debug() *insMaterialCategoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialCategoryDo) WithContext(ctx context.Context) *insMaterialCategoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialCategoryDo) ReadDB() *insMaterialCategoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialCategoryDo) WriteDB() *insMaterialCategoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialCategoryDo) Session(config *gorm.Session) *insMaterialCategoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialCategoryDo) Clauses(conds ...clause.Expression) *insMaterialCategoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialCategoryDo) Returning(value interface{}, columns ...string) *insMaterialCategoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialCategoryDo) Not(conds ...gen.Condition) *insMaterialCategoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialCategoryDo) Or(conds ...gen.Condition) *insMaterialCategoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialCategoryDo) Select(conds ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialCategoryDo) Where(conds ...gen.Condition) *insMaterialCategoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialCategoryDo) Order(conds ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialCategoryDo) Distinct(cols ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialCategoryDo) Omit(cols ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialCategoryDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialCategoryDo) Group(cols ...field.Expr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialCategoryDo) Having(conds ...gen.Condition) *insMaterialCategoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialCategoryDo) Limit(limit int) *insMaterialCategoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialCategoryDo) Offset(offset int) *insMaterialCategoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialCategoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialCategoryDo) Unscoped() *insMaterialCategoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialCategoryDo) Create(values ...*insbuy.InsMaterialCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialCategoryDo) CreateInBatches(values []*insbuy.InsMaterialCategory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialCategoryDo) Save(values ...*insbuy.InsMaterialCategory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialCategoryDo) First() (*insbuy.InsMaterialCategory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialCategory), nil
	}
}

func (i insMaterialCategoryDo) Take() (*insbuy.InsMaterialCategory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialCategory), nil
	}
}

func (i insMaterialCategoryDo) Last() (*insbuy.InsMaterialCategory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialCategory), nil
	}
}

func (i insMaterialCategoryDo) Find() ([]*insbuy.InsMaterialCategory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterialCategory), err
}

func (i insMaterialCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterialCategory, err error) {
	buf := make([]*insbuy.InsMaterialCategory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialCategoryDo) FindInBatches(result *[]*insbuy.InsMaterialCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialCategoryDo) Attrs(attrs ...field.AssignExpr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialCategoryDo) Assign(attrs ...field.AssignExpr) *insMaterialCategoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialCategoryDo) Joins(fields ...field.RelationField) *insMaterialCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialCategoryDo) Preload(fields ...field.RelationField) *insMaterialCategoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialCategoryDo) FirstOrInit() (*insbuy.InsMaterialCategory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialCategory), nil
	}
}

func (i insMaterialCategoryDo) FirstOrCreate() (*insbuy.InsMaterialCategory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialCategory), nil
	}
}

func (i insMaterialCategoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterialCategory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialCategoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialCategoryDo) Delete(models ...*insbuy.InsMaterialCategory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialCategoryDo) withDO(do gen.Dao) *insMaterialCategoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
