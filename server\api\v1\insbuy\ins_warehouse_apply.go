package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseApplyApi struct {
}

// GetApplyList 获取申购列表
// @Tags InsWarehouseApply
// @Summary 获取申购列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyListReq true "获取申购列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/getApplyList [get]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) GetApplyList(c *gin.Context) {
	var req insbuyReq.ApplyListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insWarehouseApplyService.ApplyList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExportApplyListDetails 获取申购列表导出
// @Tags InsWarehouseApply
// @Summary 获取申购列表导出
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyListReq true "获取申购列表导出"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insWarehouse/exportApplyListDetails [get]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) ExportApplyListDetails(c *gin.Context) {
	var req insbuyReq.ApplyListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehouseApplyService.ExportApplyListDetails(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.IsExport == 1 {
			return
		}
		response.OkWithData(list, c)
	}
}

// CreateApply 创建申购
// @Tags InsWarehouseApply
// @Summary 创建申购
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateApplyReq true "创建申购"
// @Success   200   {object}  response.Response{data=insbuyResp.CreateInsWarehouseInout,msg=string}  "查询成功"
// @Router /insWarehouse/createApply [post]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) CreateApply(c *gin.Context) {
	var req insbuyReq.CreateApplyReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp := insbuyResp.CreateInsWarehouseInout{}
	resp, err = insWarehouseApplyService.CreateApply(req)
	response.ResultErr(resp, err, c)
}

// FindApply 查找申购详情
// @Tags InsWarehouseApply
// @Summary 查找申购详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.ApplyDetailReq true "查找申购详情"
// @Success   200   {object}  response.Response{data=insbuyResp.FindApplyResp,msg=string}  "查询成功"
// @Router /insWarehouse/findApply [get]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) FindApply(c *gin.Context) {
	var req insbuyReq.ApplyDetailReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insWarehouseApplyService.FindApply(req); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// DeleteApply 删除申购单
// @Tags InsWarehouseApply
// @Summary 删除申购单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DeleteInsWarehouseInoutReq true "删除申购单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insWarehouse/deleteApply [delete]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) DeleteApply(c *gin.Context) {
	var req insbuyReq.DeleteInsWarehouseInoutReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insWarehouseApplyService.DeleteApply(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// UpdateApply 申购单修改
// @Tags InsWarehouseApply
// @Summary 申购单修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateApplyReq true "申购单修改"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insWarehouse/updateApply [put]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) UpdateApply(c *gin.Context) {
	var req insbuyReq.UpdateApplyReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insWarehouseApplyService.UpdateApply(req); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// StartApply 发起申请
// @Tags InsWarehouseApply
// @Summary 发起申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartApplyReq true "发起申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发起申请成功"}"
// @Router /insWarehouse/startApply [post]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) StartApply(c *gin.Context) {
	var req insbuyReq.StartApplyReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseApplyService.StartApply(req); err != nil {
		global.GVA_LOG.Error("发起申请失败!", zap.Error(err))
		response.FailWithMessage("发起申请失败"+err.Error(), c)
	} else {
		response.OkWithMessage("发起申请成功", c)
	}
}

// ApplyPass 通过申请
// @Tags InsWarehouseApply
// @Summary 通过申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ApplyPassReq true "通过申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"通过申请成功"}"
// @Router /insWarehouse/applyPass [put]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) ApplyPass(c *gin.Context) {
	var req insbuyReq.ApplyPassReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if _, err = insWarehouseApplyService.ApplyPass(req); err != nil {
		global.GVA_LOG.Error("通过申请失败!", zap.Error(err))
		response.FailWithMessage("通过申请失败"+err.Error(), c)
	} else {
		response.OkWithMessage("通过申请成功", c)
	}
}

// ApplyReject 驳回申请
// @Tags InsWarehouseApply
// @Summary 驳回申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ApplyRejectReq true "驳回申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"驳回申请成功"}"
// @Router /insWarehouse/applyReject [put]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) ApplyReject(c *gin.Context) {
	var req insbuyReq.ApplyRejectReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if _, err = insWarehouseApplyService.ApplyReject(req); err != nil {
		global.GVA_LOG.Error("驳回申请失败!", zap.Error(err))
		response.FailWithMessage("驳回申请失败"+err.Error(), c)
	} else {
		response.OkWithMessage("驳回申请成功", c)
	}
}

// ApplyInoutAgain 重新生成申请单
// @Tags InsWarehouseApply
// @Summary 重新生成申请单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ApplyInoutAgainReq true "重新生成申请单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"重新生成申请单成功"}"
// @Router /insWarehouse/applyInoutAgain [post]
func (InsWarehouseApplyApi *InsWarehouseApplyApi) ApplyInoutAgain(c *gin.Context) {
	var req insbuyReq.ApplyInoutAgainReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseApplyService.ApplyInoutAgain(req); err != nil {
		global.GVA_LOG.Error("重新生成申请单失败!", zap.Error(err))
		response.FailWithMessage("重新生成申请单失败"+err.Error(), c)
	} else {
		response.OkWithMessage("重新生成申请单成功", c)
	}
}
