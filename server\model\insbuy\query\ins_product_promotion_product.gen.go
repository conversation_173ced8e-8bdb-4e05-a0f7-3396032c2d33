// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotionProduct(db *gorm.DB, opts ...gen.DOOption) insProductPromotionProduct {
	_insProductPromotionProduct := insProductPromotionProduct{}

	_insProductPromotionProduct.insProductPromotionProductDo.UseDB(db, opts...)
	_insProductPromotionProduct.insProductPromotionProductDo.UseModel(&insbuy.InsProductPromotionProduct{})

	tableName := _insProductPromotionProduct.insProductPromotionProductDo.TableName()
	_insProductPromotionProduct.ALL = field.NewAsterisk(tableName)
	_insProductPromotionProduct.ID = field.NewUint(tableName, "id")
	_insProductPromotionProduct.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotionProduct.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotionProduct.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotionProduct.PromotionId = field.NewUint(tableName, "promotion_id")
	_insProductPromotionProduct.ProductId = field.NewUint(tableName, "product_id")
	_insProductPromotionProduct.DiscountRate = field.NewFloat64(tableName, "discount_rate")

	_insProductPromotionProduct.fillFieldMap()

	return _insProductPromotionProduct
}

type insProductPromotionProduct struct {
	insProductPromotionProductDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	PromotionId  field.Uint
	ProductId    field.Uint
	DiscountRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insProductPromotionProduct) Table(newTableName string) *insProductPromotionProduct {
	i.insProductPromotionProductDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotionProduct) As(alias string) *insProductPromotionProduct {
	i.insProductPromotionProductDo.DO = *(i.insProductPromotionProductDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotionProduct) updateTableName(table string) *insProductPromotionProduct {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PromotionId = field.NewUint(table, "promotion_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotionProduct) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotionProduct) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["promotion_id"] = i.PromotionId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["discount_rate"] = i.DiscountRate
}

func (i insProductPromotionProduct) clone(db *gorm.DB) insProductPromotionProduct {
	i.insProductPromotionProductDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotionProduct) replaceDB(db *gorm.DB) insProductPromotionProduct {
	i.insProductPromotionProductDo.ReplaceDB(db)
	return i
}

type insProductPromotionProductDo struct{ gen.DO }

func (i insProductPromotionProductDo) Debug() *insProductPromotionProductDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionProductDo) WithContext(ctx context.Context) *insProductPromotionProductDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionProductDo) ReadDB() *insProductPromotionProductDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionProductDo) WriteDB() *insProductPromotionProductDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionProductDo) Session(config *gorm.Session) *insProductPromotionProductDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionProductDo) Clauses(conds ...clause.Expression) *insProductPromotionProductDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionProductDo) Returning(value interface{}, columns ...string) *insProductPromotionProductDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionProductDo) Not(conds ...gen.Condition) *insProductPromotionProductDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionProductDo) Or(conds ...gen.Condition) *insProductPromotionProductDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionProductDo) Select(conds ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionProductDo) Where(conds ...gen.Condition) *insProductPromotionProductDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionProductDo) Order(conds ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionProductDo) Distinct(cols ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionProductDo) Omit(cols ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionProductDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionProductDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionProductDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionProductDo) Group(cols ...field.Expr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionProductDo) Having(conds ...gen.Condition) *insProductPromotionProductDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionProductDo) Limit(limit int) *insProductPromotionProductDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionProductDo) Offset(offset int) *insProductPromotionProductDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionProductDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionProductDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionProductDo) Unscoped() *insProductPromotionProductDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionProductDo) Create(values ...*insbuy.InsProductPromotionProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionProductDo) CreateInBatches(values []*insbuy.InsProductPromotionProduct, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionProductDo) Save(values ...*insbuy.InsProductPromotionProduct) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionProductDo) First() (*insbuy.InsProductPromotionProduct, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionProduct), nil
	}
}

func (i insProductPromotionProductDo) Take() (*insbuy.InsProductPromotionProduct, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionProduct), nil
	}
}

func (i insProductPromotionProductDo) Last() (*insbuy.InsProductPromotionProduct, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionProduct), nil
	}
}

func (i insProductPromotionProductDo) Find() ([]*insbuy.InsProductPromotionProduct, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotionProduct), err
}

func (i insProductPromotionProductDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotionProduct, err error) {
	buf := make([]*insbuy.InsProductPromotionProduct, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionProductDo) FindInBatches(result *[]*insbuy.InsProductPromotionProduct, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionProductDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionProductDo) Assign(attrs ...field.AssignExpr) *insProductPromotionProductDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionProductDo) Joins(fields ...field.RelationField) *insProductPromotionProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionProductDo) Preload(fields ...field.RelationField) *insProductPromotionProductDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionProductDo) FirstOrInit() (*insbuy.InsProductPromotionProduct, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionProduct), nil
	}
}

func (i insProductPromotionProductDo) FirstOrCreate() (*insbuy.InsProductPromotionProduct, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionProduct), nil
	}
}

func (i insProductPromotionProductDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotionProduct, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionProductDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionProductDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionProductDo) Delete(models ...*insbuy.InsProductPromotionProduct) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionProductDo) withDO(do gen.Dao) *insProductPromotionProductDo {
	i.DO = *do.(*gen.DO)
	return i
}
