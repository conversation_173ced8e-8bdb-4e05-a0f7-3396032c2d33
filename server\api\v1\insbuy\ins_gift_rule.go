package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsGiftRulesApi struct {
}

// CreateInsGiftRules 创建赠送规则
// @Tags InsGiftRules
// @Summary 创建赠送规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsGiftRuleCreate true "创建赠送规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insGiftRules/createInsGiftRules [post]
func (insGiftRulesApi *InsGiftRulesApi) CreateInsGiftRules(c *gin.Context) {
	var insGiftRules insbuyReq.InsGiftRuleCreate
	err := GinMustBind(c, &insGiftRules)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insGiftRulesService.CreateInsGiftRules(c, insGiftRules); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsGiftRules 删除InsGiftRules
// @Tags InsGiftRules
// @Summary 删除InsGiftRules
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsGiftRules true "删除InsGiftRules"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insGiftRules/deleteInsGiftRules [delete]
func (insGiftRulesApi *InsGiftRulesApi) DeleteInsGiftRules(c *gin.Context) {
	var insGiftRules insbuy.InsGiftRules
	err := c.ShouldBindJSON(&insGiftRules)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftRulesService.DeleteInsGiftRules(insGiftRules); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// UpdateInsGiftRules 更新赠送规则
// @Tags InsGiftRules
// @Summary 更新赠送规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsGiftRuleItem true "更新赠送规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insGiftRules/updateInsGiftRules [put]
func (insGiftRulesApi *InsGiftRulesApi) UpdateInsGiftRules(c *gin.Context) {
	var insGiftRules insbuyReq.InsGiftRuleItem
	err := GinMustBind(c, &insGiftRules)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"StoreId": {utils.NotEmpty()},
		"RuleId":  {utils.NotEmpty()},
	}
	insGiftRules.StoreId = int(utils.GetHeaderStoreIdUint(c))
	if err := utils.Verify(insGiftRules, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insGiftRulesService.UpdateInsGiftRules(c, insGiftRules); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsGiftRules 用id查询赠送规则
// @Tags InsGiftRules
// @Summary 用id查询赠送规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsGiftRules true "用id查询赠送规则"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insGiftRules/findInsGiftRules [get]
func (insGiftRulesApi *InsGiftRulesApi) FindInsGiftRules(c *gin.Context) {
	var insGiftRules insbuy.InsGiftRules
	err := c.ShouldBindQuery(&insGiftRules)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsGiftRules, err := insGiftRulesService.GetInsGiftRules(insGiftRules.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsGiftRules": reinsGiftRules}, c)
	}
}

// GetInsGiftRulesList 分页获取赠送规则列表
// @Tags InsGiftRules
// @Summary 分页获取赠送规则列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsGiftRulesSearch true "分页获取赠送规则列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insGiftRules/getInsGiftRulesList [get]
func (insGiftRulesApi *InsGiftRulesApi) GetInsGiftRulesList(c *gin.Context) {
	var pageInfo insbuyReq.InsGiftRulesSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"page":     {utils.NotEmpty()},
		"pageSize": {utils.NotEmpty()},
	}
	if err := utils.Verify(pageInfo, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if list, total, err := insGiftRulesService.GetInsGiftRulesInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// UpdateInsGiftRulesStatus 更新赠送规则状态
// @Tags InsGiftRules
// @Summary 更新赠送规则状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsGiftRuleStatusUpdate true "更新赠送规则状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insGiftRules/updateInsGiftRulesStatus [put]
func (insGiftRulesApi *InsGiftRulesApi) UpdateInsGiftRulesStatus(c *gin.Context) {
	var insGiftRule insbuyReq.InsGiftRuleStatusUpdate
	err := c.ShouldBindJSON(&insGiftRule)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	verify := utils.Rules{
		"RuleId": {utils.NotEmpty()},
	}
	if err := utils.Verify(insGiftRule, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if err := insGiftRulesService.UpdateInsGiftRulesStatus(insGiftRule); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GetGiftUserSurplusAmount 获取用户剩余赠送额度
// @Tags InsGiftRules
// @Summary 获取用户剩余赠送额度
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetGiftUserSurplusAmountReq true "获取用户剩余赠送额度"
// @Success   200   {object}  response.Response{data=insbuyResp.RemainAmountRes,msg=string}  "获取用户剩余赠送额度"
// @Router /insGiftRules/getGiftUserSurplusAmount [get]
func (insGiftRulesApi *InsGiftRulesApi) GetGiftUserSurplusAmount(c *gin.Context) {
	var req insbuyReq.GetGiftUserSurplusAmountReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp := make([]insbuyResp.RemainAmountResp, 0)
	if resp, err = insGiftRulesService.GetGiftUserSurplusAmount(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GiftQuotaAssignUserList 获取发起分配额度用户列表
// @Tags InsGiftRules
// @Summary 获取发起分配额度用户列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetGiftUserSurplusAmountReq true "获取发起分配额度用户列表"
// @Success   200   {object}  response.Response{data=insbuyResp.QuotaAssignUserListRes,msg=string}  "获取发起分配额度用户列表"
// @Router /insGiftRules/giftQuotaAssignUserList [get]
func (insGiftRulesApi *InsGiftRulesApi) GiftQuotaAssignUserList(c *gin.Context) {
	var req insbuyReq.GiftQuotaAssignUserListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp := make([]insbuyResp.QuotaAssignUserListRes, 0)
	if resp, err = insGiftRulesService.GiftQuotaAssignUserList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// StartGiftQuotaAssign 发起额度申请
// @Tags InsGiftRules
// @Summary 发起额度申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartGiftQuotaAssignReq true "发起额度申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"申请成功"}"
// @Router /insGiftRules/startGiftQuotaAssign [post]
func (insGiftRulesApi *InsGiftRulesApi) StartGiftQuotaAssign(c *gin.Context) {
	var req insbuyReq.StartGiftQuotaAssignReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insGiftRulesService.StartGiftQuotaAssign(req); err != nil {
		global.GVA_LOG.Error("发起失败!", zap.Error(err))
		response.FailWithMessage("发起失败", c)
	} else {
		response.OkWithMessage("发起成功", c)
	}
}

// GiftQuotaApplyLogList 获取额度申请记录列表
// @Tags InsGiftRules
// @Summary 获取额度申请记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GiftQuotaApplyLogListReq true "获取额度申请记录列表"
// @Success   200   {object}  response.Response{data=insbuyResp.QuotaAssignUserListRes,msg=string}  "获取发起分配额度用户列表"
// @Router /insGiftRules/giftQuotaApplyLogList [get]
func (insGiftRulesApi *InsGiftRulesApi) GiftQuotaApplyLogList(c *gin.Context) {
	var req insbuyReq.GiftQuotaApplyLogListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insGiftRulesService.GiftQuotaApplyLogList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GiftQuotaApplyAuditList 获取额度申请审核列表
// @Tags InsGiftRules
// @Summary 获取额度申请审核列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GiftQuotaApplyAuditListReq true "获取额度申请审核列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GiftQuotaApplyAuditListResp,msg=string}  "获取额度申请审核列表"
// @Router /insGiftRules/giftQuotaApplyAuditList [get]
func (insGiftRulesApi *InsGiftRulesApi) GiftQuotaApplyAuditList(c *gin.Context) {
	var req insbuyReq.GiftQuotaApplyAuditListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insGiftRulesService.GiftQuotaApplyAuditList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GiftQuotaAssign 额度分配
// @Tags InsGiftRules
// @Summary 额度分配
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GiftQuotaAssignReq true "额度分配"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"分配成功"}"
// @Router /insGiftRules/giftQuotaAssign [post]
func (insGiftRulesApi *InsGiftRulesApi) GiftQuotaAssign(c *gin.Context) {
	var req insbuyReq.GiftQuotaAssignReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insGiftRulesService.GiftQuotaAssign(req); err != nil {
		global.GVA_LOG.Error("分配失败!", zap.Error(err))
		response.FailWithMessage("分配失败", c)
	} else {
		response.OkWithMessage("分配成功", c)
	}
}

// GiftQuotaAssignUserDetail 获取额度分配用户详情
// @Tags InsGiftRules
// @Summary 获取额度分配用户详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GiftQuotaAssignUserDetailReq true "获取额度分配用户详情"
// @Success   200   {object}  response.Response{data=insbuyResp.GiftQuotaAssignUserDetailResp,msg=string}  "获取额度分配用户详情"
// @Router /insGiftRules/giftQuotaAssignUserDetail [get]
func (insGiftRulesApi *InsGiftRulesApi) GiftQuotaAssignUserDetail(c *gin.Context) {
	var req insbuyReq.GiftQuotaAssignUserDetailReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insGiftRulesService.GiftQuotaAssignUserDetail(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// AddUserToGiftRules 添加用户到赠送规则
// @Tags InsGiftRules
// @Summary 添加用户到赠送规则
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AddUserToGiftRulesReq true "添加用户到赠送规则"
// @Success   200   {object}  response.Response{msg=string}  "添加用户到赠送规则"
// @Router /insGiftRules/addUserToGiftRules [post]
func (insGiftRulesApi *InsGiftRulesApi) AddUserToGiftRules(c *gin.Context) {
	var req insbuyReq.AddUserToGiftRulesReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftRulesService.AddUserToGiftRules(req); err != nil {
		global.GVA_LOG.Error("添加失败!", zap.Error(err))
		response.FailWithMessage("添加失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// CalculateSaleMonthQuota
// @Tags InsGiftRules
// @Summary 计算销售月度额度
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CalculateSaleMonthQuotaReq true "计算销售月度额度"
// @Success   200   {object}  response.Response{msg=string}  "计算销售月度额度"
// @Router /insGiftRules/calculateSaleMonthQuota [post]
func (insGiftRulesApi *InsGiftRulesApi) CalculateSaleMonthQuota(c *gin.Context) {
	var req insbuyReq.CalculateSaleMonthQuotaReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insGiftRulesService.CalculateSaleMonthQuota(req); err != nil {
		global.GVA_LOG.Error("计算失败!", zap.Error(err))
		response.FailWithMessage("计算失败"+err.Error(), c)
	} else {
		response.Ok(c)
	}
}

// CalculateSaleMonthQuota2
// @Tags InsGiftRules
// @Summary 计算销售月度额度
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CalculateSaleMonthQuotaReq true "计算销售月度额度"
// @Success   200   {object}  response.Response{msg=string}  "计算销售月度额度"
// @Router /insGiftRules/calculateSaleMonthQuota2 [post]
func (insGiftRulesApi *InsGiftRulesApi) CalculateSaleMonthQuota2(c *gin.Context) {
	var req insbuyReq.CalculateSaleMonthQuotaReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insGiftRulesService.CalculateSaleMonthQuota2(req)
	response.Err(err, c)
}
