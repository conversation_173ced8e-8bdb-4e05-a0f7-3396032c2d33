// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftRuleMember(db *gorm.DB, opts ...gen.DOOption) insGiftRuleMember {
	_insGiftRuleMember := insGiftRuleMember{}

	_insGiftRuleMember.insGiftRuleMemberDo.UseDB(db, opts...)
	_insGiftRuleMember.insGiftRuleMemberDo.UseModel(&insbuy.InsGiftRuleMember{})

	tableName := _insGiftRuleMember.insGiftRuleMemberDo.TableName()
	_insGiftRuleMember.ALL = field.NewAsterisk(tableName)
	_insGiftRuleMember.ID = field.NewUint(tableName, "id")
	_insGiftRuleMember.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftRuleMember.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftRuleMember.RuleId = field.NewInt(tableName, "rule_id")
	_insGiftRuleMember.UserId = field.NewInt(tableName, "user_id")

	_insGiftRuleMember.fillFieldMap()

	return _insGiftRuleMember
}

type insGiftRuleMember struct {
	insGiftRuleMemberDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	RuleId    field.Int
	UserId    field.Int

	fieldMap map[string]field.Expr
}

func (i insGiftRuleMember) Table(newTableName string) *insGiftRuleMember {
	i.insGiftRuleMemberDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftRuleMember) As(alias string) *insGiftRuleMember {
	i.insGiftRuleMemberDo.DO = *(i.insGiftRuleMemberDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftRuleMember) updateTableName(table string) *insGiftRuleMember {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.RuleId = field.NewInt(table, "rule_id")
	i.UserId = field.NewInt(table, "user_id")

	i.fillFieldMap()

	return i
}

func (i *insGiftRuleMember) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftRuleMember) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["user_id"] = i.UserId
}

func (i insGiftRuleMember) clone(db *gorm.DB) insGiftRuleMember {
	i.insGiftRuleMemberDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftRuleMember) replaceDB(db *gorm.DB) insGiftRuleMember {
	i.insGiftRuleMemberDo.ReplaceDB(db)
	return i
}

type insGiftRuleMemberDo struct{ gen.DO }

func (i insGiftRuleMemberDo) Debug() *insGiftRuleMemberDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftRuleMemberDo) WithContext(ctx context.Context) *insGiftRuleMemberDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftRuleMemberDo) ReadDB() *insGiftRuleMemberDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftRuleMemberDo) WriteDB() *insGiftRuleMemberDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftRuleMemberDo) Session(config *gorm.Session) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftRuleMemberDo) Clauses(conds ...clause.Expression) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftRuleMemberDo) Returning(value interface{}, columns ...string) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftRuleMemberDo) Not(conds ...gen.Condition) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftRuleMemberDo) Or(conds ...gen.Condition) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftRuleMemberDo) Select(conds ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftRuleMemberDo) Where(conds ...gen.Condition) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftRuleMemberDo) Order(conds ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftRuleMemberDo) Distinct(cols ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftRuleMemberDo) Omit(cols ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftRuleMemberDo) Join(table schema.Tabler, on ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftRuleMemberDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftRuleMemberDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftRuleMemberDo) Group(cols ...field.Expr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftRuleMemberDo) Having(conds ...gen.Condition) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftRuleMemberDo) Limit(limit int) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftRuleMemberDo) Offset(offset int) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftRuleMemberDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftRuleMemberDo) Unscoped() *insGiftRuleMemberDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftRuleMemberDo) Create(values ...*insbuy.InsGiftRuleMember) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftRuleMemberDo) CreateInBatches(values []*insbuy.InsGiftRuleMember, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftRuleMemberDo) Save(values ...*insbuy.InsGiftRuleMember) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftRuleMemberDo) First() (*insbuy.InsGiftRuleMember, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleMember), nil
	}
}

func (i insGiftRuleMemberDo) Take() (*insbuy.InsGiftRuleMember, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleMember), nil
	}
}

func (i insGiftRuleMemberDo) Last() (*insbuy.InsGiftRuleMember, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleMember), nil
	}
}

func (i insGiftRuleMemberDo) Find() ([]*insbuy.InsGiftRuleMember, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftRuleMember), err
}

func (i insGiftRuleMemberDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftRuleMember, err error) {
	buf := make([]*insbuy.InsGiftRuleMember, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftRuleMemberDo) FindInBatches(result *[]*insbuy.InsGiftRuleMember, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftRuleMemberDo) Attrs(attrs ...field.AssignExpr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftRuleMemberDo) Assign(attrs ...field.AssignExpr) *insGiftRuleMemberDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftRuleMemberDo) Joins(fields ...field.RelationField) *insGiftRuleMemberDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftRuleMemberDo) Preload(fields ...field.RelationField) *insGiftRuleMemberDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftRuleMemberDo) FirstOrInit() (*insbuy.InsGiftRuleMember, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleMember), nil
	}
}

func (i insGiftRuleMemberDo) FirstOrCreate() (*insbuy.InsGiftRuleMember, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleMember), nil
	}
}

func (i insGiftRuleMemberDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftRuleMember, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftRuleMemberDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftRuleMemberDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftRuleMemberDo) Delete(models ...*insbuy.InsGiftRuleMember) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftRuleMemberDo) withDO(do gen.Dao) *insGiftRuleMemberDo {
	i.DO = *do.(*gen.DO)
	return i
}
