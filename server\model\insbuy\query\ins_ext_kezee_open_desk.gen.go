// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtKezeeOpenDesk(db *gorm.DB, opts ...gen.DOOption) insExtKezeeOpenDesk {
	_insExtKezeeOpenDesk := insExtKezeeOpenDesk{}

	_insExtKezeeOpenDesk.insExtKezeeOpenDeskDo.UseDB(db, opts...)
	_insExtKezeeOpenDesk.insExtKezeeOpenDeskDo.UseModel(&insbuy.InsExtKezeeOpenDesk{})

	tableName := _insExtKezeeOpenDesk.insExtKezeeOpenDeskDo.TableName()
	_insExtKezeeOpenDesk.ALL = field.NewAsterisk(tableName)
	_insExtKezeeOpenDesk.Id = field.NewUint(tableName, "id")
	_insExtKezeeOpenDesk.MainCode = field.NewString(tableName, "main_code")
	_insExtKezeeOpenDesk.OpenTime = field.NewField(tableName, "open_time")
	_insExtKezeeOpenDesk.SettleTime = field.NewField(tableName, "settle_time")
	_insExtKezeeOpenDesk.PointId = field.NewString(tableName, "point_id")
	_insExtKezeeOpenDesk.PointName = field.NewString(tableName, "point_name")
	_insExtKezeeOpenDesk.SalesmanId = field.NewString(tableName, "salesman_id")
	_insExtKezeeOpenDesk.SalesmanName = field.NewString(tableName, "salesman_name")
	_insExtKezeeOpenDesk.BookPerson = field.NewString(tableName, "book_person")
	_insExtKezeeOpenDesk.PeopleQty = field.NewInt(tableName, "people_qty")
	_insExtKezeeOpenDesk.OrigTotal = field.NewFloat64(tableName, "orig_total")
	_insExtKezeeOpenDesk.DiscTotal = field.NewFloat64(tableName, "disc_total")
	_insExtKezeeOpenDesk.LastTotal = field.NewFloat64(tableName, "last_total")
	_insExtKezeeOpenDesk.IncomeMoney = field.NewFloat64(tableName, "income_money")
	_insExtKezeeOpenDesk.BusinessDay = field.NewTime(tableName, "business_day")

	_insExtKezeeOpenDesk.fillFieldMap()

	return _insExtKezeeOpenDesk
}

type insExtKezeeOpenDesk struct {
	insExtKezeeOpenDeskDo

	ALL          field.Asterisk
	Id           field.Uint
	MainCode     field.String
	OpenTime     field.Field
	SettleTime   field.Field
	PointId      field.String
	PointName    field.String
	SalesmanId   field.String
	SalesmanName field.String
	BookPerson   field.String
	PeopleQty    field.Int
	OrigTotal    field.Float64
	DiscTotal    field.Float64
	LastTotal    field.Float64
	IncomeMoney  field.Float64
	BusinessDay  field.Time

	fieldMap map[string]field.Expr
}

func (i insExtKezeeOpenDesk) Table(newTableName string) *insExtKezeeOpenDesk {
	i.insExtKezeeOpenDeskDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtKezeeOpenDesk) As(alias string) *insExtKezeeOpenDesk {
	i.insExtKezeeOpenDeskDo.DO = *(i.insExtKezeeOpenDeskDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtKezeeOpenDesk) updateTableName(table string) *insExtKezeeOpenDesk {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.MainCode = field.NewString(table, "main_code")
	i.OpenTime = field.NewField(table, "open_time")
	i.SettleTime = field.NewField(table, "settle_time")
	i.PointId = field.NewString(table, "point_id")
	i.PointName = field.NewString(table, "point_name")
	i.SalesmanId = field.NewString(table, "salesman_id")
	i.SalesmanName = field.NewString(table, "salesman_name")
	i.BookPerson = field.NewString(table, "book_person")
	i.PeopleQty = field.NewInt(table, "people_qty")
	i.OrigTotal = field.NewFloat64(table, "orig_total")
	i.DiscTotal = field.NewFloat64(table, "disc_total")
	i.LastTotal = field.NewFloat64(table, "last_total")
	i.IncomeMoney = field.NewFloat64(table, "income_money")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insExtKezeeOpenDesk) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtKezeeOpenDesk) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.Id
	i.fieldMap["main_code"] = i.MainCode
	i.fieldMap["open_time"] = i.OpenTime
	i.fieldMap["settle_time"] = i.SettleTime
	i.fieldMap["point_id"] = i.PointId
	i.fieldMap["point_name"] = i.PointName
	i.fieldMap["salesman_id"] = i.SalesmanId
	i.fieldMap["salesman_name"] = i.SalesmanName
	i.fieldMap["book_person"] = i.BookPerson
	i.fieldMap["people_qty"] = i.PeopleQty
	i.fieldMap["orig_total"] = i.OrigTotal
	i.fieldMap["disc_total"] = i.DiscTotal
	i.fieldMap["last_total"] = i.LastTotal
	i.fieldMap["income_money"] = i.IncomeMoney
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insExtKezeeOpenDesk) clone(db *gorm.DB) insExtKezeeOpenDesk {
	i.insExtKezeeOpenDeskDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtKezeeOpenDesk) replaceDB(db *gorm.DB) insExtKezeeOpenDesk {
	i.insExtKezeeOpenDeskDo.ReplaceDB(db)
	return i
}

type insExtKezeeOpenDeskDo struct{ gen.DO }

func (i insExtKezeeOpenDeskDo) Debug() *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtKezeeOpenDeskDo) WithContext(ctx context.Context) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtKezeeOpenDeskDo) ReadDB() *insExtKezeeOpenDeskDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtKezeeOpenDeskDo) WriteDB() *insExtKezeeOpenDeskDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtKezeeOpenDeskDo) Session(config *gorm.Session) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtKezeeOpenDeskDo) Clauses(conds ...clause.Expression) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtKezeeOpenDeskDo) Returning(value interface{}, columns ...string) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtKezeeOpenDeskDo) Not(conds ...gen.Condition) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtKezeeOpenDeskDo) Or(conds ...gen.Condition) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtKezeeOpenDeskDo) Select(conds ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtKezeeOpenDeskDo) Where(conds ...gen.Condition) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtKezeeOpenDeskDo) Order(conds ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtKezeeOpenDeskDo) Distinct(cols ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtKezeeOpenDeskDo) Omit(cols ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtKezeeOpenDeskDo) Join(table schema.Tabler, on ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtKezeeOpenDeskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtKezeeOpenDeskDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtKezeeOpenDeskDo) Group(cols ...field.Expr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtKezeeOpenDeskDo) Having(conds ...gen.Condition) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtKezeeOpenDeskDo) Limit(limit int) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtKezeeOpenDeskDo) Offset(offset int) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtKezeeOpenDeskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtKezeeOpenDeskDo) Unscoped() *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtKezeeOpenDeskDo) Create(values ...*insbuy.InsExtKezeeOpenDesk) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtKezeeOpenDeskDo) CreateInBatches(values []*insbuy.InsExtKezeeOpenDesk, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtKezeeOpenDeskDo) Save(values ...*insbuy.InsExtKezeeOpenDesk) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtKezeeOpenDeskDo) First() (*insbuy.InsExtKezeeOpenDesk, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOpenDesk), nil
	}
}

func (i insExtKezeeOpenDeskDo) Take() (*insbuy.InsExtKezeeOpenDesk, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOpenDesk), nil
	}
}

func (i insExtKezeeOpenDeskDo) Last() (*insbuy.InsExtKezeeOpenDesk, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOpenDesk), nil
	}
}

func (i insExtKezeeOpenDeskDo) Find() ([]*insbuy.InsExtKezeeOpenDesk, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtKezeeOpenDesk), err
}

func (i insExtKezeeOpenDeskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtKezeeOpenDesk, err error) {
	buf := make([]*insbuy.InsExtKezeeOpenDesk, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtKezeeOpenDeskDo) FindInBatches(result *[]*insbuy.InsExtKezeeOpenDesk, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtKezeeOpenDeskDo) Attrs(attrs ...field.AssignExpr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtKezeeOpenDeskDo) Assign(attrs ...field.AssignExpr) *insExtKezeeOpenDeskDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtKezeeOpenDeskDo) Joins(fields ...field.RelationField) *insExtKezeeOpenDeskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtKezeeOpenDeskDo) Preload(fields ...field.RelationField) *insExtKezeeOpenDeskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtKezeeOpenDeskDo) FirstOrInit() (*insbuy.InsExtKezeeOpenDesk, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOpenDesk), nil
	}
}

func (i insExtKezeeOpenDeskDo) FirstOrCreate() (*insbuy.InsExtKezeeOpenDesk, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOpenDesk), nil
	}
}

func (i insExtKezeeOpenDeskDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtKezeeOpenDesk, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtKezeeOpenDeskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtKezeeOpenDeskDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtKezeeOpenDeskDo) Delete(models ...*insbuy.InsExtKezeeOpenDesk) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtKezeeOpenDeskDo) withDO(do gen.Dao) *insExtKezeeOpenDeskDo {
	i.DO = *do.(*gen.DO)
	return i
}
