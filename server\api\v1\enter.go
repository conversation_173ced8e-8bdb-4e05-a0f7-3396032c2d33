package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/priinventory"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
)

type ApiGroup struct {
	SystemApiGroup  system.ApiGroup
	ExampleApiGroup example.ApiGroup
	InsbuyApiGroup  insbuy.ApiGroup
	PriApiGroup     priinventory.ApiGroup
}

var ApiGroupApp = new(ApiGroup)
