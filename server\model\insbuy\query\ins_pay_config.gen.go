// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsPayConfig(db *gorm.DB, opts ...gen.DOOption) insPayConfig {
	_insPayConfig := insPayConfig{}

	_insPayConfig.insPayConfigDo.UseDB(db, opts...)
	_insPayConfig.insPayConfigDo.UseModel(&insbuy.InsPayConfig{})

	tableName := _insPayConfig.insPayConfigDo.TableName()
	_insPayConfig.ALL = field.NewAsterisk(tableName)
	_insPayConfig.ID = field.NewUint(tableName, "id")
	_insPayConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_insPayConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insPayConfig.PayId = field.NewUint(tableName, "pay_id")
	_insPayConfig.PayConfig = field.NewString(tableName, "pay_config")
	_insPayConfig.Remark = field.NewString(tableName, "remark")
	_insPayConfig.StoreId = field.NewUint(tableName, "store_id")

	_insPayConfig.fillFieldMap()

	return _insPayConfig
}

type insPayConfig struct {
	insPayConfigDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	PayId     field.Uint
	PayConfig field.String
	Remark    field.String
	StoreId   field.Uint

	fieldMap map[string]field.Expr
}

func (i insPayConfig) Table(newTableName string) *insPayConfig {
	i.insPayConfigDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insPayConfig) As(alias string) *insPayConfig {
	i.insPayConfigDo.DO = *(i.insPayConfigDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insPayConfig) updateTableName(table string) *insPayConfig {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.PayId = field.NewUint(table, "pay_id")
	i.PayConfig = field.NewString(table, "pay_config")
	i.Remark = field.NewString(table, "remark")
	i.StoreId = field.NewUint(table, "store_id")

	i.fillFieldMap()

	return i
}

func (i *insPayConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insPayConfig) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["pay_id"] = i.PayId
	i.fieldMap["pay_config"] = i.PayConfig
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["store_id"] = i.StoreId
}

func (i insPayConfig) clone(db *gorm.DB) insPayConfig {
	i.insPayConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insPayConfig) replaceDB(db *gorm.DB) insPayConfig {
	i.insPayConfigDo.ReplaceDB(db)
	return i
}

type insPayConfigDo struct{ gen.DO }

func (i insPayConfigDo) Debug() *insPayConfigDo {
	return i.withDO(i.DO.Debug())
}

func (i insPayConfigDo) WithContext(ctx context.Context) *insPayConfigDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insPayConfigDo) ReadDB() *insPayConfigDo {
	return i.Clauses(dbresolver.Read)
}

func (i insPayConfigDo) WriteDB() *insPayConfigDo {
	return i.Clauses(dbresolver.Write)
}

func (i insPayConfigDo) Session(config *gorm.Session) *insPayConfigDo {
	return i.withDO(i.DO.Session(config))
}

func (i insPayConfigDo) Clauses(conds ...clause.Expression) *insPayConfigDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insPayConfigDo) Returning(value interface{}, columns ...string) *insPayConfigDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insPayConfigDo) Not(conds ...gen.Condition) *insPayConfigDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insPayConfigDo) Or(conds ...gen.Condition) *insPayConfigDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insPayConfigDo) Select(conds ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insPayConfigDo) Where(conds ...gen.Condition) *insPayConfigDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insPayConfigDo) Order(conds ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insPayConfigDo) Distinct(cols ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insPayConfigDo) Omit(cols ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insPayConfigDo) Join(table schema.Tabler, on ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insPayConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insPayConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insPayConfigDo) Group(cols ...field.Expr) *insPayConfigDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insPayConfigDo) Having(conds ...gen.Condition) *insPayConfigDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insPayConfigDo) Limit(limit int) *insPayConfigDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insPayConfigDo) Offset(offset int) *insPayConfigDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insPayConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insPayConfigDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insPayConfigDo) Unscoped() *insPayConfigDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insPayConfigDo) Create(values ...*insbuy.InsPayConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insPayConfigDo) CreateInBatches(values []*insbuy.InsPayConfig, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insPayConfigDo) Save(values ...*insbuy.InsPayConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insPayConfigDo) First() (*insbuy.InsPayConfig, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayConfig), nil
	}
}

func (i insPayConfigDo) Take() (*insbuy.InsPayConfig, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayConfig), nil
	}
}

func (i insPayConfigDo) Last() (*insbuy.InsPayConfig, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayConfig), nil
	}
}

func (i insPayConfigDo) Find() ([]*insbuy.InsPayConfig, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsPayConfig), err
}

func (i insPayConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsPayConfig, err error) {
	buf := make([]*insbuy.InsPayConfig, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insPayConfigDo) FindInBatches(result *[]*insbuy.InsPayConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insPayConfigDo) Attrs(attrs ...field.AssignExpr) *insPayConfigDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insPayConfigDo) Assign(attrs ...field.AssignExpr) *insPayConfigDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insPayConfigDo) Joins(fields ...field.RelationField) *insPayConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insPayConfigDo) Preload(fields ...field.RelationField) *insPayConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insPayConfigDo) FirstOrInit() (*insbuy.InsPayConfig, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayConfig), nil
	}
}

func (i insPayConfigDo) FirstOrCreate() (*insbuy.InsPayConfig, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsPayConfig), nil
	}
}

func (i insPayConfigDo) FindByPage(offset int, limit int) (result []*insbuy.InsPayConfig, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insPayConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insPayConfigDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insPayConfigDo) Delete(models ...*insbuy.InsPayConfig) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insPayConfigDo) withDO(do gen.Dao) *insPayConfigDo {
	i.DO = *do.(*gen.DO)
	return i
}
