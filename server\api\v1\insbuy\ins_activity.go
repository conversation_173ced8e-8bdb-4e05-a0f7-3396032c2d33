package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
)

type InsActivityApi struct {
}

// GetContentActivityList 内容活动列表
// @Tags InsActivity
// @Summary 内容活动列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insActivity/getContentActivityList [get]
func (i *InsActivityApi) GetContentActivityList(c *gin.Context) {
	res, err := insActivityService.GetContentActivityList()
	response.ResultErr(res, err, c)
}
