package insbuy

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"net/http"
)

type InsPayApi struct{}

// MiniAppPay
// @Tags      MiniAppPay
// @Summary   测试支付接口,小程序专用
// @accept    application/json
// @Produce   application/json
// @Param data body insbuyReq.InsMicroOrder true "小程序模拟订单"
// @Success   200   {object}  response.Response{msg=string}  "测试支付接口"
// @Router    /pay/pay [post]
func (s *InsPayApi) MiniAppPay(c *gin.Context) {
	req := insbuyReq.InsMicroOrder{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	rsp, err := insMicroService.Order(c, req)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
	}
	response.OkWithDetailed(rsp.Data, "请求成功", c)
}

// Query
// @Tags      Query
// @Summary   支付查询接口,小程序专用
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{msg=string}  "查询接口"
// @Router    /pay/query [get]
func (s *InsPayApi) Query(c *gin.Context) {
	req := insbuyReq.InsMicroQuery{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	rsp, err := insMicroService.Query(c, req)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
	}
	response.OkWithDetailed(rsp, "请求成功", c)
}

// Callback
// @Tags      Callback
// @Summary   支付回调接口
// @accept    application/json
// @Produce   application/json
// @Success   200   {object}  response.Response{msg=string}  "回调接口"
// @Router    /pay/callback [post]
func (s *InsPayApi) Callback(c *gin.Context) {
	//回调参数
	/*req := insbuyReq.InsMicroNotify{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insMicroService.Callback(c, req)
	if err != nil {
		global.GVA_LOG.Error(err.Error())
	}*/
	err := c.Request.ParseForm()
	fmt.Println(err)
	// 解析表单数据
	formData := make(map[string]string)
	for key, values := range c.Request.Form {
		formData[key] = values[0] // 假设每个字段只有一个值
	}
	fmt.Println(c.Request.Method)
	fmt.Println(c.Request.Header)
	fmt.Printf("AlipayOrder: %v\n", formData)
	c.JSON(http.StatusOK, "success")
}
