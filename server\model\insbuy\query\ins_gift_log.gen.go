// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftLog(db *gorm.DB, opts ...gen.DOOption) insGiftLog {
	_insGiftLog := insGiftLog{}

	_insGiftLog.insGiftLogDo.UseDB(db, opts...)
	_insGiftLog.insGiftLogDo.UseModel(&insbuy.InsGiftLog{})

	tableName := _insGiftLog.insGiftLogDo.TableName()
	_insGiftLog.ALL = field.NewAsterisk(tableName)
	_insGiftLog.ID = field.NewUint(tableName, "id")
	_insGiftLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftLog.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insGiftLog.StoreId = field.NewUint(tableName, "store_id")
	_insGiftLog.GiftUser = field.NewUint(tableName, "gift_user")
	_insGiftLog.RuleId = field.NewUint(tableName, "rule_id")
	_insGiftLog.OperatorId = field.NewUint(tableName, "operator_id")
	_insGiftLog.RemarkExt = field.NewField(tableName, "remark_ext")
	_insGiftLog.BusinessDay = field.NewTime(tableName, "business_day")

	_insGiftLog.fillFieldMap()

	return _insGiftLog
}

type insGiftLog struct {
	insGiftLogDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	OpenDeskId  field.Uint
	StoreId     field.Uint
	GiftUser    field.Uint
	RuleId      field.Uint
	OperatorId  field.Uint
	RemarkExt   field.Field
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insGiftLog) Table(newTableName string) *insGiftLog {
	i.insGiftLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftLog) As(alias string) *insGiftLog {
	i.insGiftLogDo.DO = *(i.insGiftLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftLog) updateTableName(table string) *insGiftLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.GiftUser = field.NewUint(table, "gift_user")
	i.RuleId = field.NewUint(table, "rule_id")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.RemarkExt = field.NewField(table, "remark_ext")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insGiftLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["gift_user"] = i.GiftUser
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["remark_ext"] = i.RemarkExt
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insGiftLog) clone(db *gorm.DB) insGiftLog {
	i.insGiftLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftLog) replaceDB(db *gorm.DB) insGiftLog {
	i.insGiftLogDo.ReplaceDB(db)
	return i
}

type insGiftLogDo struct{ gen.DO }

func (i insGiftLogDo) Debug() *insGiftLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftLogDo) WithContext(ctx context.Context) *insGiftLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftLogDo) ReadDB() *insGiftLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftLogDo) WriteDB() *insGiftLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftLogDo) Session(config *gorm.Session) *insGiftLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftLogDo) Clauses(conds ...clause.Expression) *insGiftLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftLogDo) Returning(value interface{}, columns ...string) *insGiftLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftLogDo) Not(conds ...gen.Condition) *insGiftLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftLogDo) Or(conds ...gen.Condition) *insGiftLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftLogDo) Select(conds ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftLogDo) Where(conds ...gen.Condition) *insGiftLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftLogDo) Order(conds ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftLogDo) Distinct(cols ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftLogDo) Omit(cols ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftLogDo) Join(table schema.Tabler, on ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftLogDo) Group(cols ...field.Expr) *insGiftLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftLogDo) Having(conds ...gen.Condition) *insGiftLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftLogDo) Limit(limit int) *insGiftLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftLogDo) Offset(offset int) *insGiftLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftLogDo) Unscoped() *insGiftLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftLogDo) Create(values ...*insbuy.InsGiftLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftLogDo) CreateInBatches(values []*insbuy.InsGiftLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftLogDo) Save(values ...*insbuy.InsGiftLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftLogDo) First() (*insbuy.InsGiftLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLog), nil
	}
}

func (i insGiftLogDo) Take() (*insbuy.InsGiftLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLog), nil
	}
}

func (i insGiftLogDo) Last() (*insbuy.InsGiftLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLog), nil
	}
}

func (i insGiftLogDo) Find() ([]*insbuy.InsGiftLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftLog), err
}

func (i insGiftLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftLog, err error) {
	buf := make([]*insbuy.InsGiftLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftLogDo) FindInBatches(result *[]*insbuy.InsGiftLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftLogDo) Attrs(attrs ...field.AssignExpr) *insGiftLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftLogDo) Assign(attrs ...field.AssignExpr) *insGiftLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftLogDo) Joins(fields ...field.RelationField) *insGiftLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftLogDo) Preload(fields ...field.RelationField) *insGiftLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftLogDo) FirstOrInit() (*insbuy.InsGiftLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLog), nil
	}
}

func (i insGiftLogDo) FirstOrCreate() (*insbuy.InsGiftLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLog), nil
	}
}

func (i insGiftLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftLogDo) Delete(models ...*insbuy.InsGiftLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftLogDo) withDO(do gen.Dao) *insGiftLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
