package priinventory

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/priinventory"
	priReq "github.com/flipped-aurora/gin-vue-admin/server/model/priinventory/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PriInventoryApi struct {
}

// CreatePriProductCategory 创建私人仓库商品分类
// @Tags PriInventoryApi
// @Summary 创建分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     data  body      priReq.PriProductCategoryCreateReq          true  "创建私人仓库商品分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriProductCategory [post]
func (p *PriInventoryApi) CreatePriProductCategory(c *gin.Context) {
	var productCategory priReq.PriProductCategoryCreateReq
	err := c.ShouldBindJSON(&productCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriProductCategory(productCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeletePriProductCategory 删除私人仓库商品分类
// @Tags PriInventoryApi
// @Summary 删除私人仓库商品分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priinventory.PriProductCategory true "删除私人仓库商品分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /priInventory/deletePriProductCategory [delete]
func (p *PriInventoryApi) DeletePriProductCategory(c *gin.Context) {
	var insProductCategory priinventory.PriProductCategory
	err := c.ShouldBindJSON(&insProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.DeletePriProductCategory(insProductCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeletePriProductCategoryByIds 批量删除私人仓库商品分类
// @Tags PriInventoryApi
// @Summary 批量删除私人仓库商品分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除私人仓库商品分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /priInventory/deletePriInventoryApiByIds [delete]
func (p *PriInventoryApi) DeletePriProductCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.DeletePriProductCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdatePriProductCategory 更新私人仓库商品分类
// @Tags PriInventoryApi
// @Summary 更新私人仓库商品分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.PriProductCategoryUpdateReq true "更新私人仓库商品分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /priInventory/updatePriProductCategory [put]
func (p *PriInventoryApi) UpdatePriProductCategory(c *gin.Context) {
	var priProductCategory priReq.PriProductCategoryUpdateReq
	err := c.ShouldBindJSON(&priProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.UpdatePriProductCategory(priProductCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindPriProductCategory 用id查询私人仓库商品分类
// @Tags PriInventoryApi
// @Summary 用id查询PriInventoryApi
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priinventory.PriProductCategory true "用id查询私人仓库商品分类"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /priInventory/findPriProductCategory [get]
func (p *PriInventoryApi) FindPriProductCategory(c *gin.Context) {
	var priProductCategory priinventory.PriProductCategory
	err := c.ShouldBindQuery(&priProductCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsProductCategory, err := priInventoryService.GetPriProductCategory(priProductCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsProductCategory": reinsProductCategory}, c)
	}
}

// GetPriProductCategoryList 分页获取私人仓库商品分类列表
// @Tags PriInventoryApi
// @Summary 分页获取私人仓库商品分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priReq.PriProductCategorySearch true "分页获取私人仓库商品分类列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/getPriProductCategoryList [get]
func (p *PriInventoryApi) GetPriProductCategoryList(c *gin.Context) {
	var pageInfo priReq.PriProductCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := priInventoryService.GetPriProductCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreatePriProduct 创建私人仓库商品
// @Tags PriInventoryApi
// @Summary 创建PriProduct
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.PriProductCreateReq true "创建私人仓库商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriProduct [post]
func (p *PriInventoryApi) CreatePriProduct(c *gin.Context) {
	var priProduct priReq.PriProductCreateReq
	err := c.ShouldBindJSON(&priProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriProduct(priProduct); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeletePriProduct 删除私人仓库商品
// @Tags PriInventoryApi
// @Summary 删除私人仓库商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priinventory.PriProduct true "删除私人仓库商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /priInventory/deletePriProduct [delete]
func (p *PriInventoryApi) DeletePriProduct(c *gin.Context) {
	var PriProduct priinventory.PriProduct
	err := c.ShouldBindJSON(&PriProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.DeletePriProduct(PriProduct); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeletePriProductByIds 批量删除私人仓库商品
// @Tags PriInventoryApi
// @Summary 批量删除私人仓库商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除私人仓库商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /priInventory/deletePriProductByIds [delete]
func (p *PriInventoryApi) DeletePriProductByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.DeletePriProductByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdatePriProduct 更新私人仓库商品
// @Tags PriInventoryApi
// @Summary 更新私人仓库商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priinventory.PriProduct true "更新私人仓库商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /priInventory/updatePriProduct [put]
func (p *PriInventoryApi) UpdatePriProduct(c *gin.Context) {
	var PriProduct priinventory.PriProduct
	err := insbuy.GinMustBind(c, &PriProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.UpdatePriProduct(PriProduct); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindPriProduct 用id查询私人仓库商品
// @Tags PriInventoryApi
// @Summary 用id查询私人仓库商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priinventory.PriProduct true "用id查询私人仓库商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /priInventory/findPriProduct [get]
func (p *PriInventoryApi) FindPriProduct(c *gin.Context) {
	var PriProduct priinventory.PriProduct
	err := c.ShouldBindQuery(&PriProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if rePriProduct, err := priInventoryService.GetPriProduct(PriProduct.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"rePriProduct": rePriProduct}, c)
	}
}

// GetPriProductList 分页获取私人仓库商品列表
// @Tags PriInventoryApi
// @Summary 分页获取私人仓库商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priReq.PriProductSearch true "分页获取私人仓库商品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/getPriProductList [get]
func (p *PriInventoryApi) GetPriProductList(c *gin.Context) {
	var pageInfo priReq.PriProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := priInventoryService.GetPriProductInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreatePriWarehouse 创建私人仓库位置
// @Tags PriInventoryApi
// @Summary 创建私人仓库位置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.PriWarehouseCreateReq true "创建私人仓库位置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriWarehouse [post]
func (p *PriInventoryApi) CreatePriWarehouse(c *gin.Context) {
	var priWarehouse priReq.PriWarehouseCreateReq
	err := c.ShouldBindJSON(&priWarehouse)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriWarehouse(priWarehouse); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// GetPriWarehouseList 分页获取私人仓库位置列表
// @Tags PriInventoryApi
// @Summary 分页获取私人仓库位置列表 - 仓库列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priReq.PriWarehouseSearch true "分页获取私人仓库位置列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/getPriWarehouseList [get]
func (p *PriInventoryApi) GetPriWarehouseList(c *gin.Context) {
	var pageInfo priReq.PriWarehouseSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := priInventoryService.GetPriWarehouseInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetPriWarehouseDetailList 分页获取私人仓库明细列表
// @Tags PriInventoryApi
// @Summary 分页获取私人仓库明细列表 - 仓库总览
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priReq.PriWarehouseDetailSearch true "分页获取私人仓库明细列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/getPriWarehouseDetailList [get]
func (p *PriInventoryApi) GetPriWarehouseDetailList(c *gin.Context) {
	var req priReq.PriWarehouseDetailSearch
	err := insbuy.GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := priInventoryService.GetPriWarehouseDetailList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.Export() {
			if req.Export() {
				_, e := insImportService.ExcelCommonList(c, 22, list)
				if e != nil {
					return
				}
				c.Abort()
				return
			}
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetPriWarehouseLogList 分页获取私人仓库出入库记录
// @Tags PriInventoryApi
// @Summary 分页获取私人仓库出入库记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query priReq.PriWarehouseLogSearch true "分页获取私人仓库出入库记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/getPriWarehouseLogList [get]
func (p *PriInventoryApi) GetPriWarehouseLogList(c *gin.Context) {
	var pageInfo priReq.PriWarehouseLogSearch
	err := insbuy.GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := priInventoryService.GetPriWarehouseLogList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreatePriWarehouseIn 创建私人仓库入库-批量创建入库
// @Tags PriInventoryApi
// @Summary 创建私人仓库入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.CreatePriWarehouseIn true "创建私人仓库入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriWarehouseIn [post]
func (p *PriInventoryApi) CreatePriWarehouseIn(c *gin.Context) {
	var priWarehouseIn priReq.CreatePriWarehouseIn
	err := insbuy.GinMustBind(c, &priWarehouseIn)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriWarehouseIn(priWarehouseIn); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// CreatePriWarehouseOut 创建私人仓库出库
// @Tags PriInventoryApi
// @Summary 创建私人仓库出库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.CreatePriWarehouseOut true "创建私人仓库出库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriWarehouseOut [post]
func (p *PriInventoryApi) CreatePriWarehouseOut(c *gin.Context) {
	var priWarehouseOut priReq.CreatePriWarehouseOut
	err := insbuy.GinMustBind(c, &priWarehouseOut)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriWarehouseOut(priWarehouseOut); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// CreatePriWarehouseInOut 创建私人仓库出入库
// @Tags PriInventoryApi
// @Summary 出入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.CreatePriWarehouseInOut true "创建私人仓库出入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/createPriWarehouseInOut [post]
func (p *PriInventoryApi) CreatePriWarehouseInOut(c *gin.Context) {
	var priWarehouseOut priReq.CreatePriWarehouseInOut
	err := insbuy.GinMustBind(c, &priWarehouseOut)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.CreatePriWarehouseInOut(priWarehouseOut); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdatePriWarehouseInOut 修改私人仓库出入库
// @Tags PriInventoryApi
// @Summary 出入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.CreatePriWarehouseInOut true "修改私人仓库出入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /priInventory/updatePriWarehouseInOut [post]
func (p *PriInventoryApi) UpdatePriWarehouseInOut(c *gin.Context) {
	var priWarehouseOut priReq.UpdatePriWarehouseInOut
	err := insbuy.GinMustBind(c, &priWarehouseOut)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.UpdatePriWarehouseInOut(priWarehouseOut); err != nil {
		global.GVA_LOG.Error("修改库存失败!", zap.Error(err))
		response.FailWithMessage("修改库存失败"+err.Error(), c)
	} else {
		response.OkWithMessage("修改库存成功", c)
	}
}

// ApplyCheckInventory 审核私人仓库出入库
// @Tags PriInventoryApi
// @Summary 审核私人仓库出入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.ApplyCheckPriventory true "审核私人仓库出入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"审核成功"}"
// @Router /priInventory/applyCheckInventory [put]
func (p *PriInventoryApi) ApplyCheckInventory(c *gin.Context) {
	var checkPri priReq.ApplyCheckPriventory
	err := insbuy.GinMustBind(c, &checkPri)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.ApplyCheckInventory(checkPri); err != nil {
		global.GVA_LOG.Error("审核失败!", zap.Error(err))
		response.FailWithMessage("审核失败"+err.Error(), c)
	} else {
		response.OkWithMessage("审核成功", c)
	}
}

// Apply 提交审核
// @Tags PriInventoryApi
// @Summary 从草稿提交审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.ApplyPriventory true "审核私人仓库出入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"审核成功"}"
// @Router /priInventory/apply [put]
func (p *PriInventoryApi) Apply(c *gin.Context) {
	var checkPri priReq.ApplyPriventory
	err := insbuy.GinMustBind(c, &checkPri)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.Apply(checkPri); err != nil {
		global.GVA_LOG.Error("提交审核失败!", zap.Error(err))
		response.FailWithMessage("提交审核失败"+err.Error(), c)
	} else {
		response.OkWithMessage("提交审核成功", c)
	}
}

// DeletePriInventory 删除私人仓库库存
// @Tags PriInventoryApi
// @Summary 删除私人仓库库存
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body priReq.ApplyPriventory true "删除私人仓库库存"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /priInventory/deletePriInventory [delete]
func (p *PriInventoryApi) DeletePriInventory(c *gin.Context) {
	var PriProduct priReq.ApplyPriventory
	err := c.ShouldBindJSON(&PriProduct)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := priInventoryService.DeletePriInventory(PriProduct); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}
