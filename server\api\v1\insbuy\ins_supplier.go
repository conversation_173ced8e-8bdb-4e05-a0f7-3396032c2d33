package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSupplierApi struct {
}

// CreateInsSupplier 创建InsSupplier
// @Tags InsSupplier
// @Summary 创建InsSupplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SupplierReq true "创建InsSupplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSupplier/createInsSupplier [post]
func (insSupplierApi *InsSupplierApi) CreateInsSupplier(c *gin.Context) {
	var insSupplier insbuyReq.SupplierReq
	err := c.ShouldBindJSON(&insSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name": {utils.NotEmpty()},
	}
	if err := utils.Verify(insSupplier, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSupplierService.CreateInsSupplier(c, &insSupplier); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSupplier 删除InsSupplier
// @Tags InsSupplier
// @Summary 删除InsSupplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSupplier true "删除InsSupplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSupplier/deleteInsSupplier [delete]
func (insSupplierApi *InsSupplierApi) DeleteInsSupplier(c *gin.Context) {
	var insSupplier insbuy.InsSupplier
	err := c.ShouldBindJSON(&insSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSupplierService.DeleteInsSupplier(insSupplier); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSupplierByIds 批量删除InsSupplier
// @Tags InsSupplier
// @Summary 批量删除InsSupplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSupplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insSupplier/deleteInsSupplierByIds [delete]
func (insSupplierApi *InsSupplierApi) DeleteInsSupplierByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSupplierService.DeleteInsSupplierByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSupplier 更新InsSupplier
// @Tags InsSupplier
// @Summary 更新InsSupplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.SupplierReq true "更新InsSupplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSupplier/updateInsSupplier [put]
func (insSupplierApi *InsSupplierApi) UpdateInsSupplier(c *gin.Context) {
	var insSupplier insbuyReq.SupplierReq
	err := c.ShouldBindJSON(&insSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name": {utils.NotEmpty()},
	}
	if err := utils.Verify(insSupplier, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSupplierService.UpdateInsSupplier(insSupplier); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSupplier 用id查询InsSupplier
// @Tags InsSupplier
// @Summary 用id查询InsSupplier
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsSupplier true "用id查询InsSupplier"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insSupplier/findInsSupplier [get]
func (insSupplierApi *InsSupplierApi) FindInsSupplier(c *gin.Context) {
	var insSupplier insbuy.InsSupplier
	err := c.ShouldBindQuery(&insSupplier)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsSupplier, err := insSupplierService.GetInsSupplier(insSupplier.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsSupplier": reinsSupplier}, c)
	}
}

// GetInsSupplierList 分页获取InsSupplier列表
// @Tags InsSupplier
// @Summary 分页获取InsSupplier列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSupplierSearch true "分页获取InsSupplier列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSupplier/getInsSupplierList [get]
func (insSupplierApi *InsSupplierApi) GetInsSupplierList(c *gin.Context) {
	var pageInfo insbuyReq.InsSupplierSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Export() {
		list, e1 := insSupplierService.ExportInsSupplierInfoList(pageInfo)
		if e1 != nil {
			err = e1
			return
		}
		_, e := insImportService.ExcelCommonList(c, insbuy.ETSupplierList.ToInt(), list)
		if e != nil {
			return
		}
		return
	}
	if list, total, err := insSupplierService.GetInsSupplierInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
