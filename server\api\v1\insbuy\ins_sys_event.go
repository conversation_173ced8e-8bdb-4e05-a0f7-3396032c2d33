package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSysEventApi struct {
}

// WriteEvent 写入事件日志
// @Tags InsSysEvent
// @Summary 写入事件日志
// @Description 写入事件日志
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsEventReq true "创建 事件 记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"记录成功"}"
// @Router /event/write [post]
func (InsSysEventApi *InsSysEventApi) WriteEvent(c *gin.Context) {
	var req insbuyReq.InsEventReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := eventTrackingService.WriteEvent(req); err != nil {
		global.GVA_LOG.Error("记录失败!", zap.Error(err))
		response.FailWithMessage("记录失败", c)
	} else {
		response.OkWithMessage("记录成功", c)
	}
}
