// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTransferExecutionStep(db *gorm.DB, opts ...gen.DOOption) insTransferExecutionStep {
	_insTransferExecutionStep := insTransferExecutionStep{}

	_insTransferExecutionStep.insTransferExecutionStepDo.UseDB(db, opts...)
	_insTransferExecutionStep.insTransferExecutionStepDo.UseModel(&insbuy.InsTransferExecutionStep{})

	tableName := _insTransferExecutionStep.insTransferExecutionStepDo.TableName()
	_insTransferExecutionStep.ALL = field.NewAsterisk(tableName)
	_insTransferExecutionStep.ID = field.NewUint(tableName, "id")
	_insTransferExecutionStep.CreatedAt = field.NewTime(tableName, "created_at")
	_insTransferExecutionStep.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTransferExecutionStep.ExecutionId = field.NewUint(tableName, "execution_id")
	_insTransferExecutionStep.ProcessID = field.NewUint(tableName, "process_id")
	_insTransferExecutionStep.InoutId = field.NewUint(tableName, "inout_id")
	_insTransferExecutionStep.StepID = field.NewUint(tableName, "step_id")
	_insTransferExecutionStep.InoutTypeId = field.NewUint(tableName, "inout_type_id")
	_insTransferExecutionStep.PurchaseType = field.NewInt(tableName, "purchase_type")
	_insTransferExecutionStep.IsLinkPrevStep = field.NewInt(tableName, "is_link_prev_step")
	_insTransferExecutionStep.IsInStock = field.NewInt(tableName, "is_in_stock")
	_insTransferExecutionStep.InoutStatus = field.NewInt(tableName, "inout_status")
	_insTransferExecutionStep.Status = field.NewString(tableName, "status")
	_insTransferExecutionStep.ProcessedAt = field.NewTime(tableName, "processed_at")
	_insTransferExecutionStep.Ext = field.NewField(tableName, "ext")

	_insTransferExecutionStep.fillFieldMap()

	return _insTransferExecutionStep
}

type insTransferExecutionStep struct {
	insTransferExecutionStepDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	ExecutionId    field.Uint
	ProcessID      field.Uint
	InoutId        field.Uint
	StepID         field.Uint
	InoutTypeId    field.Uint
	PurchaseType   field.Int
	IsLinkPrevStep field.Int
	IsInStock      field.Int
	InoutStatus    field.Int
	Status         field.String
	ProcessedAt    field.Time
	Ext            field.Field

	fieldMap map[string]field.Expr
}

func (i insTransferExecutionStep) Table(newTableName string) *insTransferExecutionStep {
	i.insTransferExecutionStepDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTransferExecutionStep) As(alias string) *insTransferExecutionStep {
	i.insTransferExecutionStepDo.DO = *(i.insTransferExecutionStepDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTransferExecutionStep) updateTableName(table string) *insTransferExecutionStep {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ExecutionId = field.NewUint(table, "execution_id")
	i.ProcessID = field.NewUint(table, "process_id")
	i.InoutId = field.NewUint(table, "inout_id")
	i.StepID = field.NewUint(table, "step_id")
	i.InoutTypeId = field.NewUint(table, "inout_type_id")
	i.PurchaseType = field.NewInt(table, "purchase_type")
	i.IsLinkPrevStep = field.NewInt(table, "is_link_prev_step")
	i.IsInStock = field.NewInt(table, "is_in_stock")
	i.InoutStatus = field.NewInt(table, "inout_status")
	i.Status = field.NewString(table, "status")
	i.ProcessedAt = field.NewTime(table, "processed_at")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insTransferExecutionStep) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTransferExecutionStep) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["execution_id"] = i.ExecutionId
	i.fieldMap["process_id"] = i.ProcessID
	i.fieldMap["inout_id"] = i.InoutId
	i.fieldMap["step_id"] = i.StepID
	i.fieldMap["inout_type_id"] = i.InoutTypeId
	i.fieldMap["purchase_type"] = i.PurchaseType
	i.fieldMap["is_link_prev_step"] = i.IsLinkPrevStep
	i.fieldMap["is_in_stock"] = i.IsInStock
	i.fieldMap["inout_status"] = i.InoutStatus
	i.fieldMap["status"] = i.Status
	i.fieldMap["processed_at"] = i.ProcessedAt
	i.fieldMap["ext"] = i.Ext
}

func (i insTransferExecutionStep) clone(db *gorm.DB) insTransferExecutionStep {
	i.insTransferExecutionStepDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTransferExecutionStep) replaceDB(db *gorm.DB) insTransferExecutionStep {
	i.insTransferExecutionStepDo.ReplaceDB(db)
	return i
}

type insTransferExecutionStepDo struct{ gen.DO }

func (i insTransferExecutionStepDo) Debug() *insTransferExecutionStepDo {
	return i.withDO(i.DO.Debug())
}

func (i insTransferExecutionStepDo) WithContext(ctx context.Context) *insTransferExecutionStepDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTransferExecutionStepDo) ReadDB() *insTransferExecutionStepDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTransferExecutionStepDo) WriteDB() *insTransferExecutionStepDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTransferExecutionStepDo) Session(config *gorm.Session) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTransferExecutionStepDo) Clauses(conds ...clause.Expression) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTransferExecutionStepDo) Returning(value interface{}, columns ...string) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTransferExecutionStepDo) Not(conds ...gen.Condition) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTransferExecutionStepDo) Or(conds ...gen.Condition) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTransferExecutionStepDo) Select(conds ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTransferExecutionStepDo) Where(conds ...gen.Condition) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTransferExecutionStepDo) Order(conds ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTransferExecutionStepDo) Distinct(cols ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTransferExecutionStepDo) Omit(cols ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTransferExecutionStepDo) Join(table schema.Tabler, on ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTransferExecutionStepDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTransferExecutionStepDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTransferExecutionStepDo) Group(cols ...field.Expr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTransferExecutionStepDo) Having(conds ...gen.Condition) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTransferExecutionStepDo) Limit(limit int) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTransferExecutionStepDo) Offset(offset int) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTransferExecutionStepDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTransferExecutionStepDo) Unscoped() *insTransferExecutionStepDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTransferExecutionStepDo) Create(values ...*insbuy.InsTransferExecutionStep) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTransferExecutionStepDo) CreateInBatches(values []*insbuy.InsTransferExecutionStep, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTransferExecutionStepDo) Save(values ...*insbuy.InsTransferExecutionStep) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTransferExecutionStepDo) First() (*insbuy.InsTransferExecutionStep, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecutionStep), nil
	}
}

func (i insTransferExecutionStepDo) Take() (*insbuy.InsTransferExecutionStep, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecutionStep), nil
	}
}

func (i insTransferExecutionStepDo) Last() (*insbuy.InsTransferExecutionStep, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecutionStep), nil
	}
}

func (i insTransferExecutionStepDo) Find() ([]*insbuy.InsTransferExecutionStep, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTransferExecutionStep), err
}

func (i insTransferExecutionStepDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTransferExecutionStep, err error) {
	buf := make([]*insbuy.InsTransferExecutionStep, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTransferExecutionStepDo) FindInBatches(result *[]*insbuy.InsTransferExecutionStep, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTransferExecutionStepDo) Attrs(attrs ...field.AssignExpr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTransferExecutionStepDo) Assign(attrs ...field.AssignExpr) *insTransferExecutionStepDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTransferExecutionStepDo) Joins(fields ...field.RelationField) *insTransferExecutionStepDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTransferExecutionStepDo) Preload(fields ...field.RelationField) *insTransferExecutionStepDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTransferExecutionStepDo) FirstOrInit() (*insbuy.InsTransferExecutionStep, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecutionStep), nil
	}
}

func (i insTransferExecutionStepDo) FirstOrCreate() (*insbuy.InsTransferExecutionStep, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferExecutionStep), nil
	}
}

func (i insTransferExecutionStepDo) FindByPage(offset int, limit int) (result []*insbuy.InsTransferExecutionStep, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTransferExecutionStepDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTransferExecutionStepDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTransferExecutionStepDo) Delete(models ...*insbuy.InsTransferExecutionStep) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTransferExecutionStepDo) withDO(do gen.Dao) *insTransferExecutionStepDo {
	i.DO = *do.(*gen.DO)
	return i
}
