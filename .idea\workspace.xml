<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2362e54a-ff83-4b98-bbf6-9837c11141be" name="Changes" comment="fix(server): 修复管报月份解析和日期格式问题&#10;&#10;- 更新了有效日期格式列表，将日-月-年格式改为月-日-年格式&#10;- 使用 time.DateOnly 布局解析管报月份，提高代码可读性">
      <change beforePath="$PROJECT_DIR$/server/cmd/test/report_test.go" beforeDir="false" afterPath="$PROJECT_DIR$/server/cmd/test/report_test.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/config.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/server/config.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/docs/docs.go" beforeDir="false" afterPath="$PROJECT_DIR$/server/docs/docs.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/go.mod" beforeDir="false" afterPath="$PROJECT_DIR$/server/go.mod" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/server/go.sum" beforeDir="false" afterPath="$PROJECT_DIR$/server/go.sum" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="DatabaseLocalColorSettings">
    <colors>
      <entry key="3ffa7ddc-3b16-4054-a682-789fb9068215" value="Green" />
      <entry key="6fe7f565-a7a4-412f-bad8-c65ac1b20688" value="410002" />
    </colors>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Proto File" />
        <option value="HTML File" />
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../server/go1.20.1" />
  <component name="Git.Pull.Settings">
    <option name="OPTIONS">
      <set>
        <option value="REBASE" />
      </set>
    </option>
  </component>
  <component name="Git.Rebase.Settings">
    <option name="NEW_BASE" value="feature/1.0-print-list-back" />
    <option name="OPTIONS">
      <set>
        <option value="INTERACTIVE" />
      </set>
    </option>
  </component>
  <component name="Git.Settings">
    <favorite-branches>
      <branch-storage>
        <map>
          <entry type="LOCAL">
            <value>
              <list>
                <branch-info repo="$PROJECT_DIR$" source="feature/1.0" />
              </list>
            </value>
          </entry>
        </map>
      </branch-storage>
    </favorite-branches>
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/1.0" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="fix: 去除废弃的接口和方法" />
        <option name="rewordedMessage" value="fix: 去除废弃的接口和方法" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="06039f6251ce744a4c4f98fa42b454bf40e6e2f6" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/gocolly/colly/v2@v2.1.0/colly.go" root0="SKIP_INSPECTION" />
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/shakinm/xls!reader@v0.9.12/xls/xls.go" root0="SKIP_INSPECTION" />
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/silenceper/wechat/v2@v2.1.6/cache/redis.go" root0="SKIP_INSPECTION" />
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/xtulnx/jkit-go@v0.0.0-20230904071435-d1758810abfe/jtime/datetime.go" root0="SKIP_INSPECTION" />
    <setting file="file://$USER_HOME$/go/pkg/mod/github.com/xtulnx/jkit-go@v0.0.0-20240113165526-3f911461f066/jtime/datetime.go" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../php/code/ins-code/dnf.srv/common/resource/wechatpay.pem" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-09/出品单.html" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-09/领用单(1).html" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-09/领用单(3).html" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-09/领用单(4).html" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-09/领用单.html" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2023-12/取酒单_改(2).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2024-01/存酒单_改(3).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2024-01/消费清单_改(2).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2024-01/消费清单_改.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2024-07/sql_tool.go" root0="SKIP_INSPECTION" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-01/结账单_餐厅 (1).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/cs(2).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/rowt专用 - EN.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/rowt专用.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/消费清单 -KO.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/消费清单-EN(1).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../wechatData/WeChat Files/wxid_uzk1093uyro22/FileStorage/File/2025-02/结账单_餐厅 .html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../成都集祥物-工作文档/打印模板改小字体/存酒单_改(1).html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../成都集祥物-工作文档/餐厅模板/rowt专用 - EN.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../成都集祥物-工作文档/餐厅模板/rowt专用 - KO.html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../成都集祥物-工作文档/餐厅模板/结账单_餐厅 .html" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../../成都集祥物-工作文档/餐厅模板/预结单_餐厅.html" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="Make.Settings">
    <option name="path" value="C:\Program Files\mingw64\bin\make.exe" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MigrationScriptOptions">
    <option name="ignoreImplicitObjects" value="true" />
    <option name="ignoreSourceCode" value="false" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2TjcNYXlJA8fzjRwdg4zKFZu9d8" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "DefaultGoTemplateProperty": "Go File",
    "DefaultHtmlFileTemplate": "HTML File",
    "DefaultProtoTemplateProperty": "Proto File",
    "Git.Interactive.Rebase.Dialog.Commit.Message.Height": "295",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.automatic.dependencies.download": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "ToolWindowRun.ShowToolbar": "false",
    "ToolWindowServices.ShowToolbar": "false",
    "WebServerToolWindowFactoryState": "false",
    "database.data.extractors.current.export.id": "Excel (xlsx)",
    "database.data.extractors.current.id": "Comma-separated (CSV)_id",
    "git-widget-placeholder": "feature/1.0-hot-move",
    "go.import.settings.migrated": "true",
    "import_schema_mapping": "<schema-mapping>\n  <databases>\n    <list>\n      <schema-mapping-item>\n        <source-data-source-id>0f6f2916-8b3e-4316-9f76-0c7941e9b86d</source-data-source-id>\n        <source-path>schema/&quot;dnf2&quot;</source-path>\n        <target-data-source-id>a845fdad-8b66-4530-b1cd-812986fe0b33</target-data-source-id>\n        <target-path>schema/&quot;dnf&quot;</target-path>\n      </schema-mapping-item>\n    </list>\n  </databases>\n</schema-mapping>",
    "last_opened_file_path": "D:/go/code/insbuy-code/insbuy.srv",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  },
  "keyToStringList": {
    "ChangesTree.GroupingKeys": [
      "directory"
    ],
    "DatabaseDriversLRU": [
      "mysql"
    ],
    "RunConfigurationTargetLRU": [
      "6fe7f565-a7a4-412f-bad8-c65ac1b20688",
      "6fe7f565-a7a4-412f-bad8-c65ac1b20688/schema/\"ins-prod\""
    ],
    "com.intellij.ide.scratch.LRUPopupBuilder$1/": [
      "MySQL"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\config\contract_mappings" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\cmd\test" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\initialize" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\service\insbuy\insconfig" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\model\insbuy\printmodel" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\cmd\test" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\cmd\test\config" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\service\insbuy\insexcel" />
      <recent name="D:\go\code\insbuy-code\insbuy.srv\server\service\insbuy\insstore\lang" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value=".VipRechargePrint" />
      <command value="is" />
      <command value="spec" />
      <command value="calculateSaleMonthQuota" />
      <command value="v" />
      <command value="COLUMNS " />
      <command value="wa" />
      <command value="1000" />
      <command value="195302" />
      <command value="finout" />
      <command value="user" />
      <command value="135486" />
      <command value="587108" />
      <command value="sale" />
    </option>
  </component>
  <component name="RunManager" selected="Makefile Target.repo-release">
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="go build github.com/flipped-aurora/gin-vue-admin/server" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="insbuy.srv" />
      <working_directory value="$PROJECT_DIR$/server" />
      <kind value="PACKAGE" />
      <package value="github.com/flipped-aurora/gin-vue-admin/server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/server/main.go" />
      <method v="2" />
    </configuration>
    <configuration name="TestDailyFullSyncTask in github.com/flipped-aurora/gin-vue-admin/server/cmd/test" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="insbuy.srv" />
      <working_directory value="$PROJECT_DIR$/server/cmd/test" />
      <root_directory value="$PROJECT_DIR$/server" />
      <kind value="PACKAGE" />
      <package value="github.com/flipped-aurora/gin-vue-admin/server/cmd/test" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <pattern value="^\QTestDailyFullSyncTask\E$" />
      <method v="2" />
    </configuration>
    <configuration name="TestInstancesList in github.com/flipped-aurora/gin-vue-admin/server/cmd/test" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="insbuy.srv" />
      <working_directory value="$PROJECT_DIR$/server/cmd/test" />
      <root_directory value="$PROJECT_DIR$/server" />
      <kind value="PACKAGE" />
      <package value="github.com/flipped-aurora/gin-vue-admin/server/cmd/test" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <pattern value="^\QTestInstancesList\E$" />
      <method v="2" />
    </configuration>
    <configuration name="gen" type="MAKEFILE_TARGET_RUN_CONFIGURATION" factoryName="Makefile" temporary="true">
      <makefile filename="$PROJECT_DIR$/server/Makefile" target="gen" workingDirectory="" arguments="">
        <envs />
      </makefile>
      <method v="2" />
    </configuration>
    <configuration name="repo-release" type="MAKEFILE_TARGET_RUN_CONFIGURATION" factoryName="Makefile" temporary="true">
      <makefile filename="$PROJECT_DIR$/server/Makefile" target="repo-release" workingDirectory="" arguments="">
        <envs />
      </makefile>
      <method v="2" />
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Makefile Target.repo-release" />
        <item itemvalue="Makefile Target.gen" />
        <item itemvalue="Go Test.TestInstancesList in github.com/flipped-aurora/gin-vue-admin/server/cmd/test" />
        <item itemvalue="Go Test.TestDailyFullSyncTask in github.com/flipped-aurora/gin-vue-admin/server/cmd/test" />
        <item itemvalue="Go Build.go build github.com/flipped-aurora/gin-vue-admin/server" />
      </list>
    </recent_temporary>
  </component>
  <component name="SQLScriptSettings">
    <option name="matrix">
      <map>
        <entry key="MYSQL.CreateComplete" value="CreateIfNotExists=1" />
      </map>
    </option>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="executable:docker" />
    <option featureType="dependencySupport" implementationName="executable:kubectl" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-INS20250312072" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-INS20250224061" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-ins-data" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-ktvme-scan-codde" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-payler-dit" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-hot-exprot" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-hotfix" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-discount-price" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feature/1.0-discount-limit" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="Peterzhang" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="71" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="138" />
                  <entry key="Table.Space.CommitStatus.ColumnIdWidth" value="77" />
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="LOCAL_CHANGES_DETAILS_PREVIEW_SHOWN" value="false" />
    <MESSAGE value="feat: 添加集团管报筛选项功能&#10;&#10;- 在路由中添加了 financial/filters 接口&#10;- 实现了 RegionalFinancialFilters 函数，提供集团管报的筛选项&#10;- 更新了相关结构体和搜索参数，支持新的筛选条件" />
    <MESSAGE value="fix: 更新财务报告主体筛选条件&#10;&#10;- 将北京地区的筛选条件从 &quot;area: beijing&quot; 修改为 &quot;report_entity2: 总部-北京&quot;&#10;- 新增海外市场的筛选条件 &quot;report_entity2: 总部-海外市场&quot;" />
    <MESSAGE value="feat: 新增计算型费用分类功能&#10;&#10;- 在 CostTypeDto 和 CostTypeConfigItem 中添加计算型分类相关字段&#10;- 实现计算型分类的处理逻辑，包括依赖关系解析和各种计算类型的支持&#10;- 优化数据结构和算法，提高计算效率" />
    <MESSAGE value="fix: 优化财务报表中的计算型分类处理&#10;&#10;- 排除计算型分类的一级分类处理&#10;- 在构建树形结构时忽略计算型分类&#10;- 实现按排序顺序插入计算型分类的功能" />
    <MESSAGE value="fix: 移除乐园广告相关的冗余代码&#10;&#10;- 删除了 FinancialGroupItem 切片中与&quot;乐园-广告&quot;相关的条目&#10;- 优化了代码结构，减少了不必要的数据定义" />
    <MESSAGE value="refactor(insbuy): 重构计算型分类处理逻辑&#10;&#10;- 移除了原有的计算类型常量和复杂计算逻辑&#10;- 简化了 CostTypeConfigItem 结构，删除了 DependsOn 和 CalculationType 字段&#10;- 新增了 SimpleJSONProcessor 结构和 ProcessCalculatedCategoriesJSON 方法来处理计算" />
    <MESSAGE value="feat: 报表费用分类配置项增加计算公式字段" />
    <MESSAGE value="feat: 添加简化 JSON 公式处理器&#10;&#10;- 实现了一个新的简化 JSON 公式处理器，用于处理财务摘要中的计算型分类- 新增了公式解析、计算、验证等功能&#10;- 支持百分比计算和基本的四则运算&#10;- 具备依赖关系分析和拓扑排序能力&#10;- 提供了公式 JSON 的创建和验证方法" />
    <MESSAGE value="feat: 实现门票报表查询功能并支持特殊店铺数据获取&#10;&#10;- 实现了 StoreTicket 数据源的查询方法，支持按日期和店铺筛选&#10;- 新增 StoreTicketReportList 函数，用于获取门票报表列表&#10;- 在预约桌位功能中增加对门票报表数据的支持，用于特殊店铺（如&quot;ins&quot;）" />
    <MESSAGE value="fix: 修复财务报表计算中的运算符优先级问题&#10;&#10;- 重构 calculateBinaryExpression 函数，正确处理运算符优先级和括号&#10;- 添加 findOperatorOutsideParentheses 和 isBalancedParentheses辅助函数" />
    <MESSAGE value="feat: 添加营业日字段并优化退单处理&#10;&#10;- 在 InsOrderInfo 和 WarehouseOut 模型中添加 BusinessDay 字段- 更新退单处理逻辑，支持指定营业日&#10;- 优化赠品和退单类型的处理流程" />
    <MESSAGE value="feat: 增加报表实体和二级业务实体的查询功能&#10;&#10;- 在查询费用项和收入项时，增加了对报表实体（ReportEntity）和二级业务实体（ReportEntity2）的筛选条件&#10;- 新增了对应的数据库查询语句，以支持这两个新查询条件" />
    <MESSAGE value="fix: 优化计算型分类的处理逻辑&#10;&#10;- 支持计算字段之间的相互引用&#10;-改进依赖关系处理，区分基础数据依赖和计算字段依赖&#10;- 增加缺失依赖的验证和提示&#10;- 优化拓扑排序算法，提高处理效率" />
    <MESSAGE value="fix: 添加订位人字段到挂账列表&#10;&#10;- 在 ExportAcctList 结构体中添加 SaleName 字段，用于显示订位人&#10;- 在生成挂账报表时，将 SalesName 字段添加到导出数据中" />
    <MESSAGE value="feat: 添加飞书应用配置" />
    <MESSAGE value="fix: 修改飞书应用配置项名称" />
    <MESSAGE value="feat: 查询商品成本卡-返回对应的库存" />
    <MESSAGE value="fix: 商品销售明细调整仓库名称字段位置" />
    <MESSAGE value="feat: 添加飞书合同管理相关模型和测试&#10;&#10;- 新增 InsContract、InsContractComment、InsContractFile、InsContractTask、InsContractTimeline、InsContractSyncLog 等模型&#10;- 添加飞书合同管理相关的测试用例- 更新数据库迁移文件，增加飞书合同管理相关表结构&#10;- 修改配置文件，增加飞书审批代码配置" />
    <MESSAGE value="feat: 添加飞书客户端封装&#10;&#10;- 新增 FeishuClient 结构体和相关方法，用于与飞书 API 进行交互&#10;- 实现了获取合同列表和合同详情的功能&#10;- 添加了批量获取合同详情的方法&#10;-集成了日志记录和错误处理" />
    <MESSAGE value="fix: 添加换台功能并优化开台记录" />
    <MESSAGE value="fix: 转台状态调整" />
    <MESSAGE value="test(server): 添加合同数据转换和导出功能的单元测试&#10;&#10;- 新增 AttachmentInfo 转换测试用例&#10;- 新增配置解析测试用例&#10;- 新增 合同数据转换测试用例&#10;- 新增 通用数据导出测试用例&#10;- 新增 批量转换和导出测试用例" />
    <MESSAGE value="fix(server): 修复管报月份解析和日期格式问题&#10;&#10;- 更新了有效日期格式列表，将日-月-年格式改为月-日-年格式&#10;- 使用 time.DateOnly 布局解析管报月份，提高代码可读性" />
    <MESSAGE value="fix: 扫码下单分类排序" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: 扫码下单分类排序" />
  </component>
  <component name="VgoProject">
    <environment>
      <map>
        <entry key="GOPROXY" value="https://goproxy.cn,direct" />
      </map>
    </environment>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/server/service/insbuy/ins_payment.go</url>
          <line>321</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/server/cmd/test/report_test.go</url>
          <line>26</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/insbuy_srv$Test_in_github_com_flipped_aurora_gin_vue_admin_server_cmd_test.out" NAME="Test报告 in github.com/flipped-aurora/gin-vue-admin/server/cmd/test Coverage Results" MODIFIED="1731930445581" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="GoCoverage" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>