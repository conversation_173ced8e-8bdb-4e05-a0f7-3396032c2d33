[insbuy]2025/07/30 - 10:06:36.206	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:08:49.369	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:10:32.801	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:14:21.024	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:15:13.892	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:17:27.667	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:20:31.441	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:22:56.186	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:25:39.574	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:28:00.015	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:30:16.213	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:35:23.489	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:55:57.752	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:55:57.991	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 10:55:57.991	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "0e54e09f5627b685ea028043a27a7562", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 10:56:32.251	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:56:32.430	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 10:56:32.430	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "c53879c39ef1675084c9b34c5c3ce8b8", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 10:58:00.889	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 10:58:01.194	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 business_type 失败: 字段 widget16701426685000001 不存在"}
[insbuy]2025/07/30 - 10:58:01.194	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "c323e8b929029625c6e3bb479fdcebd1", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 business_type 失败: 字段 widget16701426685000001 不存在"}
[insbuy]2025/07/30 - 11:02:22.026	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:02:22.543	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:02:22.543	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "66c046e0a811ab666a747e1e359647d0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:06:51.614	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:06:51.846	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:06:51.846	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "7893173a351d34177d83856c42c50a42", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:12:37.701	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:12:38.017	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "设置字段 attachments 失败: 未知字段: attachments"}
[insbuy]2025/07/30 - 11:12:38.017	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "1d3b12fc6f9603ca835ff85864f04cdd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "设置字段 attachments 失败: 未知字段: attachments"}
[insbuy]2025/07/30 - 11:17:34.441	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:17:34.623	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:17:34.623	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "ffe572522a615fbced6e8650ef425a57", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:19:50.490	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:19:50.845	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "设置字段 attachments 失败: 未知字段: attachments"}
[insbuy]2025/07/30 - 11:19:50.845	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "9decf2e3b8afd702b5647272871196cd", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "设置字段 attachments 失败: 未知字段: attachments"}
[insbuy]2025/07/30 - 11:25:23.569	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:25:23.773	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:25:23.773	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "88194a0e28f9aa73939db65f3217464a", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:26:16.977	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:26:17.181	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 11:26:17.181	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "3cd1d5199f454ea480c27f5290807396", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 11:27:15.699	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:27:15.905	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:27:15.905	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "5799831126e0e7f2fee4073b24cfe001", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 contract_sign_amount 失败: 字段 widget16671961125560001 不存在"}
[insbuy]2025/07/30 - 11:28:55.847	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:28:56.032	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 business_type 失败: 字段 widget16701426685000001 不存在"}
[insbuy]2025/07/30 - 11:28:56.032	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "8b9ad981537f2c908ca4c85c425b8a49", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 business_type 失败: 字段 widget16701426685000001 不存在"}
[insbuy]2025/07/30 - 11:29:38.493	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:29:38.711	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 11:29:38.711	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "71c211046e84a738c585c46b216ccb31", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 payment_currency 失败: 字段 widget16799083691540001 不存在"}
[insbuy]2025/07/30 - 11:30:20.542	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:30:20.738	[31merror[0m	insbuy/contract_transformer.go:224	数据转换失败	{"instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "approval_code": "PAYMENT_REQUEST_APPROVAL", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:30:20.738	[31merror[0m	test/contract_transformer_test.go:64	数据转换失败	{"traceId": "30b081df10ded9a12949ac28a3f74ef0", "task": "TestWithDatabaseData", "instance_code": "C457E806-AC54-46AB-ADA7-90E3BFBEBC92", "error": "提取必填字段 current_request_amount 失败: 字段 widget1 不存在"}
[insbuy]2025/07/30 - 11:31:03.730	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:33:37.196	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 11:33:06.568	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 13:11:06.589	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:08:18.946	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:09:41.610	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:02:07.487	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:35:33.636	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:44:16.974	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:44:17.171	[31merror[0m	test/contract_transformer_test.go:586	未找到匹配的映射规则	{"traceId": "f1db1e2b63f4972dadfeb12d3e7c15b4", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "available_rules": 1}
[insbuy]2025/07/30 - 14:44:17.172	[31merror[0m	test/contract_transformer_test.go:454	选择映射规则失败	{"traceId": "331ef5dcf264209fd4a83c8beb245cdc", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "error": "未找到审批代码 F523F053-7AC6-4280-A4E7-B35E0C0431B5 的映射规则，可用规则: [PAYMENT_REQUEST_APPROVAL]"}
[insbuy]2025/07/30 - 14:26:40.428	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:26:40.624	[31merror[0m	test/contract_transformer_test.go:586	未找到匹配的映射规则	{"traceId": "56f66731d91c762a4db8fbbb0f04137a", "task": "SelectMappingRule", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "available_rules": 1}
[insbuy]2025/07/30 - 14:26:40.624	[31merror[0m	test/contract_transformer_test.go:454	选择映射规则失败	{"traceId": "85da23c230c0a0e3e86258e904cd4c35", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "error": "未找到审批代码 F523F053-7AC6-4280-A4E7-B35E0C0431B5 的映射规则，可用规则: [PAYMENT_REQUEST_APPROVAL]"}
[insbuy]2025/07/30 - 14:28:49.595	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:29:46.881	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:33:35.596	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:37:42.934	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:42:39.117	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:44:42.591	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:47:52.337	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:53:03.454	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 14:57:27.890	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:02:49.568	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:03:53.161	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:05:35.042	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:06:48.347	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:08:44.293	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:08:44.477	[31merror[0m	insbuy/contract_transformer.go:230	数据转换失败	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "error": "设置字段 tax_excluded_amount 失败: 未知字段: tax_excluded_amount"}
[insbuy]2025/07/30 - 15:08:44.477	[31merror[0m	test/contract_transformer_test.go:477	数据转换失败	{"traceId": "9817773999b01014f0e2113265383f02", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "error": "设置字段 tax_excluded_amount 失败: 未知字段: tax_excluded_amount"}
[insbuy]2025/07/30 - 15:11:00.853	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:11:01.070	[31merror[0m	insbuy/contract_transformer.go:230	数据转换失败	{"instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "error": "设置字段 tax_excluded_amount 失败: 未知字段: tax_excluded_amount"}
[insbuy]2025/07/30 - 15:11:01.070	[31merror[0m	test/contract_transformer_test.go:477	数据转换失败	{"traceId": "c3be057405c3e9be8355924dcd2ec770", "task": "TestUniversalDataExport", "instance_code": "FA4BF10C-6B76-465F-8D52-389417004EE9", "error": "设置字段 tax_excluded_amount 失败: 未知字段: tax_excluded_amount"}
[insbuy]2025/07/30 - 15:12:58.563	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:17:35.033	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/30 - 15:26:38.674	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
