package insconfig

import (
	"context"
	"gorm.io/datatypes"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"gorm.io/gen"
	"gorm.io/gen/field"
)

type ReportGroupCostType struct {
}

// ReportGroupCostTypeItem 报表费用分类配置项
type ReportGroupCostTypeItem struct {
	ConfigId           uint                       `json:"config_id"`           // 配置ID
	Id                 uint                       `json:"id"`                  // 详情ID
	StoreId            uint                       `json:"store_id"`            // 店铺ID
	Module             string                     `json:"module"`              // 模块名
	ScopeType          uint                       `json:"scope_type"`          // 范围类型
	KeyPath            string                     `json:"key_path"`            // 配置键路径
	ItemKey            string                     `json:"item_key"`            // 配置项键
	ItemIndex          uint                       `json:"item_index"`          // 排序索引
	Level              uint                       `json:"level"`               // 分类级别 1:一级 2:二级 3:三级
	ParentId           uint                       `json:"parent_id"`           // 父级ID
	CategoryName       string                     `json:"category_name"`       // 分类名称
	IsActive           bool                       `json:"is_active"`           // 是否启用
	IsCalculated       uint                       `json:"is_calculated"`       // 是否计算
	CalculationFormula datatypes.JSON             `json:"calculation_formula"` // 计算公式
	SortOrder          uint                       `json:"sort_order"`          // 排序
	Remark             string                     `json:"remark"`              // 备注
	Children           []*ReportGroupCostTypeItem `json:"children" gorm:"-"`
}

func (r ReportGroupCostType) GetDataList(ctx context.Context, q *query.Query, params ListParams) (resp []any, total int64, err error) {
	limit := params.PageSize
	offset := params.PageSize * (params.PageNum - 1)

	d, f := ReportGroupCostTypeSub(ctx, q, params, "t")
	dao1 := d.GetDaoDirect()

	var conditions []gen.Condition
	{
		if params.GroupName != "" {
			conditions = append(conditions, f.CategoryName.Like("%"+params.GroupName+"%"))
		}
		if params.ParentId != 0 {
			conditions = append(conditions, f.ParentId.Eq(params.ParentId))
		}
	}
	items := make([]ReportGroupCostTypeItem, 0)
	dao1 = dao1.Where(conditions...)
	jgorm.SelectAppend(dao1.(*gen.DO),
		f.ConfigId.As("config_id"),
		f.ConfigDId.As("id"),
		f.Module,
		f.StoreId,
		f.KeyPath,
		f.ScopeType,
		f.ItemKey,
		f.ItemIndex,
		f.Level,
		f.ParentId,
		f.CategoryName,
		f.IsActive,
		f.IsCalculated,
		f.CalculationFormula,
		f.SortOrder,
		f.Remark,
	)
	total, err = dao1.Count()
	if err != nil {
		return
	}
	if params.PageSize > 0 && params.PageSize != -1 {
		dao1 = dao1.Limit(limit).Offset(offset)
	}
	dao1 = dao1.Order(f.Level, f.SortOrder, f.ItemIndex)
	err = dao1.Scan(&items)
	if err != nil {
		return
	}
	itemMap := make(map[uint]*ReportGroupCostTypeItem)
	rootNodes := make([]*ReportGroupCostTypeItem, 0)
	// 1. 先将所有项目初始化并索引到map中
	for i := range items {
		items[i].Children = make([]*ReportGroupCostTypeItem, 0)
		itemMap[items[i].Id] = &items[i]
	}

	// 2. 遍历每个项目，找到父子关系
	for i := range items {
		node := &items[i]
		if node.Level == 1 || node.ParentId == 0 {
			rootNodes = append(rootNodes, node)
		} else {
			if parentNode, exists := itemMap[node.ParentId]; exists {
				parentNode.Children = append(parentNode.Children, node)
			}
		}
	}

	// 3. 转换为响应结构
	resp = make([]any, len(rootNodes))
	for i, node := range rootNodes {
		resp[i] = node
	}
	return
}

// 子查询封装
type ReportGroupCostTypeFields struct {
	jgorm.SimpleFields
	ConfigDId          field.Uint
	ConfigId           field.Uint
	StoreId            field.Uint
	Module             field.String
	ScopeType          field.Uint
	KeyPath            field.String
	ItemKey            field.String
	ItemIndex          field.Uint
	Level              field.Uint
	ParentId           field.Uint
	CategoryName       field.String
	IsActive           field.Bool
	IsCalculated       field.Uint
	CalculationFormula field.Field
	SortOrder          field.Uint
	Remark             field.String
}

func (i *ReportGroupCostTypeFields) UpdateTableName(table string) {
	i.SimpleFields.UpdateTableName(table)
	i.ConfigId = field.NewUint(table, "config_id")
	i.ConfigDId = field.NewUint(table, "config_d_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.Module = field.NewString(table, "module")
	i.ScopeType = field.NewUint(table, "scope_type")
	i.KeyPath = field.NewString(table, "key_path")
	i.ItemKey = field.NewString(table, "item_key")
	i.ItemIndex = field.NewUint(table, "item_index")
	i.Level = field.NewUint(table, "level")
	i.ParentId = field.NewUint(table, "parent_id")
	i.CategoryName = field.NewString(table, "category_name")
	i.IsActive = field.NewBool(table, "is_active")
	i.IsCalculated = field.NewUint(table, "is_calculated")
	i.CalculationFormula = field.NewField(table, "calculation_formula")
	i.SortOrder = field.NewUint(table, "sort_order")
	i.Remark = field.NewString(table, "remark")
}

func ReportGroupCostTypeSub(ctx context.Context, q *query.Query, p ListParams, alias string) (d jgorm.DaoWrapper, f *ReportGroupCostTypeFields) {
	f = &ReportGroupCostTypeFields{}
	f.UpdateTableName(alias)
	dbCenter := q.InsConfigCenter
	dbDetails := q.InsConfigCenterDetail

	var condition []gen.Condition
	{
		condition = append(condition, dbCenter.DeletedAt.IsNull())
		condition = append(condition, dbDetails.DeletedAt.IsNull())
		condition = append(condition, dbCenter.Module.Eq(insbuy.ModuleReport.ToString()))
		condition = append(condition, dbCenter.KeyPath.Eq(insbuy.ReportGroupCostType.ToString()))
		condition = append(condition, dbCenter.ScopeType.Eq(0))
	}

	dao1 := dbCenter.
		Where(condition...).
		LeftJoin(dbDetails, dbCenter.ID.EqCol(dbDetails.ConfigId)).
		Select(
			dbCenter.ID.As(CN(f.ConfigId)),
			dbCenter.StoreId.As(CN(f.StoreId)),
			dbCenter.Module.As(CN(f.Module)),
			dbCenter.ScopeType.As(CN(f.ScopeType)),
			dbCenter.KeyPath.As(CN(f.KeyPath)),
			dbCenter.Remark.As(CN(f.Remark)),
		)

	jgorm.SelectAppend(&dao1.DO,
		dbDetails.ID.As(CN(f.ConfigDId)),
		dbDetails.ItemKey.As(CN(f.ItemKey)),
		dbDetails.ItemIndex.As(CN(f.ItemIndex)),
		dbDetails.ItemIndex.As(CN(f.ParentId)),
		jgorm.ClauseExpr("?->>'$.level' as "+CN(f.Level), dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.category_name' as "+CN(f.CategoryName), dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.is_active' as "+CN(f.IsActive), dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.sort_order' as "+CN(f.SortOrder), dbDetails.ItemValue),
		jgorm.ClauseExpr("?->>'$.is_calculated' as "+CN(f.IsCalculated), dbDetails.ItemValue),
		jgorm.ClauseExpr("?->'$.calculation_formula' as "+CN(f.CalculationFormula), dbDetails.ItemValue),
	)

	return jgorm.NewDaoWrapperByJoin(&dao1.DO, alias), f
}
