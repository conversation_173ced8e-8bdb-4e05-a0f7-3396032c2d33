# 财务交叉统计报表 - 公式表达式系统设计文档

## 概述

本文档详细介绍了财务交叉统计报表中计算型分类的公式表达式系统的重新设计方案。新系统采用统一的公式表达式语法，提供更直观、灵活的财务计算能力。

## 设计目标

### 1. 统一性
- 废弃原有的多种计算方式（CalculationType + DependsOn + CalculationFormula）
- 采用单一的公式表达式字段（CalculationFormula）
- 所有计算逻辑通过统一的公式处理器处理

### 2. 直观性
- 支持类似数学表达式的语法：`[收入类合计] - [成本类合计]`
- 支持简洁的ID引用语法：`#1 - #2`
- 支持函数调用语法：`PERCENTAGE([利润], [收入])`

### 3. 扩展性
- 模块化的公式处理架构
- 支持新增运算符和函数
- 为复杂计算场景预留扩展空间

## 核心架构

### 1. FormulaProcessor 公式处理器

```go
type FormulaProcessor struct {
    categoryMap map[uint]string // ID -> 分类名称映射
    nameMap     map[string]uint // 分类名称 -> ID映射
}
```

**主要方法：**
- `Parse(formula string) -> Expression` - 解析公式为表达式树
- `Calculate(expression, dataMap) -> Result` - 计算表达式结果
- `ExtractDependencies(formula) -> []uint` - 提取依赖关系

### 2. Expression 表达式结构

```go
type Expression struct {
    Type     string        // 表达式类型：BINARY_OP, FUNCTION, REFERENCE, CONSTANT
    Operator string        // 操作符：+, -, *, /, %
    Left     *Expression   // 左操作数
    Right    *Expression   // 右操作数
    Function string        // 函数名：SUM, PERCENTAGE, AVG等
    Args     []Expression  // 函数参数
    RefID    uint          // 引用的分类ID
    Value    jtypes.JPrice // 常量值
}
```

### 3. 简化的配置结构

```go
type CostTypeConfigItem struct {
    Id                 uint   `json:"id"`
    Level              uint   `json:"level"`
    ParentId           uint   `json:"parent_id"`
    CategoryName       string `json:"category_name"`
    CategoryCode       string `json:"category_code"`
    SortOrder          uint   `json:"sort_order"`
    IsCalculated       bool   `json:"is_calculated"`       // 是否为计算型分类
    CalculationFormula string `json:"calculation_formula"` // 统一的公式表达式
}
```

## 公式语法规范

### 1. 基础语法

#### 分类引用
```
[分类名称]     # 通过分类名称引用，如：[收入类合计]
#ID           # 通过分类ID引用，如：#1
```

#### 运算符
```
+             # 加法
-             # 减法  
*             # 乘法
/             # 除法
%             # 百分比（除法后乘以100）
```

#### 括号
```
([表达式])    # 改变运算优先级
```

### 2. 函数调用

#### SUM 求和函数
```
SUM([分类1], [分类2], [分类3])
SUM(#1, #2, #3)
```

#### PERCENTAGE 百分比函数
```
PERCENTAGE([利润], [收入])    # 利润/收入*100%
PERCENTAGE(#100, #1)
```

#### AVG 平均值函数
```
AVG([分类1], [分类2], [分类3])
```

#### MAX/MIN 最值函数
```
MAX([分类1], [分类2])
MIN([分类1], [分类2])
```

### 3. 复合表达式

```
[收入类合计] - [成本类合计] - [费用类合计]
([收入类合计] - [成本类合计]) / [收入类合计] * 100
SUM([管理费用], [销售费用]) + [财务费用]
```

## 配置示例

### 1. 基础利润计算

```json
{
  "id": 100,
  "category_name": "经营利润",
  "level": 1,
  "parent_id": 0,
  "is_calculated": true,
  "calculation_formula": "[收入类合计] - [成本类合计]",
  "sort_order": 900
}
```

### 2. 多项求和

```json
{
  "id": 101,
  "category_name": "期间费用合计",
  "level": 1,
  "parent_id": 0,
  "is_calculated": true,
  "calculation_formula": "SUM([管理费用], [销售费用], [财务费用])",
  "sort_order": 910
}
```

### 3. 百分比计算

```json
{
  "id": 102,
  "category_name": "毛利率",
  "level": 1,
  "parent_id": 0,
  "is_calculated": true,
  "calculation_formula": "PERCENTAGE([经营利润], [收入类合计])",
  "sort_order": 950
}
```

### 4. 复合计算

```json
{
  "id": 103,
  "category_name": "净利润",
  "level": 1,
  "parent_id": 0,
  "is_calculated": true,
  "calculation_formula": "[经营利润] - [期间费用合计] - [税费]",
  "sort_order": 920
}
```

## 向后兼容性

### 1. 自动转换机制

系统自动识别并转换旧格式公式：

```
旧格式: "ADD:1,2,3"        -> 新格式: "#1 + #2 + #3"
旧格式: "SUBTRACT:1,2"     -> 新格式: "#1 - #2"
旧格式: "PERCENTAGE:1,2"   -> 新格式: "PERCENTAGE(#1, #2)"
```

### 2. 迁移策略

```go
// 在FormulaProcessor.Parse方法中自动处理
func (fp *FormulaProcessor) Parse(formula string) (*Expression, error) {
    // 检查是否为旧格式，如果是则转换
    if converted, ok := fp.convertLegacyFormat(formula); ok {
        formula = converted
    }
    
    // 继续解析新格式
    return fp.parseExpression(formula)
}
```

## 实现细节

### 1. 表达式解析流程

```
输入公式 -> 词法分析 -> 语法分析 -> 表达式树 -> 依赖提取
```

### 2. 计算执行流程

```
表达式树 -> 递归计算 -> 月度数据计算 -> 总计计算 -> 结果返回
```

### 3. 依赖关系处理

```go
// 自动提取依赖关系
dependencies := processor.ExtractDependencies("[收入类合计] - [成本类合计]")
// 结果: [1, 2] (假设收入类合计ID=1, 成本类合计ID=2)
```

### 4. 错误处理

- **解析错误**: 记录错误日志，跳过该计算项
- **计算错误**: 记录错误日志，返回零值
- **依赖缺失**: 记录警告日志，跳过该计算项

## 性能优化

### 1. 表达式缓存
- 解析后的表达式树可以缓存
- 避免重复解析相同公式

### 2. 依赖排序
- 自动分析依赖关系
- 按拓扑顺序执行计算

### 3. 并行计算
- 无依赖关系的计算项可以并行执行
- 提高大量计算项的处理效率

## 扩展功能

### 1. 条件计算
```
IF([收入类合计] > 1000000, [收入类合计] * 0.1, [收入类合计] * 0.05)
```

### 2. 时间函数
```
PREVIOUS_MONTH([收入类合计])    # 上月数据
YEAR_TO_DATE([收入类合计])     # 年初至今累计
```

### 3. 统计函数
```
VARIANCE([分类1], [分类2], [分类3])  # 方差
STDDEV([分类1], [分类2], [分类3])    # 标准差
```

## 测试验证

### 1. 单元测试
- 公式解析测试
- 表达式计算测试
- 依赖关系提取测试

### 2. 集成测试
- 完整计算流程测试
- 向后兼容性测试
- 错误处理测试

### 3. 性能测试
- 大量公式解析性能
- 复杂表达式计算性能
- 内存使用情况

## 使用指南

### 1. 配置计算型分类

```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "item_value": {
    "id": 100,
    "category_name": "经营利润",
    "is_calculated": true,
    "calculation_formula": "[收入类合计] - [成本类合计]"
  }
}
```

### 2. API调用

```go
// API调用方式保持不变
params := FinancialWarehouseParams{
    StartDate: time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
    EndDate:   time.Date(2025, 12, 1, 0, 0, 0, 0, time.Local),
}

resp, err := RegionalFinancialSummaryReport(ctx, query.Q, params)
```

### 3. 响应数据

```json
{
  "list": [
    {
      "category_id": 100,
      "category_name": "经营利润",
      "2025-01": 400000,    // 自动计算结果
      "2025-02": 500000,
      "total_amount": 900000
    }
  ]
}
```

## 总结

新的公式表达式系统提供了：

1. **统一的语法**: 所有计算都通过公式表达式定义
2. **直观的表达**: 类似数学表达式的语法，易于理解和维护
3. **强大的功能**: 支持复杂的财务计算场景
4. **良好的兼容性**: 自动转换旧格式，平滑升级
5. **优秀的扩展性**: 模块化设计，易于添加新功能

这个系统为财务交叉统计报表提供了强大而灵活的计算能力，满足各种复杂的财务分析需求。
