// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductStore(db *gorm.DB, opts ...gen.DOOption) insProductStore {
	_insProductStore := insProductStore{}

	_insProductStore.insProductStoreDo.UseDB(db, opts...)
	_insProductStore.insProductStoreDo.UseModel(&insbuy.InsProductStore{})

	tableName := _insProductStore.insProductStoreDo.TableName()
	_insProductStore.ALL = field.NewAsterisk(tableName)
	_insProductStore.Id = field.NewUint(tableName, "id")
	_insProductStore.StoreId = field.NewUint(tableName, "store_id")
	_insProductStore.ProductId = field.NewUint(tableName, "product_id")
	_insProductStore.Name = field.NewString(tableName, "name")
	_insProductStore.ShopPrice = field.NewFloat64(tableName, "shop_price")
	_insProductStore.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductStore.UpdatedAt = field.NewTime(tableName, "updated_at")

	_insProductStore.fillFieldMap()

	return _insProductStore
}

type insProductStore struct {
	insProductStoreDo

	ALL       field.Asterisk
	Id        field.Uint
	StoreId   field.Uint
	ProductId field.Uint
	Name      field.String
	ShopPrice field.Float64
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (i insProductStore) Table(newTableName string) *insProductStore {
	i.insProductStoreDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductStore) As(alias string) *insProductStore {
	i.insProductStoreDo.DO = *(i.insProductStoreDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductStore) updateTableName(table string) *insProductStore {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.Name = field.NewString(table, "name")
	i.ShopPrice = field.NewFloat64(table, "shop_price")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")

	i.fillFieldMap()

	return i
}

func (i *insProductStore) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductStore) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.Id
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["name"] = i.Name
	i.fieldMap["shop_price"] = i.ShopPrice
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
}

func (i insProductStore) clone(db *gorm.DB) insProductStore {
	i.insProductStoreDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductStore) replaceDB(db *gorm.DB) insProductStore {
	i.insProductStoreDo.ReplaceDB(db)
	return i
}

type insProductStoreDo struct{ gen.DO }

func (i insProductStoreDo) Debug() *insProductStoreDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductStoreDo) WithContext(ctx context.Context) *insProductStoreDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductStoreDo) ReadDB() *insProductStoreDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductStoreDo) WriteDB() *insProductStoreDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductStoreDo) Session(config *gorm.Session) *insProductStoreDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductStoreDo) Clauses(conds ...clause.Expression) *insProductStoreDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductStoreDo) Returning(value interface{}, columns ...string) *insProductStoreDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductStoreDo) Not(conds ...gen.Condition) *insProductStoreDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductStoreDo) Or(conds ...gen.Condition) *insProductStoreDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductStoreDo) Select(conds ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductStoreDo) Where(conds ...gen.Condition) *insProductStoreDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductStoreDo) Order(conds ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductStoreDo) Distinct(cols ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductStoreDo) Omit(cols ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductStoreDo) Join(table schema.Tabler, on ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductStoreDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductStoreDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductStoreDo) Group(cols ...field.Expr) *insProductStoreDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductStoreDo) Having(conds ...gen.Condition) *insProductStoreDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductStoreDo) Limit(limit int) *insProductStoreDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductStoreDo) Offset(offset int) *insProductStoreDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductStoreDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductStoreDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductStoreDo) Unscoped() *insProductStoreDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductStoreDo) Create(values ...*insbuy.InsProductStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductStoreDo) CreateInBatches(values []*insbuy.InsProductStore, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductStoreDo) Save(values ...*insbuy.InsProductStore) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductStoreDo) First() (*insbuy.InsProductStore, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStore), nil
	}
}

func (i insProductStoreDo) Take() (*insbuy.InsProductStore, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStore), nil
	}
}

func (i insProductStoreDo) Last() (*insbuy.InsProductStore, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStore), nil
	}
}

func (i insProductStoreDo) Find() ([]*insbuy.InsProductStore, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductStore), err
}

func (i insProductStoreDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductStore, err error) {
	buf := make([]*insbuy.InsProductStore, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductStoreDo) FindInBatches(result *[]*insbuy.InsProductStore, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductStoreDo) Attrs(attrs ...field.AssignExpr) *insProductStoreDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductStoreDo) Assign(attrs ...field.AssignExpr) *insProductStoreDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductStoreDo) Joins(fields ...field.RelationField) *insProductStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductStoreDo) Preload(fields ...field.RelationField) *insProductStoreDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductStoreDo) FirstOrInit() (*insbuy.InsProductStore, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStore), nil
	}
}

func (i insProductStoreDo) FirstOrCreate() (*insbuy.InsProductStore, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStore), nil
	}
}

func (i insProductStoreDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductStore, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductStoreDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductStoreDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductStoreDo) Delete(models ...*insbuy.InsProductStore) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductStoreDo) withDO(do gen.Dao) *insProductStoreDo {
	i.DO = *do.(*gen.DO)
	return i
}
