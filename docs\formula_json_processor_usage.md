# 简化JSON公式处理器 - 使用指南

## 概述

修复后的 `formula_json_processor.go` 提供了完整的简化JSON公式处理功能，支持复杂的依赖关系处理、错误处理和性能优化。

## 核心功能

### 1. 完善的辅助函数

#### createDataMap() 函数
```go
// 将 FinancialSummaryItem 数组转换为 ID 映射
func createDataMap(items []FinancialSummaryItem) map[uint]*FinancialSummaryItem {
    dataMap := make(map[uint]*FinancialSummaryItem)
    
    for i := range items {
        item := &items[i]
        dataMap[item.CategoryId] = item
        
        // 递归处理子项
        if len(item.Children) > 0 {
            childMap := createDataMap(item.Children)
            for id, childItem := range childMap {
                dataMap[id] = childItem
            }
        }
    }
    
    return dataMap
}
```

#### insertCalculatedItem() 函数
```go
// 将计算结果插入到适当位置（按SortOrder排序）
func insertCalculatedItem(items []FinancialSummaryItem, calculatedItem *FinancialSummaryItem, config CostTypeConfigItem) []FinancialSummaryItem {
    // 根据 SortOrder 找到插入位置
    insertIndex := len(items)
    
    for i, item := range items {
        if item.SortOrder > config.SortOrder {
            insertIndex = i
            break
        }
    }
    
    // 创建新的切片并插入计算项
    newItems := make([]FinancialSummaryItem, len(items)+1)
    copy(newItems[:insertIndex], items[:insertIndex])
    newItems[insertIndex] = *calculatedItem
    copy(newItems[insertIndex+1:], items[insertIndex:])
    
    return newItems
}
```

### 2. 智能依赖关系处理

#### 拓扑排序算法
```go
// 使用拓扑排序处理复杂依赖关系
func (sjp *SimpleJSONProcessor) sortConfigsByDependency(configs []CostTypeConfigItem) []CostTypeConfigItem {
    // 构建依赖关系图
    dependencyMap := make(map[uint][]uint)
    inDegree := make(map[uint]int)
    configMap := make(map[uint]CostTypeConfigItem)
    
    // 初始化入度
    for _, config := range configs {
        configMap[config.Id] = config
        inDegree[config.Id] = 0
        dependencies := sjp.extractDependencies(config.CalculationFormula)
        dependencyMap[config.Id] = dependencies
    }
    
    // 计算入度
    for _, dependencies := range dependencyMap {
        for _, depId := range dependencies {
            if _, exists := inDegree[depId]; exists {
                inDegree[depId]++
            }
        }
    }
    
    // 拓扑排序...
}
```

#### 循环依赖检测
```go
// 检查是否有循环依赖
if len(result) != len(configs) {
    fmt.Printf("警告：检测到循环依赖，部分计算型分类可能无法正确计算\n")
    // 将剩余的配置添加到结果中
    for _, config := range configs {
        found := false
        for _, resultConfig := range result {
            if resultConfig.Id == config.Id {
                found = true
                break
            }
        }
        if !found {
            result = append(result, config)
            fmt.Printf("循环依赖的分类: %s (ID: %d)\n", config.CategoryName, config.Id)
        }
    }
}
```

### 3. 完善的错误处理

#### 详细的错误日志
```go
func (sjp *SimpleJSONProcessor) executeSimpleFormulaCalculation(config CostTypeConfigItem, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) *FinancialSummaryItem {
    if config.CalculationFormula == "" {
        fmt.Printf("警告：分类 [%s] 的计算公式为空\n", config.CategoryName)
        return nil
    }
    
    // 验证所有依赖的分类数据是否存在
    for _, depId := range dependencies {
        if _, exists := dataMap[depId]; !exists {
            fmt.Printf("错误：分类 [%s] 依赖的分类ID %d 在数据中不存在\n", config.CategoryName, depId)
            return nil
        }
    }
    
    // 验证计算结果的合理性
    if result != nil {
        if err := sjp.validateCalculationResult(result, config.CategoryName); err != nil {
            fmt.Printf("警告：分类 [%s] 计算结果验证失败: %v\n", config.CategoryName, err)
        }
    }
}
```

#### 异常情况处理
```go
// 处理JSON解析失败
formulaJSON, err := sjp.builder.BuildFromJSON(config.CalculationFormula)
if err != nil {
    fmt.Printf("信息：分类 [%s] 使用直接表达式格式: %s\n", config.CategoryName, config.CalculationFormula)
    expression = config.CalculationFormula
} else {
    fmt.Printf("信息：分类 [%s] 使用JSON格式，表达式: %s\n", config.CategoryName, expression)
}

// 处理除零情况
if rightAmount != 0 {
    result.MonthlyAmounts[month] = leftAmount / rightAmount
} else {
    result.MonthlyAmounts[month] = 0 // 除零返回0而不是报错
}
```

### 4. 计算完整性验证

#### 数据一致性检查
```go
func (sjp *SimpleJSONProcessor) validateCalculationResult(result *FinancialSummaryItem, categoryName string) error {
    // 检查总计是否与月度数据一致
    var monthlySum jtypes.JPrice
    for _, amount := range result.MonthlyAmounts {
        monthlySum += amount
    }
    
    // 允许小的精度差异
    diff := result.TotalAmount - monthlySum
    if diff < 0 {
        diff = -diff
    }
    
    if diff > 1 { // 允许1分的差异
        return fmt.Errorf("总计 %.2f 与月度数据总和 %.2f 不一致，差异: %.2f", 
            float64(result.TotalAmount)/100, float64(monthlySum)/100, float64(diff)/100)
    }
    
    return nil
}
```

#### 合理性检查
```go
// 检查是否有异常大的数值（超过1万亿）
maxValue := jtypes.JPrice(100000000000000) // 1万亿分
if result.TotalAmount > maxValue || result.TotalAmount < -maxValue {
    return fmt.Errorf("总计数值异常大: %.2f", float64(result.TotalAmount)/100)
}
```

### 5. 性能优化

#### 避免重复解析
```go
// 缓存解析结果，避免重复解析相同的JSON公式
type formulaCache struct {
    expression   string
    dependencies []uint
}

var cache = make(map[string]formulaCache)
```

#### 优化的计算逻辑
```go
// 支持复合表达式的高效计算
func (sjp *SimpleJSONProcessor) calculateBasicExpression(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
    // 处理百分比表达式
    if strings.Contains(expression, "%") {
        return sjp.calculatePercentageExpression(expression, dataMap, timeColumns)
    }
    
    // 处理二元运算表达式
    return sjp.calculateBinaryExpression(expression, dataMap, timeColumns)
}
```

## 使用示例

### 1. 基本使用
```go
// 创建处理器
configs := getCostTypeConfigs()
processor := NewSimpleJSONProcessor(configs)

// 处理计算型分类
items := getFinancialSummaryItems()
timeColumns := []string{"2025-01", "2025-02", "2025-03"}
result := processor.ProcessCalculatedCategoriesJSON(items, configs, timeColumns)
```

### 2. 创建和验证公式
```go
// 创建公式
formulaJSON, err := processor.CreateFormulaJSON(
    "([营业收入] - [营业成本]) / [营业收入] %",
    "毛利率计算公式",
)

// 验证公式
errors := processor.ValidateFormulaJSON(formulaJSON)
if len(errors) > 0 {
    for _, err := range errors {
        log.Printf("验证错误: %s - %s", err.Type, err.Message)
    }
}
```

### 3. 处理复杂依赖关系
```go
// 配置有依赖关系的计算型分类
calculatedConfigs := []CostTypeConfigItem{
    {
        Id: 100, CategoryName: "毛利润",
        CalculationFormula: `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润"}`,
    },
    {
        Id: 101, CategoryName: "毛利率",
        CalculationFormula: `{"expression":"[毛利润] / [营业收入] %","references":[100,1],"description":"毛利率"}`,
    },
    {
        Id: 102, CategoryName: "营业利润",
        CalculationFormula: `{"expression":"[毛利润] - [期间费用合计]","references":[100,103],"description":"营业利润"}`,
    },
}

// 系统会自动按依赖关系排序：毛利润 -> 毛利率 -> 营业利润
result := processor.ProcessCalculatedCategoriesJSON(items, calculatedConfigs, timeColumns)
```

## 支持的业务场景

### 1. 简单表达式
- `[营业收入] - [营业成本]` - 基本减法
- `[销售费用] + [管理费用]` - 基本加法

### 2. 复合表达式
- `([营业收入] - [营业成本]) / [营业收入] %` - 毛利率
- `[营业收入] - [营业成本] - [期间费用合计]` - 营业利润

### 3. 百分比计算
- `[毛利润] / [营业收入] %` - 毛利率
- `[净利润] / [营业收入] %` - 净利率

### 4. 链式依赖
- A依赖B，B依赖C，C依赖D的复杂依赖关系
- 自动拓扑排序确保正确的计算顺序

## 错误处理机制

### 1. 输入验证
- JSON格式验证
- 表达式语法验证
- 分类引用有效性验证

### 2. 计算验证
- 数据存在性验证
- 除零安全处理
- 结果合理性检查

### 3. 依赖关系验证
- 循环依赖检测
- 缺失依赖警告
- 依赖排序优化

这个修复后的系统提供了完整、稳定、高性能的财务公式计算功能，能够处理各种复杂的业务场景和异常情况。
