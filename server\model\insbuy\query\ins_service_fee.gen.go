// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFee(db *gorm.DB, opts ...gen.DOOption) insServiceFee {
	_insServiceFee := insServiceFee{}

	_insServiceFee.insServiceFeeDo.UseDB(db, opts...)
	_insServiceFee.insServiceFeeDo.UseModel(&insbuy.InsServiceFee{})

	tableName := _insServiceFee.insServiceFeeDo.TableName()
	_insServiceFee.ALL = field.NewAsterisk(tableName)
	_insServiceFee.ID = field.NewUint(tableName, "id")
	_insServiceFee.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFee.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFee.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFee.CreatedBy = field.NewUint(tableName, "created_by")
	_insServiceFee.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insServiceFee.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insServiceFee.StoreId = field.NewUint(tableName, "store_id")
	_insServiceFee.Title = field.NewString(tableName, "title")
	_insServiceFee.Status = field.NewInt(tableName, "status")
	_insServiceFee.ServiceFeeType = field.NewInt(tableName, "service_fee_type")
	_insServiceFee.AllDay = field.NewInt(tableName, "all_day")
	_insServiceFee.StartDate = field.NewTime(tableName, "start_date")
	_insServiceFee.EndDate = field.NewTime(tableName, "end_date")
	_insServiceFee.DiscountRate = field.NewFloat64(tableName, "discount_rate")
	_insServiceFee.ApplicableTo = field.NewInt(tableName, "applicable_to")
	_insServiceFee.Remark = field.NewString(tableName, "remark")

	_insServiceFee.fillFieldMap()

	return _insServiceFee
}

type insServiceFee struct {
	insServiceFeeDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	CreatedBy      field.Uint
	UpdatedBy      field.Uint
	DeletedBy      field.Uint
	StoreId        field.Uint
	Title          field.String
	Status         field.Int
	ServiceFeeType field.Int
	AllDay         field.Int
	StartDate      field.Time
	EndDate        field.Time
	DiscountRate   field.Float64
	ApplicableTo   field.Int
	Remark         field.String

	fieldMap map[string]field.Expr
}

func (i insServiceFee) Table(newTableName string) *insServiceFee {
	i.insServiceFeeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFee) As(alias string) *insServiceFee {
	i.insServiceFeeDo.DO = *(i.insServiceFeeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFee) updateTableName(table string) *insServiceFee {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.StoreId = field.NewUint(table, "store_id")
	i.Title = field.NewString(table, "title")
	i.Status = field.NewInt(table, "status")
	i.ServiceFeeType = field.NewInt(table, "service_fee_type")
	i.AllDay = field.NewInt(table, "all_day")
	i.StartDate = field.NewTime(table, "start_date")
	i.EndDate = field.NewTime(table, "end_date")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")
	i.ApplicableTo = field.NewInt(table, "applicable_to")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insServiceFee) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFee) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["title"] = i.Title
	i.fieldMap["status"] = i.Status
	i.fieldMap["service_fee_type"] = i.ServiceFeeType
	i.fieldMap["all_day"] = i.AllDay
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["discount_rate"] = i.DiscountRate
	i.fieldMap["applicable_to"] = i.ApplicableTo
	i.fieldMap["remark"] = i.Remark
}

func (i insServiceFee) clone(db *gorm.DB) insServiceFee {
	i.insServiceFeeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFee) replaceDB(db *gorm.DB) insServiceFee {
	i.insServiceFeeDo.ReplaceDB(db)
	return i
}

type insServiceFeeDo struct{ gen.DO }

func (i insServiceFeeDo) Debug() *insServiceFeeDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeDo) WithContext(ctx context.Context) *insServiceFeeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeDo) ReadDB() *insServiceFeeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeDo) WriteDB() *insServiceFeeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeDo) Session(config *gorm.Session) *insServiceFeeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeDo) Clauses(conds ...clause.Expression) *insServiceFeeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeDo) Returning(value interface{}, columns ...string) *insServiceFeeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeDo) Not(conds ...gen.Condition) *insServiceFeeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeDo) Or(conds ...gen.Condition) *insServiceFeeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeDo) Select(conds ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeDo) Where(conds ...gen.Condition) *insServiceFeeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeDo) Order(conds ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeDo) Distinct(cols ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeDo) Omit(cols ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeDo) Group(cols ...field.Expr) *insServiceFeeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeDo) Having(conds ...gen.Condition) *insServiceFeeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeDo) Limit(limit int) *insServiceFeeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeDo) Offset(offset int) *insServiceFeeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeDo) Unscoped() *insServiceFeeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeDo) Create(values ...*insbuy.InsServiceFee) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeDo) CreateInBatches(values []*insbuy.InsServiceFee, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeDo) Save(values ...*insbuy.InsServiceFee) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeDo) First() (*insbuy.InsServiceFee, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFee), nil
	}
}

func (i insServiceFeeDo) Take() (*insbuy.InsServiceFee, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFee), nil
	}
}

func (i insServiceFeeDo) Last() (*insbuy.InsServiceFee, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFee), nil
	}
}

func (i insServiceFeeDo) Find() ([]*insbuy.InsServiceFee, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFee), err
}

func (i insServiceFeeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFee, err error) {
	buf := make([]*insbuy.InsServiceFee, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeDo) FindInBatches(result *[]*insbuy.InsServiceFee, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeDo) Assign(attrs ...field.AssignExpr) *insServiceFeeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeDo) Joins(fields ...field.RelationField) *insServiceFeeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeDo) Preload(fields ...field.RelationField) *insServiceFeeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeDo) FirstOrInit() (*insbuy.InsServiceFee, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFee), nil
	}
}

func (i insServiceFeeDo) FirstOrCreate() (*insbuy.InsServiceFee, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFee), nil
	}
}

func (i insServiceFeeDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFee, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeDo) Delete(models ...*insbuy.InsServiceFee) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeDo) withDO(do gen.Dao) *insServiceFeeDo {
	i.DO = *do.(*gen.DO)
	return i
}
