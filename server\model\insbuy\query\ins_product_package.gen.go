// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackage(db *gorm.DB, opts ...gen.DOOption) insProductPackage {
	_insProductPackage := insProductPackage{}

	_insProductPackage.insProductPackageDo.UseDB(db, opts...)
	_insProductPackage.insProductPackageDo.UseModel(&insbuy.InsProductPackage{})

	tableName := _insProductPackage.insProductPackageDo.TableName()
	_insProductPackage.ALL = field.NewAsterisk(tableName)
	_insProductPackage.Id = field.NewInt(tableName, "id")
	_insProductPackage.StoreId = field.NewUint(tableName, "store_id")
	_insProductPackage.PackageName = field.NewString(tableName, "package_name")
	_insProductPackage.EnName = field.NewString(tableName, "en_name")
	_insProductPackage.PackageIcon = field.NewString(tableName, "package_icon")
	_insProductPackage.PackagePrice = field.NewFloat64(tableName, "package_price")
	_insProductPackage.SortOrder = field.NewInt(tableName, "sort_order")
	_insProductPackage.PackageSn = field.NewString(tableName, "package_sn")
	_insProductPackage.PackageType = field.NewInt(tableName, "package_type")
	_insProductPackage.ProductId = field.NewInt(tableName, "product_id")
	_insProductPackage.OptionNum = field.NewInt(tableName, "option_num")
	_insProductPackage.Unit = field.NewInt(tableName, "unit")
	_insProductPackage.Ext = field.NewField(tableName, "ext")
	_insProductPackage.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackage.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackage.DeletedAt = field.NewField(tableName, "deleted_at")

	_insProductPackage.fillFieldMap()

	return _insProductPackage
}

type insProductPackage struct {
	insProductPackageDo

	ALL          field.Asterisk
	Id           field.Int
	StoreId      field.Uint
	PackageName  field.String
	EnName       field.String
	PackageIcon  field.String
	PackagePrice field.Float64
	SortOrder    field.Int
	PackageSn    field.String
	PackageType  field.Int
	ProductId    field.Int
	OptionNum    field.Int
	Unit         field.Int
	Ext          field.Field
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field

	fieldMap map[string]field.Expr
}

func (i insProductPackage) Table(newTableName string) *insProductPackage {
	i.insProductPackageDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackage) As(alias string) *insProductPackage {
	i.insProductPackageDo.DO = *(i.insProductPackageDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackage) updateTableName(table string) *insProductPackage {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.StoreId = field.NewUint(table, "store_id")
	i.PackageName = field.NewString(table, "package_name")
	i.EnName = field.NewString(table, "en_name")
	i.PackageIcon = field.NewString(table, "package_icon")
	i.PackagePrice = field.NewFloat64(table, "package_price")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.PackageSn = field.NewString(table, "package_sn")
	i.PackageType = field.NewInt(table, "package_type")
	i.ProductId = field.NewInt(table, "product_id")
	i.OptionNum = field.NewInt(table, "option_num")
	i.Unit = field.NewInt(table, "unit")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")

	i.fillFieldMap()

	return i
}

func (i *insProductPackage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackage) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 16)
	i.fieldMap["id"] = i.Id
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["package_name"] = i.PackageName
	i.fieldMap["en_name"] = i.EnName
	i.fieldMap["package_icon"] = i.PackageIcon
	i.fieldMap["package_price"] = i.PackagePrice
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["package_sn"] = i.PackageSn
	i.fieldMap["package_type"] = i.PackageType
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["option_num"] = i.OptionNum
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
}

func (i insProductPackage) clone(db *gorm.DB) insProductPackage {
	i.insProductPackageDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackage) replaceDB(db *gorm.DB) insProductPackage {
	i.insProductPackageDo.ReplaceDB(db)
	return i
}

type insProductPackageDo struct{ gen.DO }

func (i insProductPackageDo) Debug() *insProductPackageDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageDo) WithContext(ctx context.Context) *insProductPackageDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageDo) ReadDB() *insProductPackageDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageDo) WriteDB() *insProductPackageDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageDo) Session(config *gorm.Session) *insProductPackageDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageDo) Clauses(conds ...clause.Expression) *insProductPackageDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageDo) Returning(value interface{}, columns ...string) *insProductPackageDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageDo) Not(conds ...gen.Condition) *insProductPackageDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageDo) Or(conds ...gen.Condition) *insProductPackageDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageDo) Select(conds ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageDo) Where(conds ...gen.Condition) *insProductPackageDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageDo) Order(conds ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageDo) Distinct(cols ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageDo) Omit(cols ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageDo) Group(cols ...field.Expr) *insProductPackageDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageDo) Having(conds ...gen.Condition) *insProductPackageDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageDo) Limit(limit int) *insProductPackageDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageDo) Offset(offset int) *insProductPackageDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageDo) Unscoped() *insProductPackageDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageDo) Create(values ...*insbuy.InsProductPackage) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageDo) CreateInBatches(values []*insbuy.InsProductPackage, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageDo) Save(values ...*insbuy.InsProductPackage) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageDo) First() (*insbuy.InsProductPackage, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackage), nil
	}
}

func (i insProductPackageDo) Take() (*insbuy.InsProductPackage, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackage), nil
	}
}

func (i insProductPackageDo) Last() (*insbuy.InsProductPackage, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackage), nil
	}
}

func (i insProductPackageDo) Find() ([]*insbuy.InsProductPackage, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackage), err
}

func (i insProductPackageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackage, err error) {
	buf := make([]*insbuy.InsProductPackage, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageDo) FindInBatches(result *[]*insbuy.InsProductPackage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageDo) Attrs(attrs ...field.AssignExpr) *insProductPackageDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageDo) Assign(attrs ...field.AssignExpr) *insProductPackageDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageDo) Joins(fields ...field.RelationField) *insProductPackageDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageDo) Preload(fields ...field.RelationField) *insProductPackageDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageDo) FirstOrInit() (*insbuy.InsProductPackage, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackage), nil
	}
}

func (i insProductPackageDo) FirstOrCreate() (*insbuy.InsProductPackage, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackage), nil
	}
}

func (i insProductPackageDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackage, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageDo) Delete(models ...*insbuy.InsProductPackage) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageDo) withDO(do gen.Dao) *insProductPackageDo {
	i.DO = *do.(*gen.DO)
	return i
}
