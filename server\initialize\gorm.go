package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/example"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyQuery "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/model/priinventory"
	priQuery "github.com/flipped-aurora/gin-vue-admin/server/model/priinventory/query"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport/simple"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"os"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Gorm 初始化数据库并产生数据库全局变量
// Author SliverHorn
func Gorm() *gorm.DB {
	switch global.GVA_CONFIG.System.DbType {
	case "mysql":
		return GormMysql()
	case "pgsql":
		return GormPgSql()
	case "oracle":
		return GormOracle()
	case "mssql":
		return GormMssql()
	case "sqlite":
		return GormSqlite()
	default:
		return GormMysql()
	}
}

// RegisterTables 注册数据库表专用
// Author SliverHorn
func RegisterTables() {
	db := global.GVA_DB
	if global.GVA_CONFIG.System.DbAutoMigrateDisabled || db == nil {
		global.GVA_LOG.Info("register table struct disabled")
		return
	}
	sysTables := []interface{}{
		// 系统模块表
		system.SysApi{},
		system.SysUser{},
		system.SysBaseMenu{},
		system.JwtBlacklist{},
		system.SysAuthority{},
		system.SysDictionary{},
		system.SysOperationRecord{},
		system.SysAutoCodeHistory{},
		system.SysDictionaryDetail{},
		system.SysBaseMenuParameter{},
		system.SysBaseMenuBtn{},
		system.SysAuthorityBtn{},
		system.SysAutoCode{},
		system.SysChatGptOption{},
	}
	exampleTables := []interface{}{
		example.ExaFile{},
		example.ExaCustomer{},
		example.ExaFileChunk{},
		example.ExaFileUploadAndDownload{},
	}
	allTables := append(sysTables, exampleTables...)
	//私人仓库
	allTables = append(allTables, priinventory.GetInitTables()...)
	allTables = append(allTables, insbuy.GetInitTables()...)
	if !db.Config.DisableForeignKeyConstraintWhenMigrating {
		allTables = append(allTables, insbuy.GetInitTables2()...)
	}
	err := db.AutoMigrate(allTables...)
	if err != nil {
		global.GVA_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}

	var allTables2 []interface{}
	allTables2 = append(allTables2, insbuy.GetInitTables2()...)
	if len(allTables2) > 0 && db.Config.DisableForeignKeyConstraintWhenMigrating { //
		df1 := db.Config.DisableForeignKeyConstraintWhenMigrating
		db.Config.DisableForeignKeyConstraintWhenMigrating = false
		//err = db.AutoMigrate(allTables2...)
		err = jgorm.AutoMigrate(db, allTables2...)
		if err != nil {
			global.GVA_LOG.Error("register table failed", zap.Error(err))
			os.Exit(0)
		}
		db.Config.DisableForeignKeyConstraintWhenMigrating = df1
	}

	// 视图操作
	views := insbuy.GetInitViews()
	for k, v := range simple.GetInitViews() {
		views[k] = v
	}
	if len(views) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			scope := global.NewScopeVar()
			scope.Set(tx)
			for name, view := range views {
				if view == nil {
					_ = tx.Migrator().DropView(name)
					continue
				}
				q, r, c, e1 := view(tx, scope)
				if e1 != nil {
					continue
				}
				e1 = tx.Migrator().CreateView(name, gorm.ViewOption{
					Replace:     r,
					CheckOption: c,
					Query:       q,
				})
				if e1 != nil {
					return e1
				}
			}
			return nil
		})
		if err != nil {
			global.GVA_LOG.Error("register view failed", zap.Error(err))
			os.Exit(0)
		}
	}

	global.GVA_LOG.Info("register table success")
}

func BindDB() {
	if global.GVA_DB != nil && global.GVA_CONFIG.System.DbOrmDebugEnabled {
		global.GVA_DB = global.GVA_DB.Debug()
	}
	insbuyQuery.SetDefault(global.GVA_DB)
	priQuery.SetDefault(global.GVA_DB)
}
