// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsQueueItemEventHistory(db *gorm.DB, opts ...gen.DOOption) insQueueItemEventHistory {
	_insQueueItemEventHistory := insQueueItemEventHistory{}

	_insQueueItemEventHistory.insQueueItemEventHistoryDo.UseDB(db, opts...)
	_insQueueItemEventHistory.insQueueItemEventHistoryDo.UseModel(&insbuy.InsQueueItemEventHistory{})

	tableName := _insQueueItemEventHistory.insQueueItemEventHistoryDo.TableName()
	_insQueueItemEventHistory.ALL = field.NewAsterisk(tableName)
	_insQueueItemEventHistory.ID = field.NewUint(tableName, "id")
	_insQueueItemEventHistory.CreatedAt = field.NewTime(tableName, "created_at")
	_insQueueItemEventHistory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insQueueItemEventHistory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insQueueItemEventHistory.ItemId = field.NewUint(tableName, "item_id")
	_insQueueItemEventHistory.Event = field.NewInt(tableName, "event")

	_insQueueItemEventHistory.fillFieldMap()

	return _insQueueItemEventHistory
}

type insQueueItemEventHistory struct {
	insQueueItemEventHistoryDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	ItemId    field.Uint
	Event     field.Int

	fieldMap map[string]field.Expr
}

func (i insQueueItemEventHistory) Table(newTableName string) *insQueueItemEventHistory {
	i.insQueueItemEventHistoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insQueueItemEventHistory) As(alias string) *insQueueItemEventHistory {
	i.insQueueItemEventHistoryDo.DO = *(i.insQueueItemEventHistoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insQueueItemEventHistory) updateTableName(table string) *insQueueItemEventHistory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ItemId = field.NewUint(table, "item_id")
	i.Event = field.NewInt(table, "event")

	i.fillFieldMap()

	return i
}

func (i *insQueueItemEventHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insQueueItemEventHistory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["event"] = i.Event
}

func (i insQueueItemEventHistory) clone(db *gorm.DB) insQueueItemEventHistory {
	i.insQueueItemEventHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insQueueItemEventHistory) replaceDB(db *gorm.DB) insQueueItemEventHistory {
	i.insQueueItemEventHistoryDo.ReplaceDB(db)
	return i
}

type insQueueItemEventHistoryDo struct{ gen.DO }

func (i insQueueItemEventHistoryDo) Debug() *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insQueueItemEventHistoryDo) WithContext(ctx context.Context) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insQueueItemEventHistoryDo) ReadDB() *insQueueItemEventHistoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insQueueItemEventHistoryDo) WriteDB() *insQueueItemEventHistoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insQueueItemEventHistoryDo) Session(config *gorm.Session) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insQueueItemEventHistoryDo) Clauses(conds ...clause.Expression) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insQueueItemEventHistoryDo) Returning(value interface{}, columns ...string) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insQueueItemEventHistoryDo) Not(conds ...gen.Condition) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insQueueItemEventHistoryDo) Or(conds ...gen.Condition) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insQueueItemEventHistoryDo) Select(conds ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insQueueItemEventHistoryDo) Where(conds ...gen.Condition) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insQueueItemEventHistoryDo) Order(conds ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insQueueItemEventHistoryDo) Distinct(cols ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insQueueItemEventHistoryDo) Omit(cols ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insQueueItemEventHistoryDo) Join(table schema.Tabler, on ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insQueueItemEventHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insQueueItemEventHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insQueueItemEventHistoryDo) Group(cols ...field.Expr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insQueueItemEventHistoryDo) Having(conds ...gen.Condition) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insQueueItemEventHistoryDo) Limit(limit int) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insQueueItemEventHistoryDo) Offset(offset int) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insQueueItemEventHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insQueueItemEventHistoryDo) Unscoped() *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insQueueItemEventHistoryDo) Create(values ...*insbuy.InsQueueItemEventHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insQueueItemEventHistoryDo) CreateInBatches(values []*insbuy.InsQueueItemEventHistory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insQueueItemEventHistoryDo) Save(values ...*insbuy.InsQueueItemEventHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insQueueItemEventHistoryDo) First() (*insbuy.InsQueueItemEventHistory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItemEventHistory), nil
	}
}

func (i insQueueItemEventHistoryDo) Take() (*insbuy.InsQueueItemEventHistory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItemEventHistory), nil
	}
}

func (i insQueueItemEventHistoryDo) Last() (*insbuy.InsQueueItemEventHistory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItemEventHistory), nil
	}
}

func (i insQueueItemEventHistoryDo) Find() ([]*insbuy.InsQueueItemEventHistory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsQueueItemEventHistory), err
}

func (i insQueueItemEventHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsQueueItemEventHistory, err error) {
	buf := make([]*insbuy.InsQueueItemEventHistory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insQueueItemEventHistoryDo) FindInBatches(result *[]*insbuy.InsQueueItemEventHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insQueueItemEventHistoryDo) Attrs(attrs ...field.AssignExpr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insQueueItemEventHistoryDo) Assign(attrs ...field.AssignExpr) *insQueueItemEventHistoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insQueueItemEventHistoryDo) Joins(fields ...field.RelationField) *insQueueItemEventHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insQueueItemEventHistoryDo) Preload(fields ...field.RelationField) *insQueueItemEventHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insQueueItemEventHistoryDo) FirstOrInit() (*insbuy.InsQueueItemEventHistory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItemEventHistory), nil
	}
}

func (i insQueueItemEventHistoryDo) FirstOrCreate() (*insbuy.InsQueueItemEventHistory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueItemEventHistory), nil
	}
}

func (i insQueueItemEventHistoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsQueueItemEventHistory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insQueueItemEventHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insQueueItemEventHistoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insQueueItemEventHistoryDo) Delete(models ...*insbuy.InsQueueItemEventHistory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insQueueItemEventHistoryDo) withDO(do gen.Dao) *insQueueItemEventHistoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
