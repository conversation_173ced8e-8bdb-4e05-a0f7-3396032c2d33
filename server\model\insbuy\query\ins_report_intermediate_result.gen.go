// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportIntermediateResult(db *gorm.DB, opts ...gen.DOOption) insReportIntermediateResult {
	_insReportIntermediateResult := insReportIntermediateResult{}

	_insReportIntermediateResult.insReportIntermediateResultDo.UseDB(db, opts...)
	_insReportIntermediateResult.insReportIntermediateResultDo.UseModel(&insbuy.InsReportIntermediateResult{})

	tableName := _insReportIntermediateResult.insReportIntermediateResultDo.TableName()
	_insReportIntermediateResult.ALL = field.NewAsterisk(tableName)
	_insReportIntermediateResult.ID = field.NewUint64(tableName, "id")
	_insReportIntermediateResult.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportIntermediateResult.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportIntermediateResult.StoreID = field.NewUint(tableName, "store_id")
	_insReportIntermediateResult.StartDate = field.NewTime(tableName, "start_date")
	_insReportIntermediateResult.EndDate = field.NewTime(tableName, "end_date")
	_insReportIntermediateResult.DataPeriod = field.NewString(tableName, "data_period")
	_insReportIntermediateResult.DataType = field.NewString(tableName, "data_type")
	_insReportIntermediateResult.Name = field.NewString(tableName, "name")
	_insReportIntermediateResult.Code = field.NewString(tableName, "code")
	_insReportIntermediateResult.Key1 = field.NewString(tableName, "key1")
	_insReportIntermediateResult.FilePath = field.NewString(tableName, "file_path")
	_insReportIntermediateResult.Status = field.NewInt(tableName, "status")
	_insReportIntermediateResult.Description = field.NewString(tableName, "description")
	_insReportIntermediateResult.Ext = field.NewField(tableName, "ext")

	_insReportIntermediateResult.fillFieldMap()

	return _insReportIntermediateResult
}

type insReportIntermediateResult struct {
	insReportIntermediateResultDo

	ALL         field.Asterisk
	ID          field.Uint64
	CreatedAt   field.Time
	UpdatedAt   field.Time
	StoreID     field.Uint
	StartDate   field.Time
	EndDate     field.Time
	DataPeriod  field.String
	DataType    field.String
	Name        field.String
	Code        field.String
	Key1        field.String
	FilePath    field.String
	Status      field.Int
	Description field.String
	Ext         field.Field

	fieldMap map[string]field.Expr
}

func (i insReportIntermediateResult) Table(newTableName string) *insReportIntermediateResult {
	i.insReportIntermediateResultDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportIntermediateResult) As(alias string) *insReportIntermediateResult {
	i.insReportIntermediateResultDo.DO = *(i.insReportIntermediateResultDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportIntermediateResult) updateTableName(table string) *insReportIntermediateResult {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreID = field.NewUint(table, "store_id")
	i.StartDate = field.NewTime(table, "start_date")
	i.EndDate = field.NewTime(table, "end_date")
	i.DataPeriod = field.NewString(table, "data_period")
	i.DataType = field.NewString(table, "data_type")
	i.Name = field.NewString(table, "name")
	i.Code = field.NewString(table, "code")
	i.Key1 = field.NewString(table, "key1")
	i.FilePath = field.NewString(table, "file_path")
	i.Status = field.NewInt(table, "status")
	i.Description = field.NewString(table, "description")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insReportIntermediateResult) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportIntermediateResult) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreID
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["data_period"] = i.DataPeriod
	i.fieldMap["data_type"] = i.DataType
	i.fieldMap["name"] = i.Name
	i.fieldMap["code"] = i.Code
	i.fieldMap["key1"] = i.Key1
	i.fieldMap["file_path"] = i.FilePath
	i.fieldMap["status"] = i.Status
	i.fieldMap["description"] = i.Description
	i.fieldMap["ext"] = i.Ext
}

func (i insReportIntermediateResult) clone(db *gorm.DB) insReportIntermediateResult {
	i.insReportIntermediateResultDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportIntermediateResult) replaceDB(db *gorm.DB) insReportIntermediateResult {
	i.insReportIntermediateResultDo.ReplaceDB(db)
	return i
}

type insReportIntermediateResultDo struct{ gen.DO }

func (i insReportIntermediateResultDo) Debug() *insReportIntermediateResultDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportIntermediateResultDo) WithContext(ctx context.Context) *insReportIntermediateResultDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportIntermediateResultDo) ReadDB() *insReportIntermediateResultDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportIntermediateResultDo) WriteDB() *insReportIntermediateResultDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportIntermediateResultDo) Session(config *gorm.Session) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportIntermediateResultDo) Clauses(conds ...clause.Expression) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportIntermediateResultDo) Returning(value interface{}, columns ...string) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportIntermediateResultDo) Not(conds ...gen.Condition) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportIntermediateResultDo) Or(conds ...gen.Condition) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportIntermediateResultDo) Select(conds ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportIntermediateResultDo) Where(conds ...gen.Condition) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportIntermediateResultDo) Order(conds ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportIntermediateResultDo) Distinct(cols ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportIntermediateResultDo) Omit(cols ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportIntermediateResultDo) Join(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportIntermediateResultDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportIntermediateResultDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportIntermediateResultDo) Group(cols ...field.Expr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportIntermediateResultDo) Having(conds ...gen.Condition) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportIntermediateResultDo) Limit(limit int) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportIntermediateResultDo) Offset(offset int) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportIntermediateResultDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportIntermediateResultDo) Unscoped() *insReportIntermediateResultDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportIntermediateResultDo) Create(values ...*insbuy.InsReportIntermediateResult) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportIntermediateResultDo) CreateInBatches(values []*insbuy.InsReportIntermediateResult, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportIntermediateResultDo) Save(values ...*insbuy.InsReportIntermediateResult) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportIntermediateResultDo) First() (*insbuy.InsReportIntermediateResult, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResult), nil
	}
}

func (i insReportIntermediateResultDo) Take() (*insbuy.InsReportIntermediateResult, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResult), nil
	}
}

func (i insReportIntermediateResultDo) Last() (*insbuy.InsReportIntermediateResult, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResult), nil
	}
}

func (i insReportIntermediateResultDo) Find() ([]*insbuy.InsReportIntermediateResult, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportIntermediateResult), err
}

func (i insReportIntermediateResultDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportIntermediateResult, err error) {
	buf := make([]*insbuy.InsReportIntermediateResult, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportIntermediateResultDo) FindInBatches(result *[]*insbuy.InsReportIntermediateResult, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportIntermediateResultDo) Attrs(attrs ...field.AssignExpr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportIntermediateResultDo) Assign(attrs ...field.AssignExpr) *insReportIntermediateResultDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportIntermediateResultDo) Joins(fields ...field.RelationField) *insReportIntermediateResultDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportIntermediateResultDo) Preload(fields ...field.RelationField) *insReportIntermediateResultDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportIntermediateResultDo) FirstOrInit() (*insbuy.InsReportIntermediateResult, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResult), nil
	}
}

func (i insReportIntermediateResultDo) FirstOrCreate() (*insbuy.InsReportIntermediateResult, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportIntermediateResult), nil
	}
}

func (i insReportIntermediateResultDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportIntermediateResult, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportIntermediateResultDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportIntermediateResultDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportIntermediateResultDo) Delete(models ...*insbuy.InsReportIntermediateResult) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportIntermediateResultDo) withDO(do gen.Dao) *insReportIntermediateResultDo {
	i.DO = *do.(*gen.DO)
	return i
}
