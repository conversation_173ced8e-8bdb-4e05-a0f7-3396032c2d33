<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@cd-ins.ultrasdk.com-dnf">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>dnf|schema||dnf||ALTER|G
dnf|schema||dnf||ALTER ROUTINE|G
dnf|schema||dnf||CREATE|G
dnf|schema||dnf||CREATE ROUTINE|G
dnf|schema||dnf||CREATE TEMPORARY TABLES|G
dnf|schema||dnf||CREATE VIEW|G
dnf|schema||dnf||DELETE|G
dnf|schema||dnf||DROP|G
dnf|schema||dnf||EVENT|G
dnf|schema||dnf||EXECUTE|G
dnf|schema||dnf||INDEX|G
dnf|schema||dnf||INSERT|G
dnf|schema||dnf||LOCK TABLES|G
dnf|schema||dnf||REFERENCES|G
dnf|schema||dnf||SELECT|G
dnf|schema||dnf||SHOW VIEW|G
dnf|schema||dnf||TRIGGER|G
dnf|schema||dnf||UPDATE|G
dnf|schema||dnf||grant option|G
dnf2|schema||dnf||ALTER|G
dnf2|schema||dnf||ALTER ROUTINE|G
dnf2|schema||dnf||CREATE|G
dnf2|schema||dnf||CREATE ROUTINE|G
dnf2|schema||dnf||CREATE TEMPORARY TABLES|G
dnf2|schema||dnf||CREATE VIEW|G
dnf2|schema||dnf||DELETE|G
dnf2|schema||dnf||DROP|G
dnf2|schema||dnf||EVENT|G
dnf2|schema||dnf||EXECUTE|G
dnf2|schema||dnf||INDEX|G
dnf2|schema||dnf||INSERT|G
dnf2|schema||dnf||LOCK TABLES|G
dnf2|schema||dnf||REFERENCES|G
dnf2|schema||dnf||SELECT|G
dnf2|schema||dnf||SHOW VIEW|G
dnf2|schema||dnf||TRIGGER|G
dnf2|schema||dnf||UPDATE|G
dnf2|schema||dnf||grant option|G</Grants>
      <ServerVersion>8.0.33</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="dnf">
      <IntrospectionTimestamp>2024-12-31.19:42:25</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2024-12-31.03:42:27</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="dnf2">
      <IntrospectionTimestamp>2025-02-18.23:10:09</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-02-18.07:10:09</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <user id="292" parent="1" name="dnf"/>
    <table id="293" parent="290" name="c_guild">
      <Comment>俱乐部公会表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="294" parent="290" name="c_guild_apply">
      <Comment>入会申请列表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="295" parent="290" name="c_guild_cover">
      <Comment>俱乐部公会封面记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="296" parent="290" name="c_guild_fund">
      <Comment>公会众筹</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="297" parent="290" name="c_guild_luck">
      <Comment>幸运公会</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="298" parent="290" name="c_guild_member">
      <Comment>公会成员列表群</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="299" parent="290" name="c_guild_member_log">
      <Comment>公会成员加入退出日志</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="300" parent="290" name="c_guild_msg_push_log">
      <Comment>管理员通知推送表，每条申请、过期、加入、退出记录都会推送到对应的管理员，</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="301" parent="290" name="c_guild_notice">
      <Comment>公会公告表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="302" parent="290" name="c_guild_notice_view_log">
      <Comment>公会公告阅读数日志</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="303" parent="290" name="c_guild_power">
      <Comment>公会实力加成表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="290" name="c_guild_top">
      <Comment>实时榜，半小时更新</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="305" parent="290" name="c_guild_top_week">
      <Comment>周榜</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="306" parent="290" name="c_relation_guild_tag">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="307" parent="290" name="dnf_acategory">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="308" parent="290" name="dnf_activity">
      <Comment>活动基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="309" parent="290" name="dnf_activity_member">
      <Comment>活动参与人员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="310" parent="290" name="dnf_activity_member_1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="311" parent="290" name="dnf_activity_reward">
      <Comment>活动奖励表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="312" parent="290" name="dnf_activity_reward_1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="313" parent="290" name="dnf_activity_reward_assign">
      <Comment>活动奖励分配表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="314" parent="290" name="dnf_activity_reward_assign_1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="315" parent="290" name="dnf_activity_reward_log">
      <Comment>活动奖励发放记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="316" parent="290" name="dnf_activity_reward_log_1">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="317" parent="290" name="dnf_address">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="318" parent="290" name="dnf_appbuylog">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="319" parent="290" name="dnf_appmarket">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="320" parent="290" name="dnf_apprenewal">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="321" parent="290" name="dnf_article">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="322" parent="290" name="dnf_bank">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="323" parent="290" name="dnf_bind">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="324" parent="290" name="dnf_brand">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="325" parent="290" name="dnf_cart">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="326" parent="290" name="dnf_cashcard">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="327" parent="290" name="dnf_cate_pvs">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="328" parent="290" name="dnf_category_goods">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="329" parent="290" name="dnf_category_store">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="330" parent="290" name="dnf_channel">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="331" parent="290" name="dnf_cod">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="332" parent="290" name="dnf_collect">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="333" parent="290" name="dnf_coupon">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="334" parent="290" name="dnf_coupon_sn">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="335" parent="290" name="dnf_delivery_template">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="336" parent="290" name="dnf_delivery_timer">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="337" parent="290" name="dnf_deposit_account">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="338" parent="290" name="dnf_deposit_recharge">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="339" parent="290" name="dnf_deposit_record">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="340" parent="290" name="dnf_deposit_setting">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="341" parent="290" name="dnf_deposit_trade">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="342" parent="290" name="dnf_deposit_withdraw">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="343" parent="290" name="dnf_distribute">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="344" parent="290" name="dnf_distribute_items">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="345" parent="290" name="dnf_distribute_merchant">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="346" parent="290" name="dnf_distribute_order">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="347" parent="290" name="dnf_distribute_setting">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="348" parent="290" name="dnf_friend">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="349" parent="290" name="dnf_gcategory">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="350" parent="290" name="dnf_goods">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="351" parent="290" name="dnf_goods_image">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="352" parent="290" name="dnf_goods_integral">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="353" parent="290" name="dnf_goods_prop">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="354" parent="290" name="dnf_goods_prop_value">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="355" parent="290" name="dnf_goods_pvs">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="356" parent="290" name="dnf_goods_qa">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="357" parent="290" name="dnf_goods_spec">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="358" parent="290" name="dnf_goods_statistics">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="359" parent="290" name="dnf_guideshop">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="360" parent="290" name="dnf_guild">
      <Comment>公会表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="361" parent="290" name="dnf_guild_balance">
      <Comment>公会余额
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="362" parent="290" name="dnf_guild_balance_detail">
      <Comment>公会余额变动明细
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="363" parent="290" name="dnf_guild_config">
      <Comment>公会通用配置
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="364" parent="290" name="dnf_integral">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="365" parent="290" name="dnf_integral_log">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="366" parent="290" name="dnf_integral_setting">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="367" parent="290" name="dnf_limitbuy">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="368" parent="290" name="dnf_lottery">
      <Comment>抽奖基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="369" parent="290" name="dnf_lottery_prize">
      <Comment>抽奖奖品表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="370" parent="290" name="dnf_lottery_record">
      <Comment>抽奖记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="371" parent="290" name="dnf_mailbox">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="372" parent="290" name="dnf_meal">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="373" parent="290" name="dnf_meal_goods">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="374" parent="290" name="dnf_member">
      <Comment>成员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="375" parent="290" name="dnf_member_value">
      <Comment>角色数值表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="376" parent="290" name="dnf_navigation">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="377" parent="290" name="dnf_order">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="378" parent="290" name="dnf_order_express">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="379" parent="290" name="dnf_order_extm">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="380" parent="290" name="dnf_order_goods">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="381" parent="290" name="dnf_order_integral">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="382" parent="290" name="dnf_order_log">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="383" parent="290" name="dnf_partner">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="384" parent="290" name="dnf_plugin">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="385" parent="290" name="dnf_promotool_item">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="386" parent="290" name="dnf_promotool_setting">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="387" parent="290" name="dnf_questionnaire">
      <Comment>问卷基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="388" parent="290" name="dnf_questionnaire_answer">
      <Comment>问卷回答表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="389" parent="290" name="dnf_questionnaire_question">
      <Comment>问卷选项表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="390" parent="290" name="dnf_recommend">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="391" parent="290" name="dnf_recommend_goods">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="392" parent="290" name="dnf_refund">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="393" parent="290" name="dnf_refund_message">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="394" parent="290" name="dnf_region">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="395" parent="290" name="dnf_report">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="396" parent="290" name="dnf_scategory">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="397" parent="290" name="dnf_sgrade">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="398" parent="290" name="dnf_sgrade_integral">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="399" parent="290" name="dnf_sms">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="400" parent="290" name="dnf_sms_log">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="401" parent="290" name="dnf_sms_template">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="402" parent="290" name="dnf_store">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="403" parent="290" name="dnf_team">
      <Comment>队伍表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="404" parent="290" name="dnf_team_member">
      <Comment>队伍成员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="405" parent="290" name="dnf_teambuy">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="406" parent="290" name="dnf_teambuy_log">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="407" parent="290" name="dnf_uploaded_file">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="408" parent="290" name="dnf_user">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="409" parent="290" name="dnf_user_enter">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="410" parent="290" name="dnf_user_mini">
      <Comment>公会小程序用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="411" parent="290" name="dnf_user_openid">
      <Comment>user_mini表和微信openid关联关系</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="412" parent="290" name="dnf_user_priv">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="413" parent="290" name="dnf_user_token">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="414" parent="290" name="dnf_vote">
      <Comment>投票基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="415" parent="290" name="dnf_vote_option">
      <Comment>投票选项表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="416" parent="290" name="dnf_vote_result">
      <Comment>投票结果表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="417" parent="290" name="dnf_webim">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="418" parent="290" name="dnf_weixin_menu">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="419" parent="290" name="dnf_weixin_reply">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="420" parent="290" name="dnf_weixin_setting">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="421" parent="290" name="dnf_wholesale">
      <Engine>MyISAM</Engine>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="422" parent="291" name="dnf_activity">
      <Comment>活动基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="423" parent="291" name="dnf_activity_member">
      <Comment>活动参与人员表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="424" parent="291" name="dnf_activity_reward">
      <Comment>活动奖励表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="425" parent="291" name="dnf_activity_reward_assign">
      <Comment>活动奖励分配表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="426" parent="291" name="dnf_activity_reward_log">
      <Comment>活动奖励发放记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="427" parent="291" name="dnf_draw_prize_ranges">
      <Comment>现金奖励范围及概率表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="428" parent="291" name="dnf_draw_records">
      <Comment>用户抽奖记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="429" parent="291" name="dnf_draw_red_packets">
      <Comment>红包基本信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="430" parent="291" name="dnf_game_servers">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="431" parent="291" name="dnf_guild">
      <Comment>公会
数据同步时应该记录变更日志；
TODO: 服务器的关联
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="432" parent="291" name="dnf_guild_balance">
      <Comment>公会余额
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="433" parent="291" name="dnf_guild_balance_detail">
      <Comment>公会余额变动明细
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="434" parent="291" name="dnf_guild_bind">
      <Comment>公会邀请码绑定
会长首次使用邀请码，绑定或创建公会；
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="435" parent="291" name="dnf_guild_config">
      <Comment>公会通用配置
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="436" parent="291" name="dnf_guild_event">
      <Comment>公会纪事
记录公会重要事件，如：成员加入、退出、审核、公会活动、公会任务等
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="437" parent="291" name="dnf_guild_hero_class">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="438" parent="291" name="dnf_guild_hero_type">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="439" parent="291" name="dnf_guild_manager">
      <Comment>公会管理团队</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="440" parent="291" name="dnf_guild_member">
      <Comment>公会成员-冒险团
初始时没有关联用户；
每个用户或冒险团只能加入一个公会，关联或解除关联时要重置相关业务表；
不定期同步更新游戏数据；
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="441" parent="291" name="dnf_guild_member_equipments">
      <Comment>所有装备信息</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb3_general_ci</CollationName>
    </table>
    <table id="442" parent="291" name="dnf_guild_member_log">
      <Comment>公会成员变更记录
类似回收站，仅用于归档备查。
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="443" parent="291" name="dnf_guild_member_role">
      <Comment>公会成员-冒险团角色
不定期更新，每次更新会覆盖之前的数据、删除已经离开公会的角色；
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="444" parent="291" name="dnf_guild_member_value">
      <Comment>公会成员贡献值
成员离会后，应该清除数据，如果重新入会需要重新计算；
清除时应当归档处理，以便误操作时撤回；
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="445" parent="291" name="dnf_guild_messages">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="446" parent="291" name="dnf_lottery">
      <Comment>抽奖基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="447" parent="291" name="dnf_lottery_prize">
      <Comment>抽奖奖品表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="448" parent="291" name="dnf_lottery_record">
      <Comment>抽奖记录表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="449" parent="291" name="dnf_pay_record">
      <Comment>支付记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="450" parent="291" name="dnf_questionnaire">
      <Comment>问卷基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="451" parent="291" name="dnf_questionnaire_answer">
      <Comment>问卷回答表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="452" parent="291" name="dnf_questionnaire_question">
      <Comment>问卷选项表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="453" parent="291" name="dnf_sys_dict">
      <Comment>系统字典</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="454" parent="291" name="dnf_sys_dict_item">
      <Comment>系统字典项</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="455" parent="291" name="dnf_sys_log">
      <Comment>系统操作日志</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="456" parent="291" name="dnf_team">
      <Comment>战队表
连续1周3人不在一起组队（上传图无对方）则自动解散；
状态不完整（人员不齐、伤害图未上传）时不显示总任务和排名；
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="457" parent="291" name="dnf_team_record">
      <Comment>战队记录表
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="458" parent="291" name="dnf_team_role">
      <Comment>战队-出战的角色
每个冒险团最多可以选择4个角色出战。
解绑或重新绑定或同步数据有更新角色时，应该重置此表
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="459" parent="291" name="dnf_team_upload">
      <Comment>伤害图保存</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="460" parent="291" name="dnf_user">
      <Comment>公会管理团队</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="461" parent="291" name="dnf_user_mini">
      <Comment>公会小程序用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="462" parent="291" name="dnf_user_wallet">
      <Comment>用户钱包</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="463" parent="291" name="dnf_user_wallet_log">
      <Comment>用户钱包记录。
只读。如果要撤回，需要补充一条反操作记录。
用户只能提取可用余额，冻结中的余额不可提取。
解冻时应该增加可用余额，减少冻结中的余额，同时本次金额变化为0。
余额变化原因可以包含：
 1. 获得类:充值、线下转入、中奖、发起公会活动
 2. 消耗类:发奖、提现、
 3. 转换类:解冻、冻结等
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="464" parent="291" name="dnf_user_withdraws">
      <Comment>用户提现记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="465" parent="291" name="dnf_vote">
      <Comment>投票基础信息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="466" parent="291" name="dnf_vote_option">
      <Comment>投票选项表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="467" parent="291" name="dnf_vote_result">
      <Comment>投票结果表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="468" parent="291" name="dnf_wallet_rule">
      <Comment>工会钱包规则
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="469" parent="291" name="guild_balance">
      <Comment>公会余额
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="470" parent="291" name="guild_balance_detail">
      <Comment>公会余额变动明细
</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="471" parent="293" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>公会ID，10001开始自增</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="472" parent="293" name="name">
      <Comment>公会名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="473" parent="293" name="hero_user_id">
      <Comment>会长ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="474" parent="293" name="power">
      <Comment>本周实力</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="475" parent="293" name="rank">
      <Comment>本周排名</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="476" parent="293" name="total_power">
      <Comment>累计实力</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="477" parent="293" name="member_num">
      <Comment>公会人数</Comment>
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="478" parent="293" name="join_type">
      <Comment>加入条件，0全部需要申请，1自由加入，2部分自由部分申请</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="479" parent="293" name="join_level">
      <Comment>自由加入需要的等级，join_type=2时生效</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="480" parent="293" name="icon">
      <Comment>图标</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="481" parent="293" name="img">
      <Comment>背景图片</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="482" parent="293" name="desc">
      <Comment>描述简介</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="483" parent="293" name="game_ids">
      <Comment>关联的游戏id，逗号分隔存放</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="484" parent="293" name="status">
      <Comment>0已解散，1有效的</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="485" parent="293" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <index id="486" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="487" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="488" parent="294" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="489" parent="294" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="490" parent="294" name="handle_admin_id">
      <Comment>处理者的ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="491" parent="294" name="hero_user_id">
      <Comment>申请用户ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="492" parent="294" name="nickname">
      <Comment>用户昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="493" parent="294" name="status">
      <Comment>状态，0未处理，1同意，2拒绝，3忽略,4用户取消申请</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="494" parent="294" name="is_expire">
      <Comment>是否过期，0未过期，1过期</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="495" parent="294" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="496" parent="294" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="497" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="498" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="499" parent="295" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="500" parent="295" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="501" parent="295" name="hero_user_id">
      <Comment>上传者ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="502" parent="295" name="nickname">
      <Comment>上传者昵称</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="503" parent="295" name="img">
      <Comment>图片</Comment>
      <DasType>varchar(500)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="504" parent="295" name="is_reset">
      <Comment>是否重置，0否1是</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="505" parent="295" name="created_day">
      <Comment>创建的日期，格式如下:20180808</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="506" parent="295" name="created_at">
      <Comment>上传时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="507" parent="295" name="updated_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="508" parent="295" name="admin_username">
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="509" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="510" parent="295" name="created_day">
      <ColNames>created_day</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="511" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="512" parent="295" name="created_day">
      <UnderlyingIndexName>created_day</UnderlyingIndexName>
    </key>
    <column id="513" parent="296" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="514" parent="296" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="515" parent="296" name="hero_user_id">
      <Comment>成员ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="516" parent="296" name="fund_gold">
      <Comment>众筹英雄币</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="517" parent="296" name="fund_growth">
      <Comment>众筹获得的成长值</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="518" parent="296" name="fund_power">
      <Comment>众筹获得的公会加成</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="519" parent="296" name="created_week">
      <Comment>创建时间所属周的周一</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="520" parent="296" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="521" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="522" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="523" parent="297" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="524" parent="297" name="guild_id">
      <Comment>幸运公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="525" parent="297" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="526" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="527" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="528" parent="298" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="529" parent="298" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="530" parent="298" name="hero_user_id">
      <Comment>会员ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="531" parent="298" name="member_type">
      <Comment>会员类型，0成员，1副会长，2会长</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="532" parent="298" name="power">
      <Comment>会员本周实力</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="533" parent="298" name="power_level">
      <Comment>成员等级加成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="534" parent="298" name="power_sign">
      <Comment>签到加成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="535" parent="298" name="power_fund">
      <Comment>众筹加成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="536" parent="298" name="power_task">
      <Comment>任务加成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="537" parent="298" name="power_growth">
      <Comment>成长值增长加成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="538" parent="298" name="sign_times">
      <Comment>签到次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="539" parent="298" name="task_times">
      <Comment>任务次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="540" parent="298" name="qq">
      <Comment>qq号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="541" parent="298" name="created_at">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <index id="542" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="543" parent="298" name="guild_id">
      <ColNames>guild_id
hero_user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="544" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="545" parent="298" name="guild_id">
      <UnderlyingIndexName>guild_id</UnderlyingIndexName>
    </key>
    <column id="546" parent="299" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="547" parent="299" name="guild_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="548" parent="299" name="hero_user_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="549" parent="299" name="nickname">
      <Comment>用户昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="550" parent="299" name="type">
      <Comment>0退出公会，1加入公会</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="551" parent="299" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="552" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="553" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="554" parent="300" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="555" parent="300" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="556" parent="300" name="admin_id">
      <Comment>公会管理员的herouser ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="557" parent="300" name="hero_user_id">
      <Comment>申请用户ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="558" parent="300" name="nickname">
      <Comment>玩家昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="559" parent="300" name="msg_type">
      <Comment>消息类型，1申请，2过期，3加入，4退出</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="560" parent="300" name="apply_id">
      <Comment>(申请类型专有)guild_apply主键ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="561" parent="300" name="status">
      <Comment>(申请类型专有)状态，0未处理，1同意，2拒绝，3忽略,4申请自动过期</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="562" parent="300" name="is_handle">
      <Comment>是否处理/或者查看，0否1是</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="563" parent="300" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <index id="564" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="565" parent="300" name="guild_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="566" parent="300" name="hero_user_id">
      <ColNames>hero_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="567" parent="300" name="apply_id">
      <ColNames>apply_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="568" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="569" parent="301" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="570" parent="301" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="571" parent="301" name="hero_user_id">
      <Comment>作者ID,hero_user_id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="572" parent="301" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="573" parent="301" name="content">
      <Comment>内容</Comment>
      <DasType>varchar(600)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="574" parent="301" name="view_num">
      <Comment>阅读数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="575" parent="301" name="status">
      <Comment>公告状态，0删除，1可读</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="576" parent="301" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="577" parent="301" name="updated_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="578" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="579" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="580" parent="302" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="581" parent="302" name="guild_id">
      <Comment>guild表主键ID</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="582" parent="302" name="hero_user_id">
      <Comment>用户id</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="583" parent="302" name="notice_ids">
      <Comment>该用户在该公会已经阅读过的公告id，逗号分隔</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="584" parent="302" name="created_at">
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="585" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="586" parent="302" name="guild_notice_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="587" parent="302" name="hero_user_id">
      <ColNames>hero_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="588" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="589" parent="303" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="590" parent="303" name="guild_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="591" parent="303" name="hero_user_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="592" parent="303" name="type">
      <Comment>类型，1成员等级加成，2签到加成，3众筹加成，4任务加成，5成长值增长加成</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="593" parent="303" name="power">
      <Comment>实力值</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="594" parent="303" name="created_week">
      <Comment>创建周的周一</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="595" parent="303" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="596" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="597" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="598" parent="304" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="599" parent="304" name="created_hour">
      <Comment>创建时间的小时eg:201808081230，每半小时为刻度</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="600" parent="304" name="rank">
      <Comment>排名</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="601" parent="304" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="602" parent="304" name="name">
      <Comment>公会名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="603" parent="304" name="power">
      <Comment>实力值</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="604" parent="304" name="member_num">
      <Comment>人员数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="605" parent="304" name="game_ids">
      <Comment>逗号分隔，游戏(标签)id</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="606" parent="304" name="rank_change">
      <Comment>排名变化，大于0上升，0不变，小于0下降</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="607" parent="304" name="rank_percent">
      <Comment>排名百分比，前三为123，其他为10% 30% 50%，50%以下为空</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="608" parent="304" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <index id="609" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="610" parent="304" name="created_hour">
      <ColNames>created_hour
guild_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="611" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="612" parent="304" name="created_hour">
      <UnderlyingIndexName>created_hour</UnderlyingIndexName>
    </key>
    <column id="613" parent="305" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="614" parent="305" name="created_week">
      <Comment>创建周的周一</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="615" parent="305" name="rank">
      <Comment>排名</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="616" parent="305" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="617" parent="305" name="name">
      <Comment>公会名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="618" parent="305" name="power">
      <Comment>实力值</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="619" parent="305" name="total_power">
      <Comment>总实力</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="620" parent="305" name="member_num">
      <Comment>人员数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="621" parent="305" name="game_ids">
      <Comment>逗号分隔，游戏(标签)id</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="622" parent="305" name="rank_change">
      <Comment>排名变化，大于0上升，0不变，小于0下降</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="623" parent="305" name="rank_percent">
      <Comment>排名百分比，前三为123，其他为10% 30% 50%，50%以下为空</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="624" parent="305" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="625" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="626" parent="305" name="created_week">
      <ColNames>created_week
guild_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="627" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="628" parent="305" name="created_week">
      <UnderlyingIndexName>created_week</UnderlyingIndexName>
    </key>
    <column id="629" parent="306" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="630" parent="306" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="631" parent="306" name="game_id">
      <Comment>关联的游戏ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="632" parent="306" name="name">
      <Comment>游戏名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="633" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="634" parent="306" name="guild_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="635" parent="306" name="game_id">
      <ColNames>game_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="636" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="637" parent="307" name="cate_id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="638" parent="307" name="cate_name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="639" parent="307" name="parent_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="640" parent="307" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="641" parent="307" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="642" parent="307" name="if_show">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="643" parent="307" name="PRIMARY">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="644" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="645" parent="308" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="646" parent="308" name="title">
      <Comment>活动标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="647" parent="308" name="type">
      <Comment>活动类型: 娱乐活动/奖金活动</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="648" parent="308" name="game">
      <Comment>选择的游戏名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="649" parent="308" name="description">
      <Comment>活动描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="650" parent="308" name="organizer">
      <Comment>活动主办方</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="651" parent="308" name="assistant">
      <Comment>活动协助人</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="652" parent="308" name="start_at">
      <Comment>活动开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="653" parent="308" name="end_at">
      <Comment>活动结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="654" parent="308" name="registration_end">
      <Comment>报名截止时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="655" parent="308" name="rules">
      <Comment>活动规则</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="656" parent="308" name="requirements">
      <Comment>参与要求</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="657" parent="308" name="ext">
      <Comment>活动拓展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="658" parent="308" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="659" parent="308" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="660" parent="308" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="661" parent="308" name="status">
      <Comment>活动状态 0 草稿 1已撤销 2已结束</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="662" parent="308" name="participant">
      <Comment>活动参与人数</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="663" parent="308" name="cover">
      <Comment>活动封面</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>19</Position>
    </column>
    <index id="664" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="665" parent="308" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="666" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="667" parent="309" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="668" parent="309" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="669" parent="309" name="member_id">
      <Comment>参与用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="670" parent="309" name="upload_proof">
      <Comment>上传证明</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="671" parent="309" name="created_at">
      <Comment>参与时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="672" parent="309" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="673" parent="309" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="674" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="675" parent="309" name="aid">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="676" parent="309" name="idx_dnf_activity_member_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="677" parent="309" name="idx_dnf_activity_member_member_id">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="678" parent="309" name="mid">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="679" parent="309" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="680" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="681" parent="310" name="id">
      <DasType>bigint unsigned|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="682" parent="310" name="activity_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="683" parent="310" name="member_id">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="684" parent="310" name="upload_proof">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="685" parent="310" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="686" parent="310" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="687" parent="310" name="guild_id">
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="688" parent="311" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="689" parent="311" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="690" parent="311" name="level">
      <Comment>奖励等级: 一等奖/二等奖</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="691" parent="311" name="reward_type">
      <Comment>奖励类型: 奖金/道具</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="692" parent="311" name="reward_value">
      <Comment>奖励内容（JSON 格式）</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="693" parent="311" name="quantity">
      <Comment>奖励数量/金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>1.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="694" parent="311" name="quantity_max">
      <Comment>奖励人数上限</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="695" parent="311" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="696" parent="311" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="697" parent="311" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="698" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="699" parent="311" name="idx_dnf_activity_reward_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="700" parent="311" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="701" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="702" parent="312" name="id">
      <DasType>bigint unsigned|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="703" parent="312" name="activity_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="704" parent="312" name="level">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="705" parent="312" name="reward_type">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="706" parent="312" name="reward_value">
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="707" parent="312" name="quantity">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="708" parent="312" name="quantity_max">
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="709" parent="312" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="710" parent="312" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="711" parent="312" name="guild_id">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="712" parent="313" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="713" parent="313" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="714" parent="313" name="reward_id">
      <Comment>关联的奖励ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="715" parent="313" name="member_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="716" parent="313" name="member_name">
      <Comment>用户名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="717" parent="313" name="is_assign">
      <Comment>是否已发放</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="718" parent="313" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="719" parent="313" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="720" parent="313" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="721" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="722" parent="313" name="idx_dnf_activity_reward_assign_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="723" parent="313" name="idx_dnf_activity_reward_assign_reward_id">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="724" parent="313" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="725" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="726" parent="314" name="id">
      <DasType>bigint unsigned|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="727" parent="314" name="activity_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="728" parent="314" name="reward_id">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="729" parent="314" name="member_id">
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="730" parent="314" name="member_name">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="731" parent="314" name="is_assign">
      <DasType>tinyint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="732" parent="314" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="733" parent="314" name="guild_id">
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="734" parent="315" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="735" parent="315" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="736" parent="315" name="reward_id">
      <Comment>关联的奖励ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="737" parent="315" name="member_id">
      <Comment>接收用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="738" parent="315" name="member_name">
      <Comment>接收用户名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="739" parent="315" name="reward_type">
      <Comment>奖励类型: 奖金/道具</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="740" parent="315" name="reward_value">
      <Comment>奖励内容</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="741" parent="315" name="ext">
      <Comment>记录一些发放情况</Comment>
      <DasType>json|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="742" parent="315" name="status">
      <Comment>发放状态: pending/success/failed</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="743" parent="315" name="failure_reason">
      <Comment>失败原因</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="744" parent="315" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="745" parent="315" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="746" parent="315" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="747" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="748" parent="315" name="aid">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="749" parent="315" name="idx_dnf_activity_reward_log_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="750" parent="315" name="idx_dnf_activity_reward_log_reward_id">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="751" parent="315" name="rid">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="752" parent="315" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="753" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="754" parent="316" name="id">
      <DasType>bigint unsigned|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="755" parent="316" name="activity_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="756" parent="316" name="reward_id">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="757" parent="316" name="member_id">
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="758" parent="316" name="member_name">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="759" parent="316" name="reward_type">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="760" parent="316" name="reward_value">
      <DasType>json|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="761" parent="316" name="ext">
      <DasType>json|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="762" parent="316" name="status">
      <DasType>varchar(32)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="763" parent="316" name="failure_reason">
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="764" parent="316" name="created_at">
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="765" parent="316" name="updated_at">
      <DasType>timestamp|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="766" parent="316" name="guild_id">
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="767" parent="317" name="addr_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="768" parent="317" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="769" parent="317" name="consignee">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="770" parent="317" name="region_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="771" parent="317" name="address">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="772" parent="317" name="phone_tel">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="773" parent="317" name="phone_mob">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="774" parent="317" name="defaddr">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="775" parent="317" name="PRIMARY">
      <ColNames>addr_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="776" parent="317" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="777" parent="317" name="region_id">
      <ColNames>region_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="778" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="779" parent="318" name="bid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="780" parent="318" name="orderId">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="781" parent="318" name="appid">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="782" parent="318" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="783" parent="318" name="period">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="784" parent="318" name="amount">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="785" parent="318" name="status">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="786" parent="318" name="add_time">
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="787" parent="318" name="pay_time">
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="788" parent="318" name="end_time">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="789" parent="318" name="PRIMARY">
      <ColNames>bid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="790" parent="318" name="bid">
      <ColNames>bid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="791" parent="318" name="orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="792" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="793" parent="319" name="aid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="794" parent="319" name="appid">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="795" parent="319" name="title">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="796" parent="319" name="summary">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="797" parent="319" name="category">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="798" parent="319" name="description">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="799" parent="319" name="logo">
      <DasType>varchar(200)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="800" parent="319" name="price">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="801" parent="319" name="sales">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="802" parent="319" name="views">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="803" parent="319" name="status">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="804" parent="319" name="add_time">
      <DasType>int|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="805" parent="319" name="PRIMARY">
      <ColNames>aid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="806" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="807" parent="320" name="rid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="808" parent="320" name="appid">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="809" parent="320" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="810" parent="320" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="811" parent="320" name="expired">
      <DasType>int unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="812" parent="320" name="PRIMARY">
      <ColNames>rid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="813" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="814" parent="321" name="article_id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="815" parent="321" name="title">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="816" parent="321" name="cate_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="817" parent="321" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="818" parent="321" name="link">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="819" parent="321" name="description">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="820" parent="321" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="821" parent="321" name="if_show">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="822" parent="321" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="823" parent="321" name="PRIMARY">
      <ColNames>article_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="824" parent="321" name="cate_id">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="825" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="826" parent="322" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="827" parent="322" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="828" parent="322" name="bank">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="829" parent="322" name="code">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="830" parent="322" name="name">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="831" parent="322" name="account">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="832" parent="322" name="area">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="833" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="834" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="835" parent="323" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="836" parent="323" name="unionid">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="837" parent="323" name="openid">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="838" parent="323" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="839" parent="323" name="code">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="840" parent="323" name="nickname">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="841" parent="323" name="enabled">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="842" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="843" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="844" parent="324" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="845" parent="324" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="846" parent="324" name="logo">
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="847" parent="324" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="848" parent="324" name="cate_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="849" parent="324" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="850" parent="324" name="recommended">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="851" parent="324" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="852" parent="324" name="if_show">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="853" parent="324" name="tag">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="854" parent="324" name="letter">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="855" parent="324" name="description">
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="856" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="857" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="858" parent="325" name="rec_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="859" parent="325" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="860" parent="325" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="861" parent="325" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="862" parent="325" name="goods_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="863" parent="325" name="spec_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="864" parent="325" name="specification">
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="865" parent="325" name="price">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="866" parent="325" name="quantity">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="867" parent="325" name="goods_image">
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="868" parent="325" name="selected">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="869" parent="325" name="product_id">
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="870" parent="325" name="gtype">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;material&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="871" parent="325" name="invalid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
    </column>
    <index id="872" parent="325" name="PRIMARY">
      <ColNames>rec_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="873" parent="325" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="874" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="875" parent="326" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="876" parent="326" name="name">
      <DasType>varchar(30)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="877" parent="326" name="cardNo">
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="878" parent="326" name="password">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="879" parent="326" name="money">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="880" parent="326" name="useId">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="881" parent="326" name="printed">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="882" parent="326" name="add_time">
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="883" parent="326" name="active_time">
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="884" parent="326" name="expire_time">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="885" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="886" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="887" parent="327" name="cate_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="888" parent="327" name="pvs">
      <DasType>text|0s</DasType>
      <Position>2</Position>
    </column>
    <index id="889" parent="327" name="PRIMARY">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="890" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="891" parent="328" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="892" parent="328" name="cate_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="893" parent="328" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="894" parent="328" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="895" parent="328" name="cate_id">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="896" parent="328" name="goods_id">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="897" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="898" parent="329" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="899" parent="329" name="cate_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="900" parent="329" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="901" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="902" parent="329" name="cate_id">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="903" parent="329" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="904" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="905" parent="330" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="906" parent="330" name="cid">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="907" parent="330" name="title">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="908" parent="330" name="style">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="909" parent="330" name="cate_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="910" parent="330" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="911" parent="330" name="add_time">
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="912" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="913" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="914" parent="331" name="store_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="915" parent="331" name="regions">
      <DasType>text|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="916" parent="331" name="status">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="917" parent="331" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="918" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="919" parent="332" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="920" parent="332" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="921" parent="332" name="type">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;goods&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="922" parent="332" name="item_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="923" parent="332" name="keyword">
      <DasType>varchar(60)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="924" parent="332" name="add_time">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="925" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="926" parent="332" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="927" parent="332" name="type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="928" parent="332" name="item_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="929" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="930" parent="333" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="931" parent="333" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="932" parent="333" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="933" parent="333" name="money">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="934" parent="333" name="use_times">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="935" parent="333" name="start_time">
      <DasType>int unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="936" parent="333" name="end_time">
      <DasType>int unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="937" parent="333" name="amount">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="938" parent="333" name="available">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="939" parent="333" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="940" parent="333" name="total">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="941" parent="333" name="surplus">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="942" parent="333" name="received">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="943" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="944" parent="333" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="945" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="946" parent="334" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="947" parent="334" name="coupon_sn">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="948" parent="334" name="coupon_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="949" parent="334" name="remain_times">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <index id="950" parent="334" name="PRIMARY">
      <ColNames>coupon_sn</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="951" parent="334" name="coupon_id">
      <ColNames>coupon_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="952" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="953" parent="335" name="template_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="954" parent="335" name="name">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="955" parent="335" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="956" parent="335" name="types">
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="957" parent="335" name="dests">
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="958" parent="335" name="start_standards">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="959" parent="335" name="start_fees">
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="960" parent="335" name="add_standards">
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="961" parent="335" name="add_fees">
      <DasType>text|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="962" parent="335" name="created">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="963" parent="335" name="PRIMARY">
      <ColNames>template_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="964" parent="335" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="965" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="966" parent="336" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="967" parent="336" name="name">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="968" parent="336" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="969" parent="336" name="rules">
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="970" parent="336" name="created">
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="971" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="972" parent="336" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="973" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="974" parent="337" name="account_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="975" parent="337" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="976" parent="337" name="account">
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="977" parent="337" name="password">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="978" parent="337" name="money">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="979" parent="337" name="frozen">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="980" parent="337" name="nodrawal">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="981" parent="337" name="real_name">
      <DasType>varchar(30)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="982" parent="337" name="pay_status">
      <DasType>varchar(3)|0s</DasType>
      <DefaultExpression>&apos;off&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="983" parent="337" name="add_time">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="984" parent="337" name="last_update">
      <DasType>int|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="985" parent="337" name="PRIMARY">
      <ColNames>account_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="986" parent="337" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="987" parent="337" name="account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <key id="988" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="989" parent="338" name="recharge_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="990" parent="338" name="orderId">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="991" parent="338" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="992" parent="338" name="examine">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="993" parent="338" name="is_online">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <index id="994" parent="338" name="PRIMARY">
      <ColNames>recharge_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="995" parent="338" name="orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="996" parent="338" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="997" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="998" parent="339" name="record_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="999" parent="339" name="tradeNo">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1000" parent="339" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1001" parent="339" name="amount">
      <Comment>收支金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1002" parent="339" name="balance">
      <Comment>账户余额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1003" parent="339" name="flow">
      <Comment>资金方向</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;outlay&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1004" parent="339" name="tradeType">
      <Comment>收支类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;PAY&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1005" parent="339" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1006" parent="339" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="1007" parent="339" name="PRIMARY">
      <ColNames>record_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1008" parent="339" name="tradeNo">
      <ColNames>tradeNo</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1009" parent="339" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1010" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1011" parent="340" name="setting_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1012" parent="340" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1013" parent="340" name="trade_rate">
      <Comment>交易手续费</Comment>
      <DasType>decimal(10,3 digit)|0s</DasType>
      <DefaultExpression>0.000</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1014" parent="340" name="drawal_rate">
      <Comment>提现手续费</Comment>
      <DasType>decimal(10,3 digit)|0s</DasType>
      <DefaultExpression>0.000</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1015" parent="340" name="transfer_rate">
      <Comment>转账手续费</Comment>
      <DasType>decimal(10,3 digit)|0s</DasType>
      <DefaultExpression>0.000</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1016" parent="340" name="regive_rate">
      <Comment>充值赠送金额比率</Comment>
      <DasType>decimal(10,3 digit)|0s</DasType>
      <DefaultExpression>0.000</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1017" parent="340" name="guider_rate">
      <Comment>团长返佣比率</Comment>
      <DasType>decimal(10,3 digit)|0s</DasType>
      <DefaultExpression>0.000</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="1018" parent="340" name="PRIMARY">
      <ColNames>setting_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1019" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1020" parent="341" name="trade_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1021" parent="341" name="tradeNo">
      <Comment>交易号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1022" parent="341" name="outTradeNo">
      <Comment>第三方平台的交易号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1023" parent="341" name="payTradeNo">
      <Comment>支付订单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1024" parent="341" name="bizOrderId">
      <Comment>商户订单号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1025" parent="341" name="bizIdentity">
      <Comment>商户交易类型识别号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1026" parent="341" name="buyer_id">
      <Comment>交易买家</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1027" parent="341" name="seller_id">
      <Comment>交易卖家</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1028" parent="341" name="amount">
      <Comment>交易金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1029" parent="341" name="status">
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1030" parent="341" name="payment_code">
      <Comment>支付方式代号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1031" parent="341" name="pay_alter">
      <Comment>支付方式变更标记</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1032" parent="341" name="tradeCat">
      <Comment>交易分类</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="1033" parent="341" name="payType">
      <Comment>支付类型(担保即时)</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1034" parent="341" name="flow">
      <Comment>资金流向</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;outlay&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="1035" parent="341" name="payTerminal">
      <Comment>支付终端</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1036" parent="341" name="title">
      <Comment>交易标题</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="1037" parent="341" name="buyer_remark">
      <Comment>买家备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1038" parent="341" name="seller_remark">
      <Comment>卖家备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
    </column>
    <column id="1039" parent="341" name="openid">
      <Comment>第三方平台支付者用户标识</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="1040" parent="341" name="add_time">
      <DasType>int|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="1041" parent="341" name="pay_time">
      <DasType>int|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="1042" parent="341" name="end_time">
      <DasType>int|0s</DasType>
      <Position>23</Position>
    </column>
    <index id="1043" parent="341" name="PRIMARY">
      <ColNames>trade_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1044" parent="341" name="tradeNo">
      <ColNames>tradeNo</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1045" parent="341" name="payTradeNo">
      <ColNames>payTradeNo</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1046" parent="341" name="bizOrderId">
      <ColNames>bizOrderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1047" parent="341" name="buyer_id">
      <ColNames>buyer_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1048" parent="341" name="seller_id">
      <ColNames>seller_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1049" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1050" parent="342" name="draw_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1051" parent="342" name="orderId">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1052" parent="342" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1053" parent="342" name="drawtype">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;bank&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1054" parent="342" name="terminal">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1055" parent="342" name="account">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1056" parent="342" name="name">
      <DasType>varchar(50)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1057" parent="342" name="bank">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1058" parent="342" name="fee">
      <Comment>手续费</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="1059" parent="342" name="PRIMARY">
      <ColNames>draw_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1060" parent="342" name="orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1061" parent="342" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1062" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1063" parent="343" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1064" parent="343" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1065" parent="343" name="amount">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1066" parent="343" name="layer1">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1067" parent="343" name="layer2">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1068" parent="343" name="layer3">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1069" parent="343" name="goods">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1070" parent="343" name="store">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="1071" parent="343" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1072" parent="343" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1073" parent="344" name="diid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1074" parent="344" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1075" parent="344" name="item_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1076" parent="344" name="type">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1077" parent="344" name="created">
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="1078" parent="344" name="PRIMARY">
      <ColNames>diid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1079" parent="344" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1080" parent="344" name="item_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1081" parent="344" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1082" parent="345" name="dmid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1083" parent="345" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1084" parent="345" name="username">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1085" parent="345" name="parent_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1086" parent="345" name="phone_mob">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1087" parent="345" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1088" parent="345" name="logo">
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1089" parent="345" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1090" parent="345" name="remark">
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1091" parent="345" name="created">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="1092" parent="345" name="PRIMARY">
      <ColNames>dmid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1093" parent="345" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1094" parent="346" name="doid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1095" parent="346" name="rec_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1096" parent="346" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1097" parent="346" name="tradeNo">
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1098" parent="346" name="order_sn">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1099" parent="346" name="money">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1100" parent="346" name="layer">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1101" parent="346" name="ratio">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1102" parent="346" name="type">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1103" parent="346" name="created">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="1104" parent="346" name="PRIMARY">
      <ColNames>doid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1105" parent="346" name="rec_id">
      <ColNames>rec_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1106" parent="346" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1107" parent="346" name="order_sn">
      <ColNames>order_sn</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1108" parent="346" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1109" parent="347" name="dsid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1110" parent="347" name="type">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1111" parent="347" name="item_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1112" parent="347" name="ratio1">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1113" parent="347" name="ratio2">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1114" parent="347" name="ratio3">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1115" parent="347" name="enabled">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="1116" parent="347" name="PRIMARY">
      <ColNames>dsid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1117" parent="347" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1118" parent="348" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1119" parent="348" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1120" parent="348" name="friend_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1121" parent="348" name="add_time">
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="1122" parent="348" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1123" parent="348" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1124" parent="348" name="friend_id">
      <ColNames>friend_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1125" parent="348" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1126" parent="349" name="cate_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1127" parent="349" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1128" parent="349" name="cate_name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1129" parent="349" name="parent_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1130" parent="349" name="groupid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1131" parent="349" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1132" parent="349" name="if_show">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1133" parent="349" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1134" parent="349" name="ad">
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="1135" parent="349" name="PRIMARY">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1136" parent="349" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1137" parent="350" name="goods_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1138" parent="350" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1139" parent="350" name="type">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;material&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1140" parent="350" name="goods_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1141" parent="350" name="description">
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1142" parent="350" name="content">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1143" parent="350" name="cate_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1144" parent="350" name="cate_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1145" parent="350" name="brand">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1146" parent="350" name="spec_qty">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1147" parent="350" name="spec_name_1">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1148" parent="350" name="spec_name_2">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1149" parent="350" name="if_show">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="1150" parent="350" name="closed">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="1151" parent="350" name="close_reason">
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1152" parent="350" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="1153" parent="350" name="last_update">
      <DasType>int unsigned|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="1154" parent="350" name="default_spec">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1155" parent="350" name="default_image">
      <DasType>varchar(255)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="1156" parent="350" name="long_image">
      <DasType>varchar(255)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="1157" parent="350" name="video">
      <DasType>varchar(255)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="1158" parent="350" name="isnew">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>22</Position>
    </column>
    <column id="1159" parent="350" name="recommended">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>23</Position>
    </column>
    <column id="1160" parent="350" name="price">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>24</Position>
    </column>
    <column id="1161" parent="350" name="mkprice">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>25</Position>
    </column>
    <column id="1162" parent="350" name="tags">
      <DasType>varchar(102)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>26</Position>
    </column>
    <column id="1163" parent="350" name="dt_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>27</Position>
    </column>
    <index id="1164" parent="350" name="PRIMARY">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1165" parent="350" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1166" parent="350" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1167" parent="351" name="image_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1168" parent="351" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1169" parent="351" name="image_url">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1170" parent="351" name="thumbnail">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1171" parent="351" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1172" parent="351" name="file_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="1173" parent="351" name="PRIMARY">
      <ColNames>image_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1174" parent="351" name="goods_id">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1175" parent="351" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1176" parent="352" name="goods_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1177" parent="352" name="max_exchange">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
    </column>
    <index id="1178" parent="352" name="PRIMARY">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1179" parent="352" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1180" parent="353" name="pid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1181" parent="353" name="name">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1182" parent="353" name="ptype">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;select&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1183" parent="353" name="is_color">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1184" parent="353" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1185" parent="353" name="sort_order">
      <DasType>int|0s</DasType>
      <DefaultExpression>255</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="1186" parent="353" name="PRIMARY">
      <ColNames>pid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1187" parent="353" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1188" parent="354" name="vid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1189" parent="354" name="pid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1190" parent="354" name="pvalue">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1191" parent="354" name="color">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1192" parent="354" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1193" parent="354" name="sort_order">
      <DasType>int|0s</DasType>
      <DefaultExpression>255</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="1194" parent="354" name="PRIMARY">
      <ColNames>vid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1195" parent="354" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1196" parent="355" name="goods_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1197" parent="355" name="pvs">
      <DasType>text|0s</DasType>
      <Position>2</Position>
    </column>
    <index id="1198" parent="355" name="PRIMARY">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1199" parent="355" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1200" parent="356" name="ques_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1201" parent="356" name="question_content">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1202" parent="356" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1203" parent="356" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1204" parent="356" name="email">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1205" parent="356" name="item_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1206" parent="356" name="item_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1207" parent="356" name="reply_content">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1208" parent="356" name="time_post">
      <DasType>int unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1209" parent="356" name="time_reply">
      <DasType>int unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="1210" parent="356" name="if_new">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1211" parent="356" name="type">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;goods&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <index id="1212" parent="356" name="PRIMARY">
      <ColNames>ques_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1213" parent="356" name="userid">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1214" parent="356" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1215" parent="356" name="goods_id">
      <ColNames>item_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1216" parent="356" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1217" parent="357" name="spec_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1218" parent="357" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1219" parent="357" name="spec_1">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1220" parent="357" name="spec_2">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1221" parent="357" name="price">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1222" parent="357" name="mkprice">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1223" parent="357" name="stock">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1224" parent="357" name="sku">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1225" parent="357" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1226" parent="357" name="sort_order">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="1227" parent="357" name="PRIMARY">
      <ColNames>spec_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1228" parent="357" name="goods_id">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1229" parent="357" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1230" parent="358" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1231" parent="358" name="views">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1232" parent="358" name="collects">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1233" parent="358" name="orders">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1234" parent="358" name="sales">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1235" parent="358" name="comments">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="1236" parent="358" name="PRIMARY">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1237" parent="358" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1238" parent="359" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1239" parent="359" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1240" parent="359" name="owner">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1241" parent="359" name="phone_mob">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1242" parent="359" name="region_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1243" parent="359" name="address">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1244" parent="359" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1245" parent="359" name="banner">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1246" parent="359" name="longitude">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1247" parent="359" name="latitude">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1248" parent="359" name="created">
      <DasType>int|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1249" parent="359" name="status">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1250" parent="359" name="remark">
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="1251" parent="359" name="inviter">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <index id="1252" parent="359" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1253" parent="359" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1254" parent="360" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>公会ID，10001开始自增</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1255" parent="360" name="name">
      <Comment>公会名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1256" parent="360" name="hero_user_id">
      <Comment>会长ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1257" parent="360" name="power">
      <Comment>本周实力</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1258" parent="360" name="rank">
      <Comment>本周排名</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1259" parent="360" name="total_power">
      <Comment>累计实力</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1260" parent="360" name="member_num">
      <Comment>公会人数</Comment>
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1261" parent="360" name="join_type">
      <Comment>加入条件，0全部需要申请，1自由加入，2部分自由部分申请</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1262" parent="360" name="join_level">
      <Comment>自由加入需要的等级，join_type=2时生效</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="1263" parent="360" name="icon">
      <Comment>图标</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1264" parent="360" name="img">
      <Comment>背景图片</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1265" parent="360" name="desc">
      <Comment>描述简介</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1266" parent="360" name="game_ids">
      <Comment>关联的游戏id，逗号分隔存放</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="1267" parent="360" name="status">
      <Comment>0已解散，1有效的</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="1268" parent="360" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <index id="1269" parent="360" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1270" parent="360" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1271" parent="361" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1272" parent="361" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1273" parent="361" name="balance">
      <Comment>当前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1274" parent="361" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1275" parent="361" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1276" parent="361" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <index id="1277" parent="361" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1278" parent="361" name="uni_dnf_guild_balance_guild_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1279" parent="361" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1280" parent="361" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1281" parent="361" name="uni_dnf_guild_balance_guild_id">
      <UnderlyingIndexName>uni_dnf_guild_balance_guild_id</UnderlyingIndexName>
    </key>
    <column id="1282" parent="362" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1283" parent="362" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1284" parent="362" name="change_type">
      <Comment>变动类型（收入/支出）</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1285" parent="362" name="reason">
      <Comment>变动原因-代码中常量控制,充值,抽奖，方法奖励等</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1286" parent="362" name="balance">
      <Comment>变动金额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1287" parent="362" name="balance_before">
      <Comment>变动前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1288" parent="362" name="balance_after">
      <Comment>变动后余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1289" parent="362" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1290" parent="362" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1291" parent="362" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="1292" parent="362" name="created_at">
      <Comment>变动时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1293" parent="362" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
    </column>
    <index id="1294" parent="362" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1295" parent="362" name="g">
      <ColNames>guild_id
created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1296" parent="362" name="ct">
      <ColNames>change_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1297" parent="362" name="bl">
      <ColNames>balance</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1298" parent="362" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1299" parent="363" name="id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1300" parent="363" name="code">
      <Comment>业务code</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1301" parent="363" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1302" parent="363" name="ext">
      <Comment>配置信息</Comment>
      <DasType>json|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1303" parent="363" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1304" parent="363" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="1305" parent="363" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1306" parent="363" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1307" parent="363" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1308" parent="364" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1309" parent="364" name="amount">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>2</Position>
    </column>
    <index id="1310" parent="364" name="PRIMARY">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1311" parent="364" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1312" parent="365" name="log_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1313" parent="365" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1314" parent="365" name="order_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1315" parent="365" name="order_sn">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1316" parent="365" name="changes">
      <DasType>decimal(25,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1317" parent="365" name="balance">
      <DasType>decimal(25,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1318" parent="365" name="type">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1319" parent="365" name="state">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1320" parent="365" name="flag">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1321" parent="365" name="add_time">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="1322" parent="365" name="PRIMARY">
      <ColNames>log_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1323" parent="365" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1324" parent="366" name="setting_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1325" parent="366" name="rate">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1326" parent="366" name="register">
      <DasType>decimal(10)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1327" parent="366" name="signin">
      <DasType>decimal(10)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1328" parent="366" name="openshop">
      <DasType>decimal(10)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1329" parent="366" name="buygoods">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1330" parent="366" name="enabled">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="1331" parent="366" name="PRIMARY">
      <ColNames>setting_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1332" parent="366" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1333" parent="367" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1334" parent="367" name="goods_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1335" parent="367" name="title">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1336" parent="367" name="summary">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1337" parent="367" name="start_time">
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1338" parent="367" name="end_time">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1339" parent="367" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1340" parent="367" name="rules">
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1341" parent="367" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="1342" parent="367" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1343" parent="367" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1344" parent="368" name="id">
      <AutoIncrement>19</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1345" parent="368" name="name">
      <Comment>抽奖名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1346" parent="368" name="description">
      <Comment>抽奖描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1347" parent="368" name="start_at">
      <Comment>抽奖开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1348" parent="368" name="end_at">
      <Comment>抽奖结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1349" parent="368" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1350" parent="368" name="status">
      <Comment>抽奖状态 0 未开奖 1已开奖 2已作废</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1351" parent="368" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1352" parent="368" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="1353" parent="368" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1354" parent="368" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1355" parent="368" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1356" parent="369" name="id">
      <AutoIncrement>15</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1357" parent="369" name="lottery_id">
      <Comment>关联的抽奖ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1358" parent="369" name="prize_name">
      <Comment>奖品名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1359" parent="369" name="prize_count">
      <Comment>奖品数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1360" parent="369" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1361" parent="369" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1362" parent="369" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1363" parent="369" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1364" parent="369" name="idx_dnf_lottery_prize_lottery_id">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1365" parent="369" name="lid">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1366" parent="369" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1367" parent="369" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1368" parent="370" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1369" parent="370" name="lottery_id">
      <Comment>关联的抽奖ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1370" parent="370" name="user_id">
      <Comment>参与用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1371" parent="370" name="is_winner">
      <Comment>是否中奖 0未中奖 1中奖</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1372" parent="370" name="prize_id">
      <Comment>中奖的奖品ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1373" parent="370" name="created_at">
      <Comment>记录创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1374" parent="370" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1375" parent="370" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1376" parent="370" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1377" parent="370" name="idx_dnf_lottery_record_lottery_id">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1378" parent="370" name="lid">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1379" parent="370" name="idx_dnf_lottery_record_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1380" parent="370" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1381" parent="370" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1382" parent="371" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1383" parent="371" name="from_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1384" parent="371" name="to_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1385" parent="371" name="title">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1386" parent="371" name="content">
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1387" parent="371" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1388" parent="371" name="last_update">
      <DasType>int unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1389" parent="371" name="new">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1390" parent="371" name="parent_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1391" parent="371" name="status">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="1392" parent="371" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1393" parent="371" name="from_id">
      <ColNames>from_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1394" parent="371" name="to_id">
      <ColNames>to_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1395" parent="371" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1396" parent="372" name="meal_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1397" parent="372" name="store_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1398" parent="372" name="title">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1399" parent="372" name="price">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1400" parent="372" name="keyword">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1401" parent="372" name="description">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1402" parent="372" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1403" parent="372" name="created">
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1404" parent="372" name="PRIMARY">
      <ColNames>meal_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1405" parent="372" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1406" parent="373" name="mg_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1407" parent="373" name="meal_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1408" parent="373" name="goods_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="1409" parent="373" name="PRIMARY">
      <ColNames>mg_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1410" parent="373" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1411" parent="374" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>角色成员ID</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1412" parent="374" name="role_name">
      <Comment>角色昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1413" parent="374" name="role_icon">
      <Comment>角色icon</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1414" parent="374" name="adventure_name">
      <Comment>冒险团名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1415" parent="374" name="damage">
      <Comment>当前伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1416" parent="374" name="hero_type">
      <Comment>英雄类型:1 输出型、2增益型</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1417" parent="374" name="hero_class">
      <Comment>英雄职业: 1枪手、2 战士</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1418" parent="374" name="hero_set">
      <Comment>套装</Comment>
      <DasType>text|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1419" parent="374" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="1420" parent="374" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1421" parent="374" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1422" parent="375" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>1开始自增</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1423" parent="375" name="member_id">
      <Comment>关联成员</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1424" parent="375" name="vdate">
      <Comment>日期</Comment>
      <DasType>date|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1425" parent="375" name="attribute">
      <Comment>数值/进阶/伤害</Comment>
      <DasType>varchar(23)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1426" parent="375" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="1427" parent="375" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1428" parent="375" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1429" parent="376" name="nav_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1430" parent="376" name="type">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;middle&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1431" parent="376" name="title">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1432" parent="376" name="link">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1433" parent="376" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1434" parent="376" name="if_show">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1435" parent="376" name="open_new">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1436" parent="376" name="hot">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="1437" parent="376" name="PRIMARY">
      <ColNames>nav_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1438" parent="376" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1439" parent="377" name="order_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1440" parent="377" name="order_sn">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1441" parent="377" name="gtype">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;material&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1442" parent="377" name="otype">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;normal&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1443" parent="377" name="seller_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1444" parent="377" name="seller_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1445" parent="377" name="buyer_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1446" parent="377" name="buyer_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1447" parent="377" name="status">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1448" parent="377" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="1449" parent="377" name="payment_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1450" parent="377" name="payment_code">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1451" parent="377" name="pay_time">
      <DasType>int unsigned|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="1452" parent="377" name="ship_time">
      <DasType>int unsigned|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1453" parent="377" name="receive_time">
      <DasType>int unsigned|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1454" parent="377" name="finished_time">
      <DasType>int unsigned|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="1455" parent="377" name="goods_amount">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1456" parent="377" name="discount">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1457" parent="377" name="order_amount">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>19</Position>
    </column>
    <column id="1458" parent="377" name="evaluation_status">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="1459" parent="377" name="evaluation_time">
      <DasType>int unsigned|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="1460" parent="377" name="service_evaluation">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>22</Position>
    </column>
    <column id="1461" parent="377" name="shipped_evaluation">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>23</Position>
    </column>
    <column id="1462" parent="377" name="anonymous">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>24</Position>
    </column>
    <column id="1463" parent="377" name="postscript">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>25</Position>
    </column>
    <column id="1464" parent="377" name="pay_alter">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>26</Position>
    </column>
    <column id="1465" parent="377" name="flag">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>27</Position>
    </column>
    <column id="1466" parent="377" name="memo">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>28</Position>
    </column>
    <column id="1467" parent="377" name="adjust_amount">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>29</Position>
    </column>
    <column id="1468" parent="377" name="guider_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>30</Position>
    </column>
    <index id="1469" parent="377" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1470" parent="377" name="order_sn">
      <ColNames>order_sn</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1471" parent="377" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1472" parent="378" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1473" parent="378" name="order_id">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1474" parent="378" name="code">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1475" parent="378" name="company">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1476" parent="378" name="number">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="1477" parent="378" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1478" parent="378" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1479" parent="379" name="order_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1480" parent="379" name="consignee">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1481" parent="379" name="region_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1482" parent="379" name="address">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1483" parent="379" name="phone_tel">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1484" parent="379" name="phone_mob">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1485" parent="379" name="deliveryName">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1486" parent="379" name="freight">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="1487" parent="379" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1488" parent="379" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1489" parent="380" name="rec_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1490" parent="380" name="order_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1491" parent="380" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1492" parent="380" name="goods_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1493" parent="380" name="spec_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1494" parent="380" name="specification">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1495" parent="380" name="price">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1496" parent="380" name="quantity">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1497" parent="380" name="goods_image">
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1498" parent="380" name="evaluation">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1499" parent="380" name="comment">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1500" parent="380" name="images">
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="1501" parent="380" name="is_valid">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="1502" parent="380" name="reply_comment">
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1503" parent="380" name="reply_time">
      <DasType>int|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1504" parent="380" name="inviteType">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1505" parent="380" name="inviteRatio">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1506" parent="380" name="inviteUid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1507" parent="380" name="status">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
    </column>
    <index id="1508" parent="380" name="PRIMARY">
      <ColNames>rec_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1509" parent="380" name="order_id">
      <ColNames>order_id
goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1510" parent="380" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1511" parent="381" name="order_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1512" parent="381" name="buyer_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1513" parent="381" name="frozen_integral">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="1514" parent="381" name="PRIMARY">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1515" parent="381" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1516" parent="382" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1517" parent="382" name="order_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1518" parent="382" name="operator">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1519" parent="382" name="status">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1520" parent="382" name="remark">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1521" parent="382" name="created">
      <DasType>int unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="1522" parent="382" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1523" parent="382" name="order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1524" parent="382" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1525" parent="383" name="partner_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1526" parent="383" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1527" parent="383" name="title">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1528" parent="383" name="link">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1529" parent="383" name="logo">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1530" parent="383" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1531" parent="383" name="if_show">
      <DasType>tinyint unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1532" parent="383" name="PRIMARY">
      <ColNames>partner_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1533" parent="383" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1534" parent="383" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1535" parent="384" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1536" parent="384" name="instance">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1537" parent="384" name="code">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1538" parent="384" name="name">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1539" parent="384" name="subname">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1540" parent="384" name="summary">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1541" parent="384" name="config">
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1542" parent="384" name="enabled">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="1543" parent="384" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1544" parent="384" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1545" parent="385" name="piid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1546" parent="385" name="goods_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1547" parent="385" name="appid">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1548" parent="385" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1549" parent="385" name="config">
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1550" parent="385" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1551" parent="385" name="add_time">
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1552" parent="385" name="PRIMARY">
      <ColNames>piid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1553" parent="385" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1554" parent="386" name="psid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1555" parent="386" name="appid">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1556" parent="386" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1557" parent="386" name="rules">
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1558" parent="386" name="status">
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1559" parent="386" name="add_time">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="1560" parent="386" name="PRIMARY">
      <ColNames>psid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1561" parent="386" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1562" parent="387" name="id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1563" parent="387" name="title">
      <Comment>问卷标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1564" parent="387" name="description">
      <Comment>问卷描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1565" parent="387" name="start_at">
      <Comment>问卷开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1566" parent="387" name="end_at">
      <Comment>问卷结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1567" parent="387" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1568" parent="387" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1569" parent="387" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1570" parent="387" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1571" parent="387" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1572" parent="387" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1573" parent="388" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1574" parent="388" name="questionnaire_id">
      <Comment>关联的问卷ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1575" parent="388" name="question_id">
      <Comment>问卷选项id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1576" parent="388" name="member_id">
      <Comment>回答的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1577" parent="388" name="answer">
      <Comment>用户的回答</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1578" parent="388" name="created_at">
      <Comment>回答时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1579" parent="388" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1580" parent="388" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1581" parent="388" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1582" parent="388" name="idx_dnf_questionnaire_answer_questionnaire_id">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1583" parent="388" name="quid">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1584" parent="388" name="idx_dnf_questionnaire_answer_question_id">
      <ColNames>question_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1585" parent="388" name="qid">
      <ColNames>question_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1586" parent="388" name="mid">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1587" parent="388" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1588" parent="388" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1589" parent="389" name="id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1590" parent="389" name="questionnaire_id">
      <Comment>关联的问卷ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1591" parent="389" name="question">
      <Comment>问卷问题</Comment>
      <DasType>varchar(256)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1592" parent="389" name="type">
      <Comment>问题类型: 选择题/问答题</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1593" parent="389" name="options">
      <Comment>选项</Comment>
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1594" parent="389" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1595" parent="389" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1596" parent="389" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1597" parent="389" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1598" parent="389" name="idx_dnf_questionnaire_question_questionnaire_id">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1599" parent="389" name="quid">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1600" parent="389" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1601" parent="389" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1602" parent="390" name="recom_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1603" parent="390" name="recom_name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1604" parent="390" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="1605" parent="390" name="PRIMARY">
      <ColNames>recom_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1606" parent="390" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1607" parent="390" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1608" parent="391" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1609" parent="391" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1610" parent="391" name="recom_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="1611" parent="391" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1612" parent="391" name="goods_id">
      <ColNames>goods_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1613" parent="391" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1614" parent="392" name="refund_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1615" parent="392" name="tradeNo">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1616" parent="392" name="refund_sn">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1617" parent="392" name="title">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1618" parent="392" name="refund_reason">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1619" parent="392" name="refund_desc">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1620" parent="392" name="total_fee">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1621" parent="392" name="goods_fee">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1622" parent="392" name="freight">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1623" parent="392" name="refund_total_fee">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1624" parent="392" name="refund_goods_fee">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="1625" parent="392" name="refund_freight">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1626" parent="392" name="buyer_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="1627" parent="392" name="seller_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="1628" parent="392" name="status">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="1629" parent="392" name="shipped">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1630" parent="392" name="intervene">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1631" parent="392" name="created">
      <DasType>int|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="1632" parent="392" name="finished">
      <DasType>int|0s</DasType>
      <Position>19</Position>
    </column>
    <index id="1633" parent="392" name="PRIMARY">
      <ColNames>refund_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1634" parent="392" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1635" parent="393" name="rm_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1636" parent="393" name="owner_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1637" parent="393" name="owner_role">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1638" parent="393" name="refund_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1639" parent="393" name="content">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1640" parent="393" name="image">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1641" parent="393" name="created">
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1642" parent="393" name="PRIMARY">
      <ColNames>rm_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1643" parent="393" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1644" parent="394" name="region_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1645" parent="394" name="name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1646" parent="394" name="parent_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1647" parent="394" name="letter">
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1648" parent="394" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1649" parent="394" name="if_show">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="1650" parent="394" name="PRIMARY">
      <ColNames>region_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1651" parent="394" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1652" parent="394" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1653" parent="395" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1654" parent="395" name="userid">
      <Comment>举报人ID</Comment>
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1655" parent="395" name="store_id">
      <Comment>被举报店铺ID</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1656" parent="395" name="goods_id">
      <Comment>被举报商品ID</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1657" parent="395" name="content">
      <Comment>举报内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1658" parent="395" name="images">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1659" parent="395" name="add_time">
      <Comment>添加时间</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1660" parent="395" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1661" parent="395" name="examine">
      <Comment>审核员</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1662" parent="395" name="verify">
      <Comment>审核说明</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="1663" parent="395" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1664" parent="395" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1665" parent="396" name="cate_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1666" parent="396" name="cate_name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1667" parent="396" name="parent_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1668" parent="396" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1669" parent="396" name="if_show">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <index id="1670" parent="396" name="PRIMARY">
      <ColNames>cate_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1671" parent="396" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1672" parent="396" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1673" parent="397" name="grade_id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1674" parent="397" name="grade_name">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1675" parent="397" name="goods_limit">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1676" parent="397" name="space_limit">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1677" parent="397" name="charge">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1678" parent="397" name="need_confirm">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1679" parent="397" name="description">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1680" parent="397" name="skins">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1681" parent="397" name="wap_skins">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1682" parent="397" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="1683" parent="397" name="PRIMARY">
      <ColNames>grade_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1684" parent="397" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1685" parent="398" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1686" parent="398" name="sgrade_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1687" parent="398" name="buy_integral">
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <index id="1688" parent="398" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1689" parent="398" name="sgrade_id">
      <ColNames>sgrade_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1690" parent="398" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1691" parent="399" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1692" parent="399" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1693" parent="399" name="num">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1694" parent="399" name="functions">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1695" parent="399" name="state">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <index id="1696" parent="399" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1697" parent="399" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1698" parent="400" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1699" parent="400" name="code">
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1700" parent="400" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1701" parent="400" name="receiver">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1702" parent="400" name="verifycode">
      <DasType>int unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1703" parent="400" name="codekey">
      <DasType>varchar(32)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1704" parent="400" name="content">
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1705" parent="400" name="quantity">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1706" parent="400" name="type">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="1707" parent="400" name="status">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="1708" parent="400" name="message">
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1709" parent="400" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="1710" parent="400" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1711" parent="400" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1712" parent="401" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1713" parent="401" name="code">
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1714" parent="401" name="scene">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1715" parent="401" name="signName">
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1716" parent="401" name="templateId">
      <DasType>varchar(40)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1717" parent="401" name="content">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1718" parent="401" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1719" parent="401" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1720" parent="401" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1721" parent="402" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1722" parent="402" name="store_name">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1723" parent="402" name="owner">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1724" parent="402" name="identity_card">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1725" parent="402" name="region_id">
      <DasType>int unsigned|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1726" parent="402" name="address">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1727" parent="402" name="tel">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1728" parent="402" name="qq">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1729" parent="402" name="sgrade">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="1730" parent="402" name="stype">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;personal&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1731" parent="402" name="joinway">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1732" parent="402" name="apply_remark">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="1733" parent="402" name="credit_value">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="1734" parent="402" name="praise_rate">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="1735" parent="402" name="state">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="1736" parent="402" name="close_reason">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="1737" parent="402" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="1738" parent="402" name="end_time">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1739" parent="402" name="certification">
      <DasType>varchar(255)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="1740" parent="402" name="sort_order">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="1741" parent="402" name="recommended">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>21</Position>
    </column>
    <column id="1742" parent="402" name="theme">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>22</Position>
    </column>
    <column id="1743" parent="402" name="banner">
      <DasType>varchar(255)|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="1744" parent="402" name="pcbanner">
      <DasType>varchar(255)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="1745" parent="402" name="store_logo">
      <DasType>varchar(255)|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="1746" parent="402" name="description">
      <DasType>text|0s</DasType>
      <Position>26</Position>
    </column>
    <column id="1747" parent="402" name="identity_front">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>27</Position>
    </column>
    <column id="1748" parent="402" name="identity_back">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>28</Position>
    </column>
    <column id="1749" parent="402" name="business_license">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>29</Position>
    </column>
    <column id="1750" parent="402" name="swiper">
      <DasType>text|0s</DasType>
      <Position>30</Position>
    </column>
    <column id="1751" parent="402" name="longitude">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>31</Position>
    </column>
    <column id="1752" parent="402" name="latitude">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>32</Position>
    </column>
    <column id="1753" parent="402" name="zoom">
      <DasType>int|0s</DasType>
      <DefaultExpression>15</DefaultExpression>
      <Position>33</Position>
    </column>
    <index id="1754" parent="402" name="PRIMARY">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1755" parent="402" name="store_name">
      <ColNames>store_name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1756" parent="402" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1757" parent="403" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>队伍ID,自增</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1758" parent="403" name="name">
      <Comment>队伍名称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1759" parent="403" name="guid_id">
      <Comment>关联公会</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1760" parent="403" name="type">
      <Comment>类型：1 无固定队伍 2 公会成员</Comment>
      <DasType>tinyint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1761" parent="403" name="status">
      <Comment>状态 1待完成 2待确认 3已完成</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1762" parent="403" name="is_release">
      <Comment>是否解散：连续1周3人不在一起组队（上传图无对方）则自动解散</Comment>
      <DasType>tinyint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1763" parent="403" name="total_damage">
      <Comment>总伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1764" parent="403" name="guild_rank">
      <Comment>公会排名或公会贡献(无固定队友时)</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1765" parent="403" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="1766" parent="403" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1767" parent="403" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1768" parent="404" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>ID</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1769" parent="404" name="team_id">
      <Comment>关联队伍</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1770" parent="404" name="member_id">
      <Comment>关联成员</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1771" parent="404" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="1772" parent="404" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1773" parent="404" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1774" parent="405" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1775" parent="405" name="goods_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1776" parent="405" name="title">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1777" parent="405" name="status">
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1778" parent="405" name="store_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1779" parent="405" name="people">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;2&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1780" parent="405" name="specs">
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1781" parent="405" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1782" parent="405" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1783" parent="406" name="logid">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1784" parent="406" name="tbid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1785" parent="406" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1786" parent="406" name="order_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1787" parent="406" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1788" parent="406" name="teamid">
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1789" parent="406" name="leader">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1790" parent="406" name="people">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;2&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1791" parent="406" name="status">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1792" parent="406" name="created">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1793" parent="406" name="expired">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1794" parent="406" name="pay_time">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="1795" parent="406" name="PRIMARY">
      <ColNames>logid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1796" parent="406" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1797" parent="407" name="file_id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1798" parent="407" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="1799" parent="407" name="file_type">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1800" parent="407" name="file_size">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1801" parent="407" name="file_name">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1802" parent="407" name="file_path">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1803" parent="407" name="add_time">
      <DasType>int unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1804" parent="407" name="belong">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1805" parent="407" name="item_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1806" parent="407" name="link_url">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="1807" parent="407" name="PRIMARY">
      <ColNames>file_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1808" parent="407" name="store_id">
      <ColNames>store_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1809" parent="407" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1810" parent="408" name="userid">
      <AutoIncrement>3</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1811" parent="408" name="username">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1812" parent="408" name="nickname">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1813" parent="408" name="email">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1814" parent="408" name="password">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1815" parent="408" name="password_reset_token">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="1816" parent="408" name="real_name">
      <DasType>varchar(60)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1817" parent="408" name="gender">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="1818" parent="408" name="birthday">
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="1819" parent="408" name="phone_tel">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1820" parent="408" name="phone_mob">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="1821" parent="408" name="qq">
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="1822" parent="408" name="create_time">
      <DasType>int unsigned|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="1823" parent="408" name="update_time">
      <DasType>int unsigned|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1824" parent="408" name="last_login">
      <DasType>int unsigned|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1825" parent="408" name="last_ip">
      <DasType>varchar(15)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="1826" parent="408" name="logins">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1827" parent="408" name="ugrade">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1828" parent="408" name="regtype">
      <Comment>注册渠道</Comment>
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="1829" parent="408" name="portrait">
      <DasType>varchar(255)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="1830" parent="408" name="activation">
      <DasType>varchar(60)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="1831" parent="408" name="locked">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>22</Position>
    </column>
    <column id="1832" parent="408" name="imforbid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>23</Position>
    </column>
    <column id="1833" parent="408" name="auth_key">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>24</Position>
    </column>
    <index id="1834" parent="408" name="PRIMARY">
      <ColNames>userid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1835" parent="408" name="username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1836" parent="408" name="phone_mob">
      <ColNames>phone_mob</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1837" parent="408" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1838" parent="409" name="id">
      <AutoIncrement>8</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1839" parent="409" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1840" parent="409" name="username">
      <DasType>varchar(50)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1841" parent="409" name="scene">
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1842" parent="409" name="ip">
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1843" parent="409" name="address">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1844" parent="409" name="add_time">
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1845" parent="409" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1846" parent="409" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1847" parent="410" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1848" parent="410" name="phone">
      <Comment>手机号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1849" parent="410" name="nickname">
      <Comment>用户昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1850" parent="410" name="avatar_url">
      <Comment>头像地址</Comment>
      <DasType>varchar(300)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1851" parent="410" name="register_at">
      <Comment>注册时间[同步第一个绑定的角色的注册时间]</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="1852" parent="410" name="status">
      <Comment>用户状态，-1封号，0禁言，1正常</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="1853" parent="410" name="admin_uid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="1854" parent="410" name="admin_username">
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1855" parent="410" name="is_official">
      <Comment>是否官方 0 不是 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1856" parent="410" name="guild_id">
      <Comment>所属公会ID</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="1857" parent="410" name="password">
      <Comment>密码md5</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1858" parent="410" name="updated_at">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="1859" parent="410" name="created_at">
      <Comment>账号创建时间</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="1860" parent="410" name="last_login_at">
      <Comment>最近登录时间</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <index id="1861" parent="410" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1862" parent="410" name="phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1863" parent="410" name="nickname">
      <ColNames>nickname</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1864" parent="410" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1865" parent="410" name="phone">
      <UnderlyingIndexName>phone</UnderlyingIndexName>
    </key>
    <key id="1866" parent="410" name="nickname">
      <UnderlyingIndexName>nickname</UnderlyingIndexName>
    </key>
    <column id="1867" parent="411" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1868" parent="411" name="dnf_user_id">
      <Comment>user_mini表主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1869" parent="411" name="openid">
      <Comment>微信openid</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1870" parent="411" name="created_at">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="1871" parent="411" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1872" parent="411" name="dnf_user_id">
      <ColNames>dnf_user_id
openid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1873" parent="411" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="1874" parent="411" name="dnf_user_id">
      <UnderlyingIndexName>dnf_user_id</UnderlyingIndexName>
    </key>
    <column id="1875" parent="412" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1876" parent="412" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1877" parent="412" name="privs">
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="1878" parent="412" name="PRIMARY">
      <ColNames>userid
store_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1879" parent="412" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1880" parent="413" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1881" parent="413" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1882" parent="413" name="token">
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1883" parent="413" name="expire_time">
      <DasType>int unsigned|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="1884" parent="413" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1885" parent="413" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1886" parent="414" name="id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1887" parent="414" name="title">
      <Comment>投票标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1888" parent="414" name="description">
      <Comment>投票描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1889" parent="414" name="start_at">
      <Comment>投票开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1890" parent="414" name="end_at">
      <Comment>投票结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1891" parent="414" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1892" parent="414" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1893" parent="414" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="1894" parent="414" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1895" parent="414" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1896" parent="414" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1897" parent="415" name="id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1898" parent="415" name="vote_id">
      <Comment>关联的投票ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1899" parent="415" name="option">
      <Comment>投票选项</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1900" parent="415" name="vote_count">
      <Comment>选项的投票数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1901" parent="415" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1902" parent="415" name="created_at">
      <Comment>投票时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1903" parent="415" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1904" parent="415" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1905" parent="415" name="idx_dnf_vote_option_vote_id">
      <ColNames>vote_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1906" parent="415" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1907" parent="415" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1908" parent="416" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1909" parent="416" name="vote_id">
      <Comment>关联的投票ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1910" parent="416" name="option_id">
      <Comment>用户选择的选项ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1911" parent="416" name="user_id">
      <Comment>投票用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1912" parent="416" name="created_at">
      <Comment>投票时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1913" parent="416" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1914" parent="416" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="1915" parent="416" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1916" parent="416" name="idx_dnf_vote_result_vote_id">
      <ColNames>vote_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="1917" parent="416" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1918" parent="416" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1919" parent="417" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1920" parent="417" name="toid">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1921" parent="417" name="fromid">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1922" parent="417" name="groupid">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="1923" parent="417" name="store_id">
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1924" parent="417" name="store_name">
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1925" parent="417" name="content">
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </column>
    <column id="1926" parent="417" name="unread">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="1927" parent="417" name="created">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="1928" parent="417" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1929" parent="417" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1930" parent="418" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1931" parent="418" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1932" parent="418" name="parent_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1933" parent="418" name="name">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1934" parent="418" name="type">
      <DasType>varchar(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1935" parent="418" name="add_time">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1936" parent="418" name="sort_order">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;255&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="1937" parent="418" name="url">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1938" parent="418" name="reply_id">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="1939" parent="418" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1940" parent="418" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1941" parent="419" name="reply_id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1942" parent="419" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1943" parent="419" name="type">
      <Comment>回复类型0文字1图文</Comment>
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1944" parent="419" name="action">
      <Comment>消息类型：关注、文本、关键字</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1945" parent="419" name="title">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1946" parent="419" name="link">
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1947" parent="419" name="image">
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1948" parent="419" name="keywords">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1949" parent="419" name="description">
      <DasType>text|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1950" parent="419" name="add_time">
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="1951" parent="419" name="PRIMARY">
      <ColNames>reply_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1952" parent="419" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1953" parent="420" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1954" parent="420" name="userid">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1955" parent="420" name="code">
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1956" parent="420" name="name">
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1957" parent="420" name="token">
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1958" parent="420" name="appid">
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1959" parent="420" name="appsecret">
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1960" parent="420" name="codeurl">
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1961" parent="420" name="if_valid">
      <DasType>tinyint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="1962" parent="420" name="autologin">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="1963" parent="420" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1964" parent="420" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1965" parent="421" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1966" parent="421" name="goods_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1967" parent="421" name="store_id">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="1968" parent="421" name="price">
      <DasType>decimal(10,2 digit) unsigned|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="1969" parent="421" name="quantity">
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="1970" parent="421" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="1971" parent="421" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1972" parent="421" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1973" parent="422" name="id">
      <AutoIncrement>34</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1974" parent="422" name="title">
      <Comment>活动标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1975" parent="422" name="type">
      <Comment>活动类型: 娱乐活动/奖金活动</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1976" parent="422" name="game">
      <Comment>选择的游戏名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="1977" parent="422" name="description">
      <Comment>活动描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="1978" parent="422" name="organizer">
      <Comment>活动主办方</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="1979" parent="422" name="assistant">
      <Comment>活动协助人</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="1980" parent="422" name="start_at">
      <Comment>活动开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="1981" parent="422" name="end_at">
      <Comment>活动结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="1982" parent="422" name="registration_end">
      <Comment>报名截止时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="1983" parent="422" name="rules">
      <Comment>活动规则</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="1984" parent="422" name="requirements">
      <Comment>参与要求</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="1985" parent="422" name="ext">
      <Comment>活动拓展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="1986" parent="422" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="1987" parent="422" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="1988" parent="422" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="1989" parent="422" name="status">
      <Comment>活动状态 0 草稿 1已撤销 2已结束</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="1990" parent="422" name="participant">
      <Comment>活动参与人数</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="1991" parent="422" name="cover">
      <Comment>活动封面</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="1992" parent="422" name="created_user">
      <Comment>创建人</Comment>
      <DasType>int|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="1993" parent="422" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="1994" parent="422" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="1995" parent="422" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1996" parent="423" name="id">
      <AutoIncrement>29</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1997" parent="423" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="1998" parent="423" name="member_id">
      <Comment>参与用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="1999" parent="423" name="upload_proof">
      <Comment>上传证明</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2000" parent="423" name="created_at">
      <Comment>参与时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2001" parent="423" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2002" parent="423" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2003" parent="423" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2004" parent="423" name="aid">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2005" parent="423" name="idx_dnf_activity_member_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2006" parent="423" name="idx_dnf_activity_member_member_id">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2007" parent="423" name="mid">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2008" parent="423" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2009" parent="423" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2010" parent="424" name="id">
      <AutoIncrement>22</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2011" parent="424" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2012" parent="424" name="level">
      <Comment>奖励等级: 一等奖/二等奖</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2013" parent="424" name="reward_type">
      <Comment>奖励类型: 奖金/道具</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2014" parent="424" name="reward_value">
      <Comment>奖励内容（JSON 格式）</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2015" parent="424" name="quantity">
      <Comment>奖励数量/金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>1.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2016" parent="424" name="quantity_max">
      <Comment>奖励人数上限</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2017" parent="424" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2018" parent="424" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2019" parent="424" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="2020" parent="424" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2021" parent="424" name="idx_dnf_activity_reward_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2022" parent="424" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2023" parent="424" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2024" parent="425" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2025" parent="425" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2026" parent="425" name="reward_id">
      <Comment>关联的奖励ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2027" parent="425" name="member_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2028" parent="425" name="member_name">
      <Comment>用户名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2029" parent="425" name="is_assign">
      <Comment>是否已发放</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2030" parent="425" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2031" parent="425" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2032" parent="425" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="2033" parent="425" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2034" parent="425" name="idx_dnf_activity_reward_assign_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2035" parent="425" name="idx_dnf_activity_reward_assign_reward_id">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2036" parent="425" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2037" parent="425" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2038" parent="426" name="id">
      <AutoIncrement>34</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2039" parent="426" name="activity_id">
      <Comment>关联的活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2040" parent="426" name="reward_id">
      <Comment>关联的奖励ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2041" parent="426" name="member_id">
      <Comment>接收用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2042" parent="426" name="member_name">
      <Comment>接收用户名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2043" parent="426" name="reward_type">
      <Comment>奖励类型: 奖金/道具</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2044" parent="426" name="reward_value">
      <Comment>奖励内容</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2045" parent="426" name="ext">
      <Comment>记录一些发放情况</Comment>
      <DasType>json|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2046" parent="426" name="status">
      <Comment>发放状态: pending/success/failed</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="2047" parent="426" name="failure_reason">
      <Comment>失败原因</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2048" parent="426" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="2049" parent="426" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="2050" parent="426" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="2051" parent="426" name="grant">
      <Comment>发放方式</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="2052" parent="426" name="quantity">
      <Comment>奖励数量/金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>1.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <index id="2053" parent="426" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2054" parent="426" name="aid">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2055" parent="426" name="idx_dnf_activity_reward_log_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2056" parent="426" name="idx_dnf_activity_reward_log_reward_id">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2057" parent="426" name="rid">
      <ColNames>reward_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2058" parent="426" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2059" parent="426" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2060" parent="427" name="range_id">
      <AutoIncrement>7</AutoIncrement>
      <Comment>自增ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2061" parent="427" name="packet_id">
      <Comment>关联到具体的红包</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2062" parent="427" name="min_value">
      <Comment>奖励范围最小值</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2063" parent="427" name="max_value">
      <Comment>奖励范围最大值</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2064" parent="427" name="weight">
      <Comment>权重，用于计算概率</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2065" parent="427" name="type">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <foreign-key id="2066" parent="427" name="fk_dnf_draw_prize_ranges_red">
      <ColNames>packet_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>packet_id</RefColNames>
      <RefTableName>dnf_draw_red_packets</RefTableName>
    </foreign-key>
    <index id="2067" parent="427" name="PRIMARY">
      <ColNames>range_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2068" parent="427" name="fk_dnf_draw_prize_ranges_red">
      <ColNames>packet_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2069" parent="427" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2070" parent="428" name="record_id">
      <AutoIncrement>61</AutoIncrement>
      <Comment>自增ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2071" parent="428" name="user_id">
      <Comment>抽奖用户的ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2072" parent="428" name="packet_id">
      <Comment>所属红包ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2073" parent="428" name="draw_time">
      <Comment>抽奖时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2074" parent="428" name="prize_type">
      <Comment>中奖类型</Comment>
      <DasType>enum(&apos;cash&apos;, &apos;item&apos;, &apos;coupon&apos;)|0e</DasType>
      <Position>5</Position>
    </column>
    <column id="2075" parent="428" name="prize_value">
      <Comment>中奖金额或其他值</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2076" parent="428" name="prize_description">
      <Comment>对于非现金奖励，提供描述信息</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2077" parent="428" name="world_day">
      <Comment>领土日</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2078" parent="428" name="team_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <foreign-key id="2079" parent="428" name="fk_dnf_draw_records_user">
      <ColNames>user_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <foreign-key id="2080" parent="428" name="fk_dnf_draw_records_packets">
      <ColNames>packet_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>packet_id</RefColNames>
      <RefTableName>dnf_draw_red_packets</RefTableName>
    </foreign-key>
    <index id="2081" parent="428" name="PRIMARY">
      <ColNames>record_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2082" parent="428" name="fk_dnf_draw_records_user">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2083" parent="428" name="fk_dnf_draw_records_packets">
      <ColNames>packet_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2084" parent="428" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2085" parent="429" name="packet_id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>自增ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2086" parent="429" name="packet_name">
      <Comment>红包名称或描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2087" parent="429" name="total_amount">
      <Comment>总金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2088" parent="429" name="remaining_amount">
      <Comment>剩余金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2089" parent="429" name="created_user_id">
      <Comment>创建者的用户ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2090" parent="429" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2091" parent="429" name="valid_until">
      <Comment>有效截止时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2092" parent="429" name="is_active">
      <Comment>是否可用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="2093" parent="429" name="PRIMARY">
      <ColNames>packet_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2094" parent="429" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2095" parent="430" name="server_id">
      <AutoIncrement>50</AutoIncrement>
      <Comment>区服自增ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2096" parent="430" name="parent_id">
      <Comment>父级ID</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2097" parent="430" name="server_name">
      <Comment>区服名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2098" parent="430" name="server_type">
      <Comment>大区，小区</Comment>
      <DasType>enum(&apos;major&apos;, &apos;minor&apos;)|0e</DasType>
      <DefaultExpression>&apos;minor&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2099" parent="430" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <foreign-key id="2100" parent="430" name="dnf_game_servers_ibfk_1">
      <ColNames>parent_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>server_id</RefColNames>
      <RefTableName>dnf_game_servers</RefTableName>
    </foreign-key>
    <index id="2101" parent="430" name="PRIMARY">
      <ColNames>server_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2102" parent="430" name="parent_id">
      <ColNames>parent_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2103" parent="430" name="server_name">
      <ColNames>server_name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2104" parent="430" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2105" parent="431" name="id">
      <AutoIncrement>11</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2106" parent="431" name="name">
      <Comment>公会名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2107" parent="431" name="server_id">
      <Comment>服务器ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2108" parent="431" name="leader_user_id">
      <Comment>会长用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2109" parent="431" name="leader_weixin">
      <Comment>会长微信号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2110" parent="431" name="sync_account">
      <Comment>数据同步账号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2111" parent="431" name="sync_password">
      <Comment>数据同步密码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2112" parent="431" name="sync_at">
      <Comment>最近一次数据同步时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2113" parent="431" name="sync_result">
      <Comment>最近一次数数据同步结果</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2114" parent="431" name="logo">
      <Comment>公会LOGO</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2115" parent="431" name="note">
      <Comment>简要说明</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="2116" parent="431" name="invite_code">
      <Comment>邀请码</Comment>
      <DasType>varchar(8)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="2117" parent="431" name="invite_expire_at">
      <Comment>邀请码过期时间 空表示永久</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="2118" parent="431" name="status">
      <Comment>状态 0待激活 1已激活 2已禁用</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="2119" parent="431" name="vip_level">
      <Comment>公会在平台的服务等级</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="2120" parent="431" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="2121" parent="431" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="2122" parent="431" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="2123" parent="431" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>19</Position>
    </column>
    <index id="2124" parent="431" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2125" parent="431" name="n">
      <ColNames>name</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2126" parent="431" name="s">
      <ColNames>server_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2127" parent="431" name="c">
      <ColNames>invite_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2128" parent="431" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2129" parent="432" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2130" parent="432" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2131" parent="432" name="balance">
      <Comment>当前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="2132" parent="432" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2133" parent="432" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2134" parent="432" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <index id="2135" parent="432" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2136" parent="432" name="uni_dnf_guild_balance_guild_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2137" parent="432" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2138" parent="432" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2139" parent="432" name="uni_dnf_guild_balance_guild_id">
      <UnderlyingIndexName>uni_dnf_guild_balance_guild_id</UnderlyingIndexName>
    </key>
    <column id="2140" parent="433" name="id">
      <AutoIncrement>72</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2141" parent="433" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2142" parent="433" name="change_type">
      <Comment>变动类型（收入/支出）</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2143" parent="433" name="reason">
      <Comment>变动原因-代码中常量控制,充值,抽奖，方法奖励等</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2144" parent="433" name="balance">
      <Comment>变动金额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2145" parent="433" name="balance_before">
      <Comment>变动前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2146" parent="433" name="balance_after">
      <Comment>变动后余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2147" parent="433" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2148" parent="433" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2149" parent="433" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2150" parent="433" name="created_at">
      <Comment>变动时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="2151" parent="433" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
    </column>
    <column id="2152" parent="433" name="balance_id">
      <Comment>公会余额ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="2153" parent="433" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2154" parent="433" name="g">
      <ColNames>guild_id
created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2155" parent="433" name="ct">
      <ColNames>change_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2156" parent="433" name="bl">
      <ColNames>balance</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2157" parent="433" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2158" parent="434" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2159" parent="434" name="code">
      <Comment>绑定邀请码 会长首次使用</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2160" parent="434" name="expire_at">
      <Comment>绑定邀请码过期时间 空表示永久</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2161" parent="434" name="bound_at">
      <Comment>绑定时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2162" parent="434" name="bound_guild_id">
      <Comment>绑定的公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2163" parent="434" name="bound_user_id">
      <Comment>绑定的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2164" parent="434" name="status">
      <Comment>状态 0待绑定 1已绑定</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="2165" parent="434" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2166" parent="434" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="2167" parent="434" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2168" parent="434" name="c">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2169" parent="434" name="b">
      <ColNames>bound_guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2170" parent="434" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2171" parent="434" name="c">
      <UnderlyingIndexName>c</UnderlyingIndexName>
    </key>
    <column id="2172" parent="435" name="id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2173" parent="435" name="code">
      <Comment>业务code</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2174" parent="435" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2175" parent="435" name="ext">
      <Comment>配置信息</Comment>
      <DasType>json|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2176" parent="435" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2177" parent="435" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="2178" parent="435" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2179" parent="435" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2180" parent="435" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2181" parent="436" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2182" parent="436" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2183" parent="436" name="user_id">
      <Comment>关联用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2184" parent="436" name="event_type">
      <Comment>事件类型</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2185" parent="436" name="content">
      <Comment>摘要内容</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2186" parent="436" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2187" parent="436" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="2188" parent="436" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2189" parent="436" name="g">
      <ColNames>guild_id
created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2190" parent="436" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2191" parent="436" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2192" parent="437" name="id">
      <AutoIncrement>32</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2193" parent="437" name="hero_class">
      <Comment>英雄职业</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2194" parent="437" name="hero_class_alias">
      <Comment>英雄职业别名</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2195" parent="437" name="parent_id">
      <Comment>父级ID</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2196" parent="437" name="image_url">
      <Comment>图片地址</Comment>
      <DasType>varchar(300)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2197" parent="437" name="header_image_url">
      <Comment>职业头像图片地址</Comment>
      <DasType>varchar(300)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2198" parent="437" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="2199" parent="437" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2200" parent="437" name="hero_class">
      <ColNames>hero_class</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2201" parent="437" name="hero_class_alias">
      <ColNames>hero_class_alias</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2202" parent="437" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2203" parent="438" name="id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2204" parent="438" name="hero_type">
      <Comment>英雄类型</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2205" parent="438" name="status">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="2206" parent="438" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2207" parent="438" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2208" parent="439" name="id">
      <AutoIncrement>3</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2209" parent="439" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2210" parent="439" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>int unsigned|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2211" parent="439" name="guild_role">
      <Comment>身份角色</Comment>
      <DasType>varchar(128)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2212" parent="439" name="note">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2213" parent="439" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2214" parent="439" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2215" parent="439" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2216" parent="439" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2217" parent="439" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2218" parent="439" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2219" parent="440" name="id">
      <AutoIncrement>228</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2220" parent="440" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2221" parent="440" name="code">
      <Comment>成员编号 唯一值</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2222" parent="440" name="name">
      <Comment>冒险团名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2223" parent="440" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2224" parent="440" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2225" parent="440" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
    </column>
    <column id="2226" parent="440" name="bound_user_id">
      <Comment>绑定的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2227" parent="440" name="bound_at">
      <Comment>加入时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2228" parent="440" name="bound_type">
      <Comment>绑定类型 0未绑定 1用户自行绑定 2系统绑定</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="2229" parent="440" name="invite_code">
      <Comment>邀请码</Comment>
      <DasType>varchar(8)|0s</DasType>
      <Position>11</Position>
    </column>
    <foreign-key id="2230" parent="440" name="fk_dnf_guild_member_guild">
      <ColNames>guild_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_guild</RefTableName>
    </foreign-key>
    <foreign-key id="2231" parent="440" name="fk_dnf_guild_member_user">
      <ColNames>bound_user_id</ColNames>
      <OnDelete>set-null</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <index id="2232" parent="440" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2233" parent="440" name="g">
      <ColNames>guild_id
code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2234" parent="440" name="u">
      <ColNames>bound_user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2235" parent="440" name="c">
      <ColNames>invite_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2236" parent="440" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2237" parent="441" name="id">
      <AutoIncrement>883</AutoIncrement>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2238" parent="441" name="cate_one_name">
      <Comment>装备大分类名称</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2239" parent="441" name="cate_two_name">
      <Comment>装备分类名称,</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2240" parent="441" name="armor_name">
      <Comment>装备名称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2241" parent="441" name="content">
      <Comment>装备的基本信息描述</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2242" parent="441" name="effect_desc">
      <Comment>装备的效果描述</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2243" parent="441" name="suit_desc">
      <Comment>装备的套装属性</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2244" parent="441" name="armor_level">
      <Comment>装备的等级</Comment>
      <DasType>smallint unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="2245" parent="441" name="img_url">
      <Comment>装备的图片</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="2246" parent="441" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2247" parent="441" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2248" parent="442" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2249" parent="442" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2250" parent="442" name="member_code">
      <Comment>成员编号 冗余</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2251" parent="442" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2252" parent="442" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <index id="2253" parent="442" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2254" parent="442" name="g">
      <ColNames>guild_id
created_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2255" parent="442" name="m">
      <ColNames>member_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2256" parent="442" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2257" parent="443" name="id">
      <AutoIncrement>2596</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2258" parent="443" name="member_code">
      <Comment>成员编号 冗余</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2259" parent="443" name="member_id">
      <Comment>成员ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2260" parent="443" name="role_code">
      <Comment>角色编号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2261" parent="443" name="role_name">
      <Comment>角色名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2262" parent="443" name="hero_type">
      <Comment>英雄类型 输出型、增益型</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2263" parent="443" name="hero_class">
      <Comment>英雄职业 枪手、战士</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2264" parent="443" name="hero_class_id">
      <Comment>职业ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2265" parent="443" name="hero_set">
      <Comment>英雄套装</Comment>
      <DasType>varchar(512)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2266" parent="443" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2267" parent="443" name="damage">
      <Comment>角色伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="2268" parent="443" name="magic_resistance">
      <Comment>抗魔值</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="2269" parent="443" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="2270" parent="443" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
    </column>
    <index id="2271" parent="443" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2272" parent="443" name="m">
      <ColNames>member_code
member_id
role_code
role_name</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2273" parent="443" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2274" parent="444" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2275" parent="444" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2276" parent="444" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2277" parent="444" name="member_id">
      <Comment>成员ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2278" parent="444" name="member_code">
      <Comment>成员编号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2279" parent="444" name="damage">
      <Comment>输出伤害</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2280" parent="444" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2281" parent="444" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2282" parent="444" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="2283" parent="444" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2284" parent="444" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2285" parent="444" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2286" parent="444" name="m">
      <ColNames>member_id
member_code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2287" parent="444" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2288" parent="445" name="message_id">
      <AutoIncrement>81</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2289" parent="445" name="sender_id">
      <Comment>发送者的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2290" parent="445" name="receiver_id">
      <Comment>接收者的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2291" parent="445" name="subject">
      <Comment>消息主题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2292" parent="445" name="content">
      <Comment>消息内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2293" parent="445" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2294" parent="445" name="is_read">
      <Comment>是否已读</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <foreign-key id="2295" parent="445" name="dnf_guild_messages_ibfk_1">
      <ColNames>sender_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <foreign-key id="2296" parent="445" name="dnf_guild_messages_ibfk_2">
      <ColNames>receiver_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <index id="2297" parent="445" name="PRIMARY">
      <ColNames>message_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2298" parent="445" name="sender_id">
      <ColNames>sender_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2299" parent="445" name="receiver_id">
      <ColNames>receiver_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2300" parent="445" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2301" parent="446" name="id">
      <AutoIncrement>38</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2302" parent="446" name="name">
      <Comment>抽奖名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2303" parent="446" name="description">
      <Comment>抽奖描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2304" parent="446" name="start_at">
      <Comment>抽奖开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2305" parent="446" name="end_at">
      <Comment>抽奖结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2306" parent="446" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2307" parent="446" name="status">
      <Comment>抽奖状态 0 未开奖 1已开奖 2已作废</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="2308" parent="446" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2309" parent="446" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2310" parent="446" name="created_user">
      <Comment>创建人</Comment>
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="2311" parent="446" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2312" parent="446" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2313" parent="446" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2314" parent="447" name="id">
      <AutoIncrement>47</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2315" parent="447" name="lottery_id">
      <Comment>关联的抽奖ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2316" parent="447" name="prize_name">
      <Comment>奖品名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2317" parent="447" name="prize_count">
      <Comment>奖品数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2318" parent="447" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2319" parent="447" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2320" parent="447" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2321" parent="447" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2322" parent="447" name="idx_dnf_lottery_prize_lottery_id">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2323" parent="447" name="lid">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2324" parent="447" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2325" parent="447" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2326" parent="448" name="id">
      <AutoIncrement>15</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2327" parent="448" name="lottery_id">
      <Comment>关联的抽奖ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2328" parent="448" name="user_id">
      <Comment>参与用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2329" parent="448" name="is_winner">
      <Comment>是否中奖 0未中奖 1中奖</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2330" parent="448" name="prize_id">
      <Comment>中奖的奖品ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2331" parent="448" name="created_at">
      <Comment>记录创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2332" parent="448" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2333" parent="448" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="2334" parent="448" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2335" parent="448" name="idx_dnf_lottery_record_lottery_id">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2336" parent="448" name="lid">
      <ColNames>lottery_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2337" parent="448" name="idx_dnf_lottery_record_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2338" parent="448" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2339" parent="448" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2340" parent="449" name="id">
      <AutoIncrement>27</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2341" parent="449" name="member_id">
      <Comment>用户ID,支付操作人</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2342" parent="449" name="order_amount">
      <Comment>订单金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2343" parent="449" name="pay_amount">
      <Comment>实际支付金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2344" parent="449" name="pay_fee">
      <Comment>支付手续费</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2345" parent="449" name="refund_amount">
      <Comment>退款金额</Comment>
      <DasType>decimal(10,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2346" parent="449" name="pay_type">
      <Comment>支付类型: alipay/wxpay</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2347" parent="449" name="status">
      <Comment>支付状态: pending/success/failed</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;pending&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="2348" parent="449" name="failure_reason">
      <Comment>支付失败原因</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2349" parent="449" name="order_sn">
      <Comment>订单号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="2350" parent="449" name="transaction_id">
      <Comment>交易流水号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="2351" parent="449" name="target_id">
      <Comment>目标ID，可能是工会ID或活动ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="2352" parent="449" name="target_type">
      <Comment>目标类型: guild/activity</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="2353" parent="449" name="pay_time">
      <Comment>支付时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="2354" parent="449" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="2355" parent="449" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="2356" parent="449" name="ext">
      <Comment>扩展字段</Comment>
      <DasType>json|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="2357" parent="449" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2358" parent="449" name="member_idx">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2359" parent="449" name="target_idx">
      <ColNames>target_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2360" parent="449" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2361" parent="450" name="id">
      <AutoIncrement>20</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2362" parent="450" name="title">
      <Comment>问卷标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2363" parent="450" name="description">
      <Comment>问卷描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2364" parent="450" name="start_at">
      <Comment>问卷开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2365" parent="450" name="end_at">
      <Comment>问卷结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2366" parent="450" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2367" parent="450" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2368" parent="450" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2369" parent="450" name="created_user">
      <Comment>创建人</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="2370" parent="450" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2371" parent="450" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2372" parent="450" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2373" parent="451" name="id">
      <AutoIncrement>4</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2374" parent="451" name="questionnaire_id">
      <Comment>关联的问卷ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2375" parent="451" name="question_id">
      <Comment>问卷选项id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2376" parent="451" name="member_id">
      <Comment>回答的用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2377" parent="451" name="answer">
      <Comment>用户的回答</Comment>
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2378" parent="451" name="created_at">
      <Comment>回答时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2379" parent="451" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2380" parent="451" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="2381" parent="451" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2382" parent="451" name="idx_dnf_questionnaire_answer_questionnaire_id">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2383" parent="451" name="quid">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2384" parent="451" name="idx_dnf_questionnaire_answer_question_id">
      <ColNames>question_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2385" parent="451" name="qid">
      <ColNames>question_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2386" parent="451" name="mid">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2387" parent="451" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2388" parent="451" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2389" parent="452" name="id">
      <AutoIncrement>23</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2390" parent="452" name="questionnaire_id">
      <Comment>关联的问卷ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2391" parent="452" name="question">
      <Comment>问卷问题</Comment>
      <DasType>varchar(256)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2392" parent="452" name="type">
      <Comment>问题类型: 选择题/问答题</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2393" parent="452" name="options">
      <Comment>选项</Comment>
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2394" parent="452" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2395" parent="452" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2396" parent="452" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="2397" parent="452" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2398" parent="452" name="idx_dnf_questionnaire_question_questionnaire_id">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2399" parent="452" name="quid">
      <ColNames>questionnaire_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2400" parent="452" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2401" parent="452" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2402" parent="453" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2403" parent="453" name="code">
      <Comment>编码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2404" parent="453" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2405" parent="453" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2406" parent="453" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2407" parent="453" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="2408" parent="453" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2409" parent="453" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2410" parent="453" name="code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2411" parent="453" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2412" parent="454" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2413" parent="454" name="dict_code">
      <Comment>字典编码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2414" parent="454" name="label">
      <Comment>标签</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2415" parent="454" name="value">
      <Comment>值</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2416" parent="454" name="sort">
      <Comment>排序</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2417" parent="454" name="status">
      <Comment>状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2418" parent="454" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2419" parent="454" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="2420" parent="454" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2421" parent="454" name="dict">
      <ColNames>dict_code
label</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2422" parent="454" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2423" parent="455" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2424" parent="455" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2425" parent="455" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2426" parent="455" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2427" parent="455" name="ip">
      <Comment>IP</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2428" parent="455" name="content">
      <Comment>内容</Comment>
      <DasType>varchar(256)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2429" parent="455" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2430" parent="455" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="2431" parent="455" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2432" parent="455" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2433" parent="455" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2434" parent="455" name="t">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2435" parent="455" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2436" parent="456" name="id">
      <AutoIncrement>336</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2437" parent="456" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2438" parent="456" name="type">
      <Comment>战队类型 1公会成员 2无固定队伍</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="2439" parent="456" name="status">
      <Comment>状态 1待完成 2已组队 3已解散</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2440" parent="456" name="is_release">
      <Comment>是否解散：连续1周3人不在一起组队（上传图无对方）则自动解散</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2441" parent="456" name="total_damage">
      <Comment>总伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2442" parent="456" name="guild_rank">
      <Comment>公会排名或公会贡献(无固定队友时)</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2443" parent="456" name="latest_damage">
      <Comment>最近的总伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2444" parent="456" name="latest_epochid">
      <Comment>最近的排名的期数</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2445" parent="456" name="latest_at">
      <Comment>最近的时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2446" parent="456" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="2447" parent="456" name="wework">
      <Comment>小队群链接</Comment>
      <DasType>varchar(1024)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="2448" parent="456" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="2449" parent="456" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="2450" parent="456" name="latest_rank">
      <Comment>最近的排名</Comment>
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <index id="2451" parent="456" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2452" parent="456" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2453" parent="456" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2454" parent="457" name="id">
      <AutoIncrement>50</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2455" parent="457" name="guild_id">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2456" parent="457" name="team_id">
      <Comment>战队ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2457" parent="457" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2458" parent="457" name="member_id">
      <Comment>公会成员ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2459" parent="457" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2460" parent="457" name="epoch_id">
      <Comment>赛季ID</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2461" parent="457" name="score">
      <Comment>贡献值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2462" parent="457" name="damage">
      <Comment>伤害值</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2463" parent="457" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2464" parent="457" name="rank">
      <Comment>排名</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="2465" parent="457" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2466" parent="457" name="t">
      <ColNames>team_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2467" parent="457" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2468" parent="457" name="epoch_id">
      <ColNames>epoch_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2469" parent="457" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2470" parent="458" name="id">
      <AutoIncrement>403</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2471" parent="458" name="user_id">
      <Comment>用户ID 冗余</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2472" parent="458" name="member_id">
      <Comment>公会成员ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2473" parent="458" name="role_id">
      <Comment>身份角色</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2474" parent="458" name="rank">
      <Comment>出战顺序 0~3</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2475" parent="458" name="type">
      <Comment>类型 0系统分配 1手动选择</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2476" parent="458" name="team_id">
      <Comment>关联战队ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2477" parent="458" name="team_idx">
      <Comment>战队中位置 0~2</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2478" parent="458" name="status">
      <Comment>组队状态： -1 已离队 0 游离 1 未确认组队 2已组队 </Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2479" parent="458" name="status_reward">
      <Comment>组队红包： 0 未领取， 1已领取</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <index id="2480" parent="458" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2481" parent="458" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2482" parent="458" name="m">
      <ColNames>member_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2483" parent="458" name="r">
      <ColNames>role_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2484" parent="458" name="t">
      <ColNames>team_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2485" parent="458" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2486" parent="459" name="id">
      <AutoIncrement>21</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2487" parent="459" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2488" parent="459" name="team_id">
      <Comment>队伍ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2489" parent="459" name="role_id">
      <Comment>角色ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2490" parent="459" name="world_day">
      <Comment>世界领主日</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2491" parent="459" name="file_type">
      <Comment>文件类型</Comment>
      <DasType>varchar(60)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2492" parent="459" name="file_size">
      <Comment>文件大小</Comment>
      <DasType>int unsigned|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="2493" parent="459" name="file_name">
      <Comment>文件名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2494" parent="459" name="file_path">
      <Comment>文件路径</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="2495" parent="459" name="add_time">
      <Comment>上传时间</Comment>
      <DasType>int unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2496" parent="459" name="status">
      <Comment>状态：1 未识别，2已识别, 3识别失败</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="2497" parent="459" name="ret">
      <Comment>解析结果</Comment>
      <DasType>json|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="2498" parent="459" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2499" parent="459" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2500" parent="460" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2501" parent="460" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2502" parent="460" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2503" parent="460" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2504" parent="460" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2505" parent="460" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(256)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2506" parent="460" name="wx_open_id">
      <Comment>微信OpenID</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2507" parent="460" name="status">
      <Comment>状态 0禁用 1普通 2管理员</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2508" parent="460" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2509" parent="460" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="2510" parent="460" name="deleted_at">
      <Comment>删除时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="2511" parent="460" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2512" parent="460" name="u">
      <ColNames>username</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2513" parent="460" name="p">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2514" parent="460" name="wx">
      <ColNames>wx_open_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2515" parent="460" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2516" parent="461" name="id">
      <AutoIncrement>13</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2517" parent="461" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="2518" parent="461" name="openid">
      <Comment>微信openid</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2519" parent="461" name="nickname">
      <Comment>用户昵称</Comment>
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2520" parent="461" name="avatar_url">
      <Comment>头像地址</Comment>
      <DasType>varchar(300)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2521" parent="461" name="register_at">
      <Comment>注册时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2522" parent="461" name="status">
      <Comment>用户状态，0禁止，1正常</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2523" parent="461" name="admin_uid">
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="2524" parent="461" name="admin_username">
      <DasType>varchar(30)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2525" parent="461" name="is_official">
      <Comment>是否官方 0 不是 1是</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="2526" parent="461" name="guild_id">
      <Comment>所属公会ID</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="2527" parent="461" name="password">
      <Comment>密码md5</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="2528" parent="461" name="created_at">
      <Comment>账号创建时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="2529" parent="461" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="2530" parent="461" name="last_login_at">
      <Comment>最近登录时间</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="2531" parent="461" name="qr_code">
      <Comment>微信二维码</Comment>
      <DasType>varchar(300)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="2532" parent="461" name="weixin">
      <Comment>微信号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="2533" parent="461" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2534" parent="461" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2535" parent="462" name="id">
      <AutoIncrement>13</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2536" parent="462" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2537" parent="462" name="balance">
      <Comment>可提现余额(活动)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="2538" parent="462" name="frozen">
      <Comment>冻结中的余额</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2539" parent="462" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2540" parent="462" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <column id="2541" parent="462" name="guild_id">
      <Comment>关联公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2542" parent="462" name="is_active">
      <Comment>是否激活</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2543" parent="462" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2544" parent="462" name="task_balance">
      <Comment>可提现余额(任务)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>10</Position>
    </column>
    <foreign-key id="2545" parent="462" name="fk_dnf_user_wallet_user">
      <ColNames>user_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <index id="2546" parent="462" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2547" parent="462" name="u">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2548" parent="462" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2549" parent="462" name="is">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2550" parent="462" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2551" parent="463" name="id">
      <AutoIncrement>195</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2552" parent="463" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2553" parent="463" name="guild_id">
      <Comment>关联公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2554" parent="463" name="guild_name">
      <Comment>关联公会名称 冗余</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2555" parent="463" name="reason">
      <Comment>原因 充值、转账、发奖、中奖、公会活动、提现、解冻等</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2556" parent="463" name="amount">
      <Comment>本次金额变化(活动)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="2557" parent="463" name="balance">
      <Comment>可用余额</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="2558" parent="463" name="frozen">
      <Comment>冻结中的余额</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2559" parent="463" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2560" parent="463" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2561" parent="463" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="2562" parent="463" name="change_type">
      <Comment>变动类型（增加/扣除）</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="2563" parent="463" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="2564" parent="463" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>14</Position>
    </column>
    <column id="2565" parent="463" name="wallet_id">
      <Comment>用户钱包ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="2566" parent="463" name="balance_type">
      <Comment>余额类型(活动/任务)</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="2567" parent="463" name="after_balance">
      <Comment>剩余可用余额(活动)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>17</Position>
    </column>
    <column id="2568" parent="463" name="task_balance">
      <Comment>可提现余额(任务)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="2569" parent="463" name="after_task_balance">
      <Comment>剩余可提现余额(任务)</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <Position>19</Position>
    </column>
    <foreign-key id="2570" parent="463" name="fk_dnf_user_wallet_log_user">
      <ColNames>user_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <OnUpdate>cascade</OnUpdate>
      <RefColNames>id</RefColNames>
      <RefTableName>dnf_user_mini</RefTableName>
    </foreign-key>
    <index id="2571" parent="463" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2572" parent="463" name="u">
      <ColNames>user_id
reason</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2573" parent="463" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2574" parent="463" name="ct">
      <ColNames>change_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2575" parent="463" name="w">
      <ColNames>wallet_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2576" parent="463" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2577" parent="464" name="id">
      <AutoIncrement>72</AutoIncrement>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2578" parent="464" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2579" parent="464" name="amount">
      <Comment>提现金额</Comment>
      <DasType>decimal(10,4 digit)|0s</DasType>
      <DefaultExpression>0.0000</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2580" parent="464" name="status">
      <Comment>提现状态 0=待处理 1=处理中 2=成功 3=失败</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2581" parent="464" name="out_batch_no">
      <Comment>生成的批次号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2582" parent="464" name="out_detail_no">
      <Comment>生成的批次的明细号-理论上一个用户每次提取都是1对一</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2583" parent="464" name="batch_id">
      <Comment>微信返回的批次号id</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2584" parent="464" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2585" parent="464" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="2586" parent="464" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <column id="2587" parent="464" name="guild_id">
      <Comment>关联公会ID</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="2588" parent="464" name="withdraw_type">
      <Comment>提现类型 活动 任务</Comment>
      <DasType>varchar(5)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <index id="2589" parent="464" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2590" parent="464" name="idx_dnf_user_withdraws_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2591" parent="464" name="g">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2592" parent="464" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2593" parent="465" name="id">
      <AutoIncrement>22</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2594" parent="465" name="title">
      <Comment>投票标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2595" parent="465" name="description">
      <Comment>投票描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="2596" parent="465" name="start_at">
      <Comment>投票开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="2597" parent="465" name="end_at">
      <Comment>投票结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2598" parent="465" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2599" parent="465" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="2600" parent="465" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="2601" parent="465" name="created_user">
      <Comment>创建人</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="2602" parent="465" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2603" parent="465" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2604" parent="465" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2605" parent="466" name="id">
      <AutoIncrement>31</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2606" parent="466" name="vote_id">
      <Comment>关联的投票ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2607" parent="466" name="option">
      <Comment>投票选项</Comment>
      <DasType>varchar(128)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2608" parent="466" name="vote_count">
      <Comment>选项的投票数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2609" parent="466" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2610" parent="466" name="created_at">
      <Comment>投票时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2611" parent="466" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2612" parent="466" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2613" parent="466" name="idx_dnf_vote_option_vote_id">
      <ColNames>vote_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2614" parent="466" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2615" parent="466" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2616" parent="467" name="id">
      <AutoIncrement>5</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2617" parent="467" name="vote_id">
      <Comment>关联的投票ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2618" parent="467" name="option_id">
      <Comment>用户选择的选项ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2619" parent="467" name="user_id">
      <Comment>投票用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2620" parent="467" name="created_at">
      <Comment>投票时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2621" parent="467" name="guild_id">
      <Comment>公会ID</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2622" parent="467" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2623" parent="467" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2624" parent="467" name="idx_dnf_vote_result_vote_id">
      <ColNames>vote_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="2625" parent="467" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2626" parent="467" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2627" parent="468" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2628" parent="468" name="guild_id">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="2629" parent="468" name="withdraw_min">
      <Comment>提现下限</Comment>
      <DasType>decimal(15,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2630" parent="468" name="daily_withdraw_limit">
      <Comment>每日累计提现上限</Comment>
      <DasType>decimal(15,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2631" parent="468" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="2632" parent="468" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="2633" parent="468" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="2634" parent="468" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2635" parent="468" name="gid">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2636" parent="468" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="2637" parent="469" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2638" parent="469" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2639" parent="469" name="balance">
      <Comment>当前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <DefaultExpression>0.00</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="2640" parent="469" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="2641" parent="469" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="2642" parent="469" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>6</Position>
    </column>
    <index id="2643" parent="469" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2644" parent="469" name="uni_guild_balance_guild_id">
      <ColNames>guild_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="2645" parent="469" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="2646" parent="469" name="uni_guild_balance_guild_id">
      <UnderlyingIndexName>uni_guild_balance_guild_id</UnderlyingIndexName>
    </key>
    <column id="2647" parent="470" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="2648" parent="470" name="guild_id">
      <Comment>工会ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="2649" parent="470" name="change_type">
      <Comment>变动类型（收入/支出）</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="2650" parent="470" name="reason">
      <Comment>变动原因-代码中常量控制,充值,抽奖，方法奖励等</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="2651" parent="470" name="balance">
      <Comment>变动金额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="2652" parent="470" name="balance_before">
      <Comment>变动前余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="2653" parent="470" name="balance_after">
      <Comment>变动后余额</Comment>
      <DasType>decimal(18,2 digit)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="2654" parent="470" name="balance_version">
      <Comment>余额版本号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="2655" parent="470" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="2656" parent="470" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="2657" parent="470" name="created_at">
      <Comment>变动时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="2658" parent="470" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
    </column>
    <index id="2659" parent="470" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="2660" parent="470" name="g">
      <ColNames>guild_id
created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="2661" parent="470" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>