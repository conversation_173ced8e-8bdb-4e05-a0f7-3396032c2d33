# ID引用支持 - 使用示例和说明

## 概述

简化JSON公式处理器现在支持两种引用格式：
1. **分类名称引用**：`[分类名称]` （原有格式，保持不变）
2. **ID引用**：`#数字` （新增格式）
3. **混合引用**：在同一表达式中混合使用两种格式

## 支持的引用格式

### 1. 分类名称引用（原有格式）
```go
// 基本表达式
"[营业收入] - [营业成本]"
"[销售费用] + [管理费用] + [财务费用]"

// 复合表达式
"([营业收入] - [营业成本]) / [营业收入] %"
```

### 2. ID引用（新增格式）
```go
// 基本表达式
"#1 - #2"        // ID 1 减去 ID 2
"#3 + #4 + #5"   // ID 3、4、5 相加

// 复合表达式
"(#1 - #2) / #1 %"  // 毛利率计算
```

### 3. 混合引用（新增功能）
```go
// 分类名称 + ID引用
"[营业收入] - #2"
"#1 - [营业成本]"

// 复杂混合表达式
"([营业收入] - #2) / [营业收入] %"
"#1 + [管理费用] - #5"
```

## 实际业务场景示例

### 场景1：基础财务指标计算

#### 配置数据
```go
configs := []CostTypeConfigItem{
    {Id: 1, CategoryName: "营业收入", CategoryCode: "REVENUE"},
    {Id: 2, CategoryName: "营业成本", CategoryCode: "COST"},
    {Id: 3, CategoryName: "销售费用", CategoryCode: "SALES_EXPENSE"},
    {Id: 4, CategoryName: "管理费用", CategoryCode: "ADMIN_EXPENSE"},
    {Id: 100, CategoryName: "毛利润", CategoryCode: "GROSS_PROFIT"},
}
```

#### 三种等价的毛利润计算方式
```go
// 方式1：分类名称引用
formula1 := "[营业收入] - [营业成本]"

// 方式2：ID引用
formula2 := "#1 - #2"

// 方式3：混合引用
formula3 := "[营业收入] - #2"
```

#### JSON配置示例
```json
{
  "expression": "#1 - #2",
  "references": [1, 2],
  "description": "毛利润计算（使用ID引用）"
}
```

### 场景2：复杂财务比率计算

#### 毛利率计算的多种表达方式
```go
// 传统分类名称引用
"([营业收入] - [营业成本]) / [营业收入] %"

// 纯ID引用
"(#1 - #2) / #1 %"

// 混合引用
"([营业收入] - #2) / #1 %"
```

#### 期间费用率计算
```go
// 使用ID引用简化长表达式
"(#3 + #4 + #5) / #1 %"  // (销售费用+管理费用+财务费用) / 营业收入

// 等价的分类名称引用
"([销售费用] + [管理费用] + [财务费用]) / [营业收入] %"
```

### 场景3：多层级依赖计算

#### 配置示例
```go
calculatedConfigs := []CostTypeConfigItem{
    {
        Id: 100, CategoryName: "毛利润",
        CalculationFormula: `{"expression":"#1 - #2","references":[1,2],"description":"毛利润"}`,
    },
    {
        Id: 101, CategoryName: "毛利率",
        CalculationFormula: `{"expression":"#100 / #1 %","references":[100,1],"description":"毛利率"}`,
    },
    {
        Id: 102, CategoryName: "营业利润",
        CalculationFormula: `{"expression":"#100 - (#3 + #4)","references":[100,3,4],"description":"营业利润"}`,
    },
}
```

## 代码实现详解

### 1. 依赖关系提取
```go
func (sjp *SimpleJSONProcessor) extractDependenciesFromExpression(expression string) []uint {
    var dependencies []uint

    // 1. 匹配 [分类名称] 格式
    namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
    nameMatches := namePattern.FindAllStringSubmatch(expression, -1)
    
    for _, match := range nameMatches {
        if len(match) >= 2 {
            categoryName := match[1]
            // 查找对应的ID
            for _, config := range sjp.builder.configs {
                if config.CategoryName == categoryName {
                    dependencies = append(dependencies, config.Id)
                    break
                }
            }
        }
    }

    // 2. 匹配 #数字 格式的ID引用
    idPattern := regexp.MustCompile(`#(\d+)`)
    idMatches := idPattern.FindAllStringSubmatch(expression, -1)
    
    for _, match := range idMatches {
        if len(match) >= 2 {
            if id, err := strconv.ParseUint(match[1], 10, 32); err == nil {
                dependencies = append(dependencies, uint(id))
            }
        }
    }

    return removeDuplicateUints(dependencies)
}
```

### 2. 表达式验证
```go
func (sjp *SimpleJSONProcessor) isValidExpression(expression string) bool {
    hasValidReferences := false
    
    // 验证分类名称引用
    namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
    nameMatches := namePattern.FindAllStringSubmatch(expression, -1)
    
    for _, match := range nameMatches {
        if len(match) >= 2 {
            hasValidReferences = true
            categoryName := match[1]
            found := false
            for _, config := range sjp.builder.configs {
                if config.CategoryName == categoryName {
                    found = true
                    break
                }
            }
            if !found {
                fmt.Printf("警告：表达式中引用的分类名称不存在: [%s]\n", categoryName)
                return false
            }
        }
    }
    
    // 验证ID引用
    idPattern := regexp.MustCompile(`#(\d+)`)
    idMatches := idPattern.FindAllStringSubmatch(expression, -1)
    
    for _, match := range idMatches {
        if len(match) >= 2 {
            hasValidReferences = true
            if id, err := strconv.ParseUint(match[1], 10, 32); err == nil {
                categoryId := uint(id)
                found := false
                for _, config := range sjp.builder.configs {
                    if config.Id == categoryId {
                        found = true
                        break
                    }
                }
                if !found {
                    fmt.Printf("警告：表达式中引用的分类ID不存在: #%d\n", categoryId)
                    return false
                }
            } else {
                fmt.Printf("警告：表达式中ID格式无效: #%s\n", match[1])
                return false
            }
        }
    }
    
    return hasValidReferences
}
```

### 3. 操作数计算
```go
func (sjp *SimpleJSONProcessor) calculateSingleOperand(operand string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
    operand = strings.TrimSpace(operand)
    
    // 处理分类名称引用 [分类名称]
    if strings.HasPrefix(operand, "[") && strings.HasSuffix(operand, "]") {
        categoryName := operand[1 : len(operand)-1]
        // 查找对应的ID并获取数据...
    }
    
    // 处理ID引用 #数字
    if strings.HasPrefix(operand, "#") {
        idStr := operand[1:] // 去掉 # 号
        if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
            categoryId := uint(id)
            // 验证ID并获取数据...
        }
    }
    
    return nil, fmt.Errorf("无法解析操作数: %s", operand)
}
```

## 使用建议

### 1. 何时使用ID引用
- **配置管理**：在配置文件中使用ID引用，避免分类名称变更影响
- **API接口**：在API中传递公式时使用ID引用，提高性能
- **复杂表达式**：当表达式很长时，ID引用可以简化表达式

### 2. 何时使用分类名称引用
- **用户界面**：在前端显示时使用分类名称，提高可读性
- **调试和日志**：在错误信息中显示分类名称，便于理解
- **文档和示例**：在文档中使用分类名称，便于理解业务逻辑

### 3. 混合使用策略
```go
// 推荐：在复杂表达式中混合使用，平衡可读性和简洁性
"([营业收入] - #2 - #3) / [营业收入] %"  // 主要指标用名称，辅助指标用ID
```

## 错误处理

### 1. 常见错误类型
```go
// ID不存在
"#999 - #2"  // 错误：找不到ID为 999 的分类配置

// ID格式无效
"#abc - #2"  // 错误：无效的ID格式

// 混合错误
"[不存在的分类] - #999"  // 错误：分类名称和ID都不存在
```

### 2. 错误信息示例
```
警告：表达式中引用的分类ID不存在: #999
警告：表达式中ID格式无效: #abc
警告：表达式中引用的分类名称不存在: [不存在的分类]
错误：找不到ID为 999 的分类配置
错误：无效的ID格式: #abc
```

## 性能优化

### 1. ID引用的性能优势
- **解析速度**：ID引用解析比分类名称查找更快
- **存储空间**：ID引用占用更少的存储空间
- **网络传输**：ID引用减少网络传输数据量

### 2. 缓存策略
```go
// 建议：缓存ID到配置的映射，避免重复查找
var idToConfigCache = make(map[uint]CostTypeConfigItem)
```

## 向后兼容性保证

1. **现有公式不受影响**：所有使用 `[分类名称]` 格式的现有公式继续正常工作
2. **API兼容性**：现有的API接口保持不变
3. **数据库兼容性**：现有的数据库记录无需修改
4. **测试兼容性**：现有的测试用例继续通过

这个增强功能为财务公式系统提供了更大的灵活性和更好的性能，同时保持了完全的向后兼容性。
