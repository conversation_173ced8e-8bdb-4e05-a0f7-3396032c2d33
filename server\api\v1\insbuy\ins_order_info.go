package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

type InsOrderInfoApi struct {
}

// CreateInsOrderInfo 创建InsOrderInfo
// @Tags InsOrderInfo
// @Summary 创建InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsOrderInfo true "创建InsOrderInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insOrderInfo/createInsOrderInfo [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateInsOrderInfo(c *gin.Context) {
	var insOrderInfo insbuy.InsOrderInfo
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.CreateInsOrderInfo(&insOrderInfo); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsOrderInfo 删除InsOrderInfo
// @Tags InsOrderInfo
// @Summary 删除InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsOrderInfo true "删除InsOrderInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insOrderInfo/deleteInsOrderInfo [delete]
func (insOrderInfoApi *InsOrderInfoApi) DeleteInsOrderInfo(c *gin.Context) {
	var insOrderInfo insbuy.InsOrderInfo
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if err := insOrderInfoService.DeleteInsOrderInfo(insOrderInfo); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsOrderInfoByIds 批量删除InsOrderInfo
// @Tags InsOrderInfo
// @Summary 批量删除InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsOrderInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insOrderInfo/deleteInsOrderInfoByIds [delete]
func (insOrderInfoApi *InsOrderInfoApi) DeleteInsOrderInfoByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.DeleteInsOrderInfoByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsOrderInfo 更新InsOrderInfo
// @Tags InsOrderInfo
// @Summary 更新InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsOrderInfo true "更新InsOrderInfo"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insOrderInfo/updateInsOrderInfo [put]
func (insOrderInfoApi *InsOrderInfoApi) UpdateInsOrderInfo(c *gin.Context) {
	var insOrderInfo insbuy.InsOrderInfo
	err := c.ShouldBindJSON(&insOrderInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.UpdateInsOrderInfo(insOrderInfo); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsOrderInfo 用id查询InsOrderInfo
// @Tags InsOrderInfo
// @Summary 用id查询InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsOrderInfo true "用id查询InsOrderInfo"
// @Success   200   {object}  response.Response{data=insbuyResp.InsOrderInfoDetails,msg=string}  "分页获取InsOrderInfo列表"
// @Router /insOrderInfo/findInsOrderInfo [get]
func (insOrderInfoApi *InsOrderInfoApi) FindInsOrderInfo(c *gin.Context) {
	var insOrderInfo insbuyReq.FindInsOrderInfo
	err := c.ShouldBindQuery(&insOrderInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsOrderInfo, err := insOrderInfoService.GetBackstageOrderDetails(insOrderInfo.OpenDeskId); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsOrderInfo, c)
	}
}

// GetInsOrderInfoList 分页获取InsOrderInfo列表
// @Tags InsOrderInfo
// @Summary 分页获取InsOrderInfo列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsOrderInfoSearch true "分页获取InsOrderInfo列表"
// @Success   200   {object}  response.Response{data=insbuyResp.InsOrderInfoItem,msg=string}  "分页获取InsOrderInfo列表"
// @Router /insOrderInfo/getInsOrderInfoList [get]
func (insOrderInfoApi *InsOrderInfoApi) GetInsOrderInfoList(c *gin.Context) {
	var pageInfo insbuyReq.InsOrderInfoSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insOrderInfoService.GetInsOrderInfoInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			_, err = insImportService.ExcelCommonList(c, insbuy.ETOrderList.ToInt(), list)
			if err != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateOrderShoppingCart 创建购物车信息
// @Tags InsOrderInfo
// @Summary 创建InsOrderInfo
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AddOrderShoppingCart true "创建购物车信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insOrderInfo/createOrderShoppingCart [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateOrderShoppingCart(c *gin.Context) {
	var insOrderInfo insbuyReq.AddOrderShoppingCart
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.SaveOrderShoppingCart(insOrderInfo); err != nil {
		global.GVA_LOG.Error("创建购物车失败!", zap.Error(err))
		response.FailWithMessage("创建购物车失败"+err.Error(), c)
	} else {
		response.OkWithMessage("写入成功", c)
	}
}

// FindOrderShoppingCartList 用id查询购物车详情
// @Tags InsOrderInfo
// @Summary 用id查询购物车详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindOrderShoppingCartList true "用id查询购物车详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insOrderInfo/findOrderShoppingCartList [get]
func (insOrderInfoApi *InsOrderInfoApi) FindOrderShoppingCartList(c *gin.Context) {
	var insOrderInfo insbuyReq.FindOrderShoppingCartList
	err := GinMustBind(c, &insOrderInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsOrderInfo, err := insOrderInfoService.GetOrderShoppingCartList(insOrderInfo); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsOrderInfo, c)
	}
}

// UpdateOrderShoppingCartRemark 更新购物车整单备注&订单整单备注
// @Tags InsOrderInfo
// @Summary 更新购物车整单备注
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateCartRemark true "更新购物车整单备注"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insOrderInfo/updateOrderShoppingCartRemark [put]
func (insOrderInfoApi *InsOrderInfoApi) UpdateOrderShoppingCartRemark(c *gin.Context) {
	var insOrderInfo insbuyReq.UpdateCartRemark
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if err := insOrderInfoService.UpdateOrderShoppingCartRemark(insOrderInfo); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// CreateOrderInfo 创建订单
// @Tags InsOrderInfo
// @Summary 创建订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.AddOrderInfo true "创建订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insOrderInfo/createOrderInfo [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateOrderInfo(c *gin.Context) {
	var insOrderInfo insbuyReq.AddOrderInfo
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if resp, err := insOrderInfoService.CreateOrderInfoByCart(insOrderInfo); err != nil {
		global.GVA_LOG.Error("创建订单失败!", zap.Error(err))
		response.FailWithMessage("创建订单失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// CreateOrderInfoByGift 创建赠送订单
// @Tags InsOrderInfo
// @Summary 创建赠送订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateOrderInfoByGift true "创建赠送订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /insOrderInfo/createOrderInfoByGift [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateOrderInfoByGift(c *gin.Context) {
	var insOrderInfo insbuyReq.CreateOrderInfoByGift
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if resp, err := insOrderInfoService.CreateOrderInfoByGift(insOrderInfo); err != nil {
		global.GVA_LOG.Error("赠送失败!", zap.Error(err))
		response.FailWithMessage("赠送失败:"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// OrderToGift 订单转赠送
// @Tags InsOrderInfo
// @Summary 订单转赠送
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.OrderToGiftReq true "订单转赠送"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"赠送成功"}"
// @Router /insOrderInfo/orderToGift [post]
func (insOrderInfoApi *InsOrderInfoApi) OrderToGift(c *gin.Context) {
	var insOrderInfo insbuyReq.OrderToGiftReq
	if err := GinMustBind(c, &insOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if err := insOrderInfoService.OrderToGift(insOrderInfo); err != nil {
		global.GVA_LOG.Error("赠送失败!", zap.Error(err))
		response.FailWithMessage("赠送失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("赠送成功", c)
	}
}

// GetOrderInfoList 获取订单列表
// @Tags InsOrderInfo
// @Summary 获取订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetOrderInfoListReq true "分页获取订单列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insOrderInfo/getOrderInfoList [get]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderInfoList(c *gin.Context) {
	var pageInfo insbuyReq.GetOrderInfoListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insOrderInfoService.GetOrderInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

// GetOrderBill 获取支付账单
// @Tags InsOrderInfo
// @Summary 获取账单信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetOrderBillReq true "获取支付账单"
// @Success   200   {object}  response.Response{data=insbuyResp.GetOrderBillResp,msg=string}  "获取支付账单"
// @Router /insOrderInfo/getOrderBill [get]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderBill(c *gin.Context) {
	var getOrderBillReq insbuyReq.GetOrderBillReq
	err := c.ShouldBindQuery(&getOrderBillReq)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	reinsOrderInfo := insbuyResp.GetOrderBillResp{}
	if reinsOrderInfo, err = insOrderInfoService.GetOrderBill(getOrderBillReq); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(reinsOrderInfo, c)
	}
}

// CreateOrderBill 创建订单账单
// @Tags InsOrderInfo
// @Summary 发起支付
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateOrderBillReq true "创建订单账单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建订单账单"}"
// @Router /insOrderInfo/createOrderBill [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateOrderBill(c *gin.Context) {
	var payOrderInfo insbuyReq.CreateOrderBillReq
	if err := GinMustBind(c, &payOrderInfo); err != nil {
		response.Err(err, c)
		return
	}
	if info, err := insOrderInfoService.CreateOrderBill(payOrderInfo); err != nil {
		global.GVA_LOG.Error("发起支付失败!", zap.Error(err))
		response.ResultErr(info, err, c)
	} else {
		response.OkWithData(info, c)
	}
}

// CalculatePrePayAmount 计算预支付金额
// @Tags InsOrderInfo
// @Summary 计算预支付金额
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.CreateOrderBillReq true "计算预支付金额"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"计算预支付金额"}"
// @Router /insOrderInfo/calculatePrePayAmount [post]
func (insOrderInfoApi *InsOrderInfoApi) CalculatePrePayAmount(c *gin.Context) {
	var req insbuyReq.CreateOrderBillReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info, err := insOrderInfoService.CalculatePrePayAmount(req); err != nil {
		global.GVA_LOG.Error("计算预支付金额失败!", zap.Error(err))
		response.ResultErr(info, err, c)
	} else {
		response.OkWithData(info, c)
	}
}

// UnbindPayOrderDetails 解绑支付订单详情
// @Tags InsOrderInfo
// @Summary 解绑支付订单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UnbindPayOrderDetailsReq true "解绑支付订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"解绑成功"}"
// @Router /insOrderInfo/unbindPayOrderDetails [put]
func (insOrderInfoApi *InsOrderInfoApi) UnbindPayOrderDetails(c *gin.Context) {
	var req insbuyReq.UnbindPayOrderDetailsReq
	if err := GinMustBind(c, &req); err != nil {
		response.Err(err, c)
		return
	}
	if err := insOrderInfoService.UnbindPayOrderDetails(req.WaitDetailsId); err != nil {
		global.GVA_LOG.Error("解绑支付失败!", zap.Error(err))
		response.ResultErr(map[string]interface{}{}, err, c)
	} else {
		response.Ok(c)
	}
}

// GetOrderPayStatus 根据订单获取账单支付状态
// @Tags InsOrderInfo
// @Summary 发起支付
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOrderPayStatusReq true "创建订单账单"
// @Success   200   {object}  response.Response{data=insbuyResp.GetOrderPayStatusResp,msg=string}  "获取支付账单"
// @Router /insOrderInfo/getOrderPayStatus [get]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderPayStatus(c *gin.Context) {
	var payOrderInfo insbuyReq.GetOrderPayStatusReq
	err := c.ShouldBindQuery(&payOrderInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if info, err := insOrderInfoService.GetOrderPayStatus(payOrderInfo.OrderSn); err != nil {
		global.GVA_LOG.Error("查询支付状态失败!", zap.Error(err))
		response.FailWithMessage("查询支付状态失败", c)
	} else {
		response.OkWithData(info, c)
	}
}

// OrderPayCallBack 订单支付回调
// @Tags InsOrderInfo
// @Summary 订单支付回调
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"订单支付回调"}"
// @Router /insOrderInfo/orderPayCallBack [post]
func (insOrderInfoApi *InsOrderInfoApi) OrderPayCallBack(c *gin.Context) {
	// 解析表单数据
	err := c.Request.ParseForm()
	formData := make(map[string]string)
	for key, values := range c.Request.Form {
		formData[key] = values[0] // 假设每个字段只有一个值
	}
	err = insOrderInfoService.OrderPayCallBack(formData)
	if err != nil {
		global.GVA_LOG.Error("订单支付回调失败!", zap.Error(err))
		c.JSON(http.StatusOK, "fail")
		return
	}
	c.JSON(http.StatusOK, "success")
}

// UpdateInsShipment 更新出品&备品状态
// @Tags InsOrderInfo
// @Summary 更新出品&备品状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.OrderShipmentUpdate true "更新出品&备品状态"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insOrderInfo/updateInsShipment [put]
func (insOrderInfoApi *InsOrderInfoApi) UpdateInsShipment(c *gin.Context) {
	var insShipment insbuyReq.OrderShipmentUpdate
	if err := GinMustBind(c, &insShipment); err != nil {
		response.Err(err, c)
		return
	}
	if err := insOrderInfoService.UpdateInsShipment(insShipment); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GetInsShipmentList 分页获取备品&出品列表
// @Tags InsOrderInfo
// @Summary 分页获取备品&出品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.OrderShipmentSearch true "分页获取备品&出品列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insOrderInfo/getInsShipmentList [get]
func (insOrderInfoApi *InsOrderInfoApi) GetInsShipmentList(c *gin.Context) {
	var pageInfo insbuyReq.OrderShipmentSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insOrderInfoService.GetInsShipmentInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// OrderRefund 创建退单
// @Tags InsOrderInfo
// @Summary 创建退单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.OrderRefundReq true "创建退单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建退单"}"
// @Router /insOrderInfo/orderRefund [post]
func (insOrderInfoApi *InsOrderInfoApi) OrderRefund(c *gin.Context) {
	var req insbuyReq.OrderRefundReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if resp, err := insOrderInfoService.OrderRefund(req); err != nil {
		global.GVA_LOG.Error("退款失败!", zap.Error(err))
		response.FailWithMessage("退款失败"+err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GetOrderRefund 获取退款金额详情
// @Tags InsBookIn 获取退款金额详情
// @Summary 获取退款金额详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body insbuyReq.GetOrderRefundReq true "获取退款金额详情"
// @Success   200   {object}  response.Response{data=insbuyResp.GetOrderRefundResp,msg=string}  "获取退款金额详情"
// @Router /insOrderInfo/getOrderRefund [post]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderRefund(c *gin.Context) {
	var err error
	var req insbuyReq.GetOrderRefundReq
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insOrderInfoService.GetOrderRefund(req)
	response.ResultErr(resp, err, c)
}

// GetOrderReportPreview 获取班结表报表预览
// @Tags InsOrderInfo
// @Summary 获取班结表报表预览
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetOrderReportPreviewReq true "获取班结表报表预览"
// @Success   200   {object}  response.Response{data=printmodel.TplShiftListBill,msg=string}  "获取退款金额详情"
// @Router /insOrderInfo/getOrderReportPreview [get]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderReportPreview(c *gin.Context) {
	var req insbuyReq.GetOrderReportPreviewReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	printReq, err := insOrderInfoService.GetOrderReportPreview(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	h1, err := insSysPrintService.DevRender(printReq)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	response.OkWithData(gin.H{
		"html": h1,
	}, c)
}

// GetOrderOnDutyData 当班统计
// @Tags InsOrderInfo
// @Summary 当班统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetOrderOnDutyDataReq true "当班统计"
// @Success   200   {object}  response.Response{data=insbuyResp.GetOrderInfoDetailsByTimeRes}
// @Router /insOrderInfo/getOrderOnDutyData [get]
func (insOrderInfoApi *InsOrderInfoApi) GetOrderOnDutyData(c *gin.Context) {
	var req insbuyReq.GetOrderOnDutyDataReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, _, err := insOrderInfoService.GetOrderOnDutyData(req)
	response.ResultErr(resp, err, c)
}

// CreateReverseBill 创建反结账
// @Tags InsOrderInfo
// @Summary 创建反结账
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateReverseBillReq true "创建反结账"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建反结账"}"
// @Router /insOrderInfo/createReverseBill [post]
func (insOrderInfoApi *InsOrderInfoApi) CreateReverseBill(c *gin.Context) {
	var req insbuyReq.CreateReverseBillReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.CreateReverseBill(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败"+err.Error(), c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// RecalculateOrderAmount 手动修正订单的金额和总数
// @Tags InsOrderInfo
// @Summary 手动修正订单的金额和总数
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RecalculateOrderAmount true "手动修正订单的金额和总数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动修正订单的金额和总数"}"
// @Router /insOrderInfo/recalculateOrderAmount [put]
func (insOrderInfoApi *InsOrderInfoApi) RecalculateOrderAmount(c *gin.Context) {
	var req insbuyReq.RecalculateOrderAmount
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.RecalculateOrderAmount(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// ReplaceOrderPackageDetails  替换套餐明细商品
// @Tags InsOrderInfo
// @Summary 替换套餐明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ReplaceOrderPackageDetailsReq true "替换套餐明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"替换套餐明细商品"}"
// @Router /insOrderInfo/replaceOrderPackageDetails [put]
func (insOrderInfoApi *InsOrderInfoApi) ReplaceOrderPackageDetails(c *gin.Context) {
	var req insbuyReq.ReplaceOrderPackageDetailsReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.ReplaceOrderPackageDetails(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// ResetOrderPackageDetails 重置套餐明细商品
// @Tags InsOrderInfo
// @Summary 重置套餐明细商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ResetOrderPackageReq true "重置套餐明细商品"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"重置套餐明细商品"}"
// @Router /insOrderInfo/resetOrderPackageDetails [put]
func (insOrderInfoApi *InsOrderInfoApi) ResetOrderPackageDetails(c *gin.Context) {
	var req insbuyReq.ResetOrderPackageReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insOrderInfoService.ResetOrderPackageDetails(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// OrderRefundList 退单异常订单
// @Tags InsOrderInfo
// @Summary 退单异常订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.OrderRefundListReq true "退单异常订单"
// @Success   200   {object}  response.Response{data=insbuyResp.OrderRefundList}
// @Router /insOrderInfo/orderRefundList [get]
func (insOrderInfoApi *InsOrderInfoApi) OrderRefundList(c *gin.Context) {
	var req insbuyReq.OrderRefundListReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insOrderInfoService.OrderRefundList(req)
	response.ResultErr(resp, err, c)
}

// RestoreOrder 恢复异常订单
// @Tags InsOrderInfo
// @Summary 恢复异常订单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.RestoreOrderReq true "恢复异常订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"恢复异常订单成功"}"
// @Router /insOrderInfo/restoreOrder [put]
func (insOrderInfoApi *InsOrderInfoApi) RestoreOrder(c *gin.Context) {
	var req insbuyReq.RestoreOrderReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := insOrderInfoService.RestoreOrder(req)
	response.Err(err, c)
}

// OrderAdjustment 订单业绩修改
// @Tags InsOrderInfo
// @Summary 订单业绩修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.OrderAdjustmentReq true "订单业绩修改"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"订单业绩修改成功"}"
// @Router /insOrderInfo/orderAdjustment [put]
func (insOrderInfoApi *InsOrderInfoApi) OrderAdjustment(c *gin.Context) {
	var req insbuyReq.OrderAdjustmentReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := insOrderInfoService.OrderAdjustment(req)
	response.Err(err, c)
}
