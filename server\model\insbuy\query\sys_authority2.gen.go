// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newSysAuthority2(db *gorm.DB, opts ...gen.DOOption) sysAuthority2 {
	_sysAuthority2 := sysAuthority2{}

	_sysAuthority2.sysAuthority2Do.UseDB(db, opts...)
	_sysAuthority2.sysAuthority2Do.UseModel(&system.SysAuthority2{})

	tableName := _sysAuthority2.sysAuthority2Do.TableName()
	_sysAuthority2.ALL = field.NewAsterisk(tableName)
	_sysAuthority2.ID = field.NewUint(tableName, "id")
	_sysAuthority2.Name = field.NewString(tableName, "name")
	_sysAuthority2.Priority = field.NewInt(tableName, "priority")
	_sysAuthority2.Remark = field.NewString(tableName, "remark")
	_sysAuthority2.Ext = field.NewField(tableName, "ext")
	_sysAuthority2.CreatedAt = field.NewField(tableName, "created_at")
	_sysAuthority2.UpdatedAt = field.NewField(tableName, "updated_at")
	_sysAuthority2.SysAuthority2Item = sysAuthority2HasManySysAuthority2Item{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SysAuthority2Item", "system.SysAuthority2Item"),
	}

	_sysAuthority2.fillFieldMap()

	return _sysAuthority2
}

type sysAuthority2 struct {
	sysAuthority2Do

	ALL               field.Asterisk
	ID                field.Uint
	Name              field.String
	Priority          field.Int
	Remark            field.String
	Ext               field.Field
	CreatedAt         field.Field
	UpdatedAt         field.Field
	SysAuthority2Item sysAuthority2HasManySysAuthority2Item

	fieldMap map[string]field.Expr
}

func (s sysAuthority2) Table(newTableName string) *sysAuthority2 {
	s.sysAuthority2Do.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysAuthority2) As(alias string) *sysAuthority2 {
	s.sysAuthority2Do.DO = *(s.sysAuthority2Do.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysAuthority2) updateTableName(table string) *sysAuthority2 {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint(table, "id")
	s.Name = field.NewString(table, "name")
	s.Priority = field.NewInt(table, "priority")
	s.Remark = field.NewString(table, "remark")
	s.Ext = field.NewField(table, "ext")
	s.CreatedAt = field.NewField(table, "created_at")
	s.UpdatedAt = field.NewField(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *sysAuthority2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysAuthority2) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["priority"] = s.Priority
	s.fieldMap["remark"] = s.Remark
	s.fieldMap["ext"] = s.Ext
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt

}

func (s sysAuthority2) clone(db *gorm.DB) sysAuthority2 {
	s.sysAuthority2Do.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysAuthority2) replaceDB(db *gorm.DB) sysAuthority2 {
	s.sysAuthority2Do.ReplaceDB(db)
	return s
}

type sysAuthority2HasManySysAuthority2Item struct {
	db *gorm.DB

	field.RelationField
}

func (a sysAuthority2HasManySysAuthority2Item) Where(conds ...field.Expr) *sysAuthority2HasManySysAuthority2Item {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthority2HasManySysAuthority2Item) WithContext(ctx context.Context) *sysAuthority2HasManySysAuthority2Item {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthority2HasManySysAuthority2Item) Session(session *gorm.Session) *sysAuthority2HasManySysAuthority2Item {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthority2HasManySysAuthority2Item) Model(m *system.SysAuthority2) *sysAuthority2HasManySysAuthority2ItemTx {
	return &sysAuthority2HasManySysAuthority2ItemTx{a.db.Model(m).Association(a.Name())}
}

type sysAuthority2HasManySysAuthority2ItemTx struct{ tx *gorm.Association }

func (a sysAuthority2HasManySysAuthority2ItemTx) Find() (result []*system.SysAuthority2Item, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthority2HasManySysAuthority2ItemTx) Append(values ...*system.SysAuthority2Item) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthority2HasManySysAuthority2ItemTx) Replace(values ...*system.SysAuthority2Item) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthority2HasManySysAuthority2ItemTx) Delete(values ...*system.SysAuthority2Item) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthority2HasManySysAuthority2ItemTx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthority2HasManySysAuthority2ItemTx) Count() int64 {
	return a.tx.Count()
}

type sysAuthority2Do struct{ gen.DO }

func (s sysAuthority2Do) Debug() *sysAuthority2Do {
	return s.withDO(s.DO.Debug())
}

func (s sysAuthority2Do) WithContext(ctx context.Context) *sysAuthority2Do {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysAuthority2Do) ReadDB() *sysAuthority2Do {
	return s.Clauses(dbresolver.Read)
}

func (s sysAuthority2Do) WriteDB() *sysAuthority2Do {
	return s.Clauses(dbresolver.Write)
}

func (s sysAuthority2Do) Session(config *gorm.Session) *sysAuthority2Do {
	return s.withDO(s.DO.Session(config))
}

func (s sysAuthority2Do) Clauses(conds ...clause.Expression) *sysAuthority2Do {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysAuthority2Do) Returning(value interface{}, columns ...string) *sysAuthority2Do {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysAuthority2Do) Not(conds ...gen.Condition) *sysAuthority2Do {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysAuthority2Do) Or(conds ...gen.Condition) *sysAuthority2Do {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysAuthority2Do) Select(conds ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysAuthority2Do) Where(conds ...gen.Condition) *sysAuthority2Do {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysAuthority2Do) Order(conds ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysAuthority2Do) Distinct(cols ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysAuthority2Do) Omit(cols ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysAuthority2Do) Join(table schema.Tabler, on ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysAuthority2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysAuthority2Do) RightJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysAuthority2Do) Group(cols ...field.Expr) *sysAuthority2Do {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysAuthority2Do) Having(conds ...gen.Condition) *sysAuthority2Do {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysAuthority2Do) Limit(limit int) *sysAuthority2Do {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysAuthority2Do) Offset(offset int) *sysAuthority2Do {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysAuthority2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysAuthority2Do {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysAuthority2Do) Unscoped() *sysAuthority2Do {
	return s.withDO(s.DO.Unscoped())
}

func (s sysAuthority2Do) Create(values ...*system.SysAuthority2) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysAuthority2Do) CreateInBatches(values []*system.SysAuthority2, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysAuthority2Do) Save(values ...*system.SysAuthority2) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysAuthority2Do) First() (*system.SysAuthority2, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2), nil
	}
}

func (s sysAuthority2Do) Take() (*system.SysAuthority2, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2), nil
	}
}

func (s sysAuthority2Do) Last() (*system.SysAuthority2, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2), nil
	}
}

func (s sysAuthority2Do) Find() ([]*system.SysAuthority2, error) {
	result, err := s.DO.Find()
	return result.([]*system.SysAuthority2), err
}

func (s sysAuthority2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*system.SysAuthority2, err error) {
	buf := make([]*system.SysAuthority2, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysAuthority2Do) FindInBatches(result *[]*system.SysAuthority2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysAuthority2Do) Attrs(attrs ...field.AssignExpr) *sysAuthority2Do {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysAuthority2Do) Assign(attrs ...field.AssignExpr) *sysAuthority2Do {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysAuthority2Do) Joins(fields ...field.RelationField) *sysAuthority2Do {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysAuthority2Do) Preload(fields ...field.RelationField) *sysAuthority2Do {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysAuthority2Do) FirstOrInit() (*system.SysAuthority2, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2), nil
	}
}

func (s sysAuthority2Do) FirstOrCreate() (*system.SysAuthority2, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2), nil
	}
}

func (s sysAuthority2Do) FindByPage(offset int, limit int) (result []*system.SysAuthority2, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysAuthority2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysAuthority2Do) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysAuthority2Do) Delete(models ...*system.SysAuthority2) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysAuthority2Do) withDO(do gen.Dao) *sysAuthority2Do {
	s.DO = *do.(*gen.DO)
	return s
}
