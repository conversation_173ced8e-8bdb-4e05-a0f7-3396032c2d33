// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterialSupplier(db *gorm.DB, opts ...gen.DOOption) insMaterialSupplier {
	_insMaterialSupplier := insMaterialSupplier{}

	_insMaterialSupplier.insMaterialSupplierDo.UseDB(db, opts...)
	_insMaterialSupplier.insMaterialSupplierDo.UseModel(&insbuy.InsMaterialSupplier{})

	tableName := _insMaterialSupplier.insMaterialSupplierDo.TableName()
	_insMaterialSupplier.ALL = field.NewAsterisk(tableName)
	_insMaterialSupplier.ID = field.NewUint(tableName, "id")
	_insMaterialSupplier.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterialSupplier.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterialSupplier.MaterialId = field.NewInt(tableName, "material_id")
	_insMaterialSupplier.SupplierId = field.NewInt(tableName, "supplier_id")
	_insMaterialSupplier.PurchasePrice = field.NewFloat64(tableName, "purchase_price")

	_insMaterialSupplier.fillFieldMap()

	return _insMaterialSupplier
}

type insMaterialSupplier struct {
	insMaterialSupplierDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	MaterialId    field.Int
	SupplierId    field.Int
	PurchasePrice field.Float64

	fieldMap map[string]field.Expr
}

func (i insMaterialSupplier) Table(newTableName string) *insMaterialSupplier {
	i.insMaterialSupplierDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterialSupplier) As(alias string) *insMaterialSupplier {
	i.insMaterialSupplierDo.DO = *(i.insMaterialSupplierDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterialSupplier) updateTableName(table string) *insMaterialSupplier {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.MaterialId = field.NewInt(table, "material_id")
	i.SupplierId = field.NewInt(table, "supplier_id")
	i.PurchasePrice = field.NewFloat64(table, "purchase_price")

	i.fillFieldMap()

	return i
}

func (i *insMaterialSupplier) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterialSupplier) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["supplier_id"] = i.SupplierId
	i.fieldMap["purchase_price"] = i.PurchasePrice
}

func (i insMaterialSupplier) clone(db *gorm.DB) insMaterialSupplier {
	i.insMaterialSupplierDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterialSupplier) replaceDB(db *gorm.DB) insMaterialSupplier {
	i.insMaterialSupplierDo.ReplaceDB(db)
	return i
}

type insMaterialSupplierDo struct{ gen.DO }

func (i insMaterialSupplierDo) Debug() *insMaterialSupplierDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialSupplierDo) WithContext(ctx context.Context) *insMaterialSupplierDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialSupplierDo) ReadDB() *insMaterialSupplierDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialSupplierDo) WriteDB() *insMaterialSupplierDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialSupplierDo) Session(config *gorm.Session) *insMaterialSupplierDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialSupplierDo) Clauses(conds ...clause.Expression) *insMaterialSupplierDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialSupplierDo) Returning(value interface{}, columns ...string) *insMaterialSupplierDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialSupplierDo) Not(conds ...gen.Condition) *insMaterialSupplierDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialSupplierDo) Or(conds ...gen.Condition) *insMaterialSupplierDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialSupplierDo) Select(conds ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialSupplierDo) Where(conds ...gen.Condition) *insMaterialSupplierDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialSupplierDo) Order(conds ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialSupplierDo) Distinct(cols ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialSupplierDo) Omit(cols ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialSupplierDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialSupplierDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialSupplierDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialSupplierDo) Group(cols ...field.Expr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialSupplierDo) Having(conds ...gen.Condition) *insMaterialSupplierDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialSupplierDo) Limit(limit int) *insMaterialSupplierDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialSupplierDo) Offset(offset int) *insMaterialSupplierDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialSupplierDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialSupplierDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialSupplierDo) Unscoped() *insMaterialSupplierDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialSupplierDo) Create(values ...*insbuy.InsMaterialSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialSupplierDo) CreateInBatches(values []*insbuy.InsMaterialSupplier, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialSupplierDo) Save(values ...*insbuy.InsMaterialSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialSupplierDo) First() (*insbuy.InsMaterialSupplier, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialSupplier), nil
	}
}

func (i insMaterialSupplierDo) Take() (*insbuy.InsMaterialSupplier, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialSupplier), nil
	}
}

func (i insMaterialSupplierDo) Last() (*insbuy.InsMaterialSupplier, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialSupplier), nil
	}
}

func (i insMaterialSupplierDo) Find() ([]*insbuy.InsMaterialSupplier, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterialSupplier), err
}

func (i insMaterialSupplierDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterialSupplier, err error) {
	buf := make([]*insbuy.InsMaterialSupplier, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialSupplierDo) FindInBatches(result *[]*insbuy.InsMaterialSupplier, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialSupplierDo) Attrs(attrs ...field.AssignExpr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialSupplierDo) Assign(attrs ...field.AssignExpr) *insMaterialSupplierDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialSupplierDo) Joins(fields ...field.RelationField) *insMaterialSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialSupplierDo) Preload(fields ...field.RelationField) *insMaterialSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialSupplierDo) FirstOrInit() (*insbuy.InsMaterialSupplier, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialSupplier), nil
	}
}

func (i insMaterialSupplierDo) FirstOrCreate() (*insbuy.InsMaterialSupplier, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialSupplier), nil
	}
}

func (i insMaterialSupplierDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterialSupplier, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialSupplierDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialSupplierDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialSupplierDo) Delete(models ...*insbuy.InsMaterialSupplier) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialSupplierDo) withDO(do gen.Dao) *insMaterialSupplierDo {
	i.DO = *do.(*gen.DO)
	return i
}
