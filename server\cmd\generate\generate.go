package main

// db封装
//
// json.liao 2023.07.24

import (
	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/priinventory"
	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var configFile = "config.yaml"

func GormMysql(m *config.Mysql) *gorm.DB {
	mysqlConfig := mysql.Config{
		DSN:                       m.Dsn(), // DSN data source name
		DefaultStringSize:         191,     // string 类型字段的默认长度
		SkipInitializeWithVersion: false,   // 根据版本自动配置
	}
	gormCfg := &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			TablePrefix:   m.Prefix,
			SingularTable: m.<PERSON>,
		},
		DisableForeignKeyConstraintWhenMigrating: true,
		//Logger: logger.New(),
	}
	if db, err := gorm.Open(mysql.New(mysqlConfig), gormCfg); err != nil {
		return nil
	} else {
		db.InstanceSet("gorm:table_options", "ENGINE="+m.Engine)
		sqlDB, _ := db.DB()
		sqlDB.SetMaxIdleConns(m.MaxIdleConns)
		sqlDB.SetMaxOpenConns(m.MaxOpenConns)
		return db
	}
}

func openDB() *gorm.DB {
	v := viper.New()
	v.SetConfigFile(configFile)
	v.SetConfigType("yaml")
	v.SetEnvPrefix("ins")
	v.AutomaticEnv()
	err := v.ReadInConfig()
	if err != nil {
		panic(err)
	}
	var cfg config.Server
	err = v.Unmarshal(&cfg)
	if err != nil {
		panic(err)
	}
	switch cfg.System.DbType {
	case "mysql":
		return GormMysql(&cfg.Mysql)
	case "pgsql":
	//	return GormPgSql()
	case "oracle":
	//	return GormOracle()
	case "mssql":
	//	return GormMssql()
	case "sqlite":
	//	return GormSqlite()
	default:
		return GormMysql(&cfg.Mysql)
	}
	return nil
}

func genQueryInsbuy(db *gorm.DB) {
	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := gen.NewGenerator(gen.Config{
		OutPath: "./model/insbuy/query",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery,
		/* Mode: gen.WithoutContext|gen.WithDefaultQuery*/
		//if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable: true,
		//if you want to assign field which has default value in `Create` API, set FieldCoverable true, reference: https://gorm.io/docs/create.html#Default-Values
		/* FieldCoverable: true,*/
		// if you want generate field with unsigned integer type, set FieldSignable true
		/* FieldSignable: true,*/
		//if you want to generate index tags from database, set FieldWithIndexTag true
		/* FieldWithIndexTag: true,*/
		//if you want to generate type tags from database, set FieldWithTypeTag true
		/* FieldWithTypeTag: true,*/
		//if you need unit tests for query code, set WithUnitTest true
		/* WithUnitTest: true, */
	})

	g.UseDB(db)

	//g.WithDataTypeMap(map[string]func(detailType string) (dataType string){
	//	"date": func(detailType string) (dataType string) {
	//		return "time.Time"
	//	},
	//})

	ormModels := []interface{}{}
	ormModels = append(ormModels, insbuy.GetInitTables()...)
	ormModels = append(ormModels, insbuy.GetInitTables2()...)
	ormModels = append(ormModels, insbuy.GetOrmTables()...)

	// apply basic crud api on structs or table models which is specified by table name with function
	// GenerateModel/GenerateModelAs. And generator will generate table models' code when calling Excute.
	// 想对已有的model生成crud等基础方法可以直接指定model struct ，例如model.User{}
	// 如果是想直接生成表的model和crud方法，则可以指定标名称，例如g.GenerateModel("company")
	// 想自定义某个表生成特性，比如struct的名称/字段类型/tag等，可以指定opt，例如g.GenerateModel("company",gen.FieldIgnore("address")), g.GenerateModelAs("people", "Person", gen.FieldIgnore("address"))
	g.ApplyBasic(
		//access.SysDict{},
		//g.GenerateModel("company"),
		//g.GenerateModelAs("people", "Person", gen.FieldIgnore("address")),
		ormModels...,
	)

	for k, v := range g.Data {
		if k == "" {

		}
		for _, i := range v.Fields {
			if i.Type == "field" && i.Name == "BusinessDay" {
				i.Type = "time.Time"
			}
		}
	}

	// apply diy interfaces on structs or table models
	// 如果想给某些表或者model生成自定义方法，可以用ApplyInterface，第一个参数是方法接口，可以参考DIY部分文档定义
	//g.ApplyInterface(
	//	func(method access.Method) {},
	//
	//	access.AllTables...,
	//)

	g.Execute()
}

// 私人仓库
func genQueryPri(db *gorm.DB) {
	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := gen.NewGenerator(gen.Config{
		OutPath: "./model/priinventory/query",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery,
		//if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable: true,
	})

	g.UseDB(db)

	ormModels := []interface{}{}
	ormModels = append(ormModels, priinventory.GetOrmTables()...)

	// apply basic crud api on structs or table models which is specified by table name with function
	// GenerateModel/GenerateModelAs. And generator will generate table models' code when calling Excute.
	// 想对已有的model生成crud等基础方法可以直接指定model struct ，例如model.User{}
	// 如果是想直接生成表的model和crud方法，则可以指定标名称，例如g.GenerateModel("company")
	// 想自定义某个表生成特性，比如struct的名称/字段类型/tag等，可以指定opt，例如g.GenerateModel("company",gen.FieldIgnore("address")), g.GenerateModelAs("people", "Person", gen.FieldIgnore("address"))
	g.ApplyBasic(
		//access.SysDict{},
		//g.GenerateModel("company"),
		//g.GenerateModelAs("people", "Person", gen.FieldIgnore("address")),
		ormModels...,
	)

	g.Execute()
}

func main() {
	//db := openDB()
	//if db == nil {
	//	panic("无法连接数据库")
	//}
	var db *gorm.DB = nil
	genQueryInsbuy(db)
	genQueryPri(db)
}
