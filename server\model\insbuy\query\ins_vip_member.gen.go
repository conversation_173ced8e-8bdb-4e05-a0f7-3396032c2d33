// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsVipMember(db *gorm.DB, opts ...gen.DOOption) insVipMember {
	_insVipMember := insVipMember{}

	_insVipMember.insVipMemberDo.UseDB(db, opts...)
	_insVipMember.insVipMemberDo.UseModel(&insbuy.InsVipMember{})

	tableName := _insVipMember.insVipMemberDo.TableName()
	_insVipMember.ALL = field.NewAsterisk(tableName)
	_insVipMember.ID = field.NewUint(tableName, "id")
	_insVipMember.CreatedAt = field.NewTime(tableName, "created_at")
	_insVipMember.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insVipMember.DeletedAt = field.NewField(tableName, "deleted_at")
	_insVipMember.Code = field.NewString(tableName, "code")
	_insVipMember.Phone = field.NewString(tableName, "phone")
	_insVipMember.SubAccount = field.NewString(tableName, "sub_account")
	_insVipMember.Email = field.NewString(tableName, "email")
	_insVipMember.UserName = field.NewString(tableName, "user_name")
	_insVipMember.Viplevel = field.NewUint(tableName, "viplevel")
	_insVipMember.Source = field.NewUint(tableName, "source")
	_insVipMember.Sex = field.NewUint(tableName, "sex")
	_insVipMember.Birthday = field.NewTime(tableName, "birthday")
	_insVipMember.LastLogin = field.NewTime(tableName, "last_login")
	_insVipMember.LastIp = field.NewString(tableName, "last_ip")
	_insVipMember.VisitCount = field.NewInt(tableName, "visit_count")
	_insVipMember.Alias_ = field.NewString(tableName, "alias")
	_insVipMember.StoreId = field.NewUint(tableName, "store_id")
	_insVipMember.SalesId = field.NewUint(tableName, "sales_id")
	_insVipMember.Remark = field.NewString(tableName, "remark")

	_insVipMember.fillFieldMap()

	return _insVipMember
}

type insVipMember struct {
	insVipMemberDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	Code       field.String
	Phone      field.String
	SubAccount field.String
	Email      field.String
	UserName   field.String
	Viplevel   field.Uint
	Source     field.Uint
	Sex        field.Uint
	Birthday   field.Time
	LastLogin  field.Time
	LastIp     field.String
	VisitCount field.Int
	Alias_     field.String
	StoreId    field.Uint
	SalesId    field.Uint
	Remark     field.String

	fieldMap map[string]field.Expr
}

func (i insVipMember) Table(newTableName string) *insVipMember {
	i.insVipMemberDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insVipMember) As(alias string) *insVipMember {
	i.insVipMemberDo.DO = *(i.insVipMemberDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insVipMember) updateTableName(table string) *insVipMember {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Code = field.NewString(table, "code")
	i.Phone = field.NewString(table, "phone")
	i.SubAccount = field.NewString(table, "sub_account")
	i.Email = field.NewString(table, "email")
	i.UserName = field.NewString(table, "user_name")
	i.Viplevel = field.NewUint(table, "viplevel")
	i.Source = field.NewUint(table, "source")
	i.Sex = field.NewUint(table, "sex")
	i.Birthday = field.NewTime(table, "birthday")
	i.LastLogin = field.NewTime(table, "last_login")
	i.LastIp = field.NewString(table, "last_ip")
	i.VisitCount = field.NewInt(table, "visit_count")
	i.Alias_ = field.NewString(table, "alias")
	i.StoreId = field.NewUint(table, "store_id")
	i.SalesId = field.NewUint(table, "sales_id")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insVipMember) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insVipMember) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 20)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["code"] = i.Code
	i.fieldMap["phone"] = i.Phone
	i.fieldMap["sub_account"] = i.SubAccount
	i.fieldMap["email"] = i.Email
	i.fieldMap["user_name"] = i.UserName
	i.fieldMap["viplevel"] = i.Viplevel
	i.fieldMap["source"] = i.Source
	i.fieldMap["sex"] = i.Sex
	i.fieldMap["birthday"] = i.Birthday
	i.fieldMap["last_login"] = i.LastLogin
	i.fieldMap["last_ip"] = i.LastIp
	i.fieldMap["visit_count"] = i.VisitCount
	i.fieldMap["alias"] = i.Alias_
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["sales_id"] = i.SalesId
	i.fieldMap["remark"] = i.Remark
}

func (i insVipMember) clone(db *gorm.DB) insVipMember {
	i.insVipMemberDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insVipMember) replaceDB(db *gorm.DB) insVipMember {
	i.insVipMemberDo.ReplaceDB(db)
	return i
}

type insVipMemberDo struct{ gen.DO }

func (i insVipMemberDo) Debug() *insVipMemberDo {
	return i.withDO(i.DO.Debug())
}

func (i insVipMemberDo) WithContext(ctx context.Context) *insVipMemberDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insVipMemberDo) ReadDB() *insVipMemberDo {
	return i.Clauses(dbresolver.Read)
}

func (i insVipMemberDo) WriteDB() *insVipMemberDo {
	return i.Clauses(dbresolver.Write)
}

func (i insVipMemberDo) Session(config *gorm.Session) *insVipMemberDo {
	return i.withDO(i.DO.Session(config))
}

func (i insVipMemberDo) Clauses(conds ...clause.Expression) *insVipMemberDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insVipMemberDo) Returning(value interface{}, columns ...string) *insVipMemberDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insVipMemberDo) Not(conds ...gen.Condition) *insVipMemberDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insVipMemberDo) Or(conds ...gen.Condition) *insVipMemberDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insVipMemberDo) Select(conds ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insVipMemberDo) Where(conds ...gen.Condition) *insVipMemberDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insVipMemberDo) Order(conds ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insVipMemberDo) Distinct(cols ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insVipMemberDo) Omit(cols ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insVipMemberDo) Join(table schema.Tabler, on ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insVipMemberDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insVipMemberDo) RightJoin(table schema.Tabler, on ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insVipMemberDo) Group(cols ...field.Expr) *insVipMemberDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insVipMemberDo) Having(conds ...gen.Condition) *insVipMemberDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insVipMemberDo) Limit(limit int) *insVipMemberDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insVipMemberDo) Offset(offset int) *insVipMemberDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insVipMemberDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insVipMemberDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insVipMemberDo) Unscoped() *insVipMemberDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insVipMemberDo) Create(values ...*insbuy.InsVipMember) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insVipMemberDo) CreateInBatches(values []*insbuy.InsVipMember, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insVipMemberDo) Save(values ...*insbuy.InsVipMember) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insVipMemberDo) First() (*insbuy.InsVipMember, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipMember), nil
	}
}

func (i insVipMemberDo) Take() (*insbuy.InsVipMember, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipMember), nil
	}
}

func (i insVipMemberDo) Last() (*insbuy.InsVipMember, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipMember), nil
	}
}

func (i insVipMemberDo) Find() ([]*insbuy.InsVipMember, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsVipMember), err
}

func (i insVipMemberDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsVipMember, err error) {
	buf := make([]*insbuy.InsVipMember, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insVipMemberDo) FindInBatches(result *[]*insbuy.InsVipMember, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insVipMemberDo) Attrs(attrs ...field.AssignExpr) *insVipMemberDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insVipMemberDo) Assign(attrs ...field.AssignExpr) *insVipMemberDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insVipMemberDo) Joins(fields ...field.RelationField) *insVipMemberDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insVipMemberDo) Preload(fields ...field.RelationField) *insVipMemberDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insVipMemberDo) FirstOrInit() (*insbuy.InsVipMember, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipMember), nil
	}
}

func (i insVipMemberDo) FirstOrCreate() (*insbuy.InsVipMember, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipMember), nil
	}
}

func (i insVipMemberDo) FindByPage(offset int, limit int) (result []*insbuy.InsVipMember, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insVipMemberDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insVipMemberDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insVipMemberDo) Delete(models ...*insbuy.InsVipMember) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insVipMemberDo) withDO(do gen.Dao) *insVipMemberDo {
	i.DO = *do.(*gen.DO)
	return i
}
