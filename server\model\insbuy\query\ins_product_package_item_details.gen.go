// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageItemDetails(db *gorm.DB, opts ...gen.DOOption) insProductPackageItemDetails {
	_insProductPackageItemDetails := insProductPackageItemDetails{}

	_insProductPackageItemDetails.insProductPackageItemDetailsDo.UseDB(db, opts...)
	_insProductPackageItemDetails.insProductPackageItemDetailsDo.UseModel(&insbuy.InsProductPackageItemDetails{})

	tableName := _insProductPackageItemDetails.insProductPackageItemDetailsDo.TableName()
	_insProductPackageItemDetails.ALL = field.NewAsterisk(tableName)
	_insProductPackageItemDetails.Id = field.NewInt(tableName, "id")
	_insProductPackageItemDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageItemDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageItemDetails.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPackageItemDetails.PackageId = field.NewInt(tableName, "package_id")
	_insProductPackageItemDetails.ItemId = field.NewInt(tableName, "item_id")
	_insProductPackageItemDetails.ProductId = field.NewInt(tableName, "product_id")
	_insProductPackageItemDetails.Num = field.NewInt(tableName, "num")
	_insProductPackageItemDetails.IfGive = field.NewInt(tableName, "if_give")

	_insProductPackageItemDetails.fillFieldMap()

	return _insProductPackageItemDetails
}

type insProductPackageItemDetails struct {
	insProductPackageItemDetailsDo

	ALL       field.Asterisk
	Id        field.Int
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	PackageId field.Int
	ItemId    field.Int
	ProductId field.Int
	Num       field.Int
	IfGive    field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPackageItemDetails) Table(newTableName string) *insProductPackageItemDetails {
	i.insProductPackageItemDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageItemDetails) As(alias string) *insProductPackageItemDetails {
	i.insProductPackageItemDetailsDo.DO = *(i.insProductPackageItemDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageItemDetails) updateTableName(table string) *insProductPackageItemDetails {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PackageId = field.NewInt(table, "package_id")
	i.ItemId = field.NewInt(table, "item_id")
	i.ProductId = field.NewInt(table, "product_id")
	i.Num = field.NewInt(table, "num")
	i.IfGive = field.NewInt(table, "if_give")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageItemDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageItemDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.Id
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["num"] = i.Num
	i.fieldMap["if_give"] = i.IfGive
}

func (i insProductPackageItemDetails) clone(db *gorm.DB) insProductPackageItemDetails {
	i.insProductPackageItemDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageItemDetails) replaceDB(db *gorm.DB) insProductPackageItemDetails {
	i.insProductPackageItemDetailsDo.ReplaceDB(db)
	return i
}

type insProductPackageItemDetailsDo struct{ gen.DO }

func (i insProductPackageItemDetailsDo) Debug() *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageItemDetailsDo) WithContext(ctx context.Context) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageItemDetailsDo) ReadDB() *insProductPackageItemDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageItemDetailsDo) WriteDB() *insProductPackageItemDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageItemDetailsDo) Session(config *gorm.Session) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageItemDetailsDo) Clauses(conds ...clause.Expression) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageItemDetailsDo) Returning(value interface{}, columns ...string) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageItemDetailsDo) Not(conds ...gen.Condition) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageItemDetailsDo) Or(conds ...gen.Condition) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageItemDetailsDo) Select(conds ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageItemDetailsDo) Where(conds ...gen.Condition) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageItemDetailsDo) Order(conds ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageItemDetailsDo) Distinct(cols ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageItemDetailsDo) Omit(cols ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageItemDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageItemDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageItemDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageItemDetailsDo) Group(cols ...field.Expr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageItemDetailsDo) Having(conds ...gen.Condition) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageItemDetailsDo) Limit(limit int) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageItemDetailsDo) Offset(offset int) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageItemDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageItemDetailsDo) Unscoped() *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageItemDetailsDo) Create(values ...*insbuy.InsProductPackageItemDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageItemDetailsDo) CreateInBatches(values []*insbuy.InsProductPackageItemDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageItemDetailsDo) Save(values ...*insbuy.InsProductPackageItemDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageItemDetailsDo) First() (*insbuy.InsProductPackageItemDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemDetails), nil
	}
}

func (i insProductPackageItemDetailsDo) Take() (*insbuy.InsProductPackageItemDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemDetails), nil
	}
}

func (i insProductPackageItemDetailsDo) Last() (*insbuy.InsProductPackageItemDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemDetails), nil
	}
}

func (i insProductPackageItemDetailsDo) Find() ([]*insbuy.InsProductPackageItemDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageItemDetails), err
}

func (i insProductPackageItemDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageItemDetails, err error) {
	buf := make([]*insbuy.InsProductPackageItemDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageItemDetailsDo) FindInBatches(result *[]*insbuy.InsProductPackageItemDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageItemDetailsDo) Attrs(attrs ...field.AssignExpr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageItemDetailsDo) Assign(attrs ...field.AssignExpr) *insProductPackageItemDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageItemDetailsDo) Joins(fields ...field.RelationField) *insProductPackageItemDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageItemDetailsDo) Preload(fields ...field.RelationField) *insProductPackageItemDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageItemDetailsDo) FirstOrInit() (*insbuy.InsProductPackageItemDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemDetails), nil
	}
}

func (i insProductPackageItemDetailsDo) FirstOrCreate() (*insbuy.InsProductPackageItemDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemDetails), nil
	}
}

func (i insProductPackageItemDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageItemDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageItemDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageItemDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageItemDetailsDo) Delete(models ...*insbuy.InsProductPackageItemDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageItemDetailsDo) withDO(do gen.Dao) *insProductPackageItemDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
