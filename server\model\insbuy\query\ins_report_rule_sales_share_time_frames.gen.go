// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportRuleSalesShareTimeFrame(db *gorm.DB, opts ...gen.DOOption) insReportRuleSalesShareTimeFrame {
	_insReportRuleSalesShareTimeFrame := insReportRuleSalesShareTimeFrame{}

	_insReportRuleSalesShareTimeFrame.insReportRuleSalesShareTimeFrameDo.UseDB(db, opts...)
	_insReportRuleSalesShareTimeFrame.insReportRuleSalesShareTimeFrameDo.UseModel(&insbuy.InsReportRuleSalesShareTimeFrame{})

	tableName := _insReportRuleSalesShareTimeFrame.insReportRuleSalesShareTimeFrameDo.TableName()
	_insReportRuleSalesShareTimeFrame.ALL = field.NewAsterisk(tableName)
	_insReportRuleSalesShareTimeFrame.ID = field.NewUint(tableName, "id")
	_insReportRuleSalesShareTimeFrame.RuleId = field.NewUint(tableName, "rule_id")
	_insReportRuleSalesShareTimeFrame.StartTime = field.NewField(tableName, "start_time")
	_insReportRuleSalesShareTimeFrame.EndTime = field.NewField(tableName, "end_time")

	_insReportRuleSalesShareTimeFrame.fillFieldMap()

	return _insReportRuleSalesShareTimeFrame
}

type insReportRuleSalesShareTimeFrame struct {
	insReportRuleSalesShareTimeFrameDo

	ALL       field.Asterisk
	ID        field.Uint
	RuleId    field.Uint
	StartTime field.Field
	EndTime   field.Field

	fieldMap map[string]field.Expr
}

func (i insReportRuleSalesShareTimeFrame) Table(newTableName string) *insReportRuleSalesShareTimeFrame {
	i.insReportRuleSalesShareTimeFrameDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportRuleSalesShareTimeFrame) As(alias string) *insReportRuleSalesShareTimeFrame {
	i.insReportRuleSalesShareTimeFrameDo.DO = *(i.insReportRuleSalesShareTimeFrameDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportRuleSalesShareTimeFrame) updateTableName(table string) *insReportRuleSalesShareTimeFrame {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.RuleId = field.NewUint(table, "rule_id")
	i.StartTime = field.NewField(table, "start_time")
	i.EndTime = field.NewField(table, "end_time")

	i.fillFieldMap()

	return i
}

func (i *insReportRuleSalesShareTimeFrame) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportRuleSalesShareTimeFrame) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 4)
	i.fieldMap["id"] = i.ID
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
}

func (i insReportRuleSalesShareTimeFrame) clone(db *gorm.DB) insReportRuleSalesShareTimeFrame {
	i.insReportRuleSalesShareTimeFrameDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportRuleSalesShareTimeFrame) replaceDB(db *gorm.DB) insReportRuleSalesShareTimeFrame {
	i.insReportRuleSalesShareTimeFrameDo.ReplaceDB(db)
	return i
}

type insReportRuleSalesShareTimeFrameDo struct{ gen.DO }

func (i insReportRuleSalesShareTimeFrameDo) Debug() *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportRuleSalesShareTimeFrameDo) WithContext(ctx context.Context) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportRuleSalesShareTimeFrameDo) ReadDB() *insReportRuleSalesShareTimeFrameDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportRuleSalesShareTimeFrameDo) WriteDB() *insReportRuleSalesShareTimeFrameDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportRuleSalesShareTimeFrameDo) Session(config *gorm.Session) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportRuleSalesShareTimeFrameDo) Clauses(conds ...clause.Expression) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Returning(value interface{}, columns ...string) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportRuleSalesShareTimeFrameDo) Not(conds ...gen.Condition) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Or(conds ...gen.Condition) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Select(conds ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Where(conds ...gen.Condition) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Order(conds ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Distinct(cols ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportRuleSalesShareTimeFrameDo) Omit(cols ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportRuleSalesShareTimeFrameDo) Join(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportRuleSalesShareTimeFrameDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportRuleSalesShareTimeFrameDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportRuleSalesShareTimeFrameDo) Group(cols ...field.Expr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportRuleSalesShareTimeFrameDo) Having(conds ...gen.Condition) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportRuleSalesShareTimeFrameDo) Limit(limit int) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportRuleSalesShareTimeFrameDo) Offset(offset int) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportRuleSalesShareTimeFrameDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportRuleSalesShareTimeFrameDo) Unscoped() *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportRuleSalesShareTimeFrameDo) Create(values ...*insbuy.InsReportRuleSalesShareTimeFrame) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportRuleSalesShareTimeFrameDo) CreateInBatches(values []*insbuy.InsReportRuleSalesShareTimeFrame, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportRuleSalesShareTimeFrameDo) Save(values ...*insbuy.InsReportRuleSalesShareTimeFrame) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportRuleSalesShareTimeFrameDo) First() (*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareTimeFrame), nil
	}
}

func (i insReportRuleSalesShareTimeFrameDo) Take() (*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareTimeFrame), nil
	}
}

func (i insReportRuleSalesShareTimeFrameDo) Last() (*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareTimeFrame), nil
	}
}

func (i insReportRuleSalesShareTimeFrameDo) Find() ([]*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportRuleSalesShareTimeFrame), err
}

func (i insReportRuleSalesShareTimeFrameDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportRuleSalesShareTimeFrame, err error) {
	buf := make([]*insbuy.InsReportRuleSalesShareTimeFrame, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportRuleSalesShareTimeFrameDo) FindInBatches(result *[]*insbuy.InsReportRuleSalesShareTimeFrame, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportRuleSalesShareTimeFrameDo) Attrs(attrs ...field.AssignExpr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportRuleSalesShareTimeFrameDo) Assign(attrs ...field.AssignExpr) *insReportRuleSalesShareTimeFrameDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportRuleSalesShareTimeFrameDo) Joins(fields ...field.RelationField) *insReportRuleSalesShareTimeFrameDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportRuleSalesShareTimeFrameDo) Preload(fields ...field.RelationField) *insReportRuleSalesShareTimeFrameDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportRuleSalesShareTimeFrameDo) FirstOrInit() (*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareTimeFrame), nil
	}
}

func (i insReportRuleSalesShareTimeFrameDo) FirstOrCreate() (*insbuy.InsReportRuleSalesShareTimeFrame, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportRuleSalesShareTimeFrame), nil
	}
}

func (i insReportRuleSalesShareTimeFrameDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportRuleSalesShareTimeFrame, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportRuleSalesShareTimeFrameDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportRuleSalesShareTimeFrameDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportRuleSalesShareTimeFrameDo) Delete(models ...*insbuy.InsReportRuleSalesShareTimeFrame) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportRuleSalesShareTimeFrameDo) withDO(do gen.Dao) *insReportRuleSalesShareTimeFrameDo {
	i.DO = *do.(*gen.DO)
	return i
}
