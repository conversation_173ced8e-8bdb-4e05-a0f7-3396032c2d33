package printmodel

type TplTakeBill struct {
	TplBase
	UserDeskName string // 用户桌台名称
	DeskName     string // 桌台名称
	DeskArea     string
	//点单员-存酒员
	Waiter string
	//取酒单号
	StoreSn string
	//取酒人电话
	VipPhone string
	//取酒人
	StoreUser string
	//取酒时间
	TakeTime    string
	Items       []TplTakeBillItem // 商品列表
	TotalNum    int               // 合计数量
	TotalAmount float64           // 合计金额
	Remark      string            //整单备注
}

type TplTakeBillItem struct {
	Name      string  // 商品名称
	Spec      float64 // 余量
	DepositSn string  //存酒编号
	Remark    string  // 备注（可选）
	Num       int     // 数量
	Unit      string  // 套
	//编号
	No string
}
