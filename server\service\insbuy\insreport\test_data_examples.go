package insreport

import (
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
)

// GetTestCostTypeConfigs 获取测试用的分类配置数据
func GetTestCostTypeConfigs() []CostTypeConfigItem {
	return []CostTypeConfigItem{
		// 基础数据分类
		{
			Id: 1, CategoryName: "营业收入", CategoryCode: "REVENUE",
			IsCalculated: false, SortOrder: 100, Level: 1, ParentId: 0,
		},
		{
			Id: 2, CategoryName: "营业成本", CategoryCode: "COST",
			IsCalculated: false, SortOrder: 200, Level: 1, ParentId: 0,
		},
		{
			Id: 3, CategoryName: "销售费用", CategoryCode: "SALES_EXPENSE",
			IsCalculated: false, SortOrder: 400, Level: 1, ParentId: 0,
		},
		{
			Id: 4, CategoryName: "管理费用", CategoryCode: "ADMIN_EXPENSE",
			IsCalculated: false, SortOrder: 500, Level: 1, ParentId: 0,
		},
		{
			Id: 5, CategoryName: "财务费用", CategoryCode: "FINANCE_EXPENSE",
			IsCalculated: false, SortOrder: 600, Level: 1, ParentId: 0,
		},

		// 计算型分类 - 使用ID引用
		{
			Id: 100, CategoryName: "毛利润", CategoryCode: "GROSS_PROFIT",
			IsCalculated: true, SortOrder: 300, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"#1 - #2","references":[1,2],"description":"毛利润计算：使用ID引用"}`,
		},
		{
			Id: 101, CategoryName: "毛利率", CategoryCode: "GROSS_MARGIN",
			IsCalculated: true, SortOrder: 350, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"#100 / #1 %","references":[100,1],"description":"毛利率计算：使用ID引用"}`,
		},
		{
			Id: 102, CategoryName: "期间费用合计", CategoryCode: "PERIOD_EXPENSE_TOTAL",
			IsCalculated: true, SortOrder: 700, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"#3 + #4 + #5","references":[3,4,5],"description":"期间费用合计：使用ID引用"}`,
		},
		{
			Id: 103, CategoryName: "营业利润", CategoryCode: "OPERATING_PROFIT",
			IsCalculated: true, SortOrder: 800, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"#100 - #102","references":[100,102],"description":"营业利润计算：使用ID引用"}`,
		},
		{
			Id: 104, CategoryName: "营业利润率", CategoryCode: "OPERATING_MARGIN",
			IsCalculated: true, SortOrder: 850, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"#103 / #1 %","references":[103,1],"description":"营业利润率计算：使用ID引用"}`,
		},

		// 计算型分类 - 使用分类名称引用（向后兼容）
		{
			Id: 200, CategoryName: "毛利润_名称引用", CategoryCode: "GROSS_PROFIT_NAME",
			IsCalculated: true, SortOrder: 301, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润计算：使用分类名称引用"}`,
		},

		// 计算型分类 - 使用混合引用
		{
			Id: 300, CategoryName: "毛利润_混合引用", CategoryCode: "GROSS_PROFIT_MIXED",
			IsCalculated: true, SortOrder: 302, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"[营业收入] - #2","references":[1,2],"description":"毛利润计算：使用混合引用"}`,
		},

		// 复杂表达式示例
		{
			Id: 400, CategoryName: "综合利润率", CategoryCode: "COMPREHENSIVE_MARGIN",
			IsCalculated: true, SortOrder: 900, Level: 1, ParentId: 0,
			CalculationFormula: `{"expression":"((#1 - #2) - (#3 + #4 + #5)) / #1 %","references":[1,2,3,4,5],"description":"综合利润率：完全展开的复杂计算"}`,
		},
	}
}

// GetTestFinancialSummaryItems 获取测试用的财务汇总数据
func GetTestFinancialSummaryItems() []FinancialSummaryItem {
	return []FinancialSummaryItem{
		{
			CategoryId: 1, CategoryName: "营业收入", CategoryCode: "REVENUE",
			SortOrder: 100, Level: 1, ParentId: 0,
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 10000000, // 1000万
				"2025-02": 12000000, // 1200万
				"2025-03": 11000000, // 1100万
			},
			TotalAmount: 33000000, // 3300万
			Children:    []FinancialSummaryItem{},
		},
		{
			CategoryId: 2, CategoryName: "营业成本", CategoryCode: "COST",
			SortOrder: 200, Level: 1, ParentId: 0,
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 6000000, // 600万
				"2025-02": 7200000, // 720万
				"2025-03": 6600000, // 660万
			},
			TotalAmount: 19800000, // 1980万
			Children:    []FinancialSummaryItem{},
		},
		{
			CategoryId: 3, CategoryName: "销售费用", CategoryCode: "SALES_EXPENSE",
			SortOrder: 400, Level: 1, ParentId: 0,
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 1000000, // 100万
				"2025-02": 1200000, // 120万
				"2025-03": 1100000, // 110万
			},
			TotalAmount: 3300000, // 330万
			Children:    []FinancialSummaryItem{},
		},
		{
			CategoryId: 4, CategoryName: "管理费用", CategoryCode: "ADMIN_EXPENSE",
			SortOrder: 500, Level: 1, ParentId: 0,
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 800000, // 80万
				"2025-02": 960000, // 96万
				"2025-03": 880000, // 88万
			},
			TotalAmount: 2640000, // 264万
			Children:    []FinancialSummaryItem{},
		},
		{
			CategoryId: 5, CategoryName: "财务费用", CategoryCode: "FINANCE_EXPENSE",
			SortOrder: 600, Level: 1, ParentId: 0,
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 200000, // 20万
				"2025-02": 240000, // 24万
				"2025-03": 220000, // 22万
			},
			TotalAmount: 660000, // 66万
			Children:    []FinancialSummaryItem{},
		},
	}
}

// GetTestTimeColumns 获取测试用的时间列
func GetTestTimeColumns() []string {
	return []string{"2025-01", "2025-02", "2025-03"}
}

// GetExpectedCalculationResults 获取预期的计算结果
func GetExpectedCalculationResults() map[uint]FinancialSummaryItem {
	return map[uint]FinancialSummaryItem{
		// 毛利润 = 营业收入 - 营业成本
		100: {
			CategoryId: 100, CategoryName: "毛利润",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 4000000,  // 1000-600=400万
				"2025-02": 4800000,  // 1200-720=480万
				"2025-03": 4400000,  // 1100-660=440万
			},
			TotalAmount: 13200000, // 3300-1980=1320万
		},

		// 毛利率 = 毛利润 / 营业收入 %
		101: {
			CategoryId: 101, CategoryName: "毛利率",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 4000, // 400/1000*100=40%，存储为4000（保持精度）
				"2025-02": 4000, // 480/1200*100=40%
				"2025-03": 4000, // 440/1100*100=40%
			},
			TotalAmount: 4000, // 1320/3300*100=40%
		},

		// 期间费用合计 = 销售费用 + 管理费用 + 财务费用
		102: {
			CategoryId: 102, CategoryName: "期间费用合计",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 2000000, // 100+80+20=200万
				"2025-02": 2400000, // 120+96+24=240万
				"2025-03": 2200000, // 110+88+22=220万
			},
			TotalAmount: 6600000, // 330+264+66=660万
		},

		// 营业利润 = 毛利润 - 期间费用合计
		103: {
			CategoryId: 103, CategoryName: "营业利润",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 2000000, // 400-200=200万
				"2025-02": 2400000, // 480-240=240万
				"2025-03": 2200000, // 440-220=220万
			},
			TotalAmount: 6600000, // 1320-660=660万
		},

		// 营业利润率 = 营业利润 / 营业收入 %
		104: {
			CategoryId: 104, CategoryName: "营业利润率",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 2000, // 200/1000*100=20%
				"2025-02": 2000, // 240/1200*100=20%
				"2025-03": 2000, // 220/1100*100=20%
			},
			TotalAmount: 2000, // 660/3300*100=20%
		},

		// 综合利润率 = ((营业收入 - 营业成本) - (销售费用 + 管理费用 + 财务费用)) / 营业收入 %
		400: {
			CategoryId: 400, CategoryName: "综合利润率",
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": 2000, // ((1000-600)-(100+80+20))/1000*100=20%
				"2025-02": 2000, // ((1200-720)-(120+96+24))/1200*100=20%
				"2025-03": 2000, // ((1100-660)-(110+88+22))/1100*100=20%
			},
			TotalAmount: 2000, // ((3300-1980)-(330+264+66))/3300*100=20%
		},
	}
}

// TestCompleteIDReferenceScenario 完整的ID引用测试场景
func TestCompleteIDReferenceScenario() {
	// 1. 获取测试配置
	configs := GetTestCostTypeConfigs()
	
	// 2. 创建处理器
	processor := NewSimpleJSONProcessor(configs)
	
	// 3. 获取基础数据
	items := GetTestFinancialSummaryItems()
	timeColumns := GetTestTimeColumns()
	
	// 4. 处理计算型分类
	result := processor.ProcessCalculatedCategoriesJSON(items, configs, timeColumns)
	
	// 5. 验证结果
	expectedResults := GetExpectedCalculationResults()
	
	// 输出结果用于验证
	for _, item := range result {
		if item.CategoryId >= 100 { // 只检查计算型分类
			if expected, exists := expectedResults[item.CategoryId]; exists {
				// 验证计算结果是否正确
				_ = expected // 这里可以添加具体的验证逻辑
			}
		}
	}
}

// ExampleFormulaJSONs 示例公式JSON字符串
var ExampleFormulaJSONs = map[string]string{
	"毛利润_ID引用": `{"expression":"#1 - #2","references":[1,2],"description":"毛利润计算：使用ID引用"}`,
	
	"毛利润_名称引用": `{"expression":"[营业收入] - [营业成本]","references":[1,2],"description":"毛利润计算：使用分类名称引用"}`,
	
	"毛利润_混合引用": `{"expression":"[营业收入] - #2","references":[1,2],"description":"毛利润计算：使用混合引用"}`,
	
	"毛利率_ID引用": `{"expression":"#100 / #1 %","references":[100,1],"description":"毛利率计算：使用ID引用"}`,
	
	"期间费用_ID引用": `{"expression":"#3 + #4 + #5","references":[3,4,5],"description":"期间费用合计：使用ID引用"}`,
	
	"营业利润_ID引用": `{"expression":"#100 - #102","references":[100,102],"description":"营业利润计算：使用ID引用"}`,
	
	"营业利润率_ID引用": `{"expression":"#103 / #1 %","references":[103,1],"description":"营业利润率计算：使用ID引用"}`,
	
	"综合利润率_复杂表达式": `{"expression":"((#1 - #2) - (#3 + #4 + #5)) / #1 %","references":[1,2,3,4,5],"description":"综合利润率：完全展开的复杂计算"}`,
	
	"混合复杂表达式": `{"expression":"([营业收入] - #2 - (#3 + [管理费用])) / [营业收入] %","references":[1,2,3,4],"description":"混合引用的复杂表达式"}`,
}

// ValidateAllExampleFormulas 验证所有示例公式
func ValidateAllExampleFormulas() map[string][]ValidationError {
	configs := GetTestCostTypeConfigs()
	processor := NewSimpleJSONProcessor(configs)
	
	results := make(map[string][]ValidationError)
	
	for name, formulaJSON := range ExampleFormulaJSONs {
		errors := processor.ValidateFormulaJSON(formulaJSON)
		results[name] = errors
	}
	
	return results
}
