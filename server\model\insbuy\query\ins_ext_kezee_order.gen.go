// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtKezeeOrder(db *gorm.DB, opts ...gen.DOOption) insExtKezeeOrder {
	_insExtKezeeOrder := insExtKezeeOrder{}

	_insExtKezeeOrder.insExtKezeeOrderDo.UseDB(db, opts...)
	_insExtKezeeOrder.insExtKezeeOrderDo.UseModel(&insbuy.InsExtKezeeOrder{})

	tableName := _insExtKezeeOrder.insExtKezeeOrderDo.TableName()
	_insExtKezeeOrder.ALL = field.NewAsterisk(tableName)
	_insExtKezeeOrder.Id = field.NewUint(tableName, "id")
	_insExtKezeeOrder.AppId = field.NewString(tableName, "app_id")
	_insExtKezeeOrder.BusinessDay = field.NewTime(tableName, "business_day")
	_insExtKezeeOrder.KzId = field.NewString(tableName, "kz_id")
	_insExtKezeeOrder.AreaId = field.NewString(tableName, "area_id")
	_insExtKezeeOrder.AreaName = field.NewString(tableName, "area_name")
	_insExtKezeeOrder.PointId = field.NewString(tableName, "point_id")
	_insExtKezeeOrder.PointName = field.NewString(tableName, "point_name")
	_insExtKezeeOrder.SalesmanId = field.NewString(tableName, "salesman_id")
	_insExtKezeeOrder.SalesmanName = field.NewString(tableName, "salesman_name")
	_insExtKezeeOrder.BookPerson = field.NewString(tableName, "book_person")
	_insExtKezeeOrder.Code = field.NewString(tableName, "code")
	_insExtKezeeOrder.MainCode = field.NewString(tableName, "main_code")
	_insExtKezeeOrder.PeopleQty = field.NewInt(tableName, "people_qty")
	_insExtKezeeOrder.OpenTime = field.NewField(tableName, "open_time")
	_insExtKezeeOrder.SettleTime = field.NewField(tableName, "settle_time")
	_insExtKezeeOrder.OrigTotal = field.NewFloat64(tableName, "orig_total")
	_insExtKezeeOrder.DiscTotal = field.NewFloat64(tableName, "disc_total")
	_insExtKezeeOrder.LastTotal = field.NewFloat64(tableName, "last_total")
	_insExtKezeeOrder.IncomeMoney = field.NewFloat64(tableName, "income_money")
	_insExtKezeeOrder.PayEname = field.NewString(tableName, "pay_ename")
	_insExtKezeeOrder.Ext = field.NewField(tableName, "ext")
	_insExtKezeeOrder.Hash = field.NewString(tableName, "hash")
	_insExtKezeeOrder.PayData = insExtKezeeOrderHasManyPayData{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("PayData", "insbuy.InsExtKezeeOrderPay"),
	}

	_insExtKezeeOrder.ItemData = insExtKezeeOrderHasManyItemData{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("ItemData", "insbuy.InsExtKezeeOrderItem"),
	}

	_insExtKezeeOrder.fillFieldMap()

	return _insExtKezeeOrder
}

type insExtKezeeOrder struct {
	insExtKezeeOrderDo

	ALL          field.Asterisk
	Id           field.Uint
	AppId        field.String
	BusinessDay  field.Time
	KzId         field.String
	AreaId       field.String
	AreaName     field.String
	PointId      field.String
	PointName    field.String
	SalesmanId   field.String
	SalesmanName field.String
	BookPerson   field.String
	Code         field.String
	MainCode     field.String
	PeopleQty    field.Int
	OpenTime     field.Field
	SettleTime   field.Field
	OrigTotal    field.Float64
	DiscTotal    field.Float64
	LastTotal    field.Float64
	IncomeMoney  field.Float64
	PayEname     field.String
	Ext          field.Field
	Hash         field.String
	PayData      insExtKezeeOrderHasManyPayData

	ItemData insExtKezeeOrderHasManyItemData

	fieldMap map[string]field.Expr
}

func (i insExtKezeeOrder) Table(newTableName string) *insExtKezeeOrder {
	i.insExtKezeeOrderDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtKezeeOrder) As(alias string) *insExtKezeeOrder {
	i.insExtKezeeOrderDo.DO = *(i.insExtKezeeOrderDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtKezeeOrder) updateTableName(table string) *insExtKezeeOrder {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewUint(table, "id")
	i.AppId = field.NewString(table, "app_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.KzId = field.NewString(table, "kz_id")
	i.AreaId = field.NewString(table, "area_id")
	i.AreaName = field.NewString(table, "area_name")
	i.PointId = field.NewString(table, "point_id")
	i.PointName = field.NewString(table, "point_name")
	i.SalesmanId = field.NewString(table, "salesman_id")
	i.SalesmanName = field.NewString(table, "salesman_name")
	i.BookPerson = field.NewString(table, "book_person")
	i.Code = field.NewString(table, "code")
	i.MainCode = field.NewString(table, "main_code")
	i.PeopleQty = field.NewInt(table, "people_qty")
	i.OpenTime = field.NewField(table, "open_time")
	i.SettleTime = field.NewField(table, "settle_time")
	i.OrigTotal = field.NewFloat64(table, "orig_total")
	i.DiscTotal = field.NewFloat64(table, "disc_total")
	i.LastTotal = field.NewFloat64(table, "last_total")
	i.IncomeMoney = field.NewFloat64(table, "income_money")
	i.PayEname = field.NewString(table, "pay_ename")
	i.Ext = field.NewField(table, "ext")
	i.Hash = field.NewString(table, "hash")

	i.fillFieldMap()

	return i
}

func (i *insExtKezeeOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtKezeeOrder) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 25)
	i.fieldMap["id"] = i.Id
	i.fieldMap["app_id"] = i.AppId
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["kz_id"] = i.KzId
	i.fieldMap["area_id"] = i.AreaId
	i.fieldMap["area_name"] = i.AreaName
	i.fieldMap["point_id"] = i.PointId
	i.fieldMap["point_name"] = i.PointName
	i.fieldMap["salesman_id"] = i.SalesmanId
	i.fieldMap["salesman_name"] = i.SalesmanName
	i.fieldMap["book_person"] = i.BookPerson
	i.fieldMap["code"] = i.Code
	i.fieldMap["main_code"] = i.MainCode
	i.fieldMap["people_qty"] = i.PeopleQty
	i.fieldMap["open_time"] = i.OpenTime
	i.fieldMap["settle_time"] = i.SettleTime
	i.fieldMap["orig_total"] = i.OrigTotal
	i.fieldMap["disc_total"] = i.DiscTotal
	i.fieldMap["last_total"] = i.LastTotal
	i.fieldMap["income_money"] = i.IncomeMoney
	i.fieldMap["pay_ename"] = i.PayEname
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["hash"] = i.Hash

}

func (i insExtKezeeOrder) clone(db *gorm.DB) insExtKezeeOrder {
	i.insExtKezeeOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtKezeeOrder) replaceDB(db *gorm.DB) insExtKezeeOrder {
	i.insExtKezeeOrderDo.ReplaceDB(db)
	return i
}

type insExtKezeeOrderHasManyPayData struct {
	db *gorm.DB

	field.RelationField
}

func (a insExtKezeeOrderHasManyPayData) Where(conds ...field.Expr) *insExtKezeeOrderHasManyPayData {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insExtKezeeOrderHasManyPayData) WithContext(ctx context.Context) *insExtKezeeOrderHasManyPayData {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insExtKezeeOrderHasManyPayData) Session(session *gorm.Session) *insExtKezeeOrderHasManyPayData {
	a.db = a.db.Session(session)
	return &a
}

func (a insExtKezeeOrderHasManyPayData) Model(m *insbuy.InsExtKezeeOrder) *insExtKezeeOrderHasManyPayDataTx {
	return &insExtKezeeOrderHasManyPayDataTx{a.db.Model(m).Association(a.Name())}
}

type insExtKezeeOrderHasManyPayDataTx struct{ tx *gorm.Association }

func (a insExtKezeeOrderHasManyPayDataTx) Find() (result []*insbuy.InsExtKezeeOrderPay, err error) {
	return result, a.tx.Find(&result)
}

func (a insExtKezeeOrderHasManyPayDataTx) Append(values ...*insbuy.InsExtKezeeOrderPay) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insExtKezeeOrderHasManyPayDataTx) Replace(values ...*insbuy.InsExtKezeeOrderPay) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insExtKezeeOrderHasManyPayDataTx) Delete(values ...*insbuy.InsExtKezeeOrderPay) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insExtKezeeOrderHasManyPayDataTx) Clear() error {
	return a.tx.Clear()
}

func (a insExtKezeeOrderHasManyPayDataTx) Count() int64 {
	return a.tx.Count()
}

type insExtKezeeOrderHasManyItemData struct {
	db *gorm.DB

	field.RelationField
}

func (a insExtKezeeOrderHasManyItemData) Where(conds ...field.Expr) *insExtKezeeOrderHasManyItemData {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insExtKezeeOrderHasManyItemData) WithContext(ctx context.Context) *insExtKezeeOrderHasManyItemData {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insExtKezeeOrderHasManyItemData) Session(session *gorm.Session) *insExtKezeeOrderHasManyItemData {
	a.db = a.db.Session(session)
	return &a
}

func (a insExtKezeeOrderHasManyItemData) Model(m *insbuy.InsExtKezeeOrder) *insExtKezeeOrderHasManyItemDataTx {
	return &insExtKezeeOrderHasManyItemDataTx{a.db.Model(m).Association(a.Name())}
}

type insExtKezeeOrderHasManyItemDataTx struct{ tx *gorm.Association }

func (a insExtKezeeOrderHasManyItemDataTx) Find() (result []*insbuy.InsExtKezeeOrderItem, err error) {
	return result, a.tx.Find(&result)
}

func (a insExtKezeeOrderHasManyItemDataTx) Append(values ...*insbuy.InsExtKezeeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insExtKezeeOrderHasManyItemDataTx) Replace(values ...*insbuy.InsExtKezeeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insExtKezeeOrderHasManyItemDataTx) Delete(values ...*insbuy.InsExtKezeeOrderItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insExtKezeeOrderHasManyItemDataTx) Clear() error {
	return a.tx.Clear()
}

func (a insExtKezeeOrderHasManyItemDataTx) Count() int64 {
	return a.tx.Count()
}

type insExtKezeeOrderDo struct{ gen.DO }

func (i insExtKezeeOrderDo) Debug() *insExtKezeeOrderDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtKezeeOrderDo) WithContext(ctx context.Context) *insExtKezeeOrderDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtKezeeOrderDo) ReadDB() *insExtKezeeOrderDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtKezeeOrderDo) WriteDB() *insExtKezeeOrderDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtKezeeOrderDo) Session(config *gorm.Session) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtKezeeOrderDo) Clauses(conds ...clause.Expression) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtKezeeOrderDo) Returning(value interface{}, columns ...string) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtKezeeOrderDo) Not(conds ...gen.Condition) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtKezeeOrderDo) Or(conds ...gen.Condition) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtKezeeOrderDo) Select(conds ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtKezeeOrderDo) Where(conds ...gen.Condition) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtKezeeOrderDo) Order(conds ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtKezeeOrderDo) Distinct(cols ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtKezeeOrderDo) Omit(cols ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtKezeeOrderDo) Join(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtKezeeOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtKezeeOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtKezeeOrderDo) Group(cols ...field.Expr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtKezeeOrderDo) Having(conds ...gen.Condition) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtKezeeOrderDo) Limit(limit int) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtKezeeOrderDo) Offset(offset int) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtKezeeOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtKezeeOrderDo) Unscoped() *insExtKezeeOrderDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtKezeeOrderDo) Create(values ...*insbuy.InsExtKezeeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtKezeeOrderDo) CreateInBatches(values []*insbuy.InsExtKezeeOrder, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtKezeeOrderDo) Save(values ...*insbuy.InsExtKezeeOrder) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtKezeeOrderDo) First() (*insbuy.InsExtKezeeOrder, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrder), nil
	}
}

func (i insExtKezeeOrderDo) Take() (*insbuy.InsExtKezeeOrder, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrder), nil
	}
}

func (i insExtKezeeOrderDo) Last() (*insbuy.InsExtKezeeOrder, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrder), nil
	}
}

func (i insExtKezeeOrderDo) Find() ([]*insbuy.InsExtKezeeOrder, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtKezeeOrder), err
}

func (i insExtKezeeOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtKezeeOrder, err error) {
	buf := make([]*insbuy.InsExtKezeeOrder, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtKezeeOrderDo) FindInBatches(result *[]*insbuy.InsExtKezeeOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtKezeeOrderDo) Attrs(attrs ...field.AssignExpr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtKezeeOrderDo) Assign(attrs ...field.AssignExpr) *insExtKezeeOrderDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtKezeeOrderDo) Joins(fields ...field.RelationField) *insExtKezeeOrderDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtKezeeOrderDo) Preload(fields ...field.RelationField) *insExtKezeeOrderDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtKezeeOrderDo) FirstOrInit() (*insbuy.InsExtKezeeOrder, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrder), nil
	}
}

func (i insExtKezeeOrderDo) FirstOrCreate() (*insbuy.InsExtKezeeOrder, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtKezeeOrder), nil
	}
}

func (i insExtKezeeOrderDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtKezeeOrder, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtKezeeOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtKezeeOrderDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtKezeeOrderDo) Delete(models ...*insbuy.InsExtKezeeOrder) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtKezeeOrderDo) withDO(do gen.Dao) *insExtKezeeOrderDo {
	i.DO = *do.(*gen.DO)
	return i
}
