package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// {{ServiceName}}Create 创建请求
type {{ServiceName}}Create struct {
	// TODO: 添加创建字段
}

// {{ServiceName}}Update 更新请求
type {{ServiceName}}Update struct {
	ID uint `json:"id" binding:"required"` // ID
	// TODO: 添加更新字段
}

// {{ServiceName}}Search 搜索请求
type {{ServiceName}}Search struct {
	request.PageInfo
	// TODO: 添加搜索字段
} 