// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsRechargeCard(db *gorm.DB, opts ...gen.DOOption) insRechargeCard {
	_insRechargeCard := insRechargeCard{}

	_insRechargeCard.insRechargeCardDo.UseDB(db, opts...)
	_insRechargeCard.insRechargeCardDo.UseModel(&insbuy.InsRechargeCard{})

	tableName := _insRechargeCard.insRechargeCardDo.TableName()
	_insRechargeCard.ALL = field.NewAsterisk(tableName)
	_insRechargeCard.ID = field.NewUint(tableName, "id")
	_insRechargeCard.CreatedAt = field.NewTime(tableName, "created_at")
	_insRechargeCard.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insRechargeCard.StoreId = field.NewUint(tableName, "store_id")
	_insRechargeCard.MemberId = field.NewUint(tableName, "member_id")
	_insRechargeCard.RechargeAmount = field.NewFloat64(tableName, "recharge_amount")
	_insRechargeCard.RuleId = field.NewUint(tableName, "rule_id")
	_insRechargeCard.GiveAmount = field.NewFloat64(tableName, "give_amount")
	_insRechargeCard.TradeId = field.NewUint64(tableName, "trade_id")
	_insRechargeCard.OperatorId = field.NewUint(tableName, "operator_id")
	_insRechargeCard.ActiveStatus = field.NewUint(tableName, "active_status")
	_insRechargeCard.RemainAmount = field.NewFloat64(tableName, "remain_amount")
	_insRechargeCard.RemainGiveAmount = field.NewFloat64(tableName, "remain_give_amount")
	_insRechargeCard.FrozenAmount = field.NewFloat64(tableName, "frozen_amount")
	_insRechargeCard.FrozenGiveAmount = field.NewFloat64(tableName, "frozen_give_amount")
	_insRechargeCard.Ext = field.NewField(tableName, "ext")
	_insRechargeCard.RemarkExt = field.NewField(tableName, "remark_ext")

	_insRechargeCard.fillFieldMap()

	return _insRechargeCard
}

type insRechargeCard struct {
	insRechargeCardDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	StoreId          field.Uint
	MemberId         field.Uint
	RechargeAmount   field.Float64
	RuleId           field.Uint
	GiveAmount       field.Float64
	TradeId          field.Uint64
	OperatorId       field.Uint
	ActiveStatus     field.Uint
	RemainAmount     field.Float64
	RemainGiveAmount field.Float64
	FrozenAmount     field.Float64
	FrozenGiveAmount field.Float64
	Ext              field.Field
	RemarkExt        field.Field

	fieldMap map[string]field.Expr
}

func (i insRechargeCard) Table(newTableName string) *insRechargeCard {
	i.insRechargeCardDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insRechargeCard) As(alias string) *insRechargeCard {
	i.insRechargeCardDo.DO = *(i.insRechargeCardDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insRechargeCard) updateTableName(table string) *insRechargeCard {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.MemberId = field.NewUint(table, "member_id")
	i.RechargeAmount = field.NewFloat64(table, "recharge_amount")
	i.RuleId = field.NewUint(table, "rule_id")
	i.GiveAmount = field.NewFloat64(table, "give_amount")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.ActiveStatus = field.NewUint(table, "active_status")
	i.RemainAmount = field.NewFloat64(table, "remain_amount")
	i.RemainGiveAmount = field.NewFloat64(table, "remain_give_amount")
	i.FrozenAmount = field.NewFloat64(table, "frozen_amount")
	i.FrozenGiveAmount = field.NewFloat64(table, "frozen_give_amount")
	i.Ext = field.NewField(table, "ext")
	i.RemarkExt = field.NewField(table, "remark_ext")

	i.fillFieldMap()

	return i
}

func (i *insRechargeCard) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insRechargeCard) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["member_id"] = i.MemberId
	i.fieldMap["recharge_amount"] = i.RechargeAmount
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["give_amount"] = i.GiveAmount
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["active_status"] = i.ActiveStatus
	i.fieldMap["remain_amount"] = i.RemainAmount
	i.fieldMap["remain_give_amount"] = i.RemainGiveAmount
	i.fieldMap["frozen_amount"] = i.FrozenAmount
	i.fieldMap["frozen_give_amount"] = i.FrozenGiveAmount
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["remark_ext"] = i.RemarkExt
}

func (i insRechargeCard) clone(db *gorm.DB) insRechargeCard {
	i.insRechargeCardDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insRechargeCard) replaceDB(db *gorm.DB) insRechargeCard {
	i.insRechargeCardDo.ReplaceDB(db)
	return i
}

type insRechargeCardDo struct{ gen.DO }

func (i insRechargeCardDo) Debug() *insRechargeCardDo {
	return i.withDO(i.DO.Debug())
}

func (i insRechargeCardDo) WithContext(ctx context.Context) *insRechargeCardDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insRechargeCardDo) ReadDB() *insRechargeCardDo {
	return i.Clauses(dbresolver.Read)
}

func (i insRechargeCardDo) WriteDB() *insRechargeCardDo {
	return i.Clauses(dbresolver.Write)
}

func (i insRechargeCardDo) Session(config *gorm.Session) *insRechargeCardDo {
	return i.withDO(i.DO.Session(config))
}

func (i insRechargeCardDo) Clauses(conds ...clause.Expression) *insRechargeCardDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insRechargeCardDo) Returning(value interface{}, columns ...string) *insRechargeCardDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insRechargeCardDo) Not(conds ...gen.Condition) *insRechargeCardDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insRechargeCardDo) Or(conds ...gen.Condition) *insRechargeCardDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insRechargeCardDo) Select(conds ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insRechargeCardDo) Where(conds ...gen.Condition) *insRechargeCardDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insRechargeCardDo) Order(conds ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insRechargeCardDo) Distinct(cols ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insRechargeCardDo) Omit(cols ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insRechargeCardDo) Join(table schema.Tabler, on ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insRechargeCardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insRechargeCardDo) RightJoin(table schema.Tabler, on ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insRechargeCardDo) Group(cols ...field.Expr) *insRechargeCardDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insRechargeCardDo) Having(conds ...gen.Condition) *insRechargeCardDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insRechargeCardDo) Limit(limit int) *insRechargeCardDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insRechargeCardDo) Offset(offset int) *insRechargeCardDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insRechargeCardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insRechargeCardDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insRechargeCardDo) Unscoped() *insRechargeCardDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insRechargeCardDo) Create(values ...*insbuy.InsRechargeCard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insRechargeCardDo) CreateInBatches(values []*insbuy.InsRechargeCard, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insRechargeCardDo) Save(values ...*insbuy.InsRechargeCard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insRechargeCardDo) First() (*insbuy.InsRechargeCard, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeCard), nil
	}
}

func (i insRechargeCardDo) Take() (*insbuy.InsRechargeCard, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeCard), nil
	}
}

func (i insRechargeCardDo) Last() (*insbuy.InsRechargeCard, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeCard), nil
	}
}

func (i insRechargeCardDo) Find() ([]*insbuy.InsRechargeCard, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsRechargeCard), err
}

func (i insRechargeCardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsRechargeCard, err error) {
	buf := make([]*insbuy.InsRechargeCard, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insRechargeCardDo) FindInBatches(result *[]*insbuy.InsRechargeCard, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insRechargeCardDo) Attrs(attrs ...field.AssignExpr) *insRechargeCardDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insRechargeCardDo) Assign(attrs ...field.AssignExpr) *insRechargeCardDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insRechargeCardDo) Joins(fields ...field.RelationField) *insRechargeCardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insRechargeCardDo) Preload(fields ...field.RelationField) *insRechargeCardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insRechargeCardDo) FirstOrInit() (*insbuy.InsRechargeCard, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeCard), nil
	}
}

func (i insRechargeCardDo) FirstOrCreate() (*insbuy.InsRechargeCard, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeCard), nil
	}
}

func (i insRechargeCardDo) FindByPage(offset int, limit int) (result []*insbuy.InsRechargeCard, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insRechargeCardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insRechargeCardDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insRechargeCardDo) Delete(models ...*insbuy.InsRechargeCard) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insRechargeCardDo) withDO(do gen.Dao) *insRechargeCardDo {
	i.DO = *do.(*gen.DO)
	return i
}
