# 门票报表子查询功能

## 概述

本模块为 `json.store.ticket` 数据类型实现了完整的子查询功能，参考了 `opendeskreport.go` 的实现模式，提供了对门票数据的结构化查询能力。

## 功能特性

- 支持多种渠道的门票数据查询（小程序、美团、抖音、现场POS、小红书、海外平台、携程）
- 提供完整的字段映射和JSON路径解析
- 支持时间范围和店铺筛选
- 兼容现有的报表架构和查询模式

## 数据结构

### 支持的字段

| 中文字段名 | 英文字段名 | 数据类型 | 描述 |
|-----------|-----------|---------|------|
| 日期 | day | string | 营业日期 |
| 星期 | week | string | 星期几 |
| 小程序 | mini_program | float64 | 小程序收入 |
| 小程序售票数 | mini_program_ticket_num | int | 小程序售票数量 |
| 美团 | meituan | float64 | 美团收入 |
| 美团售票数 | meituan_ticket_num | int | 美团售票数量 |
| 抖音 | douyin | float64 | 抖音收入 |
| 抖音售票数 | douyin_ticket_num | int | 抖音售票数量 |
| 现场（pos收款） | onsite_pos | float64 | 现场POS收入 |
| POS机售票数 | pos_ticket_num | int | POS机售票数量 |
| 小红书 | xiaohongshu | float64 | 小红书收入 |
| 小红书售票数 | xiaohongshu_ticket_num | int | 小红书售票数量 |
| 海外平台 | overseas_platform | float64 | 海外平台收入 |
| 海外平台售票数 | overseas_ticket_num | int | 海外平台售票数量 |
| 携程 | ctrip | float64 | 携程收入 |
| 携程售票数 | ctrip_ticket_num | int | 携程售票数量 |
| 通票每日收入合计 | daily_total_income | float64 | 每日总收入 |
| 通票每日售卖总数 | daily_total_ticket_num | float64 | 每日总售票数 |

## 使用方法

### 基本查询

```go
import (
    "context"
    "time"
    "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport/simple"
)

func QueryStoreTicketReport() {
    ctx := context.Background()
    q := query.Q
    
    // 设置查询参数
    params := simple.StoreTicketReportParams{
        StartDate: time.Date(2025, 6, 1, 0, 0, 0, 0, time.Local),
        EndDate:   time.Date(2025, 6, 30, 23, 59, 59, 0, time.Local),
        StoreIds:  []uint{1, 2, 3}, // 店铺ID列表
    }
    
    // 执行查询
    dao, fields := simple.StoreTicketReportList(ctx, q, params, "ticket_report")
    
    // 获取结果
    var results []YourResultStruct
    err := dao.GetDao().Scan(&results)
    if err != nil {
        // 处理错误
    }
}
```

### 带分页的查询

```go
func QueryWithPagination(page, pageSize int) {
    // ... 设置参数 ...
    dao, _ := simple.StoreTicketReportList(ctx, q, params, "ticket_report")
    
    // 获取总数
    var total int64
    dao.GetDao().Count(&total)
    
    // 分页查询
    offset := (page - 1) * pageSize
    var results []YourResultStruct
    dao.GetDao().Offset(offset).Limit(pageSize).Scan(&results)
}
```

### 带筛选条件的查询

```go
func QueryWithFilter(minIncome float64) {
    // ... 设置参数 ...
    dao, fields := simple.StoreTicketReportList(ctx, q, params, "ticket_report")
    
    // 添加筛选条件
    var results []YourResultStruct
    dao.GetDao().Where("daily_total_income > ?", minIncome).Scan(&results)
}
```

## JSON数据格式

### 输入数据示例

```json
{
    "field": {
        "抖音": "53634.50",
        "携程": "27856.00", 
        "日期": "2025.6.6",
        "星期": "周五",
        "美团": "74528.00",
        "小程序": "330823.41",
        "小红书": "2512.00",
        "海外平台": "573.73",
        "POS机售票数": "80",
        "抖音售票数": "199",
        "携程售票数": "97",
        "美团售票数": "271",
        "小程序售票数": "1252.00",
        "小红书售票数": "9",
        "海外平台售票数": "2",
        "现场（pos收款）": "23288.00",
        "通票每日售卖总数": "1910.00",
        "通票每日收入合计": "1.00"
    }
}
```

### 字段配置示例

```json
[
    {"color": "", "field": "日期", "title": "日期"},
    {"color": "", "field": "周几", "title": "周几"},
    {"color": "", "field": "小程序", "title": "小程序"},
    {"color": "", "field": "小程序售票数", "title": "小程序售票数"},
    {"color": "", "field": "美团", "title": "美团"},
    {"color": "", "field": "美团售票数", "title": "美团售票数"}
    // ... 更多字段配置
]
```

## 实现细节

### 架构设计

1. **字段定义**: `StoreTicketReportFields` 结构体定义了所有支持的字段
2. **参数结构**: `StoreTicketReportParams` 定义查询参数
3. **查询函数**: `StoreTicketReportList` 实现主要的查询逻辑
4. **JSON路径**: 使用MySQL的JSON函数提取字段数据

### JSON路径映射

- 使用 `?->>'$.field.字段名'` 提取字符串类型字段
- 使用 `?->'$.field.字段名'` 提取数值类型字段
- 特殊字符字段使用双引号转义：`"现场（pos收款）"`

### 数据源集成

- 数据源代码：`datasource.StoreTicketData` (`json.store.ticket`)
- 存储表：`ins_report_intermediate_result` 和 `ins_report_intermediate_result_details`
- 状态筛选：只查询 `status = 1` 的有效数据

## 测试

运行测试：

```bash
go test ./server/service/insbuy/insreport/simple -v -run TestStoreTicket
```

## 注意事项

1. 确保数据库中存在对应的 `json.store.ticket` 类型数据
2. JSON字段名必须与实际数据中的字段名完全匹配
3. 特殊字符字段需要正确转义
4. 数值类型字段会自动转换，但需要确保数据格式正确

## 扩展

如需添加新字段：

1. 在 `StoreTicketReportFields` 中添加字段定义
2. 在 `UpdateTableName` 方法中初始化字段
3. 在 `StoreTicketReportList` 中添加对应的JSON路径查询
4. 更新测试和文档
