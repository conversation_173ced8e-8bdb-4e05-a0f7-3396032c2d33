// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageItem(db *gorm.DB, opts ...gen.DOOption) insProductPackageItem {
	_insProductPackageItem := insProductPackageItem{}

	_insProductPackageItem.insProductPackageItemDo.UseDB(db, opts...)
	_insProductPackageItem.insProductPackageItemDo.UseModel(&insbuy.InsProductPackageItem{})

	tableName := _insProductPackageItem.insProductPackageItemDo.TableName()
	_insProductPackageItem.ALL = field.NewAsterisk(tableName)
	_insProductPackageItem.Id = field.NewInt(tableName, "id")
	_insProductPackageItem.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageItem.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageItem.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPackageItem.IsFull = field.NewInt(tableName, "is_full")
	_insProductPackageItem.OptionName = field.NewString(tableName, "option_name")
	_insProductPackageItem.ItemName = field.NewString(tableName, "item_name")
	_insProductPackageItem.SortOrder = field.NewInt(tableName, "sort_order")
	_insProductPackageItem.PackageId = field.NewInt(tableName, "package_id")
	_insProductPackageItem.OptionNum = field.NewInt(tableName, "option_num")

	_insProductPackageItem.fillFieldMap()

	return _insProductPackageItem
}

type insProductPackageItem struct {
	insProductPackageItemDo

	ALL        field.Asterisk
	Id         field.Int
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	IsFull     field.Int
	OptionName field.String
	ItemName   field.String
	SortOrder  field.Int
	PackageId  field.Int
	OptionNum  field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPackageItem) Table(newTableName string) *insProductPackageItem {
	i.insProductPackageItemDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageItem) As(alias string) *insProductPackageItem {
	i.insProductPackageItemDo.DO = *(i.insProductPackageItemDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageItem) updateTableName(table string) *insProductPackageItem {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.IsFull = field.NewInt(table, "is_full")
	i.OptionName = field.NewString(table, "option_name")
	i.ItemName = field.NewString(table, "item_name")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.PackageId = field.NewInt(table, "package_id")
	i.OptionNum = field.NewInt(table, "option_num")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageItem) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.Id
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["is_full"] = i.IsFull
	i.fieldMap["option_name"] = i.OptionName
	i.fieldMap["item_name"] = i.ItemName
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["option_num"] = i.OptionNum
}

func (i insProductPackageItem) clone(db *gorm.DB) insProductPackageItem {
	i.insProductPackageItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageItem) replaceDB(db *gorm.DB) insProductPackageItem {
	i.insProductPackageItemDo.ReplaceDB(db)
	return i
}

type insProductPackageItemDo struct{ gen.DO }

func (i insProductPackageItemDo) Debug() *insProductPackageItemDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageItemDo) WithContext(ctx context.Context) *insProductPackageItemDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageItemDo) ReadDB() *insProductPackageItemDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageItemDo) WriteDB() *insProductPackageItemDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageItemDo) Session(config *gorm.Session) *insProductPackageItemDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageItemDo) Clauses(conds ...clause.Expression) *insProductPackageItemDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageItemDo) Returning(value interface{}, columns ...string) *insProductPackageItemDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageItemDo) Not(conds ...gen.Condition) *insProductPackageItemDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageItemDo) Or(conds ...gen.Condition) *insProductPackageItemDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageItemDo) Select(conds ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageItemDo) Where(conds ...gen.Condition) *insProductPackageItemDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageItemDo) Order(conds ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageItemDo) Distinct(cols ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageItemDo) Omit(cols ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageItemDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageItemDo) Group(cols ...field.Expr) *insProductPackageItemDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageItemDo) Having(conds ...gen.Condition) *insProductPackageItemDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageItemDo) Limit(limit int) *insProductPackageItemDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageItemDo) Offset(offset int) *insProductPackageItemDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageItemDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageItemDo) Unscoped() *insProductPackageItemDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageItemDo) Create(values ...*insbuy.InsProductPackageItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageItemDo) CreateInBatches(values []*insbuy.InsProductPackageItem, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageItemDo) Save(values ...*insbuy.InsProductPackageItem) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageItemDo) First() (*insbuy.InsProductPackageItem, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItem), nil
	}
}

func (i insProductPackageItemDo) Take() (*insbuy.InsProductPackageItem, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItem), nil
	}
}

func (i insProductPackageItemDo) Last() (*insbuy.InsProductPackageItem, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItem), nil
	}
}

func (i insProductPackageItemDo) Find() ([]*insbuy.InsProductPackageItem, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageItem), err
}

func (i insProductPackageItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageItem, err error) {
	buf := make([]*insbuy.InsProductPackageItem, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageItemDo) FindInBatches(result *[]*insbuy.InsProductPackageItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageItemDo) Attrs(attrs ...field.AssignExpr) *insProductPackageItemDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageItemDo) Assign(attrs ...field.AssignExpr) *insProductPackageItemDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageItemDo) Joins(fields ...field.RelationField) *insProductPackageItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageItemDo) Preload(fields ...field.RelationField) *insProductPackageItemDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageItemDo) FirstOrInit() (*insbuy.InsProductPackageItem, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItem), nil
	}
}

func (i insProductPackageItemDo) FirstOrCreate() (*insbuy.InsProductPackageItem, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItem), nil
	}
}

func (i insProductPackageItemDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageItem, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageItemDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageItemDo) Delete(models ...*insbuy.InsProductPackageItem) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageItemDo) withDO(do gen.Dao) *insProductPackageItemDo {
	i.DO = *do.(*gen.DO)
	return i
}
