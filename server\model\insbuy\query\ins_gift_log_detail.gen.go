// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftLogDetail(db *gorm.DB, opts ...gen.DOOption) insGiftLogDetail {
	_insGiftLogDetail := insGiftLogDetail{}

	_insGiftLogDetail.insGiftLogDetailDo.UseDB(db, opts...)
	_insGiftLogDetail.insGiftLogDetailDo.UseModel(&insbuy.InsGiftLogDetail{})

	tableName := _insGiftLogDetail.insGiftLogDetailDo.TableName()
	_insGiftLogDetail.ALL = field.NewAsterisk(tableName)
	_insGiftLogDetail.ID = field.NewUint(tableName, "id")
	_insGiftLogDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftLogDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftLogDetail.LogId = field.NewUint(tableName, "log_id")
	_insGiftLogDetail.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insGiftLogDetail.ProductId = field.NewUint(tableName, "product_id")
	_insGiftLogDetail.ProductName = field.NewString(tableName, "product_name")
	_insGiftLogDetail.OriginalPrice = field.NewFloat64(tableName, "original_price")
	_insGiftLogDetail.Unit = field.NewInt(tableName, "unit")
	_insGiftLogDetail.PriceType = field.NewInt(tableName, "price_type")
	_insGiftLogDetail.GiftAmount = field.NewFloat64(tableName, "gift_amount")
	_insGiftLogDetail.Nums = field.NewInt(tableName, "nums")
	_insGiftLogDetail.TotalPrice = field.NewFloat64(tableName, "total_price")
	_insGiftLogDetail.BackNums = field.NewInt(tableName, "back_nums")
	_insGiftLogDetail.BackAmount = field.NewFloat64(tableName, "back_amount")
	_insGiftLogDetail.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insGiftLogDetail.BusinessDay = field.NewTime(tableName, "business_day")

	_insGiftLogDetail.fillFieldMap()

	return _insGiftLogDetail
}

type insGiftLogDetail struct {
	insGiftLogDetailDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	LogId          field.Uint
	OrderDetailsId field.Uint64
	ProductId      field.Uint
	ProductName    field.String
	OriginalPrice  field.Float64
	Unit           field.Int
	PriceType      field.Int
	GiftAmount     field.Float64
	Nums           field.Int
	TotalPrice     field.Float64
	BackNums       field.Int
	BackAmount     field.Float64
	WarehouseId    field.Int
	BusinessDay    field.Time

	fieldMap map[string]field.Expr
}

func (i insGiftLogDetail) Table(newTableName string) *insGiftLogDetail {
	i.insGiftLogDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftLogDetail) As(alias string) *insGiftLogDetail {
	i.insGiftLogDetailDo.DO = *(i.insGiftLogDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftLogDetail) updateTableName(table string) *insGiftLogDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.LogId = field.NewUint(table, "log_id")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.ProductName = field.NewString(table, "product_name")
	i.OriginalPrice = field.NewFloat64(table, "original_price")
	i.Unit = field.NewInt(table, "unit")
	i.PriceType = field.NewInt(table, "price_type")
	i.GiftAmount = field.NewFloat64(table, "gift_amount")
	i.Nums = field.NewInt(table, "nums")
	i.TotalPrice = field.NewFloat64(table, "total_price")
	i.BackNums = field.NewInt(table, "back_nums")
	i.BackAmount = field.NewFloat64(table, "back_amount")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insGiftLogDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftLogDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["log_id"] = i.LogId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["original_price"] = i.OriginalPrice
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["price_type"] = i.PriceType
	i.fieldMap["gift_amount"] = i.GiftAmount
	i.fieldMap["nums"] = i.Nums
	i.fieldMap["total_price"] = i.TotalPrice
	i.fieldMap["back_nums"] = i.BackNums
	i.fieldMap["back_amount"] = i.BackAmount
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insGiftLogDetail) clone(db *gorm.DB) insGiftLogDetail {
	i.insGiftLogDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftLogDetail) replaceDB(db *gorm.DB) insGiftLogDetail {
	i.insGiftLogDetailDo.ReplaceDB(db)
	return i
}

type insGiftLogDetailDo struct{ gen.DO }

func (i insGiftLogDetailDo) Debug() *insGiftLogDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftLogDetailDo) WithContext(ctx context.Context) *insGiftLogDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftLogDetailDo) ReadDB() *insGiftLogDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftLogDetailDo) WriteDB() *insGiftLogDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftLogDetailDo) Session(config *gorm.Session) *insGiftLogDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftLogDetailDo) Clauses(conds ...clause.Expression) *insGiftLogDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftLogDetailDo) Returning(value interface{}, columns ...string) *insGiftLogDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftLogDetailDo) Not(conds ...gen.Condition) *insGiftLogDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftLogDetailDo) Or(conds ...gen.Condition) *insGiftLogDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftLogDetailDo) Select(conds ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftLogDetailDo) Where(conds ...gen.Condition) *insGiftLogDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftLogDetailDo) Order(conds ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftLogDetailDo) Distinct(cols ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftLogDetailDo) Omit(cols ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftLogDetailDo) Join(table schema.Tabler, on ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftLogDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftLogDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftLogDetailDo) Group(cols ...field.Expr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftLogDetailDo) Having(conds ...gen.Condition) *insGiftLogDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftLogDetailDo) Limit(limit int) *insGiftLogDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftLogDetailDo) Offset(offset int) *insGiftLogDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftLogDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftLogDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftLogDetailDo) Unscoped() *insGiftLogDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftLogDetailDo) Create(values ...*insbuy.InsGiftLogDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftLogDetailDo) CreateInBatches(values []*insbuy.InsGiftLogDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftLogDetailDo) Save(values ...*insbuy.InsGiftLogDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftLogDetailDo) First() (*insbuy.InsGiftLogDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLogDetail), nil
	}
}

func (i insGiftLogDetailDo) Take() (*insbuy.InsGiftLogDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLogDetail), nil
	}
}

func (i insGiftLogDetailDo) Last() (*insbuy.InsGiftLogDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLogDetail), nil
	}
}

func (i insGiftLogDetailDo) Find() ([]*insbuy.InsGiftLogDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftLogDetail), err
}

func (i insGiftLogDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftLogDetail, err error) {
	buf := make([]*insbuy.InsGiftLogDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftLogDetailDo) FindInBatches(result *[]*insbuy.InsGiftLogDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftLogDetailDo) Attrs(attrs ...field.AssignExpr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftLogDetailDo) Assign(attrs ...field.AssignExpr) *insGiftLogDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftLogDetailDo) Joins(fields ...field.RelationField) *insGiftLogDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftLogDetailDo) Preload(fields ...field.RelationField) *insGiftLogDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftLogDetailDo) FirstOrInit() (*insbuy.InsGiftLogDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLogDetail), nil
	}
}

func (i insGiftLogDetailDo) FirstOrCreate() (*insbuy.InsGiftLogDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftLogDetail), nil
	}
}

func (i insGiftLogDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftLogDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftLogDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftLogDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftLogDetailDo) Delete(models ...*insbuy.InsGiftLogDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftLogDetailDo) withDO(do gen.Dao) *insGiftLogDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
