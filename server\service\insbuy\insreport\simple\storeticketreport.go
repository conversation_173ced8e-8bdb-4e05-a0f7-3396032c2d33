package simple

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdata/datasource"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"time"
)

type StoreTicketReportParams struct {
	StartDate time.Time
	EndDate   time.Time
	StoreIds  []uint
}

type StoreTicketReportFields struct {
	jgorm.SimpleFields
	Day                  field.String //日期
	Week                 field.String //星期
	MiniProgram          field.String //小程序
	MiniProgramTicketNum field.Int    //小程序售票数
	Meituan              field.String //美团
	MeituanTicketNum     field.Int    //美团售票数
	Douyin               field.String //抖音
	DouyinTicketNum      field.Int    //抖音售票数
	OnSitePOS            field.String //现场（pos收款）
	POSTicketNum         field.Int    //POS机售票数
	Xiaohongshu          field.String //小红书
	XiaohongshuTicketNum field.Int    //小红书售票数
	OverseasPlatform     field.String //海外平台
	OverseasTicketNum    field.Int    //海外平台售票数
	Ctrip                field.String //携程
	CtripTicketNum       field.Int    //携程售票数
	DailyTotalIncome     field.String //通票每日收入合计
	DailyTotalTicketNum  field.String //通票每日售卖总数

	StoreId     field.Uint
	BusinessDay field.Time
	Header      field.Field
	Data        field.Field
}

func (i *StoreTicketReportFields) UpdateTableName(table string) {
	i.SimpleFields.UpdateTableName(table)
	i.Day = field.NewString(table, "day")
	i.Week = field.NewString(table, "week")
	i.MiniProgram = field.NewString(table, "mini_program")
	i.MiniProgramTicketNum = field.NewInt(table, "mini_program_ticket_num")
	i.Meituan = field.NewString(table, "meituan")
	i.MeituanTicketNum = field.NewInt(table, "meituan_ticket_num")
	i.Douyin = field.NewString(table, "douyin")
	i.DouyinTicketNum = field.NewInt(table, "douyin_ticket_num")
	i.OnSitePOS = field.NewString(table, "onsite_pos")
	i.POSTicketNum = field.NewInt(table, "pos_ticket_num")
	i.Xiaohongshu = field.NewString(table, "xiaohongshu")
	i.XiaohongshuTicketNum = field.NewInt(table, "xiaohongshu_ticket_num")
	i.OverseasPlatform = field.NewString(table, "overseas_platform")
	i.OverseasTicketNum = field.NewInt(table, "overseas_ticket_num")
	i.Ctrip = field.NewString(table, "ctrip")
	i.CtripTicketNum = field.NewInt(table, "ctrip_ticket_num")
	i.DailyTotalIncome = field.NewString(table, "daily_total_income")
	i.DailyTotalTicketNum = field.NewString(table, "daily_total_ticket_num")

	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.Header = field.NewField(table, "header")
	i.Data = field.NewField(table, "data")
}

// StoreTicketReportList 获取门票报表列表
func StoreTicketReportList(ctx context.Context, q *query.Query, p StoreTicketReportParams, alias string) (d jgorm.DaoWrapper, f *StoreTicketReportFields) {
	f = &StoreTicketReportFields{}
	f.UpdateTableName(alias)
	dbResult := q.InsReportIntermediateResult
	dbDetails := q.InsReportIntermediateResultDetails
	tdbResult := dbResult.As("s")
	var condition []gen.Condition
	{
		condition = append(condition, tdbResult.StoreID.Eq(0))
		if !p.StartDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Gte(p.StartDate))
		}
		if !p.EndDate.IsZero() {
			condition = append(condition, dbDetails.BusinessDay.Lte(p.EndDate))
		}
	}

	dao1 := tdbResult.
		LeftJoin(dbDetails, tdbResult.ID.EqCol(dbDetails.ResultId)).
		Unscoped().As("s").Where(condition...).Select(
		tdbResult.StoreID.As(CN(f.StoreId)),
		dbDetails.BusinessDay.As(CN(f.BusinessDay)),
	).Where(tdbResult.Status.Eq(1), tdbResult.Code.Eq(datasource.StoreTicketData.ToString()))
	jgorm.SelectAppend(dao1.(*gen.DO),
		jgorm.ClauseExpr("?->>'$.field.日期' "+CN(f.Day), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.星期' "+CN(f.Week), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.小程序' "+CN(f.MiniProgram), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.小程序售票数' "+CN(f.MiniProgramTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.美团' "+CN(f.Meituan), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.美团售票数' "+CN(f.MeituanTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.抖音' "+CN(f.Douyin), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.抖音售票数' "+CN(f.DouyinTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.现场（pos收款）' "+CN(f.OnSitePOS), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.POS机售票数' "+CN(f.POSTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.小红书' "+CN(f.Xiaohongshu), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.小红书售票数' "+CN(f.XiaohongshuTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.海外平台' "+CN(f.OverseasPlatform), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.海外平台售票数' "+CN(f.OverseasTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.携程' "+CN(f.Ctrip), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.携程售票数' "+CN(f.CtripTicketNum), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.通票每日收入合计' "+CN(f.DailyTotalIncome), dbDetails.ResultData),
		jgorm.ClauseExpr("?->>'$.field.通票每日售卖总数' "+CN(f.DailyTotalTicketNum), dbDetails.ResultData),
		dbDetails.ResultData.As(CN(f.Data)),
		tdbResult.Ext.As(CN(f.Header)),
	)

	return jgorm.NewDaoWrapperByJoin(dao1, alias), f
}
