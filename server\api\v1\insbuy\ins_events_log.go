package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsEventsLogApi struct {
}

// GetEventsLogList 分页获取InsDesk列表
// @Tags InsEventsLog
// @Summary 分页获取InsDesk列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetEventsLogListReq true "分页获取InsDesk列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insEventsLog/getEventsLogList [get]
func (e *InsEventsLogApi) GetEventsLogList(c *gin.Context) {
	var req insbuyReq.GetEventsLogListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := insEventsLogService.GetEventsLogList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetEventsLogModuleList 获取事件模块列表
// @Tags InsEventsLog
// @Summary 获取事件模块列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insEventsLog/getEventsLogModuleList [get]
func (e *InsEventsLogApi) GetEventsLogModuleList(c *gin.Context) {
	if err, data := insEventsLogService.GetEventsLogModuleList(); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(data, "获取成功", c)
	}
}
