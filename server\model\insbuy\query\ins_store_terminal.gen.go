// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsStoreTerminal(db *gorm.DB, opts ...gen.DOOption) insStoreTerminal {
	_insStoreTerminal := insStoreTerminal{}

	_insStoreTerminal.insStoreTerminalDo.UseDB(db, opts...)
	_insStoreTerminal.insStoreTerminalDo.UseModel(&insbuy.InsStoreTerminal{})

	tableName := _insStoreTerminal.insStoreTerminalDo.TableName()
	_insStoreTerminal.ALL = field.NewAsterisk(tableName)
	_insStoreTerminal.ID = field.NewUint(tableName, "id")
	_insStoreTerminal.CreatedAt = field.NewTime(tableName, "created_at")
	_insStoreTerminal.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insStoreTerminal.DeletedAt = field.NewField(tableName, "deleted_at")
	_insStoreTerminal.TermNo = field.NewString(tableName, "term_no")
	_insStoreTerminal.StoreId = field.NewUint(tableName, "store_id")
	_insStoreTerminal.DeviceType = field.NewString(tableName, "device_type")
	_insStoreTerminal.TermSN = field.NewString(tableName, "term_sn")
	_insStoreTerminal.TermAddress = field.NewString(tableName, "term_address")
	_insStoreTerminal.TermState = field.NewString(tableName, "term_state")

	_insStoreTerminal.fillFieldMap()

	return _insStoreTerminal
}

type insStoreTerminal struct {
	insStoreTerminalDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	TermNo      field.String
	StoreId     field.Uint
	DeviceType  field.String
	TermSN      field.String
	TermAddress field.String
	TermState   field.String

	fieldMap map[string]field.Expr
}

func (i insStoreTerminal) Table(newTableName string) *insStoreTerminal {
	i.insStoreTerminalDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insStoreTerminal) As(alias string) *insStoreTerminal {
	i.insStoreTerminalDo.DO = *(i.insStoreTerminalDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insStoreTerminal) updateTableName(table string) *insStoreTerminal {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.TermNo = field.NewString(table, "term_no")
	i.StoreId = field.NewUint(table, "store_id")
	i.DeviceType = field.NewString(table, "device_type")
	i.TermSN = field.NewString(table, "term_sn")
	i.TermAddress = field.NewString(table, "term_address")
	i.TermState = field.NewString(table, "term_state")

	i.fillFieldMap()

	return i
}

func (i *insStoreTerminal) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insStoreTerminal) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["term_no"] = i.TermNo
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["device_type"] = i.DeviceType
	i.fieldMap["term_sn"] = i.TermSN
	i.fieldMap["term_address"] = i.TermAddress
	i.fieldMap["term_state"] = i.TermState
}

func (i insStoreTerminal) clone(db *gorm.DB) insStoreTerminal {
	i.insStoreTerminalDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insStoreTerminal) replaceDB(db *gorm.DB) insStoreTerminal {
	i.insStoreTerminalDo.ReplaceDB(db)
	return i
}

type insStoreTerminalDo struct{ gen.DO }

func (i insStoreTerminalDo) Debug() *insStoreTerminalDo {
	return i.withDO(i.DO.Debug())
}

func (i insStoreTerminalDo) WithContext(ctx context.Context) *insStoreTerminalDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insStoreTerminalDo) ReadDB() *insStoreTerminalDo {
	return i.Clauses(dbresolver.Read)
}

func (i insStoreTerminalDo) WriteDB() *insStoreTerminalDo {
	return i.Clauses(dbresolver.Write)
}

func (i insStoreTerminalDo) Session(config *gorm.Session) *insStoreTerminalDo {
	return i.withDO(i.DO.Session(config))
}

func (i insStoreTerminalDo) Clauses(conds ...clause.Expression) *insStoreTerminalDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insStoreTerminalDo) Returning(value interface{}, columns ...string) *insStoreTerminalDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insStoreTerminalDo) Not(conds ...gen.Condition) *insStoreTerminalDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insStoreTerminalDo) Or(conds ...gen.Condition) *insStoreTerminalDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insStoreTerminalDo) Select(conds ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insStoreTerminalDo) Where(conds ...gen.Condition) *insStoreTerminalDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insStoreTerminalDo) Order(conds ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insStoreTerminalDo) Distinct(cols ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insStoreTerminalDo) Omit(cols ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insStoreTerminalDo) Join(table schema.Tabler, on ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insStoreTerminalDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insStoreTerminalDo) RightJoin(table schema.Tabler, on ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insStoreTerminalDo) Group(cols ...field.Expr) *insStoreTerminalDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insStoreTerminalDo) Having(conds ...gen.Condition) *insStoreTerminalDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insStoreTerminalDo) Limit(limit int) *insStoreTerminalDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insStoreTerminalDo) Offset(offset int) *insStoreTerminalDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insStoreTerminalDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insStoreTerminalDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insStoreTerminalDo) Unscoped() *insStoreTerminalDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insStoreTerminalDo) Create(values ...*insbuy.InsStoreTerminal) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insStoreTerminalDo) CreateInBatches(values []*insbuy.InsStoreTerminal, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insStoreTerminalDo) Save(values ...*insbuy.InsStoreTerminal) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insStoreTerminalDo) First() (*insbuy.InsStoreTerminal, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTerminal), nil
	}
}

func (i insStoreTerminalDo) Take() (*insbuy.InsStoreTerminal, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTerminal), nil
	}
}

func (i insStoreTerminalDo) Last() (*insbuy.InsStoreTerminal, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTerminal), nil
	}
}

func (i insStoreTerminalDo) Find() ([]*insbuy.InsStoreTerminal, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsStoreTerminal), err
}

func (i insStoreTerminalDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsStoreTerminal, err error) {
	buf := make([]*insbuy.InsStoreTerminal, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insStoreTerminalDo) FindInBatches(result *[]*insbuy.InsStoreTerminal, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insStoreTerminalDo) Attrs(attrs ...field.AssignExpr) *insStoreTerminalDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insStoreTerminalDo) Assign(attrs ...field.AssignExpr) *insStoreTerminalDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insStoreTerminalDo) Joins(fields ...field.RelationField) *insStoreTerminalDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insStoreTerminalDo) Preload(fields ...field.RelationField) *insStoreTerminalDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insStoreTerminalDo) FirstOrInit() (*insbuy.InsStoreTerminal, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTerminal), nil
	}
}

func (i insStoreTerminalDo) FirstOrCreate() (*insbuy.InsStoreTerminal, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsStoreTerminal), nil
	}
}

func (i insStoreTerminalDo) FindByPage(offset int, limit int) (result []*insbuy.InsStoreTerminal, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insStoreTerminalDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insStoreTerminalDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insStoreTerminalDo) Delete(models ...*insbuy.InsStoreTerminal) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insStoreTerminalDo) withDO(do gen.Dao) *insStoreTerminalDo {
	i.DO = *do.(*gen.DO)
	return i
}
