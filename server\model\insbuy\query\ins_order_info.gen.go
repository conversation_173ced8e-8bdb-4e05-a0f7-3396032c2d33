// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderInfo(db *gorm.DB, opts ...gen.DOOption) insOrderInfo {
	_insOrderInfo := insOrderInfo{}

	_insOrderInfo.insOrderInfoDo.UseDB(db, opts...)
	_insOrderInfo.insOrderInfoDo.UseModel(&insbuy.InsOrderInfo{})

	tableName := _insOrderInfo.insOrderInfoDo.TableName()
	_insOrderInfo.ALL = field.NewAsterisk(tableName)
	_insOrderInfo.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderInfo.OrderSn = field.NewString(tableName, "order_sn")
	_insOrderInfo.StoreId = field.NewUint(tableName, "store_id")
	_insOrderInfo.DeskId = field.NewInt(tableName, "desk_id")
	_insOrderInfo.OpenDeskId = field.NewInt(tableName, "open_desk_id")
	_insOrderInfo.OrderTime = field.NewTime(tableName, "order_time")
	_insOrderInfo.OrderType = field.NewInt(tableName, "order_type")
	_insOrderInfo.GoodsAmount = field.NewFloat64(tableName, "goods_amount")
	_insOrderInfo.PayFee = field.NewFloat64(tableName, "pay_fee")
	_insOrderInfo.CardFee = field.NewFloat64(tableName, "card_fee")
	_insOrderInfo.GoodsDiscountFee = field.NewFloat64(tableName, "goods_discount_fee")
	_insOrderInfo.Surplus = field.NewFloat64(tableName, "surplus")
	_insOrderInfo.OrderAmount = field.NewFloat64(tableName, "order_amount")
	_insOrderInfo.OriginalAmount = field.NewFloat64(tableName, "original_amount")
	_insOrderInfo.GiveAmount = field.NewFloat64(tableName, "give_amount")
	_insOrderInfo.Discount = field.NewFloat64(tableName, "discount")
	_insOrderInfo.TotalNum = field.NewInt(tableName, "total_num")
	_insOrderInfo.OriginalTotalNum = field.NewInt(tableName, "original_total_num")
	_insOrderInfo.RemarkExt = field.NewField(tableName, "remark_ext")
	_insOrderInfo.BusinessDay = field.NewTime(tableName, "business_day")
	_insOrderInfo.Ext = field.NewField(tableName, "ext")
	_insOrderInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderInfo.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderInfo.DeletedAt = field.NewField(tableName, "deleted_at")

	_insOrderInfo.fillFieldMap()

	return _insOrderInfo
}

type insOrderInfo struct {
	insOrderInfoDo

	ALL              field.Asterisk
	OrderId          field.Uint64
	OrderSn          field.String
	StoreId          field.Uint
	DeskId           field.Int
	OpenDeskId       field.Int
	OrderTime        field.Time
	OrderType        field.Int
	GoodsAmount      field.Float64
	PayFee           field.Float64
	CardFee          field.Float64
	GoodsDiscountFee field.Float64
	Surplus          field.Float64
	OrderAmount      field.Float64
	OriginalAmount   field.Float64
	GiveAmount       field.Float64
	Discount         field.Float64
	TotalNum         field.Int
	OriginalTotalNum field.Int
	RemarkExt        field.Field
	BusinessDay      field.Time
	Ext              field.Field
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field

	fieldMap map[string]field.Expr
}

func (i insOrderInfo) Table(newTableName string) *insOrderInfo {
	i.insOrderInfoDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderInfo) As(alias string) *insOrderInfo {
	i.insOrderInfoDo.DO = *(i.insOrderInfoDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderInfo) updateTableName(table string) *insOrderInfo {
	i.ALL = field.NewAsterisk(table)
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderSn = field.NewString(table, "order_sn")
	i.StoreId = field.NewUint(table, "store_id")
	i.DeskId = field.NewInt(table, "desk_id")
	i.OpenDeskId = field.NewInt(table, "open_desk_id")
	i.OrderTime = field.NewTime(table, "order_time")
	i.OrderType = field.NewInt(table, "order_type")
	i.GoodsAmount = field.NewFloat64(table, "goods_amount")
	i.PayFee = field.NewFloat64(table, "pay_fee")
	i.CardFee = field.NewFloat64(table, "card_fee")
	i.GoodsDiscountFee = field.NewFloat64(table, "goods_discount_fee")
	i.Surplus = field.NewFloat64(table, "surplus")
	i.OrderAmount = field.NewFloat64(table, "order_amount")
	i.OriginalAmount = field.NewFloat64(table, "original_amount")
	i.GiveAmount = field.NewFloat64(table, "give_amount")
	i.Discount = field.NewFloat64(table, "discount")
	i.TotalNum = field.NewInt(table, "total_num")
	i.OriginalTotalNum = field.NewInt(table, "original_total_num")
	i.RemarkExt = field.NewField(table, "remark_ext")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")

	i.fillFieldMap()

	return i
}

func (i *insOrderInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderInfo) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 24)
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_sn"] = i.OrderSn
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["order_time"] = i.OrderTime
	i.fieldMap["order_type"] = i.OrderType
	i.fieldMap["goods_amount"] = i.GoodsAmount
	i.fieldMap["pay_fee"] = i.PayFee
	i.fieldMap["card_fee"] = i.CardFee
	i.fieldMap["goods_discount_fee"] = i.GoodsDiscountFee
	i.fieldMap["surplus"] = i.Surplus
	i.fieldMap["order_amount"] = i.OrderAmount
	i.fieldMap["original_amount"] = i.OriginalAmount
	i.fieldMap["give_amount"] = i.GiveAmount
	i.fieldMap["discount"] = i.Discount
	i.fieldMap["total_num"] = i.TotalNum
	i.fieldMap["original_total_num"] = i.OriginalTotalNum
	i.fieldMap["remark_ext"] = i.RemarkExt
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
}

func (i insOrderInfo) clone(db *gorm.DB) insOrderInfo {
	i.insOrderInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderInfo) replaceDB(db *gorm.DB) insOrderInfo {
	i.insOrderInfoDo.ReplaceDB(db)
	return i
}

type insOrderInfoDo struct{ gen.DO }

func (i insOrderInfoDo) Debug() *insOrderInfoDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderInfoDo) WithContext(ctx context.Context) *insOrderInfoDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderInfoDo) ReadDB() *insOrderInfoDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderInfoDo) WriteDB() *insOrderInfoDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderInfoDo) Session(config *gorm.Session) *insOrderInfoDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderInfoDo) Clauses(conds ...clause.Expression) *insOrderInfoDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderInfoDo) Returning(value interface{}, columns ...string) *insOrderInfoDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderInfoDo) Not(conds ...gen.Condition) *insOrderInfoDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderInfoDo) Or(conds ...gen.Condition) *insOrderInfoDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderInfoDo) Select(conds ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderInfoDo) Where(conds ...gen.Condition) *insOrderInfoDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderInfoDo) Order(conds ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderInfoDo) Distinct(cols ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderInfoDo) Omit(cols ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderInfoDo) Join(table schema.Tabler, on ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderInfoDo) Group(cols ...field.Expr) *insOrderInfoDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderInfoDo) Having(conds ...gen.Condition) *insOrderInfoDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderInfoDo) Limit(limit int) *insOrderInfoDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderInfoDo) Offset(offset int) *insOrderInfoDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderInfoDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderInfoDo) Unscoped() *insOrderInfoDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderInfoDo) Create(values ...*insbuy.InsOrderInfo) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderInfoDo) CreateInBatches(values []*insbuy.InsOrderInfo, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderInfoDo) Save(values ...*insbuy.InsOrderInfo) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderInfoDo) First() (*insbuy.InsOrderInfo, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfo), nil
	}
}

func (i insOrderInfoDo) Take() (*insbuy.InsOrderInfo, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfo), nil
	}
}

func (i insOrderInfoDo) Last() (*insbuy.InsOrderInfo, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfo), nil
	}
}

func (i insOrderInfoDo) Find() ([]*insbuy.InsOrderInfo, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderInfo), err
}

func (i insOrderInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderInfo, err error) {
	buf := make([]*insbuy.InsOrderInfo, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderInfoDo) FindInBatches(result *[]*insbuy.InsOrderInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderInfoDo) Attrs(attrs ...field.AssignExpr) *insOrderInfoDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderInfoDo) Assign(attrs ...field.AssignExpr) *insOrderInfoDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderInfoDo) Joins(fields ...field.RelationField) *insOrderInfoDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderInfoDo) Preload(fields ...field.RelationField) *insOrderInfoDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderInfoDo) FirstOrInit() (*insbuy.InsOrderInfo, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfo), nil
	}
}

func (i insOrderInfoDo) FirstOrCreate() (*insbuy.InsOrderInfo, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderInfo), nil
	}
}

func (i insOrderInfoDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderInfo, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderInfoDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderInfoDo) Delete(models ...*insbuy.InsOrderInfo) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderInfoDo) withDO(do gen.Dao) *insOrderInfoDo {
	i.DO = *do.(*gen.DO)
	return i
}
