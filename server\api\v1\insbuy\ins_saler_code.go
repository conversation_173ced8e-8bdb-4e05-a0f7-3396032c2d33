package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSalerCodeApi struct {
}

var insSalerCodeService = service.ServiceGroupApp.InsBuyServiceGroup.InsSalerCodeService

// CreateInsSalerCode 创建InsSalerCode
// @Tags InsSalerCode
// @Summary 创建InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerCode true "创建InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSalerCode/createInsSalerCode [post]
func (insSalerCodeApi *InsSalerCodeApi) CreateInsSalerCode(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindJSON(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerCodeService.CreateInsSalerCode(&insSalerCode); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSalerCode 删除InsSalerCode
// @Tags InsSalerCode
// @Summary 删除InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerCode true "删除InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSalerCode/deleteInsSalerCode [delete]
func (insSalerCodeApi *InsSalerCodeApi) DeleteInsSalerCode(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindJSON(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerCodeService.DeleteInsSalerCode(insSalerCode); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSalerCodeByUserId 根据用户删除InsSalerCode
// @Tags InsSalerCode
// @Summary 删除InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerCode true "删除InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSalerCode/deleteInsSalerCodeByUserId [delete]
func (insSalerCodeApi *InsSalerCodeApi) DeleteInsSalerCodeByUserId(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindJSON(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerCodeService.DeleteInsSalerCodeByUserId(insSalerCode.UserId); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSalerCodeByIds 批量删除InsSalerCode
// @Tags InsSalerCode
// @Summary 批量删除InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insSalerCode/deleteInsSalerCodeByIds [delete]
func (insSalerCodeApi *InsSalerCodeApi) DeleteInsSalerCodeByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerCodeService.DeleteInsSalerCodeByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSalerCode 更新InsSalerCode
// @Tags InsSalerCode
// @Summary 更新InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerCode true "更新InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSalerCode/updateInsSalerCode [put]
func (insSalerCodeApi *InsSalerCodeApi) UpdateInsSalerCode(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindJSON(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerCodeService.UpdateInsSalerCode(insSalerCode); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateInsSalerCodeByUserId 根据用户更新InsSalerCode
// @Tags InsSalerCode
// @Summary 更新InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSalerCode true "更新InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSalerCode/updateInsSalerCodeByUserId [put]
func (insSalerCodeApi *InsSalerCodeApi) UpdateInsSalerCodeByUserId(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindJSON(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if authorityService.CheckAuthority(utils.GetUserID(c), insSalerCode.UserId, nil) {
		response.FailWithMessage("角色权限不足,只能操作自己的下级角色", c)
		return
	}

	if e1 := currentUserService.CheckAuthorityByShopId(utils.GetUserID(c), insSalerCode.UserId); !menuService.CheckSuperAdmin(utils.GetUserAuthorityId(c)) && e1 != nil {
		response.FailWithMessage(e1.Error(), c)
		return
	}
	if err := insSalerCodeService.UpdateInsSalerCodeByUserId(insSalerCode.UserId, insSalerCode.Code); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSalerCode 用id查询InsSalerCode
// @Tags InsSalerCode
// @Summary 用id查询InsSalerCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsSalerCode true "用id查询InsSalerCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insSalerCode/findInsSalerCode [get]
func (insSalerCodeApi *InsSalerCodeApi) FindInsSalerCode(c *gin.Context) {
	var insSalerCode insbuy.InsSalerCode
	err := c.ShouldBindQuery(&insSalerCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsSalerCode, err := insSalerCodeService.GetInsSalerCode(insSalerCode.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsSalerCode": reinsSalerCode}, c)
	}
}

// GetInsSalerCodeList 分页获取InsSalerCode列表
// @Tags InsSalerCode
// @Summary 分页获取InsSalerCode列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSalerCodeSearch true "分页获取InsSalerCode列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSalerCode/getInsSalerCodeList [get]
func (insSalerCodeApi *InsSalerCodeApi) GetInsSalerCodeList(c *gin.Context) {
	var pageInfo insbuyReq.InsSalerCodeSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSalerCodeService.GetInsSalerCodeInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
