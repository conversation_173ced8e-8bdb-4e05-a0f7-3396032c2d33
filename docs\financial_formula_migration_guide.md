# 财务交叉统计报表 - 公式表达式系统迁移指南

## 概述

本文档提供了从旧的计算型分类系统迁移到新的公式表达式系统的完整指南，包括数据迁移、配置更新和测试验证。

## 迁移前准备

### 1. 系统备份

在开始迁移前，请务必备份以下数据：

```sql
-- 备份配置中心数据
CREATE TABLE ins_config_center_backup AS 
SELECT * FROM ins_config_center 
WHERE module = 'report' AND key_path = 'report.group.cost.type';

CREATE TABLE ins_config_center_detail_backup AS 
SELECT * FROM ins_config_center_detail 
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
);
```

### 2. 现有配置分析

分析当前系统中的计算型分类配置：

```sql
-- 查询所有计算型分类
SELECT 
    d.id,
    d.item_value->>'$.category_name' as category_name,
    d.item_value->>'$.is_calculated' as is_calculated,
    d.item_value->>'$.calculation_formula' as calculation_formula,
    d.item_value->>'$.calculation_type' as calculation_type,
    d.item_value->>'$.depends_on' as depends_on
FROM ins_config_center c
JOIN ins_config_center_detail d ON c.id = d.config_id
WHERE c.module = 'report' 
  AND c.key_path = 'report.group.cost.type'
  AND d.item_value->>'$.is_calculated' = '1';
```

## 迁移步骤

### 1. 数据结构更新

#### 移除废弃字段

旧的配置结构中需要移除以下字段：
- `calculation_type` - 计算类型（已废弃）
- `depends_on` - 依赖关系（自动从公式提取）

#### 更新配置数据

```sql
-- 更新计算型分类配置，移除废弃字段
UPDATE ins_config_center_detail 
SET item_value = JSON_REMOVE(
    JSON_REMOVE(item_value, '$.calculation_type'),
    '$.depends_on'
)
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
) AND item_value->>'$.is_calculated' = '1';
```

### 2. 公式格式转换

#### 自动转换脚本

创建转换脚本将旧格式公式转换为新格式：

```sql
-- 转换ADD格式
UPDATE ins_config_center_detail 
SET item_value = JSON_SET(
    item_value, 
    '$.calculation_formula',
    CASE 
        WHEN item_value->>'$.calculation_formula' LIKE 'ADD:%' THEN
            CONCAT('#', REPLACE(SUBSTRING(item_value->>'$.calculation_formula', 5), ',', ' + #'))
        ELSE item_value->>'$.calculation_formula'
    END
)
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
) AND item_value->>'$.calculation_formula' LIKE 'ADD:%';

-- 转换SUBTRACT格式
UPDATE ins_config_center_detail 
SET item_value = JSON_SET(
    item_value, 
    '$.calculation_formula',
    CASE 
        WHEN item_value->>'$.calculation_formula' LIKE 'SUBTRACT:%' THEN
            CONCAT('#', REPLACE(SUBSTRING(item_value->>'$.calculation_formula', 10), ',', ' - #'))
        ELSE item_value->>'$.calculation_formula'
    END
)
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
) AND item_value->>'$.calculation_formula' LIKE 'SUBTRACT:%';

-- 转换PERCENTAGE格式
UPDATE ins_config_center_detail 
SET item_value = JSON_SET(
    item_value, 
    '$.calculation_formula',
    CASE 
        WHEN item_value->>'$.calculation_formula' LIKE 'PERCENTAGE:%' THEN
            CONCAT('PERCENTAGE(#', 
                   REPLACE(SUBSTRING(item_value->>'$.calculation_formula', 12), ',', ', #'),
                   ')')
        ELSE item_value->>'$.calculation_formula'
    END
)
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
) AND item_value->>'$.calculation_formula' LIKE 'PERCENTAGE:%';
```

#### 手动转换示例

对于复杂的公式，可能需要手动转换：

```json
// 转换前
{
  "calculation_formula": "ADD:1,2,3",
  "calculation_type": "ADD",
  "depends_on": "[1,2,3]"
}

// 转换后
{
  "calculation_formula": "#1 + #2 + #3"
}
```

```json
// 转换前
{
  "calculation_formula": "SUBTRACT:1,2",
  "calculation_type": "SUBTRACT", 
  "depends_on": "[1,2]"
}

// 转换后
{
  "calculation_formula": "#1 - #2"
}
```

### 3. 代码部署

#### 部署新版本代码

1. 部署包含新公式处理器的代码
2. 确保向后兼容性功能正常工作
3. 验证自动转换机制

#### 配置验证

```go
// 验证配置迁移是否成功
func validateMigration() error {
    configs, err := getCostTypeConfig(context.Background(), query.Q, 0)
    if err != nil {
        return err
    }
    
    processor := NewFormulaProcessor(configs)
    
    for _, config := range configs {
        if config.IsCalculated && config.CalculationFormula != "" {
            // 验证公式可以正确解析
            expr, err := processor.Parse(config.CalculationFormula)
            if err != nil {
                log.Printf("公式解析失败 [ID:%d, 公式:%s]: %v", 
                    config.Id, config.CalculationFormula, err)
                continue
            }
            
            // 验证依赖关系提取
            deps, err := processor.ExtractDependencies(config.CalculationFormula)
            if err != nil {
                log.Printf("依赖提取失败 [ID:%d]: %v", config.Id, err)
                continue
            }
            
            log.Printf("配置验证成功 [ID:%d, 依赖:%v]", config.Id, deps)
        }
    }
    
    return nil
}
```

## 迁移验证

### 1. 功能测试

#### 基础功能验证

```go
func TestMigrationBasicFunctions(t *testing.T) {
    // 测试旧格式公式的自动转换
    testCases := []struct {
        oldFormula string
        expected   string
    }{
        {"ADD:1,2,3", "#1 + #2 + #3"},
        {"SUBTRACT:1,2", "#1 - #2"},
        {"PERCENTAGE:1,2", "PERCENTAGE(#1, #2)"},
    }
    
    processor := NewFormulaProcessor(getTestConfigs())
    
    for _, tc := range testCases {
        converted, ok := processor.convertLegacyFormat(tc.oldFormula)
        assert.True(t, ok)
        assert.Equal(t, tc.expected, converted)
        
        // 验证转换后的公式可以正确解析
        expr, err := processor.Parse(tc.oldFormula)
        assert.NoError(t, err)
        assert.NotNil(t, expr)
    }
}
```

#### 计算结果验证

```go
func TestMigrationCalculationResults(t *testing.T) {
    // 准备测试数据
    dataMap := getTestDataMap()
    timeColumns := []string{"2025-01", "2025-02"}
    
    // 测试旧格式和新格式的计算结果是否一致
    testCases := []struct {
        oldFormula string
        newFormula string
    }{
        {"ADD:1,2", "#1 + #2"},
        {"SUBTRACT:1,2", "#1 - #2"},
        {"PERCENTAGE:1,2", "PERCENTAGE(#1, #2)"},
    }
    
    processor := NewFormulaProcessor(getTestConfigs())
    
    for _, tc := range testCases {
        // 计算旧格式结果
        oldExpr, err := processor.Parse(tc.oldFormula)
        assert.NoError(t, err)
        oldResult, err := processor.Calculate(oldExpr, dataMap, timeColumns)
        assert.NoError(t, err)
        
        // 计算新格式结果
        newExpr, err := processor.Parse(tc.newFormula)
        assert.NoError(t, err)
        newResult, err := processor.Calculate(newExpr, dataMap, timeColumns)
        assert.NoError(t, err)
        
        // 验证结果一致
        assert.Equal(t, oldResult.TotalAmount, newResult.TotalAmount)
        for month := range oldResult.MonthlyAmounts {
            assert.Equal(t, oldResult.MonthlyAmounts[month], newResult.MonthlyAmounts[month])
        }
    }
}
```

### 2. 性能测试

```go
func BenchmarkMigrationPerformance(b *testing.B) {
    processor := NewFormulaProcessor(getTestConfigs())
    dataMap := getTestDataMap()
    timeColumns := []string{"2025-01", "2025-02", "2025-03"}
    
    // 测试新系统的性能
    formulas := []string{
        "#1 + #2 + #3",
        "#1 - #2",
        "PERCENTAGE(#1, #2)",
        "([收入类合计] - [成本类合计]) / [收入类合计] * 100",
    }
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        for _, formula := range formulas {
            expr, _ := processor.Parse(formula)
            processor.Calculate(expr, dataMap, timeColumns)
        }
    }
}
```

### 3. 数据一致性验证

```sql
-- 验证迁移前后数据一致性
WITH old_results AS (
    -- 使用旧系统计算的结果（从备份表或历史数据）
    SELECT category_id, month, amount FROM historical_calculation_results
    WHERE calculation_date = '2025-01-01'
),
new_results AS (
    -- 使用新系统计算的结果
    SELECT category_id, month, amount FROM current_calculation_results
    WHERE calculation_date = '2025-01-01'
)
SELECT 
    o.category_id,
    o.month,
    o.amount as old_amount,
    n.amount as new_amount,
    ABS(o.amount - n.amount) as difference
FROM old_results o
JOIN new_results n ON o.category_id = n.category_id AND o.month = n.month
WHERE ABS(o.amount - n.amount) > 0.01; -- 允许小数点精度差异
```

## 回滚计划

### 1. 回滚条件

如果出现以下情况，需要考虑回滚：
- 计算结果与预期不符
- 性能显著下降
- 出现无法解决的兼容性问题

### 2. 回滚步骤

```sql
-- 恢复配置数据
DELETE FROM ins_config_center_detail 
WHERE config_id IN (
    SELECT id FROM ins_config_center 
    WHERE module = 'report' AND key_path = 'report.group.cost.type'
);

INSERT INTO ins_config_center_detail 
SELECT * FROM ins_config_center_detail_backup;

-- 恢复配置中心主表
DELETE FROM ins_config_center 
WHERE module = 'report' AND key_path = 'report.group.cost.type';

INSERT INTO ins_config_center 
SELECT * FROM ins_config_center_backup;
```

### 3. 回滚验证

```go
func validateRollback() error {
    // 验证旧系统功能正常
    // 验证数据完整性
    // 验证计算结果正确性
    return nil
}
```

## 迁移后优化

### 1. 清理废弃代码

迁移完成并稳定运行后，可以清理以下废弃代码：
- 旧的计算函数（executeAddCalculation等）
- 废弃的结构体字段
- 不再使用的常量定义

### 2. 性能优化

```go
// 添加公式缓存
type CachedFormulaProcessor struct {
    *FormulaProcessor
    expressionCache map[string]*Expression
    mutex          sync.RWMutex
}

func (cfp *CachedFormulaProcessor) Parse(formula string) (*Expression, error) {
    cfp.mutex.RLock()
    if expr, exists := cfp.expressionCache[formula]; exists {
        cfp.mutex.RUnlock()
        return expr, nil
    }
    cfp.mutex.RUnlock()
    
    expr, err := cfp.FormulaProcessor.Parse(formula)
    if err != nil {
        return nil, err
    }
    
    cfp.mutex.Lock()
    cfp.expressionCache[formula] = expr
    cfp.mutex.Unlock()
    
    return expr, nil
}
```

### 3. 监控和告警

```go
// 添加计算错误监控
func monitorCalculationErrors() {
    // 监控公式解析失败率
    // 监控计算执行时间
    // 监控依赖关系错误
}
```

## 总结

本迁移指南提供了从旧系统到新公式表达式系统的完整迁移路径：

1. **平滑迁移**: 通过自动转换机制确保向后兼容
2. **数据安全**: 完整的备份和回滚方案
3. **充分验证**: 多层次的测试验证确保迁移质量
4. **性能保障**: 性能测试和优化建议
5. **风险控制**: 详细的回滚计划和监控方案

通过遵循这个迁移指南，可以安全、高效地完成系统升级，享受新公式表达式系统带来的强大功能和更好的用户体验。
