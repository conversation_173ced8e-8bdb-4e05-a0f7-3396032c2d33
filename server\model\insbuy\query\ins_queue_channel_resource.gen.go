// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsQueueChannelResource(db *gorm.DB, opts ...gen.DOOption) insQueueChannelResource {
	_insQueueChannelResource := insQueueChannelResource{}

	_insQueueChannelResource.insQueueChannelResourceDo.UseDB(db, opts...)
	_insQueueChannelResource.insQueueChannelResourceDo.UseModel(&insbuy.InsQueueChannelResource{})

	tableName := _insQueueChannelResource.insQueueChannelResourceDo.TableName()
	_insQueueChannelResource.ALL = field.NewAsterisk(tableName)
	_insQueueChannelResource.ID = field.NewUint(tableName, "id")
	_insQueueChannelResource.CreatedAt = field.NewTime(tableName, "created_at")
	_insQueueChannelResource.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insQueueChannelResource.ChannelId = field.NewUint(tableName, "channel_id")
	_insQueueChannelResource.ResourceId = field.NewUint(tableName, "resource_id")
	_insQueueChannelResource.ResourceType = field.NewInt(tableName, "resource_type")
	_insQueueChannelResource.Channel = insQueueChannelResourceBelongsToChannel{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Channel", "insbuy.InsQueueChannel"),
		Store: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("Channel.Store", "insbuy.InsStore"),
		},
	}

	_insQueueChannelResource.fillFieldMap()

	return _insQueueChannelResource
}

type insQueueChannelResource struct {
	insQueueChannelResourceDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	ChannelId    field.Uint
	ResourceId   field.Uint
	ResourceType field.Int
	Channel      insQueueChannelResourceBelongsToChannel

	fieldMap map[string]field.Expr
}

func (i insQueueChannelResource) Table(newTableName string) *insQueueChannelResource {
	i.insQueueChannelResourceDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insQueueChannelResource) As(alias string) *insQueueChannelResource {
	i.insQueueChannelResourceDo.DO = *(i.insQueueChannelResourceDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insQueueChannelResource) updateTableName(table string) *insQueueChannelResource {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ChannelId = field.NewUint(table, "channel_id")
	i.ResourceId = field.NewUint(table, "resource_id")
	i.ResourceType = field.NewInt(table, "resource_type")

	i.fillFieldMap()

	return i
}

func (i *insQueueChannelResource) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insQueueChannelResource) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["channel_id"] = i.ChannelId
	i.fieldMap["resource_id"] = i.ResourceId
	i.fieldMap["resource_type"] = i.ResourceType

}

func (i insQueueChannelResource) clone(db *gorm.DB) insQueueChannelResource {
	i.insQueueChannelResourceDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insQueueChannelResource) replaceDB(db *gorm.DB) insQueueChannelResource {
	i.insQueueChannelResourceDo.ReplaceDB(db)
	return i
}

type insQueueChannelResourceBelongsToChannel struct {
	db *gorm.DB

	field.RelationField

	Store struct {
		field.RelationField
	}
}

func (a insQueueChannelResourceBelongsToChannel) Where(conds ...field.Expr) *insQueueChannelResourceBelongsToChannel {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insQueueChannelResourceBelongsToChannel) WithContext(ctx context.Context) *insQueueChannelResourceBelongsToChannel {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insQueueChannelResourceBelongsToChannel) Session(session *gorm.Session) *insQueueChannelResourceBelongsToChannel {
	a.db = a.db.Session(session)
	return &a
}

func (a insQueueChannelResourceBelongsToChannel) Model(m *insbuy.InsQueueChannelResource) *insQueueChannelResourceBelongsToChannelTx {
	return &insQueueChannelResourceBelongsToChannelTx{a.db.Model(m).Association(a.Name())}
}

type insQueueChannelResourceBelongsToChannelTx struct{ tx *gorm.Association }

func (a insQueueChannelResourceBelongsToChannelTx) Find() (result *insbuy.InsQueueChannel, err error) {
	return result, a.tx.Find(&result)
}

func (a insQueueChannelResourceBelongsToChannelTx) Append(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insQueueChannelResourceBelongsToChannelTx) Replace(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insQueueChannelResourceBelongsToChannelTx) Delete(values ...*insbuy.InsQueueChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insQueueChannelResourceBelongsToChannelTx) Clear() error {
	return a.tx.Clear()
}

func (a insQueueChannelResourceBelongsToChannelTx) Count() int64 {
	return a.tx.Count()
}

type insQueueChannelResourceDo struct{ gen.DO }

func (i insQueueChannelResourceDo) Debug() *insQueueChannelResourceDo {
	return i.withDO(i.DO.Debug())
}

func (i insQueueChannelResourceDo) WithContext(ctx context.Context) *insQueueChannelResourceDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insQueueChannelResourceDo) ReadDB() *insQueueChannelResourceDo {
	return i.Clauses(dbresolver.Read)
}

func (i insQueueChannelResourceDo) WriteDB() *insQueueChannelResourceDo {
	return i.Clauses(dbresolver.Write)
}

func (i insQueueChannelResourceDo) Session(config *gorm.Session) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Session(config))
}

func (i insQueueChannelResourceDo) Clauses(conds ...clause.Expression) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insQueueChannelResourceDo) Returning(value interface{}, columns ...string) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insQueueChannelResourceDo) Not(conds ...gen.Condition) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insQueueChannelResourceDo) Or(conds ...gen.Condition) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insQueueChannelResourceDo) Select(conds ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insQueueChannelResourceDo) Where(conds ...gen.Condition) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insQueueChannelResourceDo) Order(conds ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insQueueChannelResourceDo) Distinct(cols ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insQueueChannelResourceDo) Omit(cols ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insQueueChannelResourceDo) Join(table schema.Tabler, on ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insQueueChannelResourceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insQueueChannelResourceDo) RightJoin(table schema.Tabler, on ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insQueueChannelResourceDo) Group(cols ...field.Expr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insQueueChannelResourceDo) Having(conds ...gen.Condition) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insQueueChannelResourceDo) Limit(limit int) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insQueueChannelResourceDo) Offset(offset int) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insQueueChannelResourceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insQueueChannelResourceDo) Unscoped() *insQueueChannelResourceDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insQueueChannelResourceDo) Create(values ...*insbuy.InsQueueChannelResource) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insQueueChannelResourceDo) CreateInBatches(values []*insbuy.InsQueueChannelResource, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insQueueChannelResourceDo) Save(values ...*insbuy.InsQueueChannelResource) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insQueueChannelResourceDo) First() (*insbuy.InsQueueChannelResource, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannelResource), nil
	}
}

func (i insQueueChannelResourceDo) Take() (*insbuy.InsQueueChannelResource, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannelResource), nil
	}
}

func (i insQueueChannelResourceDo) Last() (*insbuy.InsQueueChannelResource, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannelResource), nil
	}
}

func (i insQueueChannelResourceDo) Find() ([]*insbuy.InsQueueChannelResource, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsQueueChannelResource), err
}

func (i insQueueChannelResourceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsQueueChannelResource, err error) {
	buf := make([]*insbuy.InsQueueChannelResource, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insQueueChannelResourceDo) FindInBatches(result *[]*insbuy.InsQueueChannelResource, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insQueueChannelResourceDo) Attrs(attrs ...field.AssignExpr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insQueueChannelResourceDo) Assign(attrs ...field.AssignExpr) *insQueueChannelResourceDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insQueueChannelResourceDo) Joins(fields ...field.RelationField) *insQueueChannelResourceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insQueueChannelResourceDo) Preload(fields ...field.RelationField) *insQueueChannelResourceDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insQueueChannelResourceDo) FirstOrInit() (*insbuy.InsQueueChannelResource, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannelResource), nil
	}
}

func (i insQueueChannelResourceDo) FirstOrCreate() (*insbuy.InsQueueChannelResource, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsQueueChannelResource), nil
	}
}

func (i insQueueChannelResourceDo) FindByPage(offset int, limit int) (result []*insbuy.InsQueueChannelResource, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insQueueChannelResourceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insQueueChannelResourceDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insQueueChannelResourceDo) Delete(models ...*insbuy.InsQueueChannelResource) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insQueueChannelResourceDo) withDO(do gen.Dao) *insQueueChannelResourceDo {
	i.DO = *do.(*gen.DO)
	return i
}
