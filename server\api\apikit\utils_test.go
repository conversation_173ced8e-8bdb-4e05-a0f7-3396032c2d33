package apikit

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestWrapperApi0(t *testing.T) {
	type FooReq1 struct {
		Id    int    `json:"id" form:"id"`
		Name  string `json:"name" form:"name"`
		Score []struct {
			Subject string `json:"subject" form:"subject"`
			Grade   int    `json:"grade" form:"grade"`
		} `json:"score" form:"score"`
	}
	type FooReq1_1 struct {
		Id int `json:"id" form:"id"`
	}
	type FooReq1_2 struct {
		Name  string `json:"name" form:"name"`
		Score []struct {
			Subject string `json:"subject" form:"subject"`
			Grade   int    `json:"grade" form:"grade"`
		} `json:"score" form:"score"`
	}

	bodyJson := `{
	"id":1,"name":"test","score":[
		{"subject":"math","grade":100}
	]
}`

	// gin 的 form 不能处理数组
	type FooReq2 struct {
		Id    int    `form:"id"`
		Name  string `form:"name"`
		Score struct {
			Subject string `form:"score.subject"`
			Grade   int    `form:"score.grade"`
		}
	}
	type FooReq2_1 struct {
		Id int `form:"id"`
	}
	type FooReq2_2 struct {
		Name  string `form:"name"`
		Score struct {
			Subject string `form:"score.subject"`
			Grade   int    `form:"score.grade"`
		}
	}
	bodyForm := `id=1&name=test&score.subject=math&score.grade=100`

	fnNewRequest4JSON := func(path, s string) *http.Request {
		r1, err := http.NewRequest("POST", path, strings.NewReader(s))
		if err != nil {
			t.Fatal(err)
		}
		r1.Header.Set("Content-Type", "application/json")
		return r1
	}
	fnNewRequest4Form := func(path string, s string) *http.Request {
		r1, err := http.NewRequest("POST", path, strings.NewReader(s))
		if err != nil {
			t.Fatal(err)
		}
		r1.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		return r1
	}

	ATTR_TEST_NAME := "TestName"

	tests := []struct {
		name string
		*http.Request
		fn interface{}
	}{
		{
			"指针参数", fnNewRequest4JSON("/foo", bodyJson),
			func(req *FooReq1) {
				t.Logf("%+v", req)
				if req.Id != 1 || req.Name != "test" || req.Score[0].Subject != "math" || req.Score[0].Grade != 100 {
					t.Fatal("解析失败")
				}
			},
		},
		{
			"传值参数", fnNewRequest4JSON("/foo", bodyJson),
			func(req FooReq1) {
				t.Logf("%+v", req)
				if req.Id != 1 || req.Name != "test" || req.Score[0].Subject != "math" || req.Score[0].Grade != 100 {
					t.Fatal("解析失败")
				}
			},
		},
		{
			// body 提前被消耗
			"有选择的参数，JSON方式", fnNewRequest4JSON("/foo", bodyJson),
			func(req1 FooReq1_1, req2 FooReq1_2) {
				t.Logf("%+v %+v", req1, req2)
				if req1.Id != 1 || req2.Name != "test" || req2.Score[0].Subject != "math" || req2.Score[0].Grade != 100 {
					t.Fatal("解析失败")
				}
			},
		},
		{
			"有选择的参数，FORM 方式", fnNewRequest4Form("/foo", bodyForm),
			func(req1 FooReq2_1, req2 *FooReq2_2, ctx context.Context) {
				t.Logf("%+v %+v", req1, req2)
				if req1.Id != 1 || req2.Name != "test" || req2.Score.Subject != "math" || req2.Score.Grade != 100 {
					t.Fatal("解析失败")
				}
			},
		},
		{
			"二次指针参数", fnNewRequest4JSON("/foo", bodyJson),
			func(req0 **FooReq1) {
				req := *req0
				t.Logf("%+v", req)
				if req.Id != 1 || req.Name != "test" || req.Score[0].Subject != "math" || req.Score[0].Grade != 100 {
					t.Fatal("解析失败")
				}
			},
		},
		{
			"有「context」「参数」", fnNewRequest4Form("/foo", bodyForm),
			func(c *gin.Context, ctx context.Context, req FooReq2) {
				t.Logf("%+v", req)
				if req.Id != 1 || req.Name != "test" || req.Score.Subject != "math" || req.Score.Grade != 100 {
					t.Fatal("解析失败")
				}
				if c == nil {
					t.Fatal("解析失败")
				} else if v, ok := c.Get(ATTR_TEST_NAME); !ok || v.(string) == "" {
					t.Fatal("解析失败")
				}
				if ctx == nil {
					t.Fatal("解析失败")
				} else if v := ctx.Value(ATTR_TEST_NAME); v == nil || v.(string) == "" {
					t.Fatal("解析失败")
				}
			},
		},
	}
	gin.SetMode(gin.ReleaseMode)
	for _, tt := range tests {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = tt.Request.WithContext(context.WithValue(context.Background(), ATTR_TEST_NAME, tt.name))
		c.Set(ATTR_TEST_NAME, tt.name)
		fn := tt.fn
		t.Run(tt.name, func(t *testing.T) {
			WrapperApi0(fn)(c)
			if w.Code != http.StatusOK {
				t.Fatal(w.Body.String())
			}
		})
	}
}
