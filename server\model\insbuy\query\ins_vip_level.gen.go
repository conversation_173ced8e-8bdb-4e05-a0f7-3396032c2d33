// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsVipLevel(db *gorm.DB, opts ...gen.DOOption) insVipLevel {
	_insVipLevel := insVipLevel{}

	_insVipLevel.insVipLevelDo.UseDB(db, opts...)
	_insVipLevel.insVipLevelDo.UseModel(&insbuy.InsVipLevel{})

	tableName := _insVipLevel.insVipLevelDo.TableName()
	_insVipLevel.ALL = field.NewAsterisk(tableName)
	_insVipLevel.ID = field.NewUint(tableName, "id")
	_insVipLevel.CreatedAt = field.NewTime(tableName, "created_at")
	_insVipLevel.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insVipLevel.DeletedAt = field.NewField(tableName, "deleted_at")
	_insVipLevel.Level = field.NewInt(tableName, "level")
	_insVipLevel.Name = field.NewString(tableName, "name")
	_insVipLevel.Score = field.NewInt(tableName, "score")
	_insVipLevel.Icon = field.NewString(tableName, "icon")
	_insVipLevel.Remark = field.NewString(tableName, "remark")

	_insVipLevel.fillFieldMap()

	return _insVipLevel
}

type insVipLevel struct {
	insVipLevelDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Level     field.Int
	Name      field.String
	Score     field.Int
	Icon      field.String
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insVipLevel) Table(newTableName string) *insVipLevel {
	i.insVipLevelDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insVipLevel) As(alias string) *insVipLevel {
	i.insVipLevelDo.DO = *(i.insVipLevelDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insVipLevel) updateTableName(table string) *insVipLevel {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Level = field.NewInt(table, "level")
	i.Name = field.NewString(table, "name")
	i.Score = field.NewInt(table, "score")
	i.Icon = field.NewString(table, "icon")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insVipLevel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insVipLevel) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["level"] = i.Level
	i.fieldMap["name"] = i.Name
	i.fieldMap["score"] = i.Score
	i.fieldMap["icon"] = i.Icon
	i.fieldMap["remark"] = i.Remark
}

func (i insVipLevel) clone(db *gorm.DB) insVipLevel {
	i.insVipLevelDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insVipLevel) replaceDB(db *gorm.DB) insVipLevel {
	i.insVipLevelDo.ReplaceDB(db)
	return i
}

type insVipLevelDo struct{ gen.DO }

func (i insVipLevelDo) Debug() *insVipLevelDo {
	return i.withDO(i.DO.Debug())
}

func (i insVipLevelDo) WithContext(ctx context.Context) *insVipLevelDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insVipLevelDo) ReadDB() *insVipLevelDo {
	return i.Clauses(dbresolver.Read)
}

func (i insVipLevelDo) WriteDB() *insVipLevelDo {
	return i.Clauses(dbresolver.Write)
}

func (i insVipLevelDo) Session(config *gorm.Session) *insVipLevelDo {
	return i.withDO(i.DO.Session(config))
}

func (i insVipLevelDo) Clauses(conds ...clause.Expression) *insVipLevelDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insVipLevelDo) Returning(value interface{}, columns ...string) *insVipLevelDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insVipLevelDo) Not(conds ...gen.Condition) *insVipLevelDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insVipLevelDo) Or(conds ...gen.Condition) *insVipLevelDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insVipLevelDo) Select(conds ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insVipLevelDo) Where(conds ...gen.Condition) *insVipLevelDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insVipLevelDo) Order(conds ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insVipLevelDo) Distinct(cols ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insVipLevelDo) Omit(cols ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insVipLevelDo) Join(table schema.Tabler, on ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insVipLevelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insVipLevelDo) RightJoin(table schema.Tabler, on ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insVipLevelDo) Group(cols ...field.Expr) *insVipLevelDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insVipLevelDo) Having(conds ...gen.Condition) *insVipLevelDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insVipLevelDo) Limit(limit int) *insVipLevelDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insVipLevelDo) Offset(offset int) *insVipLevelDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insVipLevelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insVipLevelDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insVipLevelDo) Unscoped() *insVipLevelDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insVipLevelDo) Create(values ...*insbuy.InsVipLevel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insVipLevelDo) CreateInBatches(values []*insbuy.InsVipLevel, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insVipLevelDo) Save(values ...*insbuy.InsVipLevel) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insVipLevelDo) First() (*insbuy.InsVipLevel, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipLevel), nil
	}
}

func (i insVipLevelDo) Take() (*insbuy.InsVipLevel, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipLevel), nil
	}
}

func (i insVipLevelDo) Last() (*insbuy.InsVipLevel, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipLevel), nil
	}
}

func (i insVipLevelDo) Find() ([]*insbuy.InsVipLevel, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsVipLevel), err
}

func (i insVipLevelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsVipLevel, err error) {
	buf := make([]*insbuy.InsVipLevel, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insVipLevelDo) FindInBatches(result *[]*insbuy.InsVipLevel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insVipLevelDo) Attrs(attrs ...field.AssignExpr) *insVipLevelDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insVipLevelDo) Assign(attrs ...field.AssignExpr) *insVipLevelDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insVipLevelDo) Joins(fields ...field.RelationField) *insVipLevelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insVipLevelDo) Preload(fields ...field.RelationField) *insVipLevelDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insVipLevelDo) FirstOrInit() (*insbuy.InsVipLevel, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipLevel), nil
	}
}

func (i insVipLevelDo) FirstOrCreate() (*insbuy.InsVipLevel, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipLevel), nil
	}
}

func (i insVipLevelDo) FindByPage(offset int, limit int) (result []*insbuy.InsVipLevel, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insVipLevelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insVipLevelDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insVipLevelDo) Delete(models ...*insbuy.InsVipLevel) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insVipLevelDo) withDO(do gen.Dao) *insVipLevelDo {
	i.DO = *do.(*gen.DO)
	return i
}
