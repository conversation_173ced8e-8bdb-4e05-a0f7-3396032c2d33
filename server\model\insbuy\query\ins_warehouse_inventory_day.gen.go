// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventoryDay(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventoryDay {
	_insWarehouseInventoryDay := insWarehouseInventoryDay{}

	_insWarehouseInventoryDay.insWarehouseInventoryDayDo.UseDB(db, opts...)
	_insWarehouseInventoryDay.insWarehouseInventoryDayDo.UseModel(&insbuy.InsWarehouseInventoryDay{})

	tableName := _insWarehouseInventoryDay.insWarehouseInventoryDayDo.TableName()
	_insWarehouseInventoryDay.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventoryDay.ID = field.NewUint(tableName, "id")
	_insWarehouseInventoryDay.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventoryDay.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventoryDay.Day = field.NewTime(tableName, "day")
	_insWarehouseInventoryDay.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insWarehouseInventoryDay.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseInventoryDay.CostPrice = field.NewFloat64(tableName, "cost_price")
	_insWarehouseInventoryDay.InNum = field.NewFloat64(tableName, "in_num")
	_insWarehouseInventoryDay.OutNum = field.NewFloat64(tableName, "out_num")
	_insWarehouseInventoryDay.BalanceNum = field.NewFloat64(tableName, "balance_num")
	_insWarehouseInventoryDay.BusinessDay = field.NewTime(tableName, "business_day")

	_insWarehouseInventoryDay.fillFieldMap()

	return _insWarehouseInventoryDay
}

type insWarehouseInventoryDay struct {
	insWarehouseInventoryDayDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Day         field.Time
	WarehouseId field.Int
	MaterialId  field.Int
	CostPrice   field.Float64
	InNum       field.Float64
	OutNum      field.Float64
	BalanceNum  field.Float64
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventoryDay) Table(newTableName string) *insWarehouseInventoryDay {
	i.insWarehouseInventoryDayDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventoryDay) As(alias string) *insWarehouseInventoryDay {
	i.insWarehouseInventoryDayDo.DO = *(i.insWarehouseInventoryDayDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventoryDay) updateTableName(table string) *insWarehouseInventoryDay {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Day = field.NewTime(table, "day")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.CostPrice = field.NewFloat64(table, "cost_price")
	i.InNum = field.NewFloat64(table, "in_num")
	i.OutNum = field.NewFloat64(table, "out_num")
	i.BalanceNum = field.NewFloat64(table, "balance_num")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventoryDay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventoryDay) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 11)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["day"] = i.Day
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["cost_price"] = i.CostPrice
	i.fieldMap["in_num"] = i.InNum
	i.fieldMap["out_num"] = i.OutNum
	i.fieldMap["balance_num"] = i.BalanceNum
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insWarehouseInventoryDay) clone(db *gorm.DB) insWarehouseInventoryDay {
	i.insWarehouseInventoryDayDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventoryDay) replaceDB(db *gorm.DB) insWarehouseInventoryDay {
	i.insWarehouseInventoryDayDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryDayDo struct{ gen.DO }

func (i insWarehouseInventoryDayDo) Debug() *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryDayDo) WithContext(ctx context.Context) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryDayDo) ReadDB() *insWarehouseInventoryDayDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryDayDo) WriteDB() *insWarehouseInventoryDayDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryDayDo) Session(config *gorm.Session) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryDayDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryDayDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryDayDo) Not(conds ...gen.Condition) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryDayDo) Or(conds ...gen.Condition) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryDayDo) Select(conds ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryDayDo) Where(conds ...gen.Condition) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryDayDo) Order(conds ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryDayDo) Distinct(cols ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryDayDo) Omit(cols ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryDayDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryDayDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryDayDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryDayDo) Group(cols ...field.Expr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryDayDo) Having(conds ...gen.Condition) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryDayDo) Limit(limit int) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryDayDo) Offset(offset int) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryDayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryDayDo) Unscoped() *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryDayDo) Create(values ...*insbuy.InsWarehouseInventoryDay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryDayDo) CreateInBatches(values []*insbuy.InsWarehouseInventoryDay, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryDayDo) Save(values ...*insbuy.InsWarehouseInventoryDay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryDayDo) First() (*insbuy.InsWarehouseInventoryDay, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDay), nil
	}
}

func (i insWarehouseInventoryDayDo) Take() (*insbuy.InsWarehouseInventoryDay, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDay), nil
	}
}

func (i insWarehouseInventoryDayDo) Last() (*insbuy.InsWarehouseInventoryDay, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDay), nil
	}
}

func (i insWarehouseInventoryDayDo) Find() ([]*insbuy.InsWarehouseInventoryDay, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventoryDay), err
}

func (i insWarehouseInventoryDayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventoryDay, err error) {
	buf := make([]*insbuy.InsWarehouseInventoryDay, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryDayDo) FindInBatches(result *[]*insbuy.InsWarehouseInventoryDay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryDayDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryDayDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryDayDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryDayDo) Joins(fields ...field.RelationField) *insWarehouseInventoryDayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryDayDo) Preload(fields ...field.RelationField) *insWarehouseInventoryDayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryDayDo) FirstOrInit() (*insbuy.InsWarehouseInventoryDay, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDay), nil
	}
}

func (i insWarehouseInventoryDayDo) FirstOrCreate() (*insbuy.InsWarehouseInventoryDay, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryDay), nil
	}
}

func (i insWarehouseInventoryDayDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventoryDay, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryDayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryDayDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryDayDo) Delete(models ...*insbuy.InsWarehouseInventoryDay) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryDayDo) withDO(do gen.Dao) *insWarehouseInventoryDayDo {
	i.DO = *do.(*gen.DO)
	return i
}
