package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsStoreApi struct{}

// CreateInsStore 创建InsStore
// @Tags InsStore
// @Summary 创建InsStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStore true "创建InsStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/createInsStore [post]
func (insStoreApi *InsStoreApi) CreateInsStore(c *gin.Context) {
	var insStore insbuy.InsStore
	err := c.ShouldBindJSON(&insStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"Name":        {utils.NotEmpty()},
		"Manager":     {utils.NotEmpty()},
		"OfficePhone": {utils.NotEmpty()},
		"Address":     {utils.NotEmpty()},
	}
	if err := utils.Verify(insStore, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.CreateInsStore(c, &insStore); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsStore 删除InsStore
// @Tags InsStore
// @Summary 删除InsStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStore true "删除InsStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insStore/deleteInsStore [delete]
func (insStoreApi *InsStoreApi) DeleteInsStore(c *gin.Context) {
	var insStore insbuy.InsStore
	err := c.ShouldBindJSON(&insStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.DeleteInsStore(insStore); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsStoreByIds 批量删除InsStore
// @Tags InsStore
// @Summary 批量删除InsStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insStore/deleteInsStoreByIds [delete]
func (insStoreApi *InsStoreApi) DeleteInsStoreByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.DeleteInsStoreByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsStore 更新InsStore
// @Tags InsStore
// @Summary 更新InsStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsStore true "更新InsStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insStore/updateInsStore [put]
func (insStoreApi *InsStoreApi) UpdateInsStore(c *gin.Context) {
	var insStore insbuy.InsStore
	err := c.ShouldBindJSON(&insStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	verify := utils.Rules{
		"ID":          {utils.NotEmpty()},
		"Name":        {utils.NotEmpty()},
		"Manager":     {utils.NotEmpty()},
		"OfficePhone": {utils.NotEmpty()},
		"Address":     {utils.NotEmpty()},
	}
	if err := utils.Verify(insStore, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.UpdateInsStore(insStore); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsStore 用id查询InsStore
// @Tags InsStore
// @Summary 用id查询InsStore
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsStore true "用id查询InsStore"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insStore/findInsStore [get]
func (insStoreApi *InsStoreApi) FindInsStore(c *gin.Context) {
	var insStore insbuy.InsStore
	err := c.ShouldBindQuery(&insStore)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	println("insStore.ID", insStore.ID)
	if reinsStore, err := insStoreService.GetInsStore(insStore.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsStore": reinsStore}, c)
	}
}

// GetInsStoreList 分页获取InsStore列表
// @Tags InsStore
// @Summary 分页获取InsStore列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsStoreSearch true "分页获取InsStore列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/getInsStoreList [get]
func (insStoreApi *InsStoreApi) GetInsStoreList(c *gin.Context) {
	var pageInfo insbuyReq.InsStoreSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insStoreService.GetInsStoreInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			_, e1 := insImportService.ExcelCommonList(c, insbuy.ETStoreList.ToInt(), list)
			if e1 != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetStoreAuthorityList 系统用户分店权限列表
// @Tags InsStore
// @Summary 系统用户分店权限列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetStoreAuthorityListReq true "分页获取InsStore列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/getStoreAuthorityList [get]
func (insStoreApi *InsStoreApi) GetStoreAuthorityList(c *gin.Context) {
	var req insbuyReq.GetStoreAuthorityListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insStoreService.GetStoreAuthorityList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// UpdateStoreAuthority  修改用户分店权限
// @Tags InsStore
// @Summary 修改用户分店权限
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateStoreAuthorityReq true "修改用户分店权限"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insStore/updateStoreAuthority [put]
func (insStoreApi *InsStoreApi) UpdateStoreAuthority(c *gin.Context) {
	var req insbuyReq.UpdateStoreAuthorityReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.UpdateStoreAuthority(req); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// StoreConsumeSnapshot 门店消费快照
// @Tags InsStore
// @Summary 门店消费快照
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.StoreConsumeSnapshotReq true "门店消费快照"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/storeConsumeSnapshot [get]
func (insStoreApi *InsStoreApi) StoreConsumeSnapshot(c *gin.Context) {
	var req insbuyReq.StoreConsumeSnapshotReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insStoreService.StoreConsumeSnapshot(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败"+err.Error(), c)
	} else {
		response.OkWithMessage("获取成功", c)
	}
}

// CreateManual 手动记录数据填写
// @Summary 手动记录数据填写
// @Security ApiKeyAuth
// @Description 手动记录数据填写:线下门票
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动数据录入"}"
// @Router /insStore/manual [post]
func (insStoreApi *InsStoreApi) CreateManual(c *gin.Context) {
	var req insbuyReq.ManualReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insStoreService.CreateManual(&req)
	response.ResultErr(resp, err, c)
}

// UpdateManual 手动记录数据填写
// @Summary 手动记录数据填写
// @Security ApiKeyAuth
// @Description 手动记录数据填写:线下门票
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动数据录入"}"
// @Router /insStore/manual [put]
func (insStoreApi *InsStoreApi) UpdateManual(c *gin.Context) {
	var req insbuyReq.ManualReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insStoreService.UpdateManual(&req)
	response.ResultErr(resp, err, c)
}

// DelManual
// @Summary 手动记录数据填写
// @Security ApiKeyAuth
// @Description 手动记录数据填写:线下门票
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动数据删除"}"
// @Router /insStore/manual [delete]
func (insStoreApi *InsStoreApi) DelManual(c *gin.Context) {
	var req insbuyReq.ManualReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insStoreService.DelManual(&req)
	response.ResultErr(struct{}{}, err, c)
}

// BatchCreateManual 批量写入
// @Summary 批量写入
// @Security ApiKeyAuth
// @Description 批量写入
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量写入"}"
// @Router /insStore/batchCreateManual [post]
func (insStoreApi *InsStoreApi) BatchCreateManual(c *gin.Context) {
	var req insbuyReq.ManualReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insStoreService.BatchCreateManual(&req)
	response.ResultErr(resp, err, c)
}

// ManualList
// @Summary 手动记录数据填写
// @Security ApiKeyAuth
// @Description 手动记录数据填写:线下门票
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualListReq false "参数"
// @Success 200 {object} response.Response{data=insbuy.InsStoreManualOp} "手动数据录入"
// @Router /insStore/manualList [get]
func (insStoreApi *InsStoreApi) ManualList(c *gin.Context) {
	var req insbuyReq.ManualListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insStoreService.ManualList(&req)
	response.ResultErr(resp, err, c)
}

// Snapshot 统一的快照接口
// @Summary 统一的快照接口
// @Security ApiKeyAuth
// @Description 统一的快照接口
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.SnapshotReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"快照成功"}"
// @Router /insStore/snapshot [post]
func (insStoreApi *InsStoreApi) Snapshot(c *gin.Context) {
	var req insbuyReq.SnapshotReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insStoreService.Snapshot(req)
	response.ResultErr(nil, err, c)
}

// ManualDataList 手动数据列表
// @Summary 手动数据列表
// @Security ApiKeyAuth
// @Description 手动数据列表
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Param req query insbuyReq.ManualDataReq false "参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/manualDataList [get]
func (insStoreApi *InsStoreApi) ManualDataList(c *gin.Context) {
	var req insbuyReq.ManualDataReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insStoreService.ManualDataList(req)
	response.ResultErr(resp, err, c)
}

// OrgExport 店部门导出
// @Summary 店部门导出
// @Security ApiKeyAuth
// @Description 店部门导出
// @Tags 店铺相关
// @Accept application/www-form-urlencoded
// @Produce json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insStore/orgExport [get]
func (insStoreApi *InsStoreApi) OrgExport(c *gin.Context) {
	export, err := organizationService.ExportOrganization()
	if err != nil {
		response.ResultErr(export, err, c)
		return
	}
	res := make([]insbuyResp.OrganizationExport, len(export))
	for i, v := range export {
		res[i] = insbuyResp.OrganizationExport{
			StoreName:    v.StoreName,
			OrgId:        v.OrgId,
			OrgName:      v.OrgName,
			ChildOrgId:   v.ChildOrgId,
			ChildOrgName: v.ChildOrgName,
		}
	}
	_, e := insImportService.ExcelCommonList(c, insbuy.ETDeptList.ToInt(), res)
	if e != nil {
		return
	}
	return
}
