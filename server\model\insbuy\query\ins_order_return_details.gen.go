// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderReturnDetails(db *gorm.DB, opts ...gen.DOOption) insOrderReturnDetails {
	_insOrderReturnDetails := insOrderReturnDetails{}

	_insOrderReturnDetails.insOrderReturnDetailsDo.UseDB(db, opts...)
	_insOrderReturnDetails.insOrderReturnDetailsDo.UseModel(&insbuy.InsOrderReturnDetails{})

	tableName := _insOrderReturnDetails.insOrderReturnDetailsDo.TableName()
	_insOrderReturnDetails.ALL = field.NewAsterisk(tableName)
	_insOrderReturnDetails.ID = field.NewUint(tableName, "id")
	_insOrderReturnDetails.ReturnId = field.NewUint(tableName, "return_id")
	_insOrderReturnDetails.OrderDetailsId = field.NewUint64(tableName, "order_details_id")
	_insOrderReturnDetails.OrderId = field.NewUint64(tableName, "order_id")
	_insOrderReturnDetails.ProductName = field.NewString(tableName, "product_name")
	_insOrderReturnDetails.Num = field.NewInt(tableName, "num")
	_insOrderReturnDetails.ProductId = field.NewUint(tableName, "product_id")
	_insOrderReturnDetails.Unit = field.NewUint(tableName, "unit")
	_insOrderReturnDetails.Price = field.NewFloat64(tableName, "price")
	_insOrderReturnDetails.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderReturnDetails.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderReturnDetails.BusinessDay = field.NewTime(tableName, "business_day")

	_insOrderReturnDetails.fillFieldMap()

	return _insOrderReturnDetails
}

type insOrderReturnDetails struct {
	insOrderReturnDetailsDo

	ALL            field.Asterisk
	ID             field.Uint
	ReturnId       field.Uint
	OrderDetailsId field.Uint64
	OrderId        field.Uint64
	ProductName    field.String
	Num            field.Int
	ProductId      field.Uint
	Unit           field.Uint
	Price          field.Float64
	CreatedAt      field.Time
	UpdatedAt      field.Time
	BusinessDay    field.Time

	fieldMap map[string]field.Expr
}

func (i insOrderReturnDetails) Table(newTableName string) *insOrderReturnDetails {
	i.insOrderReturnDetailsDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderReturnDetails) As(alias string) *insOrderReturnDetails {
	i.insOrderReturnDetailsDo.DO = *(i.insOrderReturnDetailsDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderReturnDetails) updateTableName(table string) *insOrderReturnDetails {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.ReturnId = field.NewUint(table, "return_id")
	i.OrderDetailsId = field.NewUint64(table, "order_details_id")
	i.OrderId = field.NewUint64(table, "order_id")
	i.ProductName = field.NewString(table, "product_name")
	i.Num = field.NewInt(table, "num")
	i.ProductId = field.NewUint(table, "product_id")
	i.Unit = field.NewUint(table, "unit")
	i.Price = field.NewFloat64(table, "price")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insOrderReturnDetails) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderReturnDetails) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["return_id"] = i.ReturnId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["num"] = i.Num
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["price"] = i.Price
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insOrderReturnDetails) clone(db *gorm.DB) insOrderReturnDetails {
	i.insOrderReturnDetailsDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderReturnDetails) replaceDB(db *gorm.DB) insOrderReturnDetails {
	i.insOrderReturnDetailsDo.ReplaceDB(db)
	return i
}

type insOrderReturnDetailsDo struct{ gen.DO }

func (i insOrderReturnDetailsDo) Debug() *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderReturnDetailsDo) WithContext(ctx context.Context) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderReturnDetailsDo) ReadDB() *insOrderReturnDetailsDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderReturnDetailsDo) WriteDB() *insOrderReturnDetailsDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderReturnDetailsDo) Session(config *gorm.Session) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderReturnDetailsDo) Clauses(conds ...clause.Expression) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderReturnDetailsDo) Returning(value interface{}, columns ...string) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderReturnDetailsDo) Not(conds ...gen.Condition) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderReturnDetailsDo) Or(conds ...gen.Condition) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderReturnDetailsDo) Select(conds ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderReturnDetailsDo) Where(conds ...gen.Condition) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderReturnDetailsDo) Order(conds ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderReturnDetailsDo) Distinct(cols ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderReturnDetailsDo) Omit(cols ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderReturnDetailsDo) Join(table schema.Tabler, on ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderReturnDetailsDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderReturnDetailsDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderReturnDetailsDo) Group(cols ...field.Expr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderReturnDetailsDo) Having(conds ...gen.Condition) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderReturnDetailsDo) Limit(limit int) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderReturnDetailsDo) Offset(offset int) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderReturnDetailsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderReturnDetailsDo) Unscoped() *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderReturnDetailsDo) Create(values ...*insbuy.InsOrderReturnDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderReturnDetailsDo) CreateInBatches(values []*insbuy.InsOrderReturnDetails, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderReturnDetailsDo) Save(values ...*insbuy.InsOrderReturnDetails) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderReturnDetailsDo) First() (*insbuy.InsOrderReturnDetails, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturnDetails), nil
	}
}

func (i insOrderReturnDetailsDo) Take() (*insbuy.InsOrderReturnDetails, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturnDetails), nil
	}
}

func (i insOrderReturnDetailsDo) Last() (*insbuy.InsOrderReturnDetails, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturnDetails), nil
	}
}

func (i insOrderReturnDetailsDo) Find() ([]*insbuy.InsOrderReturnDetails, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderReturnDetails), err
}

func (i insOrderReturnDetailsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderReturnDetails, err error) {
	buf := make([]*insbuy.InsOrderReturnDetails, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderReturnDetailsDo) FindInBatches(result *[]*insbuy.InsOrderReturnDetails, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderReturnDetailsDo) Attrs(attrs ...field.AssignExpr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderReturnDetailsDo) Assign(attrs ...field.AssignExpr) *insOrderReturnDetailsDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderReturnDetailsDo) Joins(fields ...field.RelationField) *insOrderReturnDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderReturnDetailsDo) Preload(fields ...field.RelationField) *insOrderReturnDetailsDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderReturnDetailsDo) FirstOrInit() (*insbuy.InsOrderReturnDetails, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturnDetails), nil
	}
}

func (i insOrderReturnDetailsDo) FirstOrCreate() (*insbuy.InsOrderReturnDetails, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturnDetails), nil
	}
}

func (i insOrderReturnDetailsDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderReturnDetails, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderReturnDetailsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderReturnDetailsDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderReturnDetailsDo) Delete(models ...*insbuy.InsOrderReturnDetails) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderReturnDetailsDo) withDO(do gen.Dao) *insOrderReturnDetailsDo {
	i.DO = *do.(*gen.DO)
	return i
}
