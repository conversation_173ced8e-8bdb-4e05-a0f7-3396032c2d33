// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsLangTranslation(db *gorm.DB, opts ...gen.DOOption) insLangTranslation {
	_insLangTranslation := insLangTranslation{}

	_insLangTranslation.insLangTranslationDo.UseDB(db, opts...)
	_insLangTranslation.insLangTranslationDo.UseModel(&insbuy.InsLangTranslation{})

	tableName := _insLangTranslation.insLangTranslationDo.TableName()
	_insLangTranslation.ALL = field.NewAsterisk(tableName)
	_insLangTranslation.ID = field.NewUint(tableName, "id")
	_insLangTranslation.CreatedAt = field.NewTime(tableName, "created_at")
	_insLangTranslation.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insLangTranslation.DeletedAt = field.NewField(tableName, "deleted_at")
	_insLangTranslation.StoreId = field.NewUint(tableName, "store_id")
	_insLangTranslation.TargetID = field.NewUint64(tableName, "target_id")
	_insLangTranslation.TargetType = field.NewString(tableName, "target_type")
	_insLangTranslation.Language = field.NewString(tableName, "language")
	_insLangTranslation.Data = field.NewField(tableName, "data")

	_insLangTranslation.fillFieldMap()

	return _insLangTranslation
}

type insLangTranslation struct {
	insLangTranslationDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StoreId    field.Uint
	TargetID   field.Uint64
	TargetType field.String
	Language   field.String
	Data       field.Field

	fieldMap map[string]field.Expr
}

func (i insLangTranslation) Table(newTableName string) *insLangTranslation {
	i.insLangTranslationDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insLangTranslation) As(alias string) *insLangTranslation {
	i.insLangTranslationDo.DO = *(i.insLangTranslationDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insLangTranslation) updateTableName(table string) *insLangTranslation {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.TargetID = field.NewUint64(table, "target_id")
	i.TargetType = field.NewString(table, "target_type")
	i.Language = field.NewString(table, "language")
	i.Data = field.NewField(table, "data")

	i.fillFieldMap()

	return i
}

func (i *insLangTranslation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insLangTranslation) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["target_id"] = i.TargetID
	i.fieldMap["target_type"] = i.TargetType
	i.fieldMap["language"] = i.Language
	i.fieldMap["data"] = i.Data
}

func (i insLangTranslation) clone(db *gorm.DB) insLangTranslation {
	i.insLangTranslationDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insLangTranslation) replaceDB(db *gorm.DB) insLangTranslation {
	i.insLangTranslationDo.ReplaceDB(db)
	return i
}

type insLangTranslationDo struct{ gen.DO }

func (i insLangTranslationDo) Debug() *insLangTranslationDo {
	return i.withDO(i.DO.Debug())
}

func (i insLangTranslationDo) WithContext(ctx context.Context) *insLangTranslationDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insLangTranslationDo) ReadDB() *insLangTranslationDo {
	return i.Clauses(dbresolver.Read)
}

func (i insLangTranslationDo) WriteDB() *insLangTranslationDo {
	return i.Clauses(dbresolver.Write)
}

func (i insLangTranslationDo) Session(config *gorm.Session) *insLangTranslationDo {
	return i.withDO(i.DO.Session(config))
}

func (i insLangTranslationDo) Clauses(conds ...clause.Expression) *insLangTranslationDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insLangTranslationDo) Returning(value interface{}, columns ...string) *insLangTranslationDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insLangTranslationDo) Not(conds ...gen.Condition) *insLangTranslationDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insLangTranslationDo) Or(conds ...gen.Condition) *insLangTranslationDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insLangTranslationDo) Select(conds ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insLangTranslationDo) Where(conds ...gen.Condition) *insLangTranslationDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insLangTranslationDo) Order(conds ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insLangTranslationDo) Distinct(cols ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insLangTranslationDo) Omit(cols ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insLangTranslationDo) Join(table schema.Tabler, on ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insLangTranslationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insLangTranslationDo) RightJoin(table schema.Tabler, on ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insLangTranslationDo) Group(cols ...field.Expr) *insLangTranslationDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insLangTranslationDo) Having(conds ...gen.Condition) *insLangTranslationDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insLangTranslationDo) Limit(limit int) *insLangTranslationDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insLangTranslationDo) Offset(offset int) *insLangTranslationDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insLangTranslationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insLangTranslationDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insLangTranslationDo) Unscoped() *insLangTranslationDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insLangTranslationDo) Create(values ...*insbuy.InsLangTranslation) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insLangTranslationDo) CreateInBatches(values []*insbuy.InsLangTranslation, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insLangTranslationDo) Save(values ...*insbuy.InsLangTranslation) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insLangTranslationDo) First() (*insbuy.InsLangTranslation, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangTranslation), nil
	}
}

func (i insLangTranslationDo) Take() (*insbuy.InsLangTranslation, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangTranslation), nil
	}
}

func (i insLangTranslationDo) Last() (*insbuy.InsLangTranslation, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangTranslation), nil
	}
}

func (i insLangTranslationDo) Find() ([]*insbuy.InsLangTranslation, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsLangTranslation), err
}

func (i insLangTranslationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsLangTranslation, err error) {
	buf := make([]*insbuy.InsLangTranslation, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insLangTranslationDo) FindInBatches(result *[]*insbuy.InsLangTranslation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insLangTranslationDo) Attrs(attrs ...field.AssignExpr) *insLangTranslationDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insLangTranslationDo) Assign(attrs ...field.AssignExpr) *insLangTranslationDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insLangTranslationDo) Joins(fields ...field.RelationField) *insLangTranslationDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insLangTranslationDo) Preload(fields ...field.RelationField) *insLangTranslationDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insLangTranslationDo) FirstOrInit() (*insbuy.InsLangTranslation, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangTranslation), nil
	}
}

func (i insLangTranslationDo) FirstOrCreate() (*insbuy.InsLangTranslation, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsLangTranslation), nil
	}
}

func (i insLangTranslationDo) FindByPage(offset int, limit int) (result []*insbuy.InsLangTranslation, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insLangTranslationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insLangTranslationDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insLangTranslationDo) Delete(models ...*insbuy.InsLangTranslation) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insLangTranslationDo) withDO(do gen.Dao) *insLangTranslationDo {
	i.DO = *do.(*gen.DO)
	return i
}
