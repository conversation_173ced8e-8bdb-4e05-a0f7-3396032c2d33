// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderAdjustmentDetail(db *gorm.DB, opts ...gen.DOOption) insOrderAdjustmentDetail {
	_insOrderAdjustmentDetail := insOrderAdjustmentDetail{}

	_insOrderAdjustmentDetail.insOrderAdjustmentDetailDo.UseDB(db, opts...)
	_insOrderAdjustmentDetail.insOrderAdjustmentDetailDo.UseModel(&insbuy.InsOrderAdjustmentDetail{})

	tableName := _insOrderAdjustmentDetail.insOrderAdjustmentDetailDo.TableName()
	_insOrderAdjustmentDetail.ALL = field.NewAsterisk(tableName)
	_insOrderAdjustmentDetail.ID = field.NewUint(tableName, "id")
	_insOrderAdjustmentDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderAdjustmentDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderAdjustmentDetail.AdjustmentId = field.NewUint(tableName, "adjustment_id")
	_insOrderAdjustmentDetail.RelationId = field.NewUint64(tableName, "relation_id")
	_insOrderAdjustmentDetail.Ext = field.NewField(tableName, "ext")

	_insOrderAdjustmentDetail.fillFieldMap()

	return _insOrderAdjustmentDetail
}

type insOrderAdjustmentDetail struct {
	insOrderAdjustmentDetailDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	AdjustmentId field.Uint
	RelationId   field.Uint64
	Ext          field.Field

	fieldMap map[string]field.Expr
}

func (i insOrderAdjustmentDetail) Table(newTableName string) *insOrderAdjustmentDetail {
	i.insOrderAdjustmentDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderAdjustmentDetail) As(alias string) *insOrderAdjustmentDetail {
	i.insOrderAdjustmentDetailDo.DO = *(i.insOrderAdjustmentDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderAdjustmentDetail) updateTableName(table string) *insOrderAdjustmentDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.AdjustmentId = field.NewUint(table, "adjustment_id")
	i.RelationId = field.NewUint64(table, "relation_id")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insOrderAdjustmentDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderAdjustmentDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["adjustment_id"] = i.AdjustmentId
	i.fieldMap["relation_id"] = i.RelationId
	i.fieldMap["ext"] = i.Ext
}

func (i insOrderAdjustmentDetail) clone(db *gorm.DB) insOrderAdjustmentDetail {
	i.insOrderAdjustmentDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderAdjustmentDetail) replaceDB(db *gorm.DB) insOrderAdjustmentDetail {
	i.insOrderAdjustmentDetailDo.ReplaceDB(db)
	return i
}

type insOrderAdjustmentDetailDo struct{ gen.DO }

func (i insOrderAdjustmentDetailDo) Debug() *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderAdjustmentDetailDo) WithContext(ctx context.Context) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderAdjustmentDetailDo) ReadDB() *insOrderAdjustmentDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderAdjustmentDetailDo) WriteDB() *insOrderAdjustmentDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderAdjustmentDetailDo) Session(config *gorm.Session) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderAdjustmentDetailDo) Clauses(conds ...clause.Expression) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderAdjustmentDetailDo) Returning(value interface{}, columns ...string) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderAdjustmentDetailDo) Not(conds ...gen.Condition) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderAdjustmentDetailDo) Or(conds ...gen.Condition) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderAdjustmentDetailDo) Select(conds ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderAdjustmentDetailDo) Where(conds ...gen.Condition) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderAdjustmentDetailDo) Order(conds ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderAdjustmentDetailDo) Distinct(cols ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderAdjustmentDetailDo) Omit(cols ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderAdjustmentDetailDo) Join(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderAdjustmentDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderAdjustmentDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderAdjustmentDetailDo) Group(cols ...field.Expr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderAdjustmentDetailDo) Having(conds ...gen.Condition) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderAdjustmentDetailDo) Limit(limit int) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderAdjustmentDetailDo) Offset(offset int) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderAdjustmentDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderAdjustmentDetailDo) Unscoped() *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderAdjustmentDetailDo) Create(values ...*insbuy.InsOrderAdjustmentDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderAdjustmentDetailDo) CreateInBatches(values []*insbuy.InsOrderAdjustmentDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderAdjustmentDetailDo) Save(values ...*insbuy.InsOrderAdjustmentDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderAdjustmentDetailDo) First() (*insbuy.InsOrderAdjustmentDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustmentDetail), nil
	}
}

func (i insOrderAdjustmentDetailDo) Take() (*insbuy.InsOrderAdjustmentDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustmentDetail), nil
	}
}

func (i insOrderAdjustmentDetailDo) Last() (*insbuy.InsOrderAdjustmentDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustmentDetail), nil
	}
}

func (i insOrderAdjustmentDetailDo) Find() ([]*insbuy.InsOrderAdjustmentDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderAdjustmentDetail), err
}

func (i insOrderAdjustmentDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderAdjustmentDetail, err error) {
	buf := make([]*insbuy.InsOrderAdjustmentDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderAdjustmentDetailDo) FindInBatches(result *[]*insbuy.InsOrderAdjustmentDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderAdjustmentDetailDo) Attrs(attrs ...field.AssignExpr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderAdjustmentDetailDo) Assign(attrs ...field.AssignExpr) *insOrderAdjustmentDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderAdjustmentDetailDo) Joins(fields ...field.RelationField) *insOrderAdjustmentDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderAdjustmentDetailDo) Preload(fields ...field.RelationField) *insOrderAdjustmentDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderAdjustmentDetailDo) FirstOrInit() (*insbuy.InsOrderAdjustmentDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustmentDetail), nil
	}
}

func (i insOrderAdjustmentDetailDo) FirstOrCreate() (*insbuy.InsOrderAdjustmentDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderAdjustmentDetail), nil
	}
}

func (i insOrderAdjustmentDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderAdjustmentDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderAdjustmentDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderAdjustmentDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderAdjustmentDetailDo) Delete(models ...*insbuy.InsOrderAdjustmentDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderAdjustmentDetailDo) withDO(do gen.Dao) *insOrderAdjustmentDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
