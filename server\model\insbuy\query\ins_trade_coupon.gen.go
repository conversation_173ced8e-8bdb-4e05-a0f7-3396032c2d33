// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeCoupon(db *gorm.DB, opts ...gen.DOOption) insTradeCoupon {
	_insTradeCoupon := insTradeCoupon{}

	_insTradeCoupon.insTradeCouponDo.UseDB(db, opts...)
	_insTradeCoupon.insTradeCouponDo.UseModel(&insbuy.InsTradeCoupon{})

	tableName := _insTradeCoupon.insTradeCouponDo.TableName()
	_insTradeCoupon.ALL = field.NewAsterisk(tableName)
	_insTradeCoupon.ID = field.NewUint(tableName, "id")
	_insTradeCoupon.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeCoupon.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeCoupon.CouponType = field.NewInt(tableName, "coupon_type")
	_insTradeCoupon.CouponCode = field.NewString(tableName, "coupon_code")
	_insTradeCoupon.CouponAmount = field.NewFloat64(tableName, "coupon_amount")
	_insTradeCoupon.OperatorId = field.NewUint(tableName, "operator_id")
	_insTradeCoupon.TradeId = field.NewUint64(tableName, "trade_id")
	_insTradeCoupon.Status = field.NewInt(tableName, "status")

	_insTradeCoupon.fillFieldMap()

	return _insTradeCoupon
}

type insTradeCoupon struct {
	insTradeCouponDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	CouponType   field.Int
	CouponCode   field.String
	CouponAmount field.Float64
	OperatorId   field.Uint
	TradeId      field.Uint64
	Status       field.Int

	fieldMap map[string]field.Expr
}

func (i insTradeCoupon) Table(newTableName string) *insTradeCoupon {
	i.insTradeCouponDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeCoupon) As(alias string) *insTradeCoupon {
	i.insTradeCouponDo.DO = *(i.insTradeCouponDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeCoupon) updateTableName(table string) *insTradeCoupon {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.CouponType = field.NewInt(table, "coupon_type")
	i.CouponCode = field.NewString(table, "coupon_code")
	i.CouponAmount = field.NewFloat64(table, "coupon_amount")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.Status = field.NewInt(table, "status")

	i.fillFieldMap()

	return i
}

func (i *insTradeCoupon) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeCoupon) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["coupon_type"] = i.CouponType
	i.fieldMap["coupon_code"] = i.CouponCode
	i.fieldMap["coupon_amount"] = i.CouponAmount
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["status"] = i.Status
}

func (i insTradeCoupon) clone(db *gorm.DB) insTradeCoupon {
	i.insTradeCouponDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeCoupon) replaceDB(db *gorm.DB) insTradeCoupon {
	i.insTradeCouponDo.ReplaceDB(db)
	return i
}

type insTradeCouponDo struct{ gen.DO }

func (i insTradeCouponDo) Debug() *insTradeCouponDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeCouponDo) WithContext(ctx context.Context) *insTradeCouponDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeCouponDo) ReadDB() *insTradeCouponDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeCouponDo) WriteDB() *insTradeCouponDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeCouponDo) Session(config *gorm.Session) *insTradeCouponDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeCouponDo) Clauses(conds ...clause.Expression) *insTradeCouponDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeCouponDo) Returning(value interface{}, columns ...string) *insTradeCouponDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeCouponDo) Not(conds ...gen.Condition) *insTradeCouponDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeCouponDo) Or(conds ...gen.Condition) *insTradeCouponDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeCouponDo) Select(conds ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeCouponDo) Where(conds ...gen.Condition) *insTradeCouponDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeCouponDo) Order(conds ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeCouponDo) Distinct(cols ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeCouponDo) Omit(cols ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeCouponDo) Join(table schema.Tabler, on ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeCouponDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeCouponDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeCouponDo) Group(cols ...field.Expr) *insTradeCouponDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeCouponDo) Having(conds ...gen.Condition) *insTradeCouponDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeCouponDo) Limit(limit int) *insTradeCouponDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeCouponDo) Offset(offset int) *insTradeCouponDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeCouponDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeCouponDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeCouponDo) Unscoped() *insTradeCouponDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeCouponDo) Create(values ...*insbuy.InsTradeCoupon) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeCouponDo) CreateInBatches(values []*insbuy.InsTradeCoupon, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeCouponDo) Save(values ...*insbuy.InsTradeCoupon) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeCouponDo) First() (*insbuy.InsTradeCoupon, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCoupon), nil
	}
}

func (i insTradeCouponDo) Take() (*insbuy.InsTradeCoupon, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCoupon), nil
	}
}

func (i insTradeCouponDo) Last() (*insbuy.InsTradeCoupon, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCoupon), nil
	}
}

func (i insTradeCouponDo) Find() ([]*insbuy.InsTradeCoupon, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeCoupon), err
}

func (i insTradeCouponDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeCoupon, err error) {
	buf := make([]*insbuy.InsTradeCoupon, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeCouponDo) FindInBatches(result *[]*insbuy.InsTradeCoupon, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeCouponDo) Attrs(attrs ...field.AssignExpr) *insTradeCouponDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeCouponDo) Assign(attrs ...field.AssignExpr) *insTradeCouponDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeCouponDo) Joins(fields ...field.RelationField) *insTradeCouponDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeCouponDo) Preload(fields ...field.RelationField) *insTradeCouponDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeCouponDo) FirstOrInit() (*insbuy.InsTradeCoupon, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCoupon), nil
	}
}

func (i insTradeCouponDo) FirstOrCreate() (*insbuy.InsTradeCoupon, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCoupon), nil
	}
}

func (i insTradeCouponDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeCoupon, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeCouponDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeCouponDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeCouponDo) Delete(models ...*insbuy.InsTradeCoupon) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeCouponDo) withDO(do gen.Dao) *insTradeCouponDo {
	i.DO = *do.(*gen.DO)
	return i
}
