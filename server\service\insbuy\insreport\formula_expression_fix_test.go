package insreport

import (
	"fmt"
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"github.com/stretchr/testify/assert"
)

func TestFormulaExpressionFix(t *testing.T) {
	// 创建测试配置
	configs := []CostTypeConfigItem{
		{Id: 48, CategoryName: "收入类", IsCalculated: false},
		{Id: 49, CategoryName: "支出类", IsCalculated: false},
		{Id: 52, CategoryName: "大楼房租", IsCalculated: false},
		{Id: 53, CategoryName: "利润率", IsCalculated: true, CalculationFormula: `{"expression": "(#48 - #49) / #48 * #52", "references": [48, 49, 48, 52], "description": "计算利润率666"}`},
	}

	// 创建处理器
	processor := NewSimpleJSONProcessor(configs)

	// 创建测试数据
	dataMap := map[uint]*FinancialSummaryItem{
		48: {
			TotalAmount: jtypes.JPrice(100000), // 收入类：10万
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": jtypes.JPrice(30000),
				"2025-02": jtypes.JPrice(35000),
				"2025-03": jtypes.JPrice(35000),
			},
		},
		49: {
			TotalAmount: jtypes.JPrice(60000), // 支出类：6万
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": jtypes.JPrice(18000),
				"2025-02": jtypes.JPrice(21000),
				"2025-03": jtypes.JPrice(21000),
			},
		},
		52: {
			TotalAmount: jtypes.JPrice(100), // 大楼房租：100（作为百分比系数）
			MonthlyAmounts: map[string]jtypes.JPrice{
				"2025-01": jtypes.JPrice(100),
				"2025-02": jtypes.JPrice(100),
				"2025-03": jtypes.JPrice(100),
			},
		},
	}

	timeColumns := []string{"2025-01", "2025-02", "2025-03"}

	// 测试表达式解析
	t.Run("测试复合表达式解析", func(t *testing.T) {
		// 找到利润率配置
		var profitRateConfig CostTypeConfigItem
		for _, config := range configs {
			if config.Id == 53 {
				profitRateConfig = config
				break
			}
		}

		// 执行计算
		result := processor.executeSimpleFormulaCalculation(profitRateConfig, dataMap, timeColumns)

		// 验证结果不为空
		assert.NotNil(t, result, "计算结果不应为空")

		if result != nil {
			// 验证计算逻辑：(收入类 - 支出类) / 收入类 * 大楼房租
			// (100000 - 60000) / 100000 * 100 = 40000 / 100000 * 100 = 0.4 * 100 = 40
			expectedTotal := jtypes.JPrice(4000) // 40 * 100 (因为JPrice是以分为单位)
			
			fmt.Printf("实际总计: %v, 期望总计: %v\n", result.TotalAmount, expectedTotal)
			
			// 验证月度数据
			for _, month := range timeColumns {
				fmt.Printf("月份 %s: %v\n", month, result.MonthlyAmounts[month])
			}
		}
	})

	// 测试括号处理
	t.Run("测试括号处理", func(t *testing.T) {
		// 测试简单括号表达式
		expr := "(#48 - #49)"
		result, err := processor.calculateBasicExpression(expr, dataMap, timeColumns)
		
		assert.NoError(t, err, "括号表达式解析应该成功")
		assert.NotNil(t, result, "括号表达式结果不应为空")
		
		if result != nil {
			// 验证结果：100000 - 60000 = 40000
			expectedTotal := jtypes.JPrice(40000)
			assert.Equal(t, expectedTotal, result.TotalAmount, "括号表达式计算结果应该正确")
		}
	})

	// 测试运算符优先级
	t.Run("测试运算符优先级", func(t *testing.T) {
		// 测试乘除法优先级高于加减法
		expr := "#48 + #49 * #52"
		result, err := processor.calculateBasicExpression(expr, dataMap, timeColumns)
		
		assert.NoError(t, err, "运算符优先级表达式解析应该成功")
		assert.NotNil(t, result, "运算符优先级表达式结果不应为空")
		
		if result != nil {
			// 验证结果：48 + (49 * 52) = 100000 + (60000 * 100) = 100000 + 6000000 = 6100000
			expectedTotal := jtypes.JPrice(6100000)
			assert.Equal(t, expectedTotal, result.TotalAmount, "运算符优先级计算结果应该正确")
		}
	})

	// 测试单个操作数解析
	t.Run("测试单个操作数解析", func(t *testing.T) {
		// 测试ID引用
		result, err := processor.calculateSingleOperand("#48", dataMap, timeColumns)
		assert.NoError(t, err, "ID引用解析应该成功")
		assert.NotNil(t, result, "ID引用结果不应为空")
		
		if result != nil {
			assert.Equal(t, jtypes.JPrice(100000), result.TotalAmount, "ID引用结果应该正确")
		}
	})

	// 测试错误处理
	t.Run("测试错误处理", func(t *testing.T) {
		// 测试不存在的ID
		_, err := processor.calculateSingleOperand("#999", dataMap, timeColumns)
		assert.Error(t, err, "不存在的ID应该返回错误")
		
		// 测试无效的ID格式
		_, err = processor.calculateSingleOperand("#abc", dataMap, timeColumns)
		assert.Error(t, err, "无效的ID格式应该返回错误")
		
		// 测试不平衡的括号
		_, err = processor.calculateSingleOperand("(#48", dataMap, timeColumns)
		assert.Error(t, err, "不平衡的括号应该返回错误")
	})
}

func TestFindOperatorOutsideParentheses(t *testing.T) {
	configs := []CostTypeConfigItem{}
	processor := NewSimpleJSONProcessor(configs)

	// 测试在括号外查找运算符
	testCases := []struct {
		expression string
		operators  []string
		expected   int
	}{
		{"(#48 - #49) / #48 * #52", []string{"+"}, -1},           // 没有加号
		{"(#48 - #49) / #48 * #52", []string{"+", "-"}, -1},      // 减号在括号内
		{"(#48 - #49) / #48 * #52", []string{"*", "/"}, 16},      // 最后一个乘号
		{"(#48 - #49) / #48 * #52", []string{"/"}, 12},           // 除号
		{"#48 + #49 * #52", []string{"+", "-"}, 4},               // 加号
		{"#48 + #49 * #52", []string{"*", "/"}, 12},              // 乘号
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("表达式: %s, 运算符: %v", tc.expression, tc.operators), func(t *testing.T) {
			result := processor.findOperatorOutsideParentheses(tc.expression, tc.operators)
			assert.Equal(t, tc.expected, result, "运算符位置应该正确")
		})
	}
}

func TestIsBalancedParentheses(t *testing.T) {
	configs := []CostTypeConfigItem{}
	processor := NewSimpleJSONProcessor(configs)

	testCases := []struct {
		expression string
		expected   bool
	}{
		{"(#48 - #49)", true},
		{"((#48 - #49) / #48)", true},
		{"(#48 - #49) / #48", true},
		{"(#48", false},
		{"#48)", false},
		{"((#48)", false},
		{"(#48))", false},
		{"", true},
		{"#48", true},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("表达式: %s", tc.expression), func(t *testing.T) {
			result := processor.isBalancedParentheses(tc.expression)
			assert.Equal(t, tc.expected, result, "括号平衡检查应该正确")
		})
	}
}
