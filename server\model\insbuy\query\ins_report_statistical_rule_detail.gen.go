// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportStatisticalRuleDetail(db *gorm.DB, opts ...gen.DOOption) insReportStatisticalRuleDetail {
	_insReportStatisticalRuleDetail := insReportStatisticalRuleDetail{}

	_insReportStatisticalRuleDetail.insReportStatisticalRuleDetailDo.UseDB(db, opts...)
	_insReportStatisticalRuleDetail.insReportStatisticalRuleDetailDo.UseModel(&insbuy.InsReportStatisticalRuleDetail{})

	tableName := _insReportStatisticalRuleDetail.insReportStatisticalRuleDetailDo.TableName()
	_insReportStatisticalRuleDetail.ALL = field.NewAsterisk(tableName)
	_insReportStatisticalRuleDetail.ID = field.NewUint(tableName, "id")
	_insReportStatisticalRuleDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportStatisticalRuleDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportStatisticalRuleDetail.RuleId = field.NewUint(tableName, "rule_id")
	_insReportStatisticalRuleDetail.DictValue = field.NewUint(tableName, "dict_value")
	_insReportStatisticalRuleDetail.RelationId = field.NewUint(tableName, "relation_id")

	_insReportStatisticalRuleDetail.fillFieldMap()

	return _insReportStatisticalRuleDetail
}

type insReportStatisticalRuleDetail struct {
	insReportStatisticalRuleDetailDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	RuleId     field.Uint
	DictValue  field.Uint
	RelationId field.Uint

	fieldMap map[string]field.Expr
}

func (i insReportStatisticalRuleDetail) Table(newTableName string) *insReportStatisticalRuleDetail {
	i.insReportStatisticalRuleDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportStatisticalRuleDetail) As(alias string) *insReportStatisticalRuleDetail {
	i.insReportStatisticalRuleDetailDo.DO = *(i.insReportStatisticalRuleDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportStatisticalRuleDetail) updateTableName(table string) *insReportStatisticalRuleDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.RuleId = field.NewUint(table, "rule_id")
	i.DictValue = field.NewUint(table, "dict_value")
	i.RelationId = field.NewUint(table, "relation_id")

	i.fillFieldMap()

	return i
}

func (i *insReportStatisticalRuleDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportStatisticalRuleDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["dict_value"] = i.DictValue
	i.fieldMap["relation_id"] = i.RelationId
}

func (i insReportStatisticalRuleDetail) clone(db *gorm.DB) insReportStatisticalRuleDetail {
	i.insReportStatisticalRuleDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportStatisticalRuleDetail) replaceDB(db *gorm.DB) insReportStatisticalRuleDetail {
	i.insReportStatisticalRuleDetailDo.ReplaceDB(db)
	return i
}

type insReportStatisticalRuleDetailDo struct{ gen.DO }

func (i insReportStatisticalRuleDetailDo) Debug() *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportStatisticalRuleDetailDo) WithContext(ctx context.Context) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportStatisticalRuleDetailDo) ReadDB() *insReportStatisticalRuleDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportStatisticalRuleDetailDo) WriteDB() *insReportStatisticalRuleDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportStatisticalRuleDetailDo) Session(config *gorm.Session) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportStatisticalRuleDetailDo) Clauses(conds ...clause.Expression) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportStatisticalRuleDetailDo) Returning(value interface{}, columns ...string) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportStatisticalRuleDetailDo) Not(conds ...gen.Condition) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportStatisticalRuleDetailDo) Or(conds ...gen.Condition) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportStatisticalRuleDetailDo) Select(conds ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportStatisticalRuleDetailDo) Where(conds ...gen.Condition) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportStatisticalRuleDetailDo) Order(conds ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportStatisticalRuleDetailDo) Distinct(cols ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportStatisticalRuleDetailDo) Omit(cols ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportStatisticalRuleDetailDo) Join(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportStatisticalRuleDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportStatisticalRuleDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportStatisticalRuleDetailDo) Group(cols ...field.Expr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportStatisticalRuleDetailDo) Having(conds ...gen.Condition) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportStatisticalRuleDetailDo) Limit(limit int) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportStatisticalRuleDetailDo) Offset(offset int) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportStatisticalRuleDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportStatisticalRuleDetailDo) Unscoped() *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportStatisticalRuleDetailDo) Create(values ...*insbuy.InsReportStatisticalRuleDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportStatisticalRuleDetailDo) CreateInBatches(values []*insbuy.InsReportStatisticalRuleDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportStatisticalRuleDetailDo) Save(values ...*insbuy.InsReportStatisticalRuleDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportStatisticalRuleDetailDo) First() (*insbuy.InsReportStatisticalRuleDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRuleDetail), nil
	}
}

func (i insReportStatisticalRuleDetailDo) Take() (*insbuy.InsReportStatisticalRuleDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRuleDetail), nil
	}
}

func (i insReportStatisticalRuleDetailDo) Last() (*insbuy.InsReportStatisticalRuleDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRuleDetail), nil
	}
}

func (i insReportStatisticalRuleDetailDo) Find() ([]*insbuy.InsReportStatisticalRuleDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportStatisticalRuleDetail), err
}

func (i insReportStatisticalRuleDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportStatisticalRuleDetail, err error) {
	buf := make([]*insbuy.InsReportStatisticalRuleDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportStatisticalRuleDetailDo) FindInBatches(result *[]*insbuy.InsReportStatisticalRuleDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportStatisticalRuleDetailDo) Attrs(attrs ...field.AssignExpr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportStatisticalRuleDetailDo) Assign(attrs ...field.AssignExpr) *insReportStatisticalRuleDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportStatisticalRuleDetailDo) Joins(fields ...field.RelationField) *insReportStatisticalRuleDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportStatisticalRuleDetailDo) Preload(fields ...field.RelationField) *insReportStatisticalRuleDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportStatisticalRuleDetailDo) FirstOrInit() (*insbuy.InsReportStatisticalRuleDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRuleDetail), nil
	}
}

func (i insReportStatisticalRuleDetailDo) FirstOrCreate() (*insbuy.InsReportStatisticalRuleDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportStatisticalRuleDetail), nil
	}
}

func (i insReportStatisticalRuleDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportStatisticalRuleDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportStatisticalRuleDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportStatisticalRuleDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportStatisticalRuleDetailDo) Delete(models ...*insbuy.InsReportStatisticalRuleDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportStatisticalRuleDetailDo) withDO(do gen.Dao) *insReportStatisticalRuleDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
