package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsQueueApi struct {
}

// CreateQueueChannel 创建排队通道
// @Tags InsQueue
// @Summary 创建排队通道
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.QueueChannelReq true "创建排队通道"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insQueue/createQueueChannel [post]
func (InsQueueApi *InsQueueApi) CreateQueueChannel(c *gin.Context) {
	var req insbuyReq.QueueChannelReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.CreateQueueChannel(req); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// UpdateQueueChannel 修改排队通道
// @Tags InsQueue
// @Summary 修改排队通道
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.QueueChannelReq true "修改排队通道"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"修改成功"}"
// @Router /insQueue/updateQueueChannel [put]
func (InsQueueApi *InsQueueApi) UpdateQueueChannel(c *gin.Context) {
	var req insbuyReq.QueueChannelReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.UpdateQueueChannel(req); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage("修改失败", c)
	} else {
		response.OkWithMessage("修改成功", c)
	}
}

// DeleteQueueChannel 删除排队通道
// @Tags InsQueue
// @Summary 删除排队通道
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param channelId query int true "排队通道ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insQueue/deleteQueueChannel [delete]
func (InsQueueApi *InsQueueApi) DeleteQueueChannel(c *gin.Context) {
	var req insbuyReq.DeleteQueueChannelReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.DeleteQueueChannel(req); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// ChangeQueueChannelStatus 切换排队通道状态
// @Tags InsQueue
// @Summary 切换排队通道状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param channelId query int true "排队通道ID"
// @Param status query int true "状态 1:启用 0:禁用"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"切换成功"}"
// @Router /insQueue/changeQueueChannelStatus [put]
func (InsQueueApi *InsQueueApi) ChangeQueueChannelStatus(c *gin.Context) {
	var req insbuyReq.ChangeQueueChannelStatusReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChangeQueueChannelStatus(req); err != nil {
		global.GVA_LOG.Error("切换失败!", zap.Error(err))
		response.FailWithMessage("切换失败", c)
	} else {
		response.OkWithMessage("切换成功", c)
	}
}

// GetQueueChannelList 获取排队通道列表
// @Tags InsQueue
// @Summary 获取排队通道列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param storeId query int true "店铺ID"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insQueue/getQueueChannelList [get]
func (InsQueueApi *InsQueueApi) GetQueueChannelList(c *gin.Context) {
	var req insbuyReq.StoreQueueChannelListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := queueService.StoreQueueChannelList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ChannelEnqueueItem 通道取号
// @Tags InsQueue
// @Summary 通道取号
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelEnqueueItemReq true "通道取号"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"取号成功"}"
// @Router /insQueue/channelEnqueueItem [post]
func (InsQueueApi *InsQueueApi) ChannelEnqueueItem(c *gin.Context) {
	var req insbuyReq.ChannelEnqueueItemReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := queueService.ChannelEnqueueItem(req)
	if err != nil {
		global.GVA_LOG.Error("取号失败!", zap.Error(err))
		response.FailWithMessage("取号失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// ChannelSkipItem 过号
// @Tags InsQueue
// @Summary 过号
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelSkipItemReq true "过号"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"过号成功"}"
// @Router /insQueue/channelSkipItem [put]
func (InsQueueApi *InsQueueApi) ChannelSkipItem(c *gin.Context) {
	var req insbuyReq.ChannelSkipItemReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChannelSkipItem(req); err != nil {
		global.GVA_LOG.Error("过号失败!", zap.Error(err))
		response.FailWithMessage("过号失败", c)
	} else {
		response.OkWithMessage("过号成功", c)
	}
}

// ChannelCallItem 叫号
// @Tags InsQueue
// @Summary 叫号
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelCallItemReq true "叫号"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"叫号成功"}"
// @Router /insQueue/channelCallItem [post]
func (InsQueueApi *InsQueueApi) ChannelCallItem(c *gin.Context) {
	var req insbuyReq.ChannelCallItemReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChannelCallItem(req); err != nil {
		global.GVA_LOG.Error("叫号失败!", zap.Error(err))
		response.FailWithMessage("叫号失败", c)
	} else {
		response.OkWithMessage("叫号成功", c)
	}
}

// ChannelDining 就餐
// @Tags InsQueue
// @Summary 就餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelDiningReq true "就餐"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"就餐成功"}"
// @Router /insQueue/channelDining [post]
func (InsQueueApi *InsQueueApi) ChannelDining(c *gin.Context) {
	var req insbuyReq.ChannelDiningReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChannelDining(req); err != nil {
		global.GVA_LOG.Error("就餐失败!", zap.Error(err))
		response.FailWithMessage("就餐失败", c)
	} else {
		response.OkWithMessage("就餐成功", c)
	}
}

// ChannelCancelItem 取消
// @Tags InsQueue
// @Summary 取消
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelCancelItemReq true "取消"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"取消成功"}"
// @Router /insQueue/channelCancelItem [put]
func (InsQueueApi *InsQueueApi) ChannelCancelItem(c *gin.Context) {
	var req insbuyReq.ChannelCancelItemReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChannelCancelItem(req); err != nil {
		global.GVA_LOG.Error("取消失败!", zap.Error(err))
		response.FailWithMessage("取消失败", c)
	} else {
		response.OkWithMessage("取消成功", c)
	}
}

// ChannelPostpone 顺延
// @Tags InsQueue
// @Summary 顺延
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelPostponeReq true "顺延"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"顺延成功"}"
// @Router /insQueue/channelPostpone [put]
func (InsQueueApi *InsQueueApi) ChannelPostpone(c *gin.Context) {
	var req insbuyReq.ChannelPostponeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := queueService.ChannelPostpone(req); err != nil {
		global.GVA_LOG.Error("顺延失败!", zap.Error(err))
		response.FailWithMessage("顺延失败", c)
	} else {
		response.OkWithMessage("顺延成功", c)
	}
}

// ChannelQueueItemList 通道排队项列表
// @Tags InsQueue
// @Summary 通道排队项列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ChannelQueueItemListReq true "通道排队项列表"
// @Success 200 {object} response.Response{data=response.ChannelEnqueueItemListResp} "获取成功"
// @Router /insQueue/channelQueueItemList [get]
func (InsQueueApi *InsQueueApi) ChannelQueueItemList(c *gin.Context) {
	var req insbuyReq.ChannelQueueItemListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := queueService.ChannelQueueItemList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}
