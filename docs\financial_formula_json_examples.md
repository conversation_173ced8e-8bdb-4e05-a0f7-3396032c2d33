# 财务交叉统计报表 - JSON格式公式配置示例

## 概述

本文档展示了基于JSON格式的公式表达式系统的完整配置示例，包括数据结构、配置方法和API使用。

## JSON公式结构

### 1. 基础JSON格式

```json
{
  "version": "1.0",
  "expression": "[收入类合计] - [成本类合计]",
  "storage_expr": "#1 - #2",
  "references": [
    {
      "id": 1,
      "name": "收入类合计",
      "code": "INCOME_TOTAL",
      "ref_type": "direct",
      "is_valid": true
    },
    {
      "id": 2,
      "name": "成本类合计",
      "code": "COST_TOTAL",
      "ref_type": "direct",
      "is_valid": true
    }
  ],
  "metadata": {
    "created_at": "2025-01-01T10:00:00Z",
    "updated_at": "2025-01-01T10:00:00Z",
    "created_by": "admin",
    "updated_by": "admin",
    "description": "经营利润计算公式",
    "tags": ["利润", "核心指标"],
    "business_rule": "收入减去成本得到毛利润"
  }
}
```

### 2. 字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| `version` | string | 公式版本，用于兼容性管理 |
| `expression` | string | 用户友好的显示表达式（使用分类名称） |
| `storage_expr` | string | 系统内部存储表达式（使用分类ID） |
| `references` | array | 引用的分类列表 |
| `metadata` | object | 公式元数据信息 |

## 配置中心数据结构

### 1. 基础分类配置

```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "store_id": 0,
  "item_index": 1,
  "item_value": {
    "id": 1,
    "level": 1,
    "parent_id": 0,
    "category_name": "收入类合计",
    "category_code": "INCOME_TOTAL",
    "is_active": 1,
    "sort_order": 100,
    "is_calculated": 0,
    "calculation_formula": ""
  }
}
```

### 2. JSON格式计算型分类配置

```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "store_id": 0,
  "item_index": 10,
  "item_value": {
    "id": 100,
    "level": 1,
    "parent_id": 0,
    "category_name": "经营利润",
    "category_code": "OPERATING_PROFIT",
    "is_active": 1,
    "sort_order": 900,
    "is_calculated": 1,
    "calculation_formula": "{\"version\":\"1.0\",\"expression\":\"[收入类合计] - [成本类合计]\",\"storage_expr\":\"#1 - #2\",\"references\":[{\"id\":1,\"name\":\"收入类合计\",\"code\":\"INCOME_TOTAL\",\"ref_type\":\"direct\",\"is_valid\":true},{\"id\":2,\"name\":\"成本类合计\",\"code\":\"COST_TOTAL\",\"ref_type\":\"direct\",\"is_valid\":true}],\"metadata\":{\"created_at\":\"2025-01-01T10:00:00Z\",\"updated_at\":\"2025-01-01T10:00:00Z\",\"created_by\":\"admin\",\"updated_by\":\"admin\",\"description\":\"经营利润计算公式\",\"tags\":[\"利润\",\"核心指标\"],\"business_rule\":\"收入减去成本得到毛利润\"}}"
  }
}
```

## 完整业务场景示例

### 1. 损益表结构配置

#### 基础分类
```json
[
  {
    "id": 1,
    "category_name": "营业收入",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 100
  },
  {
    "id": 2,
    "category_name": "营业成本",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 200
  },
  {
    "id": 3,
    "category_name": "销售费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 400
  },
  {
    "id": 4,
    "category_name": "管理费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 500
  },
  {
    "id": 5,
    "category_name": "财务费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 600
  }
]
```

#### 计算型分类
```json
[
  {
    "id": 100,
    "category_name": "毛利润",
    "level": 1,
    "is_calculated": 1,
    "sort_order": 300,
    "calculation_formula": {
      "version": "1.0",
      "expression": "[营业收入] - [营业成本]",
      "storage_expr": "#1 - #2",
      "references": [
        {"id": 1, "name": "营业收入", "code": "REVENUE", "ref_type": "direct", "is_valid": true},
        {"id": 2, "name": "营业成本", "code": "COST", "ref_type": "direct", "is_valid": true}
      ],
      "metadata": {
        "description": "毛利润 = 营业收入 - 营业成本",
        "tags": ["利润", "基础指标"],
        "business_rule": "反映企业主营业务的盈利能力"
      }
    }
  },
  {
    "id": 101,
    "category_name": "期间费用合计",
    "level": 1,
    "is_calculated": 1,
    "sort_order": 700,
    "calculation_formula": {
      "version": "1.0",
      "expression": "SUM([销售费用], [管理费用], [财务费用])",
      "storage_expr": "SUM(#3, #4, #5)",
      "references": [
        {"id": 3, "name": "销售费用", "code": "SALES_EXPENSE", "ref_type": "direct", "is_valid": true},
        {"id": 4, "name": "管理费用", "code": "ADMIN_EXPENSE", "ref_type": "direct", "is_valid": true},
        {"id": 5, "name": "财务费用", "code": "FINANCE_EXPENSE", "ref_type": "direct", "is_valid": true}
      ],
      "metadata": {
        "description": "期间费用合计 = 销售费用 + 管理费用 + 财务费用",
        "tags": ["费用", "合计"],
        "business_rule": "企业在经营过程中发生的各项期间费用总和"
      }
    }
  },
  {
    "id": 102,
    "category_name": "营业利润",
    "level": 1,
    "is_calculated": 1,
    "sort_order": 800,
    "calculation_formula": {
      "version": "1.0",
      "expression": "[毛利润] - [期间费用合计]",
      "storage_expr": "#100 - #101",
      "references": [
        {"id": 100, "name": "毛利润", "code": "GROSS_PROFIT", "ref_type": "calculated", "is_valid": true},
        {"id": 101, "name": "期间费用合计", "code": "PERIOD_EXPENSE_TOTAL", "ref_type": "calculated", "is_valid": true}
      ],
      "metadata": {
        "description": "营业利润 = 毛利润 - 期间费用合计",
        "tags": ["利润", "核心指标"],
        "business_rule": "企业主营业务的最终盈利水平"
      }
    }
  },
  {
    "id": 103,
    "category_name": "毛利率",
    "level": 1,
    "is_calculated": 1,
    "sort_order": 900,
    "calculation_formula": {
      "version": "1.0",
      "expression": "PERCENTAGE([毛利润], [营业收入])",
      "storage_expr": "PERCENTAGE(#100, #1)",
      "references": [
        {"id": 100, "name": "毛利润", "code": "GROSS_PROFIT", "ref_type": "calculated", "is_valid": true},
        {"id": 1, "name": "营业收入", "code": "REVENUE", "ref_type": "direct", "is_valid": true}
      ],
      "metadata": {
        "description": "毛利率 = 毛利润 / 营业收入 × 100%",
        "tags": ["比率", "盈利能力"],
        "business_rule": "反映企业产品或服务的盈利能力"
      }
    }
  }
]
```

## API使用示例

### 1. 创建JSON格式公式

```go
// 创建公式处理器
configs := getCostTypeConfigs() // 获取分类配置
processor := NewFormulaJSONProcessor(configs)

// 创建新的公式
formulaJSON, err := processor.CreateFormulaJSON(
    "[营业收入] - [营业成本]",  // 显示表达式
    "毛利润计算公式",           // 描述
    "admin",                  // 创建人
    []string{"利润", "基础指标"}, // 标签
)
if err != nil {
    log.Printf("创建公式失败: %v", err)
    return
}

// 保存到配置中心
config := CostTypeConfigItem{
    Id: 100,
    CategoryName: "毛利润",
    IsCalculated: true,
    CalculationFormula: formulaJSON,
}
```

### 2. 更新JSON格式公式

```go
// 更新现有公式
updatedJSON, err := processor.UpdateFormulaJSON(
    existingFormulaJSON,      // 现有的JSON公式
    "[营业收入] - [营业成本] - [其他成本]", // 新的表达式
    "更新后的毛利润计算公式",    // 新描述
    "admin",                  // 更新人
    []string{"利润", "核心指标", "更新"}, // 新标签
)
```

### 3. 验证JSON格式公式

```go
// 验证公式
errors := processor.ValidateFormulaJSON(formulaJSON)
if len(errors) > 0 {
    for _, err := range errors {
        log.Printf("验证错误: %s - %s", err.Type, err.Message)
    }
}
```

### 4. 获取显示表达式

```go
// 获取用户友好的显示表达式
displayExpr, err := processor.GetDisplayExpression(formulaJSON)
if err != nil {
    log.Printf("获取显示表达式失败: %v", err)
    return
}

fmt.Printf("显示表达式: %s", displayExpr)
// 输出: 显示表达式: [营业收入] - [营业成本]
```

## 数据一致性保障

### 1. 引用完整性检查

```json
{
  "references": [
    {
      "id": 999,
      "name": "已删除的分类",
      "code": "DELETED_CATEGORY",
      "ref_type": "direct",
      "is_valid": false  // 标记为无效
    }
  ]
}
```

### 2. 自动修复机制

当分类名称发生变化时，系统会自动更新JSON中的引用信息：

```go
// 分类名称从"营业收入"改为"主营业务收入"
// 系统自动更新JSON中的引用
{
  "expression": "[主营业务收入] - [营业成本]",  // 自动更新显示表达式
  "storage_expr": "#1 - #2",                  // 存储表达式保持不变
  "references": [
    {
      "id": 1,
      "name": "主营业务收入",  // 自动更新名称
      "code": "REVENUE",
      "ref_type": "direct",
      "is_valid": true
    }
  ]
}
```

### 3. 损坏引用检测

```go
// 检测损坏的引用
brokenRefs := processor.DetectBrokenReferences(configs)
for _, ref := range brokenRefs {
    log.Printf("发现损坏引用: 分类[%s]中引用了不存在的分类ID[%d]", 
        ref.SourceCategoryName, ref.MissingCategoryId)
}
```

## 前端集成示例

### 1. 公式编辑器

```typescript
interface FormulaEditor {
  // 显示用户友好的表达式
  displayExpression: string;
  
  // 分类选择器
  availableCategories: Category[];
  
  // 验证结果
  validationErrors: ValidationError[];
}

// 保存公式时转换为JSON格式
const saveFormula = async (displayExpr: string) => {
  const response = await fetch('/api/formula/create', {
    method: 'POST',
    body: JSON.stringify({
      expression: displayExpr,
      description: '用户输入的描述',
      tags: ['自定义']
    })
  });
  
  const formulaJSON = await response.text();
  // 保存到配置中心
};
```

### 2. 公式显示组件

```tsx
const FormulaDisplay: React.FC<{formulaJSON: string}> = ({formulaJSON}) => {
  const [displayExpr, setDisplayExpr] = useState('');
  const [metadata, setMetadata] = useState(null);
  
  useEffect(() => {
    // 解析JSON并提取显示信息
    const formula = JSON.parse(formulaJSON);
    setDisplayExpr(formula.expression);
    setMetadata(formula.metadata);
  }, [formulaJSON]);
  
  return (
    <div>
      <div className="formula-expression">{displayExpr}</div>
      <div className="formula-description">{metadata?.description}</div>
      <div className="formula-tags">
        {metadata?.tags?.map(tag => <span key={tag}>{tag}</span>)}
      </div>
    </div>
  );
};
```

## 优势总结

### 1. 数据一致性
- **ID引用**：存储表达式使用稳定的ID引用，避免名称变更影响
- **引用跟踪**：完整记录所有引用关系，便于影响分析
- **自动修复**：分类名称变更时自动更新显示表达式

### 2. 用户友好性
- **直观显示**：用户看到的是易懂的分类名称
- **丰富元数据**：包含描述、标签、业务规则等信息
- **版本管理**：支持公式版本控制和兼容性管理

### 3. 系统可维护性
- **结构化存储**：JSON格式便于解析和处理
- **扩展性强**：易于添加新的元数据字段
- **调试友好**：完整的引用信息便于问题定位

这种JSON格式的设计既保证了数据的一致性和完整性，又提供了良好的用户体验和系统可维护性。
