[insbuy]2025/07/28 - 16:13:34.397	[31<PERSON><PERSON>r[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:14:43.368	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:16:11.540	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:20:29.439	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:23:34.226	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:35:18.293	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:35:18.505	[31merror[0m	insbuy/ins_contract.go:582	创建同步日志失败	{"traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "7C468A54-8745-2245-9675-08002B2077BF", "index": 1, "total": 4, "error": "创建同步日志失败: Error 1292 (22007): Incorrect datetime value: '0000-00-00' for column 'end_time' at row 1"}
[insbuy]2025/07/28 - 16:35:23.549	[31merror[0m	insbuy/ins_contract.go:582	创建同步日志失败	{"traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 2, "total": 4, "error": "创建同步日志失败: Error 1292 (22007): Incorrect datetime value: '0000-00-00' for column 'end_time' at row 1"}
[insbuy]2025/07/28 - 16:35:28.593	[31merror[0m	insbuy/ins_contract.go:582	创建同步日志失败	{"traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 3, "total": 4, "error": "创建同步日志失败: Error 1292 (22007): Incorrect datetime value: '0000-00-00' for column 'end_time' at row 1"}
[insbuy]2025/07/28 - 16:35:33.622	[31merror[0m	insbuy/ins_contract.go:582	创建同步日志失败	{"traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "7668795ab206be8b2a38e57b4ca4f0f7", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "index": 4, "total": 4, "error": "创建同步日志失败: Error 1292 (22007): Incorrect datetime value: '0000-00-00' for column 'end_time' at row 1"}
[insbuy]2025/07/28 - 16:35:33.623	[31merror[0m	insbuy/ins_contract_sync.go:101	每日全量同步任务失败	{"traceId": "1a8f33b184a8bb93a8b29c6132f1c460", "task": "DailyFullSyncTask", "error": "部分审批代码同步失败: 所有审批代码同步失败"}
[insbuy]2025/07/28 - 16:37:12.442	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:37:13.025	[31merror[0m	insfinance/contract.go:238	飞书API响应错误	{"request_id": "20250728163712A98B0B5F9AF282545E1E", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"20250728163712A98B0B5F9AF282545E1E\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=20250728163712A98B0B5F9AF282545E1E&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 16:37:13.025	[31merror[0m	insbuy/ins_contract.go:714	飞书API调用失败	{"traceId": "5daa6de1d6af3af7f5d02ecb432e6bc8", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "5daa6de1d6af3af7f5d02ecb432e6bc8", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "7C468A54-8745-2245-9675-08002B2077BF", "index": 1, "total": 4, "traceId": "5daa6de1d6af3af7f5d02ecb432e6bc8", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"20250728163712A98B0B5F9AF282545E1E\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=20250728163712A98B0B5F9AF282545E1E&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 16:44:38.799	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:44:39.160	[31merror[0m	insbuy/ins_contract.go:397	参数验证失败	{"traceId": "7bd395f3325fa8f46f4740c9c326112c", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "error": "结束时间不能早于开始时间"}
[insbuy]2025/07/28 - 16:44:39.160	[31merror[0m	insbuy/ins_contract_sync.go:66	全量合同同步失败	{"traceId": "db8a3f6e0f89e30e838b4e4d77d68c06", "task": "SyncAllContracts", "error": "结束时间不能早于开始时间"}
[insbuy]2025/07/28 - 16:44:39.160	[31merror[0m	insbuy/ins_contract_sync.go:101	每日全量同步任务失败	{"traceId": "d46fd963ff04fbffe43e3e1d70104328", "task": "DailyFullSyncTask", "error": "全量合同同步失败: 结束时间不能早于开始时间"}
[insbuy]2025/07/28 - 16:45:39.781	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 16:45:40.596	[31merror[0m	insfinance/contract.go:238	飞书API响应错误	{"request_id": "202507281645403A9E6B07F774EE81968A", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"202507281645403A9E6B07F774EE81968A\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=202507281645403A9E6B07F774EE81968A&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 16:45:40.597	[31merror[0m	insbuy/ins_contract.go:714	飞书API调用失败	{"traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "7C468A54-8745-2245-9675-08002B2077BF", "index": 1, "total": 4, "traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"202507281645403A9E6B07F774EE81968A\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=202507281645403A9E6B07F774EE81968A&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 16:45:45.927	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF", "0B92F2B5-922F-4570-8A83-489E476FF811", "FAFE52D3-6F77-4676-B84A-507C90655149", "FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 4, "batch_size": 20, "page_size": 100, "traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 2, "total": 4, "traceId": "747d144de8610e7fc98207bf4681cb65", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 16:46:02.687	[31merror[0m	insbuy/ins_contract_sync.go:101	每日全量同步任务失败	{"traceId": "752d57c0cd4975152247bdae8ab3ed03", "task": "DailyFullSyncTask", "error": "部分审批代码同步失败: 部分同步失败: 成功 3 个，失败 1 个审批代码"}
[insbuy]2025/07/28 - 17:25:04.924	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 17:25:06.448	[31merror[0m	insfinance/contract.go:238	飞书API响应错误	{"request_id": "2025072817250560C8E38082F0BB66AA36", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"2025072817250560C8E38082F0BB66AA36\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=2025072817250560C8E38082F0BB66AA36&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 17:25:06.449	[31merror[0m	insbuy/ins_contract.go:714	飞书API调用失败	{"traceId": "08108462c004dd99f31da23506401c34", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "08108462c004dd99f31da23506401c34", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "7C468A54-8745-2245-9675-08002B2077BF", "index": 1, "total": 1, "traceId": "08108462c004dd99f31da23506401c34", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"2025072817250560C8E38082F0BB66AA36\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=2025072817250560C8E38082F0BB66AA36&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 17:26:38.488	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 17:26:39.265	[31merror[0m	insfinance/contract.go:238	飞书API响应错误	{"request_id": "20250728172638B218596E5ACAAB4AC6EA", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"20250728172638B218596E5ACAAB4AC6EA\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=20250728172638B218596E5ACAAB4AC6EA&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 17:26:39.265	[31merror[0m	insbuy/ins_contract.go:714	飞书API调用失败	{"traceId": "b80733dc949dd9daa51c1b3e69287679", "task": "SyncMultipleContractData", "approval_codes": ["7C468A54-8745-2245-9675-08002B2077BF"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "b80733dc949dd9daa51c1b3e69287679", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "7C468A54-8745-2245-9675-08002B2077BF", "index": 1, "total": 1, "traceId": "b80733dc949dd9daa51c1b3e69287679", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "飞书API返回错误: {\n  Code: 1390002,\n  Msg: \"approval code not found\",\n  Err: {\n    LogID: \"20250728172638B218596E5ACAAB4AC6EA\",\n    Troubleshooter: \"排查建议查看(Troubleshooting suggestions): https://open.feishu.cn/search?from=openapi&log_id=20250728172638B218596E5ACAAB4AC6EA&code=1390002&method_id=7091188971153391644\"\n  }\n}"}
[insbuy]2025/07/28 - 17:26:39.796	[31merror[0m	insbuy/ins_contract.go:397	参数验证失败	{"traceId": "e71895a524b5dc0b0b926c3189036418", "task": "SyncMultipleContractData", "approval_codes": ["FAFE52D3-6F77-4676-B84A-507C90655149"], "total_codes": 1, "batch_size": 20, "page_size": 100, "error": "结束时间不能早于开始时间"}
[insbuy]2025/07/28 - 17:26:39.796	[31merror[0m	insbuy/ins_contract_sync.go:167	增量同步失败	{"traceId": "ddecff1575e7191e4f11e27efbed1bb0", "task": "SyncIncrementalContracts", "approval_code": "FAFE52D3-6F77-4676-B84A-507C90655149", "error": "结束时间不能早于开始时间"}
[insbuy]2025/07/28 - 17:26:39.796	[31merror[0m	insbuy/ins_contract_sync.go:465	增量同步定时任务失败	{"traceId": "2c99ee91b164ec0a7ee2a84757bf905d", "task": "IncrementalSyncTask", "error": "部分审批代码增量同步失败: [审批代码 FAFE52D3-6F77-4676-B84A-507C90655149: 同步失败 - 结束时间不能早于开始时间]"}
[insbuy]2025/07/28 - 17:41:13.127	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 17:41:16.892	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:17.843	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:18.842	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 3, "page_token": "IjIwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:19.587	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 4, "page_token": "IjMwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:20.428	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 5, "page_token": "IjQwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:20.929	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData", "approval_codes": ["F523F053-7AC6-4280-A4E7-B35E0C0431B5"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "index": 1, "total": 1, "traceId": "3db26065ff51a393606ff26b5bf1d269", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 6, "page_token": "IjUwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:23.397	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 1, "page_token": "", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:24.132	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 2, "page_token": "IjEwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:41:24.680	[31merror[0m	insbuy/ins_contract.go:754	批量获取合同详情失败	{"traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData", "approval_codes": ["0B92F2B5-922F-4570-8A83-489E476FF811"], "total_codes": 1, "batch_size": 20, "page_size": 100, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "index": 1, "total": 1, "traceId": "93463c76c84a0f8ec6fd9bb21b06f82a", "task": "SyncMultipleContractData/syncSingleApprovalCode/processSinglePage", "page_num": 3, "page_token": "IjIwMCI=", "error": "参数验证失败: 单次查询的实例代码数量不能超过50个"}
[insbuy]2025/07/28 - 17:58:51.491	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 18:10:33.477	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/07/28 - 18:17:21.576	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
