// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseEmptiesDetail(db *gorm.DB, opts ...gen.DOOption) insWarehouseEmptiesDetail {
	_insWarehouseEmptiesDetail := insWarehouseEmptiesDetail{}

	_insWarehouseEmptiesDetail.insWarehouseEmptiesDetailDo.UseDB(db, opts...)
	_insWarehouseEmptiesDetail.insWarehouseEmptiesDetailDo.UseModel(&insbuy.InsWarehouseEmptiesDetail{})

	tableName := _insWarehouseEmptiesDetail.insWarehouseEmptiesDetailDo.TableName()
	_insWarehouseEmptiesDetail.ALL = field.NewAsterisk(tableName)
	_insWarehouseEmptiesDetail.ID = field.NewUint(tableName, "id")
	_insWarehouseEmptiesDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseEmptiesDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseEmptiesDetail.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseEmptiesDetail.EmptiesId = field.NewUint(tableName, "empties_id")
	_insWarehouseEmptiesDetail.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseEmptiesDetail.Num = field.NewInt(tableName, "num")

	_insWarehouseEmptiesDetail.fillFieldMap()

	return _insWarehouseEmptiesDetail
}

type insWarehouseEmptiesDetail struct {
	insWarehouseEmptiesDetailDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	EmptiesId  field.Uint
	MaterialId field.Int
	Num        field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseEmptiesDetail) Table(newTableName string) *insWarehouseEmptiesDetail {
	i.insWarehouseEmptiesDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseEmptiesDetail) As(alias string) *insWarehouseEmptiesDetail {
	i.insWarehouseEmptiesDetailDo.DO = *(i.insWarehouseEmptiesDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseEmptiesDetail) updateTableName(table string) *insWarehouseEmptiesDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.EmptiesId = field.NewUint(table, "empties_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.Num = field.NewInt(table, "num")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseEmptiesDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseEmptiesDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["empties_id"] = i.EmptiesId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["num"] = i.Num
}

func (i insWarehouseEmptiesDetail) clone(db *gorm.DB) insWarehouseEmptiesDetail {
	i.insWarehouseEmptiesDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseEmptiesDetail) replaceDB(db *gorm.DB) insWarehouseEmptiesDetail {
	i.insWarehouseEmptiesDetailDo.ReplaceDB(db)
	return i
}

type insWarehouseEmptiesDetailDo struct{ gen.DO }

func (i insWarehouseEmptiesDetailDo) Debug() *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseEmptiesDetailDo) WithContext(ctx context.Context) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseEmptiesDetailDo) ReadDB() *insWarehouseEmptiesDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseEmptiesDetailDo) WriteDB() *insWarehouseEmptiesDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseEmptiesDetailDo) Session(config *gorm.Session) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseEmptiesDetailDo) Clauses(conds ...clause.Expression) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseEmptiesDetailDo) Returning(value interface{}, columns ...string) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseEmptiesDetailDo) Not(conds ...gen.Condition) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseEmptiesDetailDo) Or(conds ...gen.Condition) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseEmptiesDetailDo) Select(conds ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseEmptiesDetailDo) Where(conds ...gen.Condition) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseEmptiesDetailDo) Order(conds ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseEmptiesDetailDo) Distinct(cols ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseEmptiesDetailDo) Omit(cols ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseEmptiesDetailDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseEmptiesDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseEmptiesDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseEmptiesDetailDo) Group(cols ...field.Expr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseEmptiesDetailDo) Having(conds ...gen.Condition) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseEmptiesDetailDo) Limit(limit int) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseEmptiesDetailDo) Offset(offset int) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseEmptiesDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseEmptiesDetailDo) Unscoped() *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseEmptiesDetailDo) Create(values ...*insbuy.InsWarehouseEmptiesDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseEmptiesDetailDo) CreateInBatches(values []*insbuy.InsWarehouseEmptiesDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseEmptiesDetailDo) Save(values ...*insbuy.InsWarehouseEmptiesDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseEmptiesDetailDo) First() (*insbuy.InsWarehouseEmptiesDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmptiesDetail), nil
	}
}

func (i insWarehouseEmptiesDetailDo) Take() (*insbuy.InsWarehouseEmptiesDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmptiesDetail), nil
	}
}

func (i insWarehouseEmptiesDetailDo) Last() (*insbuy.InsWarehouseEmptiesDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmptiesDetail), nil
	}
}

func (i insWarehouseEmptiesDetailDo) Find() ([]*insbuy.InsWarehouseEmptiesDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseEmptiesDetail), err
}

func (i insWarehouseEmptiesDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseEmptiesDetail, err error) {
	buf := make([]*insbuy.InsWarehouseEmptiesDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseEmptiesDetailDo) FindInBatches(result *[]*insbuy.InsWarehouseEmptiesDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseEmptiesDetailDo) Attrs(attrs ...field.AssignExpr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseEmptiesDetailDo) Assign(attrs ...field.AssignExpr) *insWarehouseEmptiesDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseEmptiesDetailDo) Joins(fields ...field.RelationField) *insWarehouseEmptiesDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseEmptiesDetailDo) Preload(fields ...field.RelationField) *insWarehouseEmptiesDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseEmptiesDetailDo) FirstOrInit() (*insbuy.InsWarehouseEmptiesDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmptiesDetail), nil
	}
}

func (i insWarehouseEmptiesDetailDo) FirstOrCreate() (*insbuy.InsWarehouseEmptiesDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseEmptiesDetail), nil
	}
}

func (i insWarehouseEmptiesDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseEmptiesDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseEmptiesDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseEmptiesDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseEmptiesDetailDo) Delete(models ...*insbuy.InsWarehouseEmptiesDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseEmptiesDetailDo) withDO(do gen.Dao) *insWarehouseEmptiesDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
