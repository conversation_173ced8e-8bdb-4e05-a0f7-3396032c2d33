// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeDesk(db *gorm.DB, opts ...gen.DOOption) insServiceFeeDesk {
	_insServiceFeeDesk := insServiceFeeDesk{}

	_insServiceFeeDesk.insServiceFeeDeskDo.UseDB(db, opts...)
	_insServiceFeeDesk.insServiceFeeDeskDo.UseModel(&insbuy.InsServiceFeeDesk{})

	tableName := _insServiceFeeDesk.insServiceFeeDeskDo.TableName()
	_insServiceFeeDesk.ALL = field.NewAsterisk(tableName)
	_insServiceFeeDesk.ID = field.NewUint(tableName, "id")
	_insServiceFeeDesk.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeDesk.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeDesk.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeDesk.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeDesk.DeskId = field.NewUint(tableName, "desk_id")
	_insServiceFeeDesk.DiscountRate = field.NewFloat64(tableName, "discount_rate")

	_insServiceFeeDesk.fillFieldMap()

	return _insServiceFeeDesk
}

type insServiceFeeDesk struct {
	insServiceFeeDeskDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ServiceFeeId field.Uint
	DeskId       field.Uint
	DiscountRate field.Float64

	fieldMap map[string]field.Expr
}

func (i insServiceFeeDesk) Table(newTableName string) *insServiceFeeDesk {
	i.insServiceFeeDeskDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeDesk) As(alias string) *insServiceFeeDesk {
	i.insServiceFeeDeskDo.DO = *(i.insServiceFeeDeskDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeDesk) updateTableName(table string) *insServiceFeeDesk {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.DeskId = field.NewUint(table, "desk_id")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeDesk) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeDesk) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["discount_rate"] = i.DiscountRate
}

func (i insServiceFeeDesk) clone(db *gorm.DB) insServiceFeeDesk {
	i.insServiceFeeDeskDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeDesk) replaceDB(db *gorm.DB) insServiceFeeDesk {
	i.insServiceFeeDeskDo.ReplaceDB(db)
	return i
}

type insServiceFeeDeskDo struct{ gen.DO }

func (i insServiceFeeDeskDo) Debug() *insServiceFeeDeskDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeDeskDo) WithContext(ctx context.Context) *insServiceFeeDeskDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeDeskDo) ReadDB() *insServiceFeeDeskDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeDeskDo) WriteDB() *insServiceFeeDeskDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeDeskDo) Session(config *gorm.Session) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeDeskDo) Clauses(conds ...clause.Expression) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeDeskDo) Returning(value interface{}, columns ...string) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeDeskDo) Not(conds ...gen.Condition) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeDeskDo) Or(conds ...gen.Condition) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeDeskDo) Select(conds ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeDeskDo) Where(conds ...gen.Condition) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeDeskDo) Order(conds ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeDeskDo) Distinct(cols ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeDeskDo) Omit(cols ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeDeskDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeDeskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeDeskDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeDeskDo) Group(cols ...field.Expr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeDeskDo) Having(conds ...gen.Condition) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeDeskDo) Limit(limit int) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeDeskDo) Offset(offset int) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeDeskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeDeskDo) Unscoped() *insServiceFeeDeskDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeDeskDo) Create(values ...*insbuy.InsServiceFeeDesk) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeDeskDo) CreateInBatches(values []*insbuy.InsServiceFeeDesk, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeDeskDo) Save(values ...*insbuy.InsServiceFeeDesk) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeDeskDo) First() (*insbuy.InsServiceFeeDesk, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDesk), nil
	}
}

func (i insServiceFeeDeskDo) Take() (*insbuy.InsServiceFeeDesk, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDesk), nil
	}
}

func (i insServiceFeeDeskDo) Last() (*insbuy.InsServiceFeeDesk, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDesk), nil
	}
}

func (i insServiceFeeDeskDo) Find() ([]*insbuy.InsServiceFeeDesk, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeDesk), err
}

func (i insServiceFeeDeskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeDesk, err error) {
	buf := make([]*insbuy.InsServiceFeeDesk, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeDeskDo) FindInBatches(result *[]*insbuy.InsServiceFeeDesk, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeDeskDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeDeskDo) Assign(attrs ...field.AssignExpr) *insServiceFeeDeskDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeDeskDo) Joins(fields ...field.RelationField) *insServiceFeeDeskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeDeskDo) Preload(fields ...field.RelationField) *insServiceFeeDeskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeDeskDo) FirstOrInit() (*insbuy.InsServiceFeeDesk, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDesk), nil
	}
}

func (i insServiceFeeDeskDo) FirstOrCreate() (*insbuy.InsServiceFeeDesk, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeDesk), nil
	}
}

func (i insServiceFeeDeskDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeDesk, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeDeskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeDeskDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeDeskDo) Delete(models ...*insbuy.InsServiceFeeDesk) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeDeskDo) withDO(do gen.Dao) *insServiceFeeDeskDo {
	i.DO = *do.(*gen.DO)
	return i
}
