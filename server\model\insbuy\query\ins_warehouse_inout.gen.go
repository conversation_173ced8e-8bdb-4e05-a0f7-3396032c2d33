// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInout(db *gorm.DB, opts ...gen.DOOption) insWarehouseInout {
	_insWarehouseInout := insWarehouseInout{}

	_insWarehouseInout.insWarehouseInoutDo.UseDB(db, opts...)
	_insWarehouseInout.insWarehouseInoutDo.UseModel(&insbuy.InsWarehouseInout{})

	tableName := _insWarehouseInout.insWarehouseInoutDo.TableName()
	_insWarehouseInout.ALL = field.NewAsterisk(tableName)
	_insWarehouseInout.ID = field.NewUint(tableName, "id")
	_insWarehouseInout.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInout.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInout.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInout.InoutTypeId = field.NewInt(tableName, "inout_type_id")
	_insWarehouseInout.WaybillSn = field.NewString(tableName, "waybill_sn")
	_insWarehouseInout.PurchaseType = field.NewInt(tableName, "purchase_type")
	_insWarehouseInout.PurchasersId = field.NewInt(tableName, "purchasers_id")
	_insWarehouseInout.RelationId = field.NewUint(tableName, "relation_id")
	_insWarehouseInout.SourceId = field.NewInt(tableName, "source_id")
	_insWarehouseInout.ToWarehouseId = field.NewInt(tableName, "to_warehouse_id")
	_insWarehouseInout.OutBoundOperator = field.NewInt(tableName, "out_bound_operator")
	_insWarehouseInout.Status = field.NewInt(tableName, "status")
	_insWarehouseInout.PurchaseSn = field.NewString(tableName, "purchase_sn")
	_insWarehouseInout.SourceInoutId = field.NewUint(tableName, "source_inout_id")
	_insWarehouseInout.StoreId = field.NewUint(tableName, "store_id")
	_insWarehouseInout.PurchaseTime = field.NewTime(tableName, "purchase_time")
	_insWarehouseInout.WarehouseTime = field.NewTime(tableName, "warehouse_time")
	_insWarehouseInout.IsDirect = field.NewInt(tableName, "is_direct")
	_insWarehouseInout.ItemId = field.NewInt(tableName, "item_id")
	_insWarehouseInout.Receiver = field.NewInt(tableName, "receiver")
	_insWarehouseInout.TempReceiver = field.NewString(tableName, "temp_receiver")
	_insWarehouseInout.SourcePurchaseSn = field.NewString(tableName, "source_purchase_sn")
	_insWarehouseInout.IsAgain = field.NewInt(tableName, "is_again")
	_insWarehouseInout.EntitySn = field.NewString(tableName, "entity_sn")
	_insWarehouseInout.ApplyInoutType = field.NewInt(tableName, "apply_inout_type")
	_insWarehouseInout.Remark = field.NewString(tableName, "remark")
	_insWarehouseInout.PurchaseTotal = field.NewFloat64(tableName, "purchase_total")
	_insWarehouseInout.TransferRate = field.NewFloat64(tableName, "transfer_rate")
	_insWarehouseInout.InvoiceType = field.NewInt(tableName, "invoice_type")
	_insWarehouseInout.TaxRate = field.NewFloat64(tableName, "tax_rate")
	_insWarehouseInout.Apply = insWarehouseInoutBelongsToApply{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Apply", "insbuy.InsWarehouseInoutApply"),
	}

	_insWarehouseInout.fillFieldMap()

	return _insWarehouseInout
}

type insWarehouseInout struct {
	insWarehouseInoutDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	InoutTypeId      field.Int
	WaybillSn        field.String
	PurchaseType     field.Int
	PurchasersId     field.Int
	RelationId       field.Uint
	SourceId         field.Int
	ToWarehouseId    field.Int
	OutBoundOperator field.Int
	Status           field.Int
	PurchaseSn       field.String
	SourceInoutId    field.Uint
	StoreId          field.Uint
	PurchaseTime     field.Time
	WarehouseTime    field.Time
	IsDirect         field.Int
	ItemId           field.Int
	Receiver         field.Int
	TempReceiver     field.String
	SourcePurchaseSn field.String
	IsAgain          field.Int
	EntitySn         field.String
	ApplyInoutType   field.Int
	Remark           field.String
	PurchaseTotal    field.Float64
	TransferRate     field.Float64
	InvoiceType      field.Int
	TaxRate          field.Float64
	Apply            insWarehouseInoutBelongsToApply

	fieldMap map[string]field.Expr
}

func (i insWarehouseInout) Table(newTableName string) *insWarehouseInout {
	i.insWarehouseInoutDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInout) As(alias string) *insWarehouseInout {
	i.insWarehouseInoutDo.DO = *(i.insWarehouseInoutDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInout) updateTableName(table string) *insWarehouseInout {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.InoutTypeId = field.NewInt(table, "inout_type_id")
	i.WaybillSn = field.NewString(table, "waybill_sn")
	i.PurchaseType = field.NewInt(table, "purchase_type")
	i.PurchasersId = field.NewInt(table, "purchasers_id")
	i.RelationId = field.NewUint(table, "relation_id")
	i.SourceId = field.NewInt(table, "source_id")
	i.ToWarehouseId = field.NewInt(table, "to_warehouse_id")
	i.OutBoundOperator = field.NewInt(table, "out_bound_operator")
	i.Status = field.NewInt(table, "status")
	i.PurchaseSn = field.NewString(table, "purchase_sn")
	i.SourceInoutId = field.NewUint(table, "source_inout_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.PurchaseTime = field.NewTime(table, "purchase_time")
	i.WarehouseTime = field.NewTime(table, "warehouse_time")
	i.IsDirect = field.NewInt(table, "is_direct")
	i.ItemId = field.NewInt(table, "item_id")
	i.Receiver = field.NewInt(table, "receiver")
	i.TempReceiver = field.NewString(table, "temp_receiver")
	i.SourcePurchaseSn = field.NewString(table, "source_purchase_sn")
	i.IsAgain = field.NewInt(table, "is_again")
	i.EntitySn = field.NewString(table, "entity_sn")
	i.ApplyInoutType = field.NewInt(table, "apply_inout_type")
	i.Remark = field.NewString(table, "remark")
	i.PurchaseTotal = field.NewFloat64(table, "purchase_total")
	i.TransferRate = field.NewFloat64(table, "transfer_rate")
	i.InvoiceType = field.NewInt(table, "invoice_type")
	i.TaxRate = field.NewFloat64(table, "tax_rate")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInout) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInout) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 32)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["inout_type_id"] = i.InoutTypeId
	i.fieldMap["waybill_sn"] = i.WaybillSn
	i.fieldMap["purchase_type"] = i.PurchaseType
	i.fieldMap["purchasers_id"] = i.PurchasersId
	i.fieldMap["relation_id"] = i.RelationId
	i.fieldMap["source_id"] = i.SourceId
	i.fieldMap["to_warehouse_id"] = i.ToWarehouseId
	i.fieldMap["out_bound_operator"] = i.OutBoundOperator
	i.fieldMap["status"] = i.Status
	i.fieldMap["purchase_sn"] = i.PurchaseSn
	i.fieldMap["source_inout_id"] = i.SourceInoutId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["purchase_time"] = i.PurchaseTime
	i.fieldMap["warehouse_time"] = i.WarehouseTime
	i.fieldMap["is_direct"] = i.IsDirect
	i.fieldMap["item_id"] = i.ItemId
	i.fieldMap["receiver"] = i.Receiver
	i.fieldMap["temp_receiver"] = i.TempReceiver
	i.fieldMap["source_purchase_sn"] = i.SourcePurchaseSn
	i.fieldMap["is_again"] = i.IsAgain
	i.fieldMap["entity_sn"] = i.EntitySn
	i.fieldMap["apply_inout_type"] = i.ApplyInoutType
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["purchase_total"] = i.PurchaseTotal
	i.fieldMap["transfer_rate"] = i.TransferRate
	i.fieldMap["invoice_type"] = i.InvoiceType
	i.fieldMap["tax_rate"] = i.TaxRate

}

func (i insWarehouseInout) clone(db *gorm.DB) insWarehouseInout {
	i.insWarehouseInoutDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInout) replaceDB(db *gorm.DB) insWarehouseInout {
	i.insWarehouseInoutDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutBelongsToApply struct {
	db *gorm.DB

	field.RelationField
}

func (a insWarehouseInoutBelongsToApply) Where(conds ...field.Expr) *insWarehouseInoutBelongsToApply {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insWarehouseInoutBelongsToApply) WithContext(ctx context.Context) *insWarehouseInoutBelongsToApply {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insWarehouseInoutBelongsToApply) Session(session *gorm.Session) *insWarehouseInoutBelongsToApply {
	a.db = a.db.Session(session)
	return &a
}

func (a insWarehouseInoutBelongsToApply) Model(m *insbuy.InsWarehouseInout) *insWarehouseInoutBelongsToApplyTx {
	return &insWarehouseInoutBelongsToApplyTx{a.db.Model(m).Association(a.Name())}
}

type insWarehouseInoutBelongsToApplyTx struct{ tx *gorm.Association }

func (a insWarehouseInoutBelongsToApplyTx) Find() (result *insbuy.InsWarehouseInoutApply, err error) {
	return result, a.tx.Find(&result)
}

func (a insWarehouseInoutBelongsToApplyTx) Append(values ...*insbuy.InsWarehouseInoutApply) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insWarehouseInoutBelongsToApplyTx) Replace(values ...*insbuy.InsWarehouseInoutApply) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insWarehouseInoutBelongsToApplyTx) Delete(values ...*insbuy.InsWarehouseInoutApply) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insWarehouseInoutBelongsToApplyTx) Clear() error {
	return a.tx.Clear()
}

func (a insWarehouseInoutBelongsToApplyTx) Count() int64 {
	return a.tx.Count()
}

type insWarehouseInoutDo struct{ gen.DO }

func (i insWarehouseInoutDo) Debug() *insWarehouseInoutDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutDo) WithContext(ctx context.Context) *insWarehouseInoutDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutDo) ReadDB() *insWarehouseInoutDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutDo) WriteDB() *insWarehouseInoutDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutDo) Session(config *gorm.Session) *insWarehouseInoutDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutDo) Clauses(conds ...clause.Expression) *insWarehouseInoutDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutDo) Returning(value interface{}, columns ...string) *insWarehouseInoutDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutDo) Not(conds ...gen.Condition) *insWarehouseInoutDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutDo) Or(conds ...gen.Condition) *insWarehouseInoutDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutDo) Select(conds ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutDo) Where(conds ...gen.Condition) *insWarehouseInoutDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutDo) Order(conds ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutDo) Distinct(cols ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutDo) Omit(cols ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutDo) Group(cols ...field.Expr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutDo) Having(conds ...gen.Condition) *insWarehouseInoutDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutDo) Limit(limit int) *insWarehouseInoutDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutDo) Offset(offset int) *insWarehouseInoutDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutDo) Unscoped() *insWarehouseInoutDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutDo) Create(values ...*insbuy.InsWarehouseInout) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutDo) CreateInBatches(values []*insbuy.InsWarehouseInout, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutDo) Save(values ...*insbuy.InsWarehouseInout) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutDo) First() (*insbuy.InsWarehouseInout, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInout), nil
	}
}

func (i insWarehouseInoutDo) Take() (*insbuy.InsWarehouseInout, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInout), nil
	}
}

func (i insWarehouseInoutDo) Last() (*insbuy.InsWarehouseInout, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInout), nil
	}
}

func (i insWarehouseInoutDo) Find() ([]*insbuy.InsWarehouseInout, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInout), err
}

func (i insWarehouseInoutDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInout, err error) {
	buf := make([]*insbuy.InsWarehouseInout, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutDo) FindInBatches(result *[]*insbuy.InsWarehouseInout, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutDo) Joins(fields ...field.RelationField) *insWarehouseInoutDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutDo) Preload(fields ...field.RelationField) *insWarehouseInoutDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutDo) FirstOrInit() (*insbuy.InsWarehouseInout, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInout), nil
	}
}

func (i insWarehouseInoutDo) FirstOrCreate() (*insbuy.InsWarehouseInout, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInout), nil
	}
}

func (i insWarehouseInoutDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInout, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutDo) Delete(models ...*insbuy.InsWarehouseInout) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutDo) withDO(do gen.Dao) *insWarehouseInoutDo {
	i.DO = *do.(*gen.DO)
	return i
}
