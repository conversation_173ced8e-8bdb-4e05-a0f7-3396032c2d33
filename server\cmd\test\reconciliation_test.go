package test

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreconciliation"
	"testing"
)

func TestPullData(t *testing.T) {
	prepare()
	remote := insreconciliation.NewDataSource(2, 2)
	remoteData, err := remote.Adapter.PullData(insreconciliation.PullParams{
		FileName:  "D:\\Downloads\\660290058131TH6_217480967\\660290058131TH6.xlsx",
		Channel:   insreconciliation.ChannelAll,
		StartDate: "2025-02-24",
		EndDate:   "2025-03-03",
		Source:    2,
		StoreIds:  []uint{6},
		Dim:       insreconciliation.RDimTotal,
	})
	if err != nil {
		return
	}
	fmt.Println(remoteData)
}

func TestGenRz(t *testing.T) {
	prepare()
	srv := service.ServiceGroupApp.InsBuyServiceGroup.Rz
	srv.GenRz(13)
}

func TestGenerateTasks(t *testing.T) {
	prepare()
	srv := service.ServiceGroupApp.InsBuyServiceGroup.Rz
	err := srv.GenerateDataForReconciliationTasks()
	if err != nil {
		fmt.Println(err)
	}
}
