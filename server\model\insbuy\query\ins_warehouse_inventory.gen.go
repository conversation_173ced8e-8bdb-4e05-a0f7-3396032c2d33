// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventory(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventory {
	_insWarehouseInventory := insWarehouseInventory{}

	_insWarehouseInventory.insWarehouseInventoryDo.UseDB(db, opts...)
	_insWarehouseInventory.insWarehouseInventoryDo.UseModel(&insbuy.InsWarehouseInventory{})

	tableName := _insWarehouseInventory.insWarehouseInventoryDo.TableName()
	_insWarehouseInventory.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventory.ID = field.NewUint(tableName, "id")
	_insWarehouseInventory.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventory.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInventory.InventoryNum = field.NewString(tableName, "inventory_num")
	_insWarehouseInventory.InoutTypeId = field.NewInt(tableName, "inout_type_id")
	_insWarehouseInventory.Type = field.NewInt(tableName, "type")
	_insWarehouseInventory.SourceId = field.NewInt(tableName, "source_id")
	_insWarehouseInventory.WarehouseId = field.NewInt(tableName, "warehouse_id")
	_insWarehouseInventory.SourceSn = field.NewString(tableName, "source_sn")
	_insWarehouseInventory.Remark = field.NewString(tableName, "remark")
	_insWarehouseInventory.OperatorId = field.NewInt(tableName, "operator_id")
	_insWarehouseInventory.PurchaseType = field.NewInt(tableName, "purchase_type")

	_insWarehouseInventory.fillFieldMap()

	return _insWarehouseInventory
}

type insWarehouseInventory struct {
	insWarehouseInventoryDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	InventoryNum field.String
	InoutTypeId  field.Int
	Type         field.Int
	SourceId     field.Int
	WarehouseId  field.Int
	SourceSn     field.String
	Remark       field.String
	OperatorId   field.Int
	PurchaseType field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventory) Table(newTableName string) *insWarehouseInventory {
	i.insWarehouseInventoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventory) As(alias string) *insWarehouseInventory {
	i.insWarehouseInventoryDo.DO = *(i.insWarehouseInventoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventory) updateTableName(table string) *insWarehouseInventory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.InventoryNum = field.NewString(table, "inventory_num")
	i.InoutTypeId = field.NewInt(table, "inout_type_id")
	i.Type = field.NewInt(table, "type")
	i.SourceId = field.NewInt(table, "source_id")
	i.WarehouseId = field.NewInt(table, "warehouse_id")
	i.SourceSn = field.NewString(table, "source_sn")
	i.Remark = field.NewString(table, "remark")
	i.OperatorId = field.NewInt(table, "operator_id")
	i.PurchaseType = field.NewInt(table, "purchase_type")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["inventory_num"] = i.InventoryNum
	i.fieldMap["inout_type_id"] = i.InoutTypeId
	i.fieldMap["type"] = i.Type
	i.fieldMap["source_id"] = i.SourceId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["source_sn"] = i.SourceSn
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["purchase_type"] = i.PurchaseType
}

func (i insWarehouseInventory) clone(db *gorm.DB) insWarehouseInventory {
	i.insWarehouseInventoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventory) replaceDB(db *gorm.DB) insWarehouseInventory {
	i.insWarehouseInventoryDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryDo struct{ gen.DO }

func (i insWarehouseInventoryDo) Debug() *insWarehouseInventoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryDo) WithContext(ctx context.Context) *insWarehouseInventoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryDo) ReadDB() *insWarehouseInventoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryDo) WriteDB() *insWarehouseInventoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryDo) Session(config *gorm.Session) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryDo) Not(conds ...gen.Condition) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryDo) Or(conds ...gen.Condition) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryDo) Select(conds ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryDo) Where(conds ...gen.Condition) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryDo) Order(conds ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryDo) Distinct(cols ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryDo) Omit(cols ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryDo) Group(cols ...field.Expr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryDo) Having(conds ...gen.Condition) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryDo) Limit(limit int) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryDo) Offset(offset int) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryDo) Unscoped() *insWarehouseInventoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryDo) Create(values ...*insbuy.InsWarehouseInventory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryDo) CreateInBatches(values []*insbuy.InsWarehouseInventory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryDo) Save(values ...*insbuy.InsWarehouseInventory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryDo) First() (*insbuy.InsWarehouseInventory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventory), nil
	}
}

func (i insWarehouseInventoryDo) Take() (*insbuy.InsWarehouseInventory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventory), nil
	}
}

func (i insWarehouseInventoryDo) Last() (*insbuy.InsWarehouseInventory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventory), nil
	}
}

func (i insWarehouseInventoryDo) Find() ([]*insbuy.InsWarehouseInventory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventory), err
}

func (i insWarehouseInventoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventory, err error) {
	buf := make([]*insbuy.InsWarehouseInventory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryDo) FindInBatches(result *[]*insbuy.InsWarehouseInventory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryDo) Joins(fields ...field.RelationField) *insWarehouseInventoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryDo) Preload(fields ...field.RelationField) *insWarehouseInventoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryDo) FirstOrInit() (*insbuy.InsWarehouseInventory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventory), nil
	}
}

func (i insWarehouseInventoryDo) FirstOrCreate() (*insbuy.InsWarehouseInventory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventory), nil
	}
}

func (i insWarehouseInventoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryDo) Delete(models ...*insbuy.InsWarehouseInventory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryDo) withDO(do gen.Dao) *insWarehouseInventoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
