// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutApply(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutApply {
	_insWarehouseInoutApply := insWarehouseInoutApply{}

	_insWarehouseInoutApply.insWarehouseInoutApplyDo.UseDB(db, opts...)
	_insWarehouseInoutApply.insWarehouseInoutApplyDo.UseModel(&insbuy.InsWarehouseInoutApply{})

	tableName := _insWarehouseInoutApply.insWarehouseInoutApplyDo.TableName()
	_insWarehouseInoutApply.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutApply.ID = field.NewUint(tableName, "id")
	_insWarehouseInoutApply.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutApply.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutApply.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInoutApply.ApplyType = field.NewInt(tableName, "apply_type")
	_insWarehouseInoutApply.ApplyInoutType = field.NewInt(tableName, "apply_inout_type")
	_insWarehouseInoutApply.ApplyWarehouseId = field.NewInt(tableName, "apply_warehouse_id")
	_insWarehouseInoutApply.ApplyDeptId = field.NewInt(tableName, "apply_dept_id")
	_insWarehouseInoutApply.ApplyUser = field.NewInt(tableName, "apply_user")
	_insWarehouseInoutApply.Remark = field.NewString(tableName, "remark")
	_insWarehouseInoutApply.StoreId = field.NewInt(tableName, "store_id")
	_insWarehouseInoutApply.AuditNo = field.NewString(tableName, "audit_no")
	_insWarehouseInoutApply.ApplyTime = field.NewTime(tableName, "apply_time")

	_insWarehouseInoutApply.fillFieldMap()

	return _insWarehouseInoutApply
}

type insWarehouseInoutApply struct {
	insWarehouseInoutApplyDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	DeletedAt        field.Field
	ApplyType        field.Int
	ApplyInoutType   field.Int
	ApplyWarehouseId field.Int
	ApplyDeptId      field.Int
	ApplyUser        field.Int
	Remark           field.String
	StoreId          field.Int
	AuditNo          field.String
	ApplyTime        field.Time

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutApply) Table(newTableName string) *insWarehouseInoutApply {
	i.insWarehouseInoutApplyDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutApply) As(alias string) *insWarehouseInoutApply {
	i.insWarehouseInoutApplyDo.DO = *(i.insWarehouseInoutApplyDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutApply) updateTableName(table string) *insWarehouseInoutApply {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ApplyType = field.NewInt(table, "apply_type")
	i.ApplyInoutType = field.NewInt(table, "apply_inout_type")
	i.ApplyWarehouseId = field.NewInt(table, "apply_warehouse_id")
	i.ApplyDeptId = field.NewInt(table, "apply_dept_id")
	i.ApplyUser = field.NewInt(table, "apply_user")
	i.Remark = field.NewString(table, "remark")
	i.StoreId = field.NewInt(table, "store_id")
	i.AuditNo = field.NewString(table, "audit_no")
	i.ApplyTime = field.NewTime(table, "apply_time")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutApply) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutApply) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["apply_type"] = i.ApplyType
	i.fieldMap["apply_inout_type"] = i.ApplyInoutType
	i.fieldMap["apply_warehouse_id"] = i.ApplyWarehouseId
	i.fieldMap["apply_dept_id"] = i.ApplyDeptId
	i.fieldMap["apply_user"] = i.ApplyUser
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["audit_no"] = i.AuditNo
	i.fieldMap["apply_time"] = i.ApplyTime
}

func (i insWarehouseInoutApply) clone(db *gorm.DB) insWarehouseInoutApply {
	i.insWarehouseInoutApplyDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutApply) replaceDB(db *gorm.DB) insWarehouseInoutApply {
	i.insWarehouseInoutApplyDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutApplyDo struct{ gen.DO }

func (i insWarehouseInoutApplyDo) Debug() *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutApplyDo) WithContext(ctx context.Context) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutApplyDo) ReadDB() *insWarehouseInoutApplyDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutApplyDo) WriteDB() *insWarehouseInoutApplyDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutApplyDo) Session(config *gorm.Session) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutApplyDo) Clauses(conds ...clause.Expression) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutApplyDo) Returning(value interface{}, columns ...string) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutApplyDo) Not(conds ...gen.Condition) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutApplyDo) Or(conds ...gen.Condition) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutApplyDo) Select(conds ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutApplyDo) Where(conds ...gen.Condition) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutApplyDo) Order(conds ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutApplyDo) Distinct(cols ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutApplyDo) Omit(cols ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutApplyDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutApplyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutApplyDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutApplyDo) Group(cols ...field.Expr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutApplyDo) Having(conds ...gen.Condition) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutApplyDo) Limit(limit int) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutApplyDo) Offset(offset int) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutApplyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutApplyDo) Unscoped() *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutApplyDo) Create(values ...*insbuy.InsWarehouseInoutApply) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutApplyDo) CreateInBatches(values []*insbuy.InsWarehouseInoutApply, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutApplyDo) Save(values ...*insbuy.InsWarehouseInoutApply) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutApplyDo) First() (*insbuy.InsWarehouseInoutApply, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutApply), nil
	}
}

func (i insWarehouseInoutApplyDo) Take() (*insbuy.InsWarehouseInoutApply, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutApply), nil
	}
}

func (i insWarehouseInoutApplyDo) Last() (*insbuy.InsWarehouseInoutApply, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutApply), nil
	}
}

func (i insWarehouseInoutApplyDo) Find() ([]*insbuy.InsWarehouseInoutApply, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutApply), err
}

func (i insWarehouseInoutApplyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutApply, err error) {
	buf := make([]*insbuy.InsWarehouseInoutApply, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutApplyDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutApply, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutApplyDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutApplyDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutApplyDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutApplyDo) Joins(fields ...field.RelationField) *insWarehouseInoutApplyDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutApplyDo) Preload(fields ...field.RelationField) *insWarehouseInoutApplyDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutApplyDo) FirstOrInit() (*insbuy.InsWarehouseInoutApply, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutApply), nil
	}
}

func (i insWarehouseInoutApplyDo) FirstOrCreate() (*insbuy.InsWarehouseInoutApply, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutApply), nil
	}
}

func (i insWarehouseInoutApplyDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutApply, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutApplyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutApplyDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutApplyDo) Delete(models ...*insbuy.InsWarehouseInoutApply) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutApplyDo) withDO(do gen.Dao) *insWarehouseInoutApplyDo {
	i.DO = *do.(*gen.DO)
	return i
}
