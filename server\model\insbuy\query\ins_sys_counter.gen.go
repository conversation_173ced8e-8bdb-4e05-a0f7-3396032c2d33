// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSysCounter(db *gorm.DB, opts ...gen.DOOption) insSysCounter {
	_insSysCounter := insSysCounter{}

	_insSysCounter.insSysCounterDo.UseDB(db, opts...)
	_insSysCounter.insSysCounterDo.UseModel(&insbuy.InsSysCounter{})

	tableName := _insSysCounter.insSysCounterDo.TableName()
	_insSysCounter.ALL = field.NewAsterisk(tableName)
	_insSysCounter.ID = field.NewUint(tableName, "id")
	_insSysCounter.CreatedAt = field.NewTime(tableName, "created_at")
	_insSysCounter.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSysCounter.Code = field.NewString(tableName, "code")
	_insSysCounter.Serial = field.NewInt64(tableName, "serial")

	_insSysCounter.fillFieldMap()

	return _insSysCounter
}

type insSysCounter struct {
	insSysCounterDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	Code      field.String
	Serial    field.Int64

	fieldMap map[string]field.Expr
}

func (i insSysCounter) Table(newTableName string) *insSysCounter {
	i.insSysCounterDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSysCounter) As(alias string) *insSysCounter {
	i.insSysCounterDo.DO = *(i.insSysCounterDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSysCounter) updateTableName(table string) *insSysCounter {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Code = field.NewString(table, "code")
	i.Serial = field.NewInt64(table, "serial")

	i.fillFieldMap()

	return i
}

func (i *insSysCounter) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSysCounter) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["code"] = i.Code
	i.fieldMap["serial"] = i.Serial
}

func (i insSysCounter) clone(db *gorm.DB) insSysCounter {
	i.insSysCounterDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSysCounter) replaceDB(db *gorm.DB) insSysCounter {
	i.insSysCounterDo.ReplaceDB(db)
	return i
}

type insSysCounterDo struct{ gen.DO }

func (i insSysCounterDo) Debug() *insSysCounterDo {
	return i.withDO(i.DO.Debug())
}

func (i insSysCounterDo) WithContext(ctx context.Context) *insSysCounterDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSysCounterDo) ReadDB() *insSysCounterDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSysCounterDo) WriteDB() *insSysCounterDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSysCounterDo) Session(config *gorm.Session) *insSysCounterDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSysCounterDo) Clauses(conds ...clause.Expression) *insSysCounterDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSysCounterDo) Returning(value interface{}, columns ...string) *insSysCounterDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSysCounterDo) Not(conds ...gen.Condition) *insSysCounterDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSysCounterDo) Or(conds ...gen.Condition) *insSysCounterDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSysCounterDo) Select(conds ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSysCounterDo) Where(conds ...gen.Condition) *insSysCounterDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSysCounterDo) Order(conds ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSysCounterDo) Distinct(cols ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSysCounterDo) Omit(cols ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSysCounterDo) Join(table schema.Tabler, on ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSysCounterDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSysCounterDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSysCounterDo) Group(cols ...field.Expr) *insSysCounterDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSysCounterDo) Having(conds ...gen.Condition) *insSysCounterDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSysCounterDo) Limit(limit int) *insSysCounterDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSysCounterDo) Offset(offset int) *insSysCounterDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSysCounterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSysCounterDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSysCounterDo) Unscoped() *insSysCounterDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSysCounterDo) Create(values ...*insbuy.InsSysCounter) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSysCounterDo) CreateInBatches(values []*insbuy.InsSysCounter, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSysCounterDo) Save(values ...*insbuy.InsSysCounter) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSysCounterDo) First() (*insbuy.InsSysCounter, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysCounter), nil
	}
}

func (i insSysCounterDo) Take() (*insbuy.InsSysCounter, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysCounter), nil
	}
}

func (i insSysCounterDo) Last() (*insbuy.InsSysCounter, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysCounter), nil
	}
}

func (i insSysCounterDo) Find() ([]*insbuy.InsSysCounter, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSysCounter), err
}

func (i insSysCounterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSysCounter, err error) {
	buf := make([]*insbuy.InsSysCounter, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSysCounterDo) FindInBatches(result *[]*insbuy.InsSysCounter, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSysCounterDo) Attrs(attrs ...field.AssignExpr) *insSysCounterDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSysCounterDo) Assign(attrs ...field.AssignExpr) *insSysCounterDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSysCounterDo) Joins(fields ...field.RelationField) *insSysCounterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSysCounterDo) Preload(fields ...field.RelationField) *insSysCounterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSysCounterDo) FirstOrInit() (*insbuy.InsSysCounter, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysCounter), nil
	}
}

func (i insSysCounterDo) FirstOrCreate() (*insbuy.InsSysCounter, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysCounter), nil
	}
}

func (i insSysCounterDo) FindByPage(offset int, limit int) (result []*insbuy.InsSysCounter, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSysCounterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSysCounterDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSysCounterDo) Delete(models ...*insbuy.InsSysCounter) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSysCounterDo) withDO(do gen.Dao) *insSysCounterDo {
	i.DO = *do.(*gen.DO)
	return i
}
