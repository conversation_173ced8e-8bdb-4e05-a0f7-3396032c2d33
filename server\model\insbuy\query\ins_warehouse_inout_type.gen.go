// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutType(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutType {
	_insWarehouseInoutType := insWarehouseInoutType{}

	_insWarehouseInoutType.insWarehouseInoutTypeDo.UseDB(db, opts...)
	_insWarehouseInoutType.insWarehouseInoutTypeDo.UseModel(&insbuy.InsWarehouseInoutType{})

	tableName := _insWarehouseInoutType.insWarehouseInoutTypeDo.TableName()
	_insWarehouseInoutType.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutType.ID = field.NewUint(tableName, "id")
	_insWarehouseInoutType.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutType.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutType.DeletedAt = field.NewField(tableName, "deleted_at")
	_insWarehouseInoutType.Type = field.NewInt(tableName, "type")
	_insWarehouseInoutType.Name = field.NewString(tableName, "name")
	_insWarehouseInoutType.SortOrder = field.NewInt(tableName, "sort_order")
	_insWarehouseInoutType.PurchaseType = field.NewInt(tableName, "purchase_type")
	_insWarehouseInoutType.IsShow = field.NewInt(tableName, "is_show")

	_insWarehouseInoutType.fillFieldMap()

	return _insWarehouseInoutType
}

type insWarehouseInoutType struct {
	insWarehouseInoutTypeDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	Type         field.Int
	Name         field.String
	SortOrder    field.Int
	PurchaseType field.Int
	IsShow       field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutType) Table(newTableName string) *insWarehouseInoutType {
	i.insWarehouseInoutTypeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutType) As(alias string) *insWarehouseInoutType {
	i.insWarehouseInoutTypeDo.DO = *(i.insWarehouseInoutTypeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutType) updateTableName(table string) *insWarehouseInoutType {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Type = field.NewInt(table, "type")
	i.Name = field.NewString(table, "name")
	i.SortOrder = field.NewInt(table, "sort_order")
	i.PurchaseType = field.NewInt(table, "purchase_type")
	i.IsShow = field.NewInt(table, "is_show")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutType) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["type"] = i.Type
	i.fieldMap["name"] = i.Name
	i.fieldMap["sort_order"] = i.SortOrder
	i.fieldMap["purchase_type"] = i.PurchaseType
	i.fieldMap["is_show"] = i.IsShow
}

func (i insWarehouseInoutType) clone(db *gorm.DB) insWarehouseInoutType {
	i.insWarehouseInoutTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutType) replaceDB(db *gorm.DB) insWarehouseInoutType {
	i.insWarehouseInoutTypeDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutTypeDo struct{ gen.DO }

func (i insWarehouseInoutTypeDo) Debug() *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutTypeDo) WithContext(ctx context.Context) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutTypeDo) ReadDB() *insWarehouseInoutTypeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutTypeDo) WriteDB() *insWarehouseInoutTypeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutTypeDo) Session(config *gorm.Session) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutTypeDo) Clauses(conds ...clause.Expression) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutTypeDo) Returning(value interface{}, columns ...string) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutTypeDo) Not(conds ...gen.Condition) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutTypeDo) Or(conds ...gen.Condition) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutTypeDo) Select(conds ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutTypeDo) Where(conds ...gen.Condition) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutTypeDo) Order(conds ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutTypeDo) Distinct(cols ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutTypeDo) Omit(cols ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutTypeDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutTypeDo) Group(cols ...field.Expr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutTypeDo) Having(conds ...gen.Condition) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutTypeDo) Limit(limit int) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutTypeDo) Offset(offset int) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutTypeDo) Unscoped() *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutTypeDo) Create(values ...*insbuy.InsWarehouseInoutType) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutTypeDo) CreateInBatches(values []*insbuy.InsWarehouseInoutType, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutTypeDo) Save(values ...*insbuy.InsWarehouseInoutType) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutTypeDo) First() (*insbuy.InsWarehouseInoutType, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutType), nil
	}
}

func (i insWarehouseInoutTypeDo) Take() (*insbuy.InsWarehouseInoutType, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutType), nil
	}
}

func (i insWarehouseInoutTypeDo) Last() (*insbuy.InsWarehouseInoutType, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutType), nil
	}
}

func (i insWarehouseInoutTypeDo) Find() ([]*insbuy.InsWarehouseInoutType, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutType), err
}

func (i insWarehouseInoutTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutType, err error) {
	buf := make([]*insbuy.InsWarehouseInoutType, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutTypeDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutTypeDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutTypeDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutTypeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutTypeDo) Joins(fields ...field.RelationField) *insWarehouseInoutTypeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutTypeDo) Preload(fields ...field.RelationField) *insWarehouseInoutTypeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutTypeDo) FirstOrInit() (*insbuy.InsWarehouseInoutType, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutType), nil
	}
}

func (i insWarehouseInoutTypeDo) FirstOrCreate() (*insbuy.InsWarehouseInoutType, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutType), nil
	}
}

func (i insWarehouseInoutTypeDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutType, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutTypeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutTypeDo) Delete(models ...*insbuy.InsWarehouseInoutType) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutTypeDo) withDO(do gen.Dao) *insWarehouseInoutTypeDo {
	i.DO = *do.(*gen.DO)
	return i
}
