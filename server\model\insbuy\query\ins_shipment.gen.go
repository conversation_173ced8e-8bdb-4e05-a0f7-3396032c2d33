// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsShipment(db *gorm.DB, opts ...gen.DOOption) insShipment {
	_insShipment := insShipment{}

	_insShipment.insShipmentDo.UseDB(db, opts...)
	_insShipment.insShipmentDo.UseModel(&insbuy.InsShipment{})

	tableName := _insShipment.insShipmentDo.TableName()
	_insShipment.ALL = field.NewAsterisk(tableName)
	_insShipment.ID = field.NewUint(tableName, "id")
	_insShipment.CreatedAt = field.NewTime(tableName, "created_at")
	_insShipment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insShipment.OrderId = field.NewUint64(tableName, "order_id")
	_insShipment.OrderDetailsId = field.NewUint(tableName, "order_details_id")
	_insShipment.ProductId = field.NewUint(tableName, "product_id")
	_insShipment.WarehouseId = field.NewUint(tableName, "warehouse_id")
	_insShipment.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insShipment.DeskId = field.NewUint(tableName, "desk_id")
	_insShipment.StoreId = field.NewUint(tableName, "store_id")
	_insShipment.Status = field.NewInt(tableName, "status")
	_insShipment.ShipmentType = field.NewInt(tableName, "shipment_type")
	_insShipment.Nums = field.NewUint(tableName, "nums")
	_insShipment.ProductName = field.NewString(tableName, "product_name")
	_insShipment.ProductPrice = field.NewFloat64(tableName, "product_price")
	_insShipment.DepositId = field.NewUint(tableName, "deposit_id")
	_insShipment.ShipmentSn = field.NewString(tableName, "shipment_sn")

	_insShipment.fillFieldMap()

	return _insShipment
}

type insShipment struct {
	insShipmentDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	OrderId        field.Uint64
	OrderDetailsId field.Uint
	ProductId      field.Uint
	WarehouseId    field.Uint
	OpenDeskId     field.Uint
	DeskId         field.Uint
	StoreId        field.Uint
	Status         field.Int
	ShipmentType   field.Int
	Nums           field.Uint
	ProductName    field.String
	ProductPrice   field.Float64
	DepositId      field.Uint
	ShipmentSn     field.String

	fieldMap map[string]field.Expr
}

func (i insShipment) Table(newTableName string) *insShipment {
	i.insShipmentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insShipment) As(alias string) *insShipment {
	i.insShipmentDo.DO = *(i.insShipmentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insShipment) updateTableName(table string) *insShipment {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OrderId = field.NewUint64(table, "order_id")
	i.OrderDetailsId = field.NewUint(table, "order_details_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.WarehouseId = field.NewUint(table, "warehouse_id")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.DeskId = field.NewUint(table, "desk_id")
	i.StoreId = field.NewUint(table, "store_id")
	i.Status = field.NewInt(table, "status")
	i.ShipmentType = field.NewInt(table, "shipment_type")
	i.Nums = field.NewUint(table, "nums")
	i.ProductName = field.NewString(table, "product_name")
	i.ProductPrice = field.NewFloat64(table, "product_price")
	i.DepositId = field.NewUint(table, "deposit_id")
	i.ShipmentSn = field.NewString(table, "shipment_sn")

	i.fillFieldMap()

	return i
}

func (i *insShipment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insShipment) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["order_id"] = i.OrderId
	i.fieldMap["order_details_id"] = i.OrderDetailsId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["warehouse_id"] = i.WarehouseId
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["status"] = i.Status
	i.fieldMap["shipment_type"] = i.ShipmentType
	i.fieldMap["nums"] = i.Nums
	i.fieldMap["product_name"] = i.ProductName
	i.fieldMap["product_price"] = i.ProductPrice
	i.fieldMap["deposit_id"] = i.DepositId
	i.fieldMap["shipment_sn"] = i.ShipmentSn
}

func (i insShipment) clone(db *gorm.DB) insShipment {
	i.insShipmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insShipment) replaceDB(db *gorm.DB) insShipment {
	i.insShipmentDo.ReplaceDB(db)
	return i
}

type insShipmentDo struct{ gen.DO }

func (i insShipmentDo) Debug() *insShipmentDo {
	return i.withDO(i.DO.Debug())
}

func (i insShipmentDo) WithContext(ctx context.Context) *insShipmentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insShipmentDo) ReadDB() *insShipmentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insShipmentDo) WriteDB() *insShipmentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insShipmentDo) Session(config *gorm.Session) *insShipmentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insShipmentDo) Clauses(conds ...clause.Expression) *insShipmentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insShipmentDo) Returning(value interface{}, columns ...string) *insShipmentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insShipmentDo) Not(conds ...gen.Condition) *insShipmentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insShipmentDo) Or(conds ...gen.Condition) *insShipmentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insShipmentDo) Select(conds ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insShipmentDo) Where(conds ...gen.Condition) *insShipmentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insShipmentDo) Order(conds ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insShipmentDo) Distinct(cols ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insShipmentDo) Omit(cols ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insShipmentDo) Join(table schema.Tabler, on ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insShipmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insShipmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insShipmentDo) Group(cols ...field.Expr) *insShipmentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insShipmentDo) Having(conds ...gen.Condition) *insShipmentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insShipmentDo) Limit(limit int) *insShipmentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insShipmentDo) Offset(offset int) *insShipmentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insShipmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insShipmentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insShipmentDo) Unscoped() *insShipmentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insShipmentDo) Create(values ...*insbuy.InsShipment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insShipmentDo) CreateInBatches(values []*insbuy.InsShipment, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insShipmentDo) Save(values ...*insbuy.InsShipment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insShipmentDo) First() (*insbuy.InsShipment, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsShipment), nil
	}
}

func (i insShipmentDo) Take() (*insbuy.InsShipment, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsShipment), nil
	}
}

func (i insShipmentDo) Last() (*insbuy.InsShipment, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsShipment), nil
	}
}

func (i insShipmentDo) Find() ([]*insbuy.InsShipment, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsShipment), err
}

func (i insShipmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsShipment, err error) {
	buf := make([]*insbuy.InsShipment, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insShipmentDo) FindInBatches(result *[]*insbuy.InsShipment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insShipmentDo) Attrs(attrs ...field.AssignExpr) *insShipmentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insShipmentDo) Assign(attrs ...field.AssignExpr) *insShipmentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insShipmentDo) Joins(fields ...field.RelationField) *insShipmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insShipmentDo) Preload(fields ...field.RelationField) *insShipmentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insShipmentDo) FirstOrInit() (*insbuy.InsShipment, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsShipment), nil
	}
}

func (i insShipmentDo) FirstOrCreate() (*insbuy.InsShipment, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsShipment), nil
	}
}

func (i insShipmentDo) FindByPage(offset int, limit int) (result []*insbuy.InsShipment, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insShipmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insShipmentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insShipmentDo) Delete(models ...*insbuy.InsShipment) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insShipmentDo) withDO(do gen.Dao) *insShipmentDo {
	i.DO = *do.(*gen.DO)
	return i
}
