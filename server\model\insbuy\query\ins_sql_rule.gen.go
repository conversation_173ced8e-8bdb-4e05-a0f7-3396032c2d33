// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSqlRule(db *gorm.DB, opts ...gen.DOOption) insSqlRule {
	_insSqlRule := insSqlRule{}

	_insSqlRule.insSqlRuleDo.UseDB(db, opts...)
	_insSqlRule.insSqlRuleDo.UseModel(&insbuy.InsSqlRule{})

	tableName := _insSqlRule.insSqlRuleDo.TableName()
	_insSqlRule.ALL = field.NewAsterisk(tableName)
	_insSqlRule.ID = field.NewUint(tableName, "id")
	_insSqlRule.CreatedAt = field.NewTime(tableName, "created_at")
	_insSqlRule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSqlRule.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSqlRule.Name = field.NewString(tableName, "name")
	_insSqlRule.Status = field.NewUint(tableName, "status")
	_insSqlRule.Remark = field.NewString(tableName, "remark")

	_insSqlRule.fillFieldMap()

	return _insSqlRule
}

type insSqlRule struct {
	insSqlRuleDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Name      field.String
	Status    field.Uint
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insSqlRule) Table(newTableName string) *insSqlRule {
	i.insSqlRuleDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSqlRule) As(alias string) *insSqlRule {
	i.insSqlRuleDo.DO = *(i.insSqlRuleDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSqlRule) updateTableName(table string) *insSqlRule {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Name = field.NewString(table, "name")
	i.Status = field.NewUint(table, "status")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insSqlRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSqlRule) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
}

func (i insSqlRule) clone(db *gorm.DB) insSqlRule {
	i.insSqlRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSqlRule) replaceDB(db *gorm.DB) insSqlRule {
	i.insSqlRuleDo.ReplaceDB(db)
	return i
}

type insSqlRuleDo struct{ gen.DO }

func (i insSqlRuleDo) Debug() *insSqlRuleDo {
	return i.withDO(i.DO.Debug())
}

func (i insSqlRuleDo) WithContext(ctx context.Context) *insSqlRuleDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSqlRuleDo) ReadDB() *insSqlRuleDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSqlRuleDo) WriteDB() *insSqlRuleDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSqlRuleDo) Session(config *gorm.Session) *insSqlRuleDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSqlRuleDo) Clauses(conds ...clause.Expression) *insSqlRuleDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSqlRuleDo) Returning(value interface{}, columns ...string) *insSqlRuleDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSqlRuleDo) Not(conds ...gen.Condition) *insSqlRuleDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSqlRuleDo) Or(conds ...gen.Condition) *insSqlRuleDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSqlRuleDo) Select(conds ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSqlRuleDo) Where(conds ...gen.Condition) *insSqlRuleDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSqlRuleDo) Order(conds ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSqlRuleDo) Distinct(cols ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSqlRuleDo) Omit(cols ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSqlRuleDo) Join(table schema.Tabler, on ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSqlRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSqlRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSqlRuleDo) Group(cols ...field.Expr) *insSqlRuleDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSqlRuleDo) Having(conds ...gen.Condition) *insSqlRuleDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSqlRuleDo) Limit(limit int) *insSqlRuleDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSqlRuleDo) Offset(offset int) *insSqlRuleDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSqlRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSqlRuleDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSqlRuleDo) Unscoped() *insSqlRuleDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSqlRuleDo) Create(values ...*insbuy.InsSqlRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSqlRuleDo) CreateInBatches(values []*insbuy.InsSqlRule, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSqlRuleDo) Save(values ...*insbuy.InsSqlRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSqlRuleDo) First() (*insbuy.InsSqlRule, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRule), nil
	}
}

func (i insSqlRuleDo) Take() (*insbuy.InsSqlRule, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRule), nil
	}
}

func (i insSqlRuleDo) Last() (*insbuy.InsSqlRule, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRule), nil
	}
}

func (i insSqlRuleDo) Find() ([]*insbuy.InsSqlRule, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSqlRule), err
}

func (i insSqlRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSqlRule, err error) {
	buf := make([]*insbuy.InsSqlRule, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSqlRuleDo) FindInBatches(result *[]*insbuy.InsSqlRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSqlRuleDo) Attrs(attrs ...field.AssignExpr) *insSqlRuleDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSqlRuleDo) Assign(attrs ...field.AssignExpr) *insSqlRuleDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSqlRuleDo) Joins(fields ...field.RelationField) *insSqlRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSqlRuleDo) Preload(fields ...field.RelationField) *insSqlRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSqlRuleDo) FirstOrInit() (*insbuy.InsSqlRule, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRule), nil
	}
}

func (i insSqlRuleDo) FirstOrCreate() (*insbuy.InsSqlRule, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSqlRule), nil
	}
}

func (i insSqlRuleDo) FindByPage(offset int, limit int) (result []*insbuy.InsSqlRule, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSqlRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSqlRuleDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSqlRuleDo) Delete(models ...*insbuy.InsSqlRule) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSqlRuleDo) withDO(do gen.Dao) *insSqlRuleDo {
	i.DO = *do.(*gen.DO)
	return i
}
