# 财务交叉统计报表 - 公式表达式API使用示例

## 概述

本文档提供了财务交叉统计报表中计算型分类公式表达式系统的完整API使用示例，包括配置方法、API调用和响应数据格式。

## 配置示例

### 1. 配置中心数据结构

#### 基础分类配置
```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "store_id": 0,
  "item_index": 1,
  "item_value": {
    "id": 1,
    "level": 1,
    "parent_id": 0,
    "category_name": "收入类合计",
    "category_code": "INCOME_TOTAL",
    "is_active": 1,
    "sort_order": 100,
    "is_calculated": 0,
    "calculation_formula": ""
  }
}
```

#### 计算型分类配置
```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "store_id": 0,
  "item_index": 10,
  "item_value": {
    "id": 100,
    "level": 1,
    "parent_id": 0,
    "category_name": "经营利润",
    "category_code": "OPERATING_PROFIT",
    "is_active": 1,
    "sort_order": 900,
    "is_calculated": 1,
    "calculation_formula": "[收入类合计] - [成本类合计]"
  }
}
```

### 2. 完整业务场景配置

#### 损益表结构配置
```json
[
  {
    "id": 1,
    "category_name": "营业收入",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 100
  },
  {
    "id": 2,
    "category_name": "营业成本",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 200
  },
  {
    "id": 100,
    "category_name": "毛利润",
    "level": 1,
    "is_calculated": 1,
    "calculation_formula": "[营业收入] - [营业成本]",
    "sort_order": 300
  },
  {
    "id": 3,
    "category_name": "销售费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 400
  },
  {
    "id": 4,
    "category_name": "管理费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 500
  },
  {
    "id": 5,
    "category_name": "财务费用",
    "level": 1,
    "is_calculated": 0,
    "sort_order": 600
  },
  {
    "id": 101,
    "category_name": "期间费用合计",
    "level": 1,
    "is_calculated": 1,
    "calculation_formula": "SUM([销售费用], [管理费用], [财务费用])",
    "sort_order": 700
  },
  {
    "id": 102,
    "category_name": "营业利润",
    "level": 1,
    "is_calculated": 1,
    "calculation_formula": "[毛利润] - [期间费用合计]",
    "sort_order": 800
  },
  {
    "id": 103,
    "category_name": "毛利率",
    "level": 1,
    "is_calculated": 1,
    "calculation_formula": "PERCENTAGE([毛利润], [营业收入])",
    "sort_order": 900
  },
  {
    "id": 104,
    "category_name": "营业利润率",
    "level": 1,
    "is_calculated": 1,
    "calculation_formula": "PERCENTAGE([营业利润], [营业收入])",
    "sort_order": 910
  }
]
```

## API调用示例

### 1. 基础API调用

```go
package main

import (
    "context"
    "fmt"
    "time"
    
    "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport"
)

func main() {
    // 创建查询参数
    params := insreport.FinancialWarehouseParams{
        PageNum:   1,
        PageSize:  20,
        StartDate: time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
        EndDate:   time.Date(2025, 12, 1, 0, 0, 0, 0, time.Local),
        Typ:       1, // 含税
        Area:      "", // 不限制区域
    }
    
    // 调用API
    ctx := context.Background()
    resp, err := insreport.RegionalFinancialSummaryReport(ctx, query.Q, params)
    if err != nil {
        fmt.Printf("查询失败: %v\n", err)
        return
    }
    
    // 处理响应数据
    fmt.Printf("查询成功，共 %d 条记录\n", resp.Total)
    fmt.Printf("时间范围: %s 至 %s\n", resp.StartDate, resp.EndDate)
    
    // 遍历数据
    for _, item := range resp.List.([]interface{}) {
        data := item.(map[string]interface{})
        fmt.Printf("分类: %s, 总计: %v\n", 
            data["category_name"], 
            data["total_amount"])
    }
}
```

### 2. 带筛选条件的API调用

```go
// 按区域筛选
params := insreport.FinancialWarehouseParams{
    PageNum:   1,
    PageSize:  50,
    StartDate: time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
    EndDate:   time.Date(2025, 6, 1, 0, 0, 0, 0, time.Local),
    Area:      "华东", // 筛选华东区域
    Typ:       0,      // 不含税
}

resp, err := insreport.RegionalFinancialSummaryReport(ctx, query.Q, params)
```

### 3. 分页查询示例

```go
// 分页查询所有数据
var allData []interface{}
pageNum := 1
pageSize := 100

for {
    params := insreport.FinancialWarehouseParams{
        PageNum:   pageNum,
        PageSize:  pageSize,
        StartDate: time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
        EndDate:   time.Date(2025, 12, 1, 0, 0, 0, 0, time.Local),
    }
    
    resp, err := insreport.RegionalFinancialSummaryReport(ctx, query.Q, params)
    if err != nil {
        break
    }
    
    // 添加当前页数据
    pageData := resp.List.([]interface{})
    allData = append(allData, pageData...)
    
    // 检查是否还有下一页
    if pageNum >= resp.TotalPage {
        break
    }
    pageNum++
}

fmt.Printf("总共获取 %d 条数据\n", len(allData))
```

## 响应数据格式

### 1. 标准响应结构

```json
{
  "startDate": "2025-01",
  "endDate": "2025-12",
  "headers": [
    {"title": "分类名称", "field": "category_name"},
    {"title": "2025-01", "field": "2025-01"},
    {"title": "2025-02", "field": "2025-02"},
    {"title": "2025-03", "field": "2025-03"},
    {"title": "总计", "field": "total_amount"}
  ],
  "list": [
    {
      "category_id": 1,
      "category_name": "营业收入",
      "category_code": "REVENUE",
      "level": 1,
      "parent_id": 0,
      "sort_order": 100,
      "2025-01": 10000000.00,
      "2025-02": 12000000.00,
      "2025-03": 11000000.00,
      "total_amount": 33000000.00
    },
    {
      "category_id": 100,
      "category_name": "毛利润",
      "category_code": "GROSS_PROFIT",
      "level": 1,
      "parent_id": 0,
      "sort_order": 300,
      "2025-01": 4000000.00,
      "2025-02": 5000000.00,
      "2025-03": 4500000.00,
      "total_amount": 13500000.00
    },
    {
      "category_id": 103,
      "category_name": "毛利率",
      "category_code": "GROSS_MARGIN",
      "level": 1,
      "parent_id": 0,
      "sort_order": 900,
      "2025-01": 40.00,
      "2025-02": 41.67,
      "2025-03": 40.91,
      "total_amount": 40.91
    }
  ],
  "page": 1,
  "pageSize": 20,
  "total": 10,
  "totalPage": 1
}
```

### 2. 动态时间列

响应中的时间列是动态生成的，根据查询的时间范围自动调整：

```json
// 查询2025年1-3月
"headers": [
  {"title": "分类名称", "field": "category_name"},
  {"title": "2025-01", "field": "2025-01"},
  {"title": "2025-02", "field": "2025-02"},
  {"title": "2025-03", "field": "2025-03"},
  {"title": "总计", "field": "total_amount"}
]

// 查询2025年全年
"headers": [
  {"title": "分类名称", "field": "category_name"},
  {"title": "2025-01", "field": "2025-01"},
  {"title": "2025-02", "field": "2025-02"},
  // ... 其他月份
  {"title": "2025-12", "field": "2025-12"},
  {"title": "总计", "field": "total_amount"}
]
```

## 前端集成示例

### 1. JavaScript/TypeScript 调用

```typescript
interface FinancialReportParams {
  pageNum: number;
  pageSize: number;
  startDate: string;
  endDate: string;
  area?: string;
  typ?: number;
}

interface FinancialReportResponse {
  startDate: string;
  endDate: string;
  headers: Array<{title: string, field: string}>;
  list: Array<Record<string, any>>;
  page: number;
  pageSize: number;
  total: number;
  totalPage: number;
}

// API调用函数
async function getFinancialReport(params: FinancialReportParams): Promise<FinancialReportResponse> {
  const response = await fetch('/api/financial/summary-report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params)
  });
  
  if (!response.ok) {
    throw new Error('查询失败');
  }
  
  return response.json();
}

// 使用示例
async function loadReport() {
  try {
    const data = await getFinancialReport({
      pageNum: 1,
      pageSize: 20,
      startDate: '2025-01-01',
      endDate: '2025-12-01',
      typ: 1
    });
    
    console.log('报表数据:', data);
    
    // 处理动态表头
    const dynamicColumns = data.headers.map(header => ({
      title: header.title,
      dataIndex: header.field,
      key: header.field
    }));
    
    // 处理数据
    const tableData = data.list.map(item => ({
      key: item.category_id,
      ...item
    }));
    
  } catch (error) {
    console.error('查询失败:', error);
  }
}
```

### 2. React 组件示例

```tsx
import React, { useState, useEffect } from 'react';
import { Table, DatePicker, Select, Button } from 'antd';

const FinancialReportTable: React.FC = () => {
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [params, setParams] = useState({
    startDate: '2025-01-01',
    endDate: '2025-12-01',
    area: '',
    typ: 1
  });

  const loadData = async () => {
    setLoading(true);
    try {
      const response = await getFinancialReport({
        pageNum: 1,
        pageSize: 100,
        ...params
      });
      
      // 设置动态列
      const dynamicColumns = response.headers.map(header => ({
        title: header.title,
        dataIndex: header.field,
        key: header.field,
        render: (value: any) => {
          // 格式化数值显示
          if (typeof value === 'number' && header.field !== 'category_id') {
            return value.toLocaleString('zh-CN', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });
          }
          return value;
        }
      }));
      
      setColumns(dynamicColumns);
      setData(response.list);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [params]);

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <DatePicker.RangePicker
          value={[moment(params.startDate), moment(params.endDate)]}
          onChange={(dates) => {
            if (dates) {
              setParams({
                ...params,
                startDate: dates[0]?.format('YYYY-MM-DD') || '',
                endDate: dates[1]?.format('YYYY-MM-DD') || ''
              });
            }
          }}
        />
        
        <Select
          style={{ width: 120, marginLeft: 8 }}
          value={params.area}
          onChange={(value) => setParams({...params, area: value})}
          placeholder="选择区域"
        >
          <Select.Option value="">全部区域</Select.Option>
          <Select.Option value="华东">华东</Select.Option>
          <Select.Option value="华北">华北</Select.Option>
          <Select.Option value="华南">华南</Select.Option>
        </Select>
        
        <Select
          style={{ width: 100, marginLeft: 8 }}
          value={params.typ}
          onChange={(value) => setParams({...params, typ: value})}
        >
          <Select.Option value={1}>含税</Select.Option>
          <Select.Option value={0}>不含税</Select.Option>
        </Select>
        
        <Button 
          type="primary" 
          style={{ marginLeft: 8 }}
          onClick={loadData}
          loading={loading}
        >
          查询
        </Button>
      </div>
      
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
      />
    </div>
  );
};
```

## 错误处理

### 1. 常见错误类型

```go
// 公式解析错误
{
  "error": "公式解析失败",
  "message": "无法解析表达式: [不存在的分类]",
  "code": "FORMULA_PARSE_ERROR"
}

// 计算错误
{
  "error": "计算失败",
  "message": "找不到ID为 999 的分类数据",
  "code": "CALCULATION_ERROR"
}

// 参数错误
{
  "error": "参数错误",
  "message": "开始时间不能晚于结束时间",
  "code": "INVALID_PARAMS"
}
```

### 2. 错误处理最佳实践

```go
resp, err := insreport.RegionalFinancialSummaryReport(ctx, query.Q, params)
if err != nil {
    // 记录错误日志
    log.Printf("财务报表查询失败: %v", err)
    
    // 返回友好的错误信息
    return &Response{
        Code: 500,
        Message: "查询失败，请稍后重试",
        Data: nil,
    }
}

// 检查数据完整性
if resp.Total == 0 {
    return &Response{
        Code: 200,
        Message: "暂无数据",
        Data: resp,
    }
}
```

## 性能优化建议

### 1. 查询优化
- 合理设置分页大小（建议50-100条）
- 避免查询过长的时间范围（建议不超过12个月）
- 使用适当的筛选条件减少数据量

### 2. 缓存策略
- 对于相同参数的查询结果可以缓存
- 配置数据变更时清除相关缓存
- 使用Redis等缓存中间件提高响应速度

### 3. 前端优化
- 使用虚拟滚动处理大量数据
- 实现表格列的懒加载
- 合理使用防抖和节流优化查询频率

这个API使用示例文档提供了完整的使用指南，帮助开发者快速集成和使用新的公式表达式系统。
