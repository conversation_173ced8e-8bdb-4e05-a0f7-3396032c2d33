// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtStoreSales(db *gorm.DB, opts ...gen.DOOption) insExtStoreSales {
	_insExtStoreSales := insExtStoreSales{}

	_insExtStoreSales.insExtStoreSalesDo.UseDB(db, opts...)
	_insExtStoreSales.insExtStoreSalesDo.UseModel(&insbuy.InsExtStoreSales{})

	tableName := _insExtStoreSales.insExtStoreSalesDo.TableName()
	_insExtStoreSales.ALL = field.NewAsterisk(tableName)
	_insExtStoreSales.ID = field.NewUint(tableName, "id")
	_insExtStoreSales.BusinessDay = field.NewTime(tableName, "business_day")
	_insExtStoreSales.StoreId = field.NewUint(tableName, "store_id")
	_insExtStoreSales.OrderAmount = field.NewFloat64(tableName, "order_amount")
	_insExtStoreSales.OrderNum = field.NewInt(tableName, "order_num")
	_insExtStoreSales.CustomerNum = field.NewInt(tableName, "customer_num")
	_insExtStoreSales.Status = field.NewInt(tableName, "status")
	_insExtStoreSales.Ext = field.NewField(tableName, "ext")
	_insExtStoreSales.CreatedAt = field.NewTime(tableName, "created_at")
	_insExtStoreSales.CreatedBy = field.NewUint(tableName, "created_by")
	_insExtStoreSales.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insExtStoreSales.UpdatedBy = field.NewUint(tableName, "updated_by")

	_insExtStoreSales.fillFieldMap()

	return _insExtStoreSales
}

type insExtStoreSales struct {
	insExtStoreSalesDo

	ALL         field.Asterisk
	ID          field.Uint
	BusinessDay field.Time
	StoreId     field.Uint
	OrderAmount field.Float64
	OrderNum    field.Int
	CustomerNum field.Int
	Status      field.Int
	Ext         field.Field
	CreatedAt   field.Time
	CreatedBy   field.Uint
	UpdatedAt   field.Time
	UpdatedBy   field.Uint

	fieldMap map[string]field.Expr
}

func (i insExtStoreSales) Table(newTableName string) *insExtStoreSales {
	i.insExtStoreSalesDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtStoreSales) As(alias string) *insExtStoreSales {
	i.insExtStoreSalesDo.DO = *(i.insExtStoreSalesDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtStoreSales) updateTableName(table string) *insExtStoreSales {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.StoreId = field.NewUint(table, "store_id")
	i.OrderAmount = field.NewFloat64(table, "order_amount")
	i.OrderNum = field.NewInt(table, "order_num")
	i.CustomerNum = field.NewInt(table, "customer_num")
	i.Status = field.NewInt(table, "status")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.UpdatedBy = field.NewUint(table, "updated_by")

	i.fillFieldMap()

	return i
}

func (i *insExtStoreSales) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtStoreSales) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["order_amount"] = i.OrderAmount
	i.fieldMap["order_num"] = i.OrderNum
	i.fieldMap["customer_num"] = i.CustomerNum
	i.fieldMap["status"] = i.Status
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["updated_by"] = i.UpdatedBy
}

func (i insExtStoreSales) clone(db *gorm.DB) insExtStoreSales {
	i.insExtStoreSalesDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtStoreSales) replaceDB(db *gorm.DB) insExtStoreSales {
	i.insExtStoreSalesDo.ReplaceDB(db)
	return i
}

type insExtStoreSalesDo struct{ gen.DO }

func (i insExtStoreSalesDo) Debug() *insExtStoreSalesDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtStoreSalesDo) WithContext(ctx context.Context) *insExtStoreSalesDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtStoreSalesDo) ReadDB() *insExtStoreSalesDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtStoreSalesDo) WriteDB() *insExtStoreSalesDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtStoreSalesDo) Session(config *gorm.Session) *insExtStoreSalesDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtStoreSalesDo) Clauses(conds ...clause.Expression) *insExtStoreSalesDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtStoreSalesDo) Returning(value interface{}, columns ...string) *insExtStoreSalesDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtStoreSalesDo) Not(conds ...gen.Condition) *insExtStoreSalesDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtStoreSalesDo) Or(conds ...gen.Condition) *insExtStoreSalesDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtStoreSalesDo) Select(conds ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtStoreSalesDo) Where(conds ...gen.Condition) *insExtStoreSalesDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtStoreSalesDo) Order(conds ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtStoreSalesDo) Distinct(cols ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtStoreSalesDo) Omit(cols ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtStoreSalesDo) Join(table schema.Tabler, on ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtStoreSalesDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtStoreSalesDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtStoreSalesDo) Group(cols ...field.Expr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtStoreSalesDo) Having(conds ...gen.Condition) *insExtStoreSalesDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtStoreSalesDo) Limit(limit int) *insExtStoreSalesDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtStoreSalesDo) Offset(offset int) *insExtStoreSalesDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtStoreSalesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtStoreSalesDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtStoreSalesDo) Unscoped() *insExtStoreSalesDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtStoreSalesDo) Create(values ...*insbuy.InsExtStoreSales) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtStoreSalesDo) CreateInBatches(values []*insbuy.InsExtStoreSales, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtStoreSalesDo) Save(values ...*insbuy.InsExtStoreSales) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtStoreSalesDo) First() (*insbuy.InsExtStoreSales, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreSales), nil
	}
}

func (i insExtStoreSalesDo) Take() (*insbuy.InsExtStoreSales, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreSales), nil
	}
}

func (i insExtStoreSalesDo) Last() (*insbuy.InsExtStoreSales, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreSales), nil
	}
}

func (i insExtStoreSalesDo) Find() ([]*insbuy.InsExtStoreSales, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtStoreSales), err
}

func (i insExtStoreSalesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtStoreSales, err error) {
	buf := make([]*insbuy.InsExtStoreSales, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtStoreSalesDo) FindInBatches(result *[]*insbuy.InsExtStoreSales, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtStoreSalesDo) Attrs(attrs ...field.AssignExpr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtStoreSalesDo) Assign(attrs ...field.AssignExpr) *insExtStoreSalesDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtStoreSalesDo) Joins(fields ...field.RelationField) *insExtStoreSalesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtStoreSalesDo) Preload(fields ...field.RelationField) *insExtStoreSalesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtStoreSalesDo) FirstOrInit() (*insbuy.InsExtStoreSales, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreSales), nil
	}
}

func (i insExtStoreSalesDo) FirstOrCreate() (*insbuy.InsExtStoreSales, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreSales), nil
	}
}

func (i insExtStoreSalesDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtStoreSales, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtStoreSalesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtStoreSalesDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtStoreSalesDo) Delete(models ...*insbuy.InsExtStoreSales) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtStoreSalesDo) withDO(do gen.Dao) *insExtStoreSalesDo {
	i.DO = *do.(*gen.DO)
	return i
}
