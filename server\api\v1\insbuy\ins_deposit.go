package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Validation definition
var (
	checkVipMemberByUserVerify = utils.Rules{
		"Phone":   {utils.NotEmpty(), utils.IsMobile()},
		"SalesId": {utils.NotEmpty()},
	}
)

type InsDepositApi struct {
}

// CreateInsDeposit 创建存取酒
// @Tags InsDeposit
// @Summary 创建存取酒
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDepositCreate true "创建存取酒"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/createInsDeposit [post]
func (insDepositApi *InsDepositApi) CreateInsDeposit(c *gin.Context) {
	var insDeposit insbuyReq.InsDepositCreate
	if err := GinMustBind(c, &insDeposit); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if resp, err := insDepositService.CreateInsDeposit(insDeposit); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// DeleteInsDeposit 删除InsDeposit
// @Tags InsDeposit
// @Summary 删除InsDeposit
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeposit true "删除InsDeposit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDeposit/deleteInsDeposit [delete]
func (insDepositApi *InsDepositApi) DeleteInsDeposit(c *gin.Context) {
	var insDeposit insbuyReq.DeleteInsDepositReq
	if err := GinMustBind(c, &insDeposit); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDepositService.DeleteInsDeposit(insDeposit); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDepositByIds 批量删除InsDeposit
// @Tags InsDeposit
// @Summary 批量删除InsDeposit
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDeposit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDeposit/deleteInsDepositByIds [delete]
func (insDepositApi *InsDepositApi) DeleteInsDepositByIds(c *gin.Context) {
	var IDS request.IdsReq
	if err := GinMustBind(c, &IDS); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDepositService.DeleteInsDepositByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDeposit 更新InsDeposit
// @Tags InsDeposit
// @Summary 更新InsDeposit
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeposit true "更新InsDeposit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDeposit/updateInsDeposit [put]
func (insDepositApi *InsDepositApi) UpdateInsDeposit(c *gin.Context) {
	var insDeposit insbuy.InsDeposit
	if err := GinMustBind(c, &insDeposit); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDepositService.UpdateInsDeposit(insDeposit); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsDeposit 用id查询InsDeposit
// @Tags InsDeposit
// @Summary 用id查询InsDeposit
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsDeposit true "用id查询InsDeposit"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insDeposit/findInsDeposit [get]
func (insDepositApi *InsDepositApi) FindInsDeposit(c *gin.Context) {
	var insDeposit insbuy.InsDeposit
	err := GinMustBind(c, &insDeposit)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsDeposit, err := insDepositService.GetInsDeposit(insDeposit.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsDeposit": reinsDeposit}, c)
	}
}

// GetInsDepositList 分页获取InsDeposit列表
// @Tags InsDeposit
// @Summary 分页获取InsDeposit列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositSearch true "分页获取InsDeposit列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositList [get]
func (insDepositApi *InsDepositApi) GetInsDepositList(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositInfoListNew 分页获取存取酒记录
// @Tags InsDeposit
// @Summary 分页获取存取酒记录
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositSearch true "分页获取存取酒记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositListNew [get]
func (insDepositApi *InsDepositApi) GetInsDepositInfoListNew(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositInfoListNew(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			exportType := insbuy.ETDepositLogInList.ToInt()
			if pageInfo.Expire {
				exportType = insbuy.ETDepositExpire.ToInt()
			}
			_, e := insImportService.ExcelCommonList(c, exportType, list)
			if e != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsDepositLog 创建InsDepositLog
// @Tags InsDepositLog
// @Summary 创建InsDepositLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDepositLog true "创建InsDepositLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/createInsDepositLog [post]
func (InsDepositApi *InsDepositApi) CreateInsDepositLog(c *gin.Context) {
	var insDepositLog insbuy.InsDepositLog
	err := c.ShouldBindJSON(&insDepositLog)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.CreateInsDepositLog(&insDepositLog); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsDepositLog 删除InsDepositLog
// @Tags InsDepositLog
// @Summary 删除InsDepositLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDepositLog true "删除InsDepositLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDeposit/deleteInsDepositLog [delete]
func (InsDepositApi *InsDepositApi) DeleteInsDepositLog(c *gin.Context) {
	var insDepositLog insbuy.InsDepositLog
	err := c.ShouldBindJSON(&insDepositLog)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.DeleteInsDepositLog(insDepositLog); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDepositLogByIds 批量删除InsDepositLog
// @Tags InsDepositLog
// @Summary 批量删除InsDepositLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDepositLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDeposit/deleteInsDepositLogByIds [delete]
func (InsDepositApi *InsDepositApi) DeleteInsDepositLogByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.DeleteInsDepositLogByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDepositLog 延期和充公
// @Tags InsDepositLog
// @Summary 延期和充公
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsDepositLogReq true "延期和充公"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDeposit/updateInsDepositLog [put]
func (InsDepositApi *InsDepositApi) UpdateInsDepositLog(c *gin.Context) {
	var insDepositLog insbuyReq.UpdateInsDepositLogReq
	err := c.ShouldBindJSON(&insDepositLog)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.UpdateInsDepositLog(insDepositLog); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// UpdateInsDepositLogBatch 批量延期和充公
// @Tags InsDepositLog
// @Summary 批量延期和充公
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsDepositLogBatchReq true "批量延期和充公"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDeposit/updateInsDepositLogBatch [put]
func (InsDepositApi *InsDepositApi) UpdateInsDepositLogBatch(c *gin.Context) {
	var insDepositLog insbuyReq.UpdateInsDepositLogBatchReq
	if err := GinMustBind(c, &insDepositLog); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.UpdateInsDepositLogBatch(insDepositLog); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GetInsDepositLogList 分页获取InsDepositLog列表
// @Tags InsDepositLog
// @Summary 分页获取InsDepositLog列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositLogSearch true "分页获取InsDepositLog列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositLogList [get]
func (InsDepositApi *InsDepositApi) GetInsDepositLogList(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositLogSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositLogInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositReport 存酒库存报表
// @Tags InsDeposit
// @Summary 分页获取存取酒报表数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositReportSearch true "分页获取存取酒报表数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositReport [get]
func (InsDepositApi *InsDepositApi) GetInsDepositReport(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositReportSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositReport(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositReportDetail 存酒库存报表详情
// @Tags InsDeposit
// @Summary 存酒库存报表详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositReportDetailSearch true "存酒库存报表详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositReportDetail [get]
func (InsDepositApi *InsDepositApi) GetInsDepositReportDetail(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositReportDetailSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositReportDetail(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositVipReport 获取会员存酒
// @Tags InsDeposit
// @Summary 获取会员存酒
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositReportSearch true "获取会员存酒"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositVipReport [get]
func (InsDepositApi *InsDepositApi) GetInsDepositVipReport(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositReportSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositVipReport(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositVipReportDetail 获取会员存酒详情
// @Tags InsDeposit
// @Summary 获取会员存酒
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositReportDetailSearch true "获取会员存酒"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositVipReportDetail [get]
func (InsDepositApi *InsDepositApi) GetInsDepositVipReportDetail(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositReportDetailSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositVipReportDetail(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// SendSmsCode 存取酒，给会员发送短信验证码
// @Tags InsDeposit
// @Summary 存取酒，给会员发送短信验证码。CoolDown表示冷却时间，单位秒
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsDepositSendSmsReq true "存取酒，给会员发送短信验证码"
// @Success   200   {object}  response.Response{data=insbuyResp.InsDepositSendSmsResp,msg=string}  "发送成功"
// @Router /insDeposit/SendSmsCode [get]
func (A *InsDepositApi) SendSmsCode(c *gin.Context) {
	var req insbuyReq.InsDepositSendSmsReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	resp, err := insDepositService.SendSmsCode(req)
	if err != nil {
		global.GVA_LOG.Error("存取酒，发送短信验证码失败", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// CheckVipMemberCode 存取酒，验证会员
// @Tags InsDeposit
// @Summary 验证会员的短信是否 ok，目前不会自动删除验证码，但会减少有效期，如果验证码过期，需要重新发送
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsDepositVerifyAndSubmitReq true "验证会员码是否正常"
// @Success   200   {object}  response.Response{data=insbuyResp.InsDepositVerifyAndSubmitResp,msg=string}  "获取支付账单"
// @Router /insDeposit/checkVipMemberCode [get]
func (InsDepositApi *InsDepositApi) CheckVipMemberCode(c *gin.Context) {
	var req insbuyReq.InsDepositVerifyAndSubmitReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var resp *insbuyResp.InsDepositVerifyAndSubmitResp
	resp, err := insDepositService.CheckVipMemberCode(req)
	if err != nil {
		global.GVA_LOG.Error("存取洒，验证失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// GetDeskDepositGoodsList 获取当前桌台可存酒商品列表
// @Tags InsDeposit
// @Summary 获取当前桌台可存酒商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetDeskDepositGoodsListReq true "获取当前桌台可存酒商品列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetDeskDepositGoodsListResp,msg=string}  "获取支付账单"
// @Router /insDeposit/getDeskDepositGoodsList [get]
func (InsDepositApi *InsDepositApi) GetDeskDepositGoodsList(c *gin.Context) {
	var req insbuyReq.GetDeskDepositGoodsListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp := make([]insbuyResp.GetDeskDepositGoodsListResp, 0)
	if resp, err = insDepositService.GetDeskDepositGoodsList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// GetDeskDepositGoodsOutList
// @Tags InsDeposit
// @Summary 获取当前桌台可取酒商品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetDeskDepositGoodsListReq true "获取当前桌台可取酒商品列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetDeskDepositGoodsListResp,msg=string}  "获取支付账单"
// @Router /insDeposit/getDeskDepositGoodsOutList [get]
func (InsDepositApi *InsDepositApi) GetDeskDepositGoodsOutList(c *gin.Context) {
	var req insbuyReq.GetDeskDepositGoodsListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, total, err := insDepositService.GetDeskDepositGoodsOutList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDepositLogInList 获取存酒入库列表
// @Tags InsDeposit
// @Summary 获取存酒入库列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetInsDepositLogInListReq true "获取存酒入库列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GetInsDepositLogInListResp,msg=string}  "获取存酒入库列表"
// @Router /insDeposit/getInsDepositLogInList [get]
func (InsDepositApi *InsDepositApi) GetInsDepositLogInList(c *gin.Context) {
	var req insbuyReq.GetInsDepositLogInListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insDepositService.GetInsDepositLogInList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// UpdateDepositInStorage 存酒入库
// @Tags InsDeposit
// @Summary 存酒入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsDepositReq true "存酒入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/updateDepositInStorage [put]
func (InsDepositApi *InsDepositApi) UpdateDepositInStorage(c *gin.Context) {
	var req insbuyReq.UpdateInsDepositReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insDepositService.UpdateDepositInStorage(req)
	response.ResultErr(nil, err, c)
}

// GetDepositExpireData 获取已失效的存酒
// @Tags InsDeposit
// @Summary 获取已失效的存酒
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetDepositExpireReq true "获取失效的存酒"
// @Success   200   {object}  response.Response{data=insbuyResp.GetDepositExpireResp,msg=string}  "获取失效的存酒"
// @Router /insDeposit/getDepositExpireData [get]
func (InsDepositApi *InsDepositApi) GetDepositExpireData(c *gin.Context) {
	var pageInfo insbuyReq.GetDepositExpireReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	insDepositService.StoreId = utils.GetHeaderStoreIdString(c)
	if list, total, err := insDepositService.GetDepositExpireData(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CheckVipMemberServiceCode 存取酒，通过服务码验证会员
// @Tags InsDeposit
// @Summary 验证会员服务码
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.DepositVerifyServiceCodeReq true "验证会员服务码是否正常"
// @Success   200   {object}  response.Response{data=insbuyResp.InsDepositVerifyAndSubmitResp,msg=string}  "获取会员服务码"
// @Router /insDeposit/checkVipMemberServiceCode [post]
func (InsDepositApi *InsDepositApi) CheckVipMemberServiceCode(c *gin.Context) {
	var req insbuyReq.DepositVerifyServiceCodeReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var resp *insbuyResp.InsDepositVerifyAndSubmitResp
	resp, err := insDepositService.CheckVipMemberServiceCode(req)
	if err != nil {
		global.GVA_LOG.Error("服务码存取洒，验证失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// CheckVipMemberByUser 存取酒，验证会员
// @Tags InsDeposit
// @Summary 存取酒，验证会员，不存在就创建
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.DepositVerifyVipMemberByUserReq true "存取酒，验证会员"
// @Success   200   {object}  response.Response{data=insbuyResp.InsDepositVerifyAndSubmitResp,msg=string}  "存取酒，验证会员"
// @Router /insDeposit/checkVipMemberByUser [post]
func (InsDepositApi *InsDepositApi) CheckVipMemberByUser(c *gin.Context) {
	var req insbuyReq.DepositVerifyVipMemberByUserReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var resp *insbuyResp.InsDepositVerifyAndSubmitResp
	resp, err = insDepositService.CheckVipMemberByUser(req)
	if err != nil {
		global.GVA_LOG.Error("会员存取洒，验证失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// GetDespositDesk 获取存取酒桌台
// @Tags InsDeposit
// @Summary 通过会员信息获取存取酒桌台
// @Security ApiKeyAuth
// @accept application/www-form-urlencoded
// @Produce application/json
// @Param data query insbuyReq.InsDepositSendSmsReq true "通过会员信息获取存取酒桌台"
// @Success   200   {object}  response.Response{data=insbuyResp.InsDepositSendSmsRespDesk,msg=string}  "获取存取酒桌台"
// @Router /insDeposit/getDespositDesk [get]
func (InsDepositApi *InsDepositApi) GetDespositDesk(c *gin.Context) {
	var req insbuyReq.InsDepositSendSmsReq
	if err := GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	var resp []insbuyResp.InsDepositSendSmsRespDesk
	resp, err := insDepositService.GetDespositDesk(req)
	if err != nil {
		global.GVA_LOG.Error("服务码存取洒，验证失败!", zap.Error(err))
	}
	response.ResultErr(resp, err, c)
}

// CancelInsDepositLogIn 取消存酒入库
// @Tags InsDeposit
// @Summary 取消存酒入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeposit true "取消存酒入库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/cancelInsDepositLogIn [put]
func (InsDepositApi *InsDepositApi) CancelInsDepositLogIn(c *gin.Context) {
	var req insbuy.InsDeposit
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insDepositService.CancelInsDepositLogIn(req)
	response.ResultErr(nil, err, c)
}

// FindInsDepositLog 查找存酒入库详情
// @Tags InsDeposit
// @Summary 查找存酒入库详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindInsDepositLogReq true "查找存酒入库详情"
// @Success   200   {object}  response.Response{insbuyResp.FindInsDepositLogResp,msg=string}
// @Router /insDeposit/findInsDepositLog [get]
func (InsDepositApi *InsDepositApi) FindInsDepositLog(c *gin.Context) {
	var req insbuyReq.FindInsDepositLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insDepositService.FindInsDepositLog(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// UpdateInsDepositInLog 更新存酒入库
// @Tags InsDeposit
// @Summary 更新存酒入库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateInsDepositInLogReq  true "更新存酒入库"
// @Success   200   {object}  response.Response{insbuyResp.CreateInsDepositResp,msg=string}
// @Router /insDeposit/updateInsDepositInLog [put]
func (InsDepositApi *InsDepositApi) UpdateInsDepositInLog(c *gin.Context) {
	var req insbuyReq.UpdateInsDepositInLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insDepositService.UpdateInsDepositInLog(req); err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
	} else {
		response.OkWithData(resp, c)
	}
}

// RefreshDepositReport 刷新存酒报表
// @Tags InsDeposit
// @Summary 刷新存酒报表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":" 刷新成功"}"
// @Router /insDeposit/refreshDepositReport [put]
func (InsDepositApi *InsDepositApi) RefreshDepositReport(c *gin.Context) {
	err := insDepositService.RefreshDepositReport()
	response.ResultErr(nil, err, c)
}

// GetOpenDeskOutList 获取开台取酒列表
// @Tags InsDeposit
// @Summary 获取开台取酒列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetDeskDepositGoodsListReq true "获取开台取酒列表"
// @Success   200   {object}  response.Response{}
// @Router /insDeposit/getOpenDeskOutList [get]
func (InsDepositApi *InsDepositApi) GetOpenDeskOutList(c *gin.Context) {
	var req insbuyReq.GetDeskDepositGoodsListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insDepositService.GetOpenDeskOutList(req); err != nil {
		global.GVA_LOG.Error("获取开台取酒列表失败!", zap.Error(err))
		response.FailWithMessage("获取开台取酒列表失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// UpdateExtendDepositLog 充公库数据修改
// @Tags InsDepositLog
// @Summary 充公库数据修改
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.UpdateExtendDepositLogReq true "充公库数据修改"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDeposit/updateExtendDepositLog [put]
func (InsDepositApi *InsDepositApi) UpdateExtendDepositLog(c *gin.Context) {
	var insDepositLog insbuyReq.UpdateExtendDepositLogReq
	if err := GinMustBind(c, &insDepositLog); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDepositService.UpdateExtendDepositLog(insDepositLog); err != nil {
		global.GVA_LOG.Error("更新充公库失败!", zap.Error(err))
		response.FailWithMessage("更新充公库失败"+err.Error(), c)
	} else {
		response.OkWithMessage("更新充公库成功", c)
	}
}

// GetInsDepositExtendDetail 存酒库存充公库报表详情
// @Tags InsDeposit
// @Summary 存酒库存充公库报表详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDepositExtendSearch true "存酒库存充公库报表详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDeposit/getInsDepositExtendDetail [get]
func (InsDepositApi *InsDepositApi) GetInsDepositExtendDetail(c *gin.Context) {
	var pageInfo insbuyReq.InsDepositExtendSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDepositService.GetInsDepositExtendDetail(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if pageInfo.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETPublicWarehouseList.ToInt(), list)
			if e != nil {
				return
			}
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// ManualGiveList 手动赠送列表
// @Tags InsOrderInfo
// @Summary 手动赠送列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ManualGiveListReq true "手动赠送列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"手动赠送列表"}"
// @Router /insDeposit/manualGiveList [get]
func (insDepositApi *InsDepositApi) ManualGiveList(c *gin.Context) {
	var req insbuyReq.ManualGiveListReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, total, err := insOrderInfoService.ManualGiveList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     resp,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// CheckIsSamePerson 检查是否为同一人
// @Tags InsDeposit
// @Summary 检查是否为同一人
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.CheckIsSamePersonReq true "检查是否为同一人"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"检查是否为同一人"}"
// @Router /insDeposit/checkIsSamePerson [post]
func (InsDepositApi *InsDepositApi) CheckIsSamePerson(c *gin.Context) {
	var req insbuyReq.CheckIsSamePersonReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insDepositService.CheckIsSamePerson(req)
	response.ResultErr(resp, err, c)
}

// MigrateInventory 库存消耗迁移
// @Tags InsDeposit
// @Summary 库存消耗迁移
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.MigrateInventoryReq true "库存消耗迁移"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"库存消耗迁移"}"
// @Router /insDeposit/migrateInventory [post]
func (InsDepositApi *InsDepositApi) MigrateInventory(c *gin.Context) {
	var req insbuyReq.MigrateInventoryReq
	if err := GinMustBind(c, &req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err := insDepositService.MigrateInventory(req)
	response.Err(err, c)
}
