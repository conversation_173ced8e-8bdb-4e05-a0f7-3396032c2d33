// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsUserRegister(db *gorm.DB, opts ...gen.DOOption) insUserRegister {
	_insUserRegister := insUserRegister{}

	_insUserRegister.insUserRegisterDo.UseDB(db, opts...)
	_insUserRegister.insUserRegisterDo.UseModel(&insbuy.InsUserRegister{})

	tableName := _insUserRegister.insUserRegisterDo.TableName()
	_insUserRegister.ALL = field.NewAsterisk(tableName)
	_insUserRegister.ID = field.NewUint(tableName, "id")
	_insUserRegister.CreatedAt = field.NewTime(tableName, "created_at")
	_insUserRegister.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insUserRegister.DeletedAt = field.NewField(tableName, "deleted_at")
	_insUserRegister.UserName = field.NewString(tableName, "user_name")
	_insUserRegister.Password = field.NewString(tableName, "password")

	_insUserRegister.fillFieldMap()

	return _insUserRegister
}

type insUserRegister struct {
	insUserRegisterDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	UserName  field.String
	Password  field.String

	fieldMap map[string]field.Expr
}

func (i insUserRegister) Table(newTableName string) *insUserRegister {
	i.insUserRegisterDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insUserRegister) As(alias string) *insUserRegister {
	i.insUserRegisterDo.DO = *(i.insUserRegisterDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insUserRegister) updateTableName(table string) *insUserRegister {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.UserName = field.NewString(table, "user_name")
	i.Password = field.NewString(table, "password")

	i.fillFieldMap()

	return i
}

func (i *insUserRegister) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insUserRegister) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["user_name"] = i.UserName
	i.fieldMap["password"] = i.Password
}

func (i insUserRegister) clone(db *gorm.DB) insUserRegister {
	i.insUserRegisterDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insUserRegister) replaceDB(db *gorm.DB) insUserRegister {
	i.insUserRegisterDo.ReplaceDB(db)
	return i
}

type insUserRegisterDo struct{ gen.DO }

func (i insUserRegisterDo) Debug() *insUserRegisterDo {
	return i.withDO(i.DO.Debug())
}

func (i insUserRegisterDo) WithContext(ctx context.Context) *insUserRegisterDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insUserRegisterDo) ReadDB() *insUserRegisterDo {
	return i.Clauses(dbresolver.Read)
}

func (i insUserRegisterDo) WriteDB() *insUserRegisterDo {
	return i.Clauses(dbresolver.Write)
}

func (i insUserRegisterDo) Session(config *gorm.Session) *insUserRegisterDo {
	return i.withDO(i.DO.Session(config))
}

func (i insUserRegisterDo) Clauses(conds ...clause.Expression) *insUserRegisterDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insUserRegisterDo) Returning(value interface{}, columns ...string) *insUserRegisterDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insUserRegisterDo) Not(conds ...gen.Condition) *insUserRegisterDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insUserRegisterDo) Or(conds ...gen.Condition) *insUserRegisterDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insUserRegisterDo) Select(conds ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insUserRegisterDo) Where(conds ...gen.Condition) *insUserRegisterDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insUserRegisterDo) Order(conds ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insUserRegisterDo) Distinct(cols ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insUserRegisterDo) Omit(cols ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insUserRegisterDo) Join(table schema.Tabler, on ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insUserRegisterDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insUserRegisterDo) RightJoin(table schema.Tabler, on ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insUserRegisterDo) Group(cols ...field.Expr) *insUserRegisterDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insUserRegisterDo) Having(conds ...gen.Condition) *insUserRegisterDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insUserRegisterDo) Limit(limit int) *insUserRegisterDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insUserRegisterDo) Offset(offset int) *insUserRegisterDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insUserRegisterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insUserRegisterDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insUserRegisterDo) Unscoped() *insUserRegisterDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insUserRegisterDo) Create(values ...*insbuy.InsUserRegister) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insUserRegisterDo) CreateInBatches(values []*insbuy.InsUserRegister, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insUserRegisterDo) Save(values ...*insbuy.InsUserRegister) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insUserRegisterDo) First() (*insbuy.InsUserRegister, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserRegister), nil
	}
}

func (i insUserRegisterDo) Take() (*insbuy.InsUserRegister, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserRegister), nil
	}
}

func (i insUserRegisterDo) Last() (*insbuy.InsUserRegister, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserRegister), nil
	}
}

func (i insUserRegisterDo) Find() ([]*insbuy.InsUserRegister, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsUserRegister), err
}

func (i insUserRegisterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsUserRegister, err error) {
	buf := make([]*insbuy.InsUserRegister, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insUserRegisterDo) FindInBatches(result *[]*insbuy.InsUserRegister, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insUserRegisterDo) Attrs(attrs ...field.AssignExpr) *insUserRegisterDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insUserRegisterDo) Assign(attrs ...field.AssignExpr) *insUserRegisterDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insUserRegisterDo) Joins(fields ...field.RelationField) *insUserRegisterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insUserRegisterDo) Preload(fields ...field.RelationField) *insUserRegisterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insUserRegisterDo) FirstOrInit() (*insbuy.InsUserRegister, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserRegister), nil
	}
}

func (i insUserRegisterDo) FirstOrCreate() (*insbuy.InsUserRegister, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsUserRegister), nil
	}
}

func (i insUserRegisterDo) FindByPage(offset int, limit int) (result []*insbuy.InsUserRegister, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insUserRegisterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insUserRegisterDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insUserRegisterDo) Delete(models ...*insbuy.InsUserRegister) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insUserRegisterDo) withDO(do gen.Dao) *insUserRegisterDo {
	i.DO = *do.(*gen.DO)
	return i
}
