// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSysDevPrinterConfig(db *gorm.DB, opts ...gen.DOOption) insSysDevPrinterConfig {
	_insSysDevPrinterConfig := insSysDevPrinterConfig{}

	_insSysDevPrinterConfig.insSysDevPrinterConfigDo.UseDB(db, opts...)
	_insSysDevPrinterConfig.insSysDevPrinterConfigDo.UseModel(&insbuy.InsSysDevPrinterConfig{})

	tableName := _insSysDevPrinterConfig.insSysDevPrinterConfigDo.TableName()
	_insSysDevPrinterConfig.ALL = field.NewAsterisk(tableName)
	_insSysDevPrinterConfig.ID = field.NewUint(tableName, "id")
	_insSysDevPrinterConfig.CreatedAt = field.NewTime(tableName, "created_at")
	_insSysDevPrinterConfig.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSysDevPrinterConfig.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSysDevPrinterConfig.CreatedBy = field.NewUint(tableName, "created_by")
	_insSysDevPrinterConfig.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insSysDevPrinterConfig.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insSysDevPrinterConfig.StoreId = field.NewUint(tableName, "store_id")
	_insSysDevPrinterConfig.PrintNum = field.NewInt(tableName, "print_num")
	_insSysDevPrinterConfig.WaitTime = field.NewInt64(tableName, "wait_time")
	_insSysDevPrinterConfig.IsPrintTotal = field.NewBool(tableName, "is_print_total")
	_insSysDevPrinterConfig.Version = field.NewString(tableName, "version")
	_insSysDevPrinterConfig.IsPrintNo = field.NewBool(tableName, "is_print_no")
	_insSysDevPrinterConfig.ChangeDeskMod = field.NewInt(tableName, "change_desk_mod")
	_insSysDevPrinterConfig.Ext = field.NewField(tableName, "ext")

	_insSysDevPrinterConfig.fillFieldMap()

	return _insSysDevPrinterConfig
}

type insSysDevPrinterConfig struct {
	insSysDevPrinterConfigDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	CreatedBy     field.Uint
	UpdatedBy     field.Uint
	DeletedBy     field.Uint
	StoreId       field.Uint
	PrintNum      field.Int
	WaitTime      field.Int64
	IsPrintTotal  field.Bool
	Version       field.String
	IsPrintNo     field.Bool
	ChangeDeskMod field.Int
	Ext           field.Field

	fieldMap map[string]field.Expr
}

func (i insSysDevPrinterConfig) Table(newTableName string) *insSysDevPrinterConfig {
	i.insSysDevPrinterConfigDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSysDevPrinterConfig) As(alias string) *insSysDevPrinterConfig {
	i.insSysDevPrinterConfigDo.DO = *(i.insSysDevPrinterConfigDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSysDevPrinterConfig) updateTableName(table string) *insSysDevPrinterConfig {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.StoreId = field.NewUint(table, "store_id")
	i.PrintNum = field.NewInt(table, "print_num")
	i.WaitTime = field.NewInt64(table, "wait_time")
	i.IsPrintTotal = field.NewBool(table, "is_print_total")
	i.Version = field.NewString(table, "version")
	i.IsPrintNo = field.NewBool(table, "is_print_no")
	i.ChangeDeskMod = field.NewInt(table, "change_desk_mod")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insSysDevPrinterConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSysDevPrinterConfig) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["print_num"] = i.PrintNum
	i.fieldMap["wait_time"] = i.WaitTime
	i.fieldMap["is_print_total"] = i.IsPrintTotal
	i.fieldMap["version"] = i.Version
	i.fieldMap["is_print_no"] = i.IsPrintNo
	i.fieldMap["change_desk_mod"] = i.ChangeDeskMod
	i.fieldMap["ext"] = i.Ext
}

func (i insSysDevPrinterConfig) clone(db *gorm.DB) insSysDevPrinterConfig {
	i.insSysDevPrinterConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSysDevPrinterConfig) replaceDB(db *gorm.DB) insSysDevPrinterConfig {
	i.insSysDevPrinterConfigDo.ReplaceDB(db)
	return i
}

type insSysDevPrinterConfigDo struct{ gen.DO }

func (i insSysDevPrinterConfigDo) Debug() *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Debug())
}

func (i insSysDevPrinterConfigDo) WithContext(ctx context.Context) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSysDevPrinterConfigDo) ReadDB() *insSysDevPrinterConfigDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSysDevPrinterConfigDo) WriteDB() *insSysDevPrinterConfigDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSysDevPrinterConfigDo) Session(config *gorm.Session) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSysDevPrinterConfigDo) Clauses(conds ...clause.Expression) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSysDevPrinterConfigDo) Returning(value interface{}, columns ...string) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSysDevPrinterConfigDo) Not(conds ...gen.Condition) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSysDevPrinterConfigDo) Or(conds ...gen.Condition) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSysDevPrinterConfigDo) Select(conds ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSysDevPrinterConfigDo) Where(conds ...gen.Condition) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSysDevPrinterConfigDo) Order(conds ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSysDevPrinterConfigDo) Distinct(cols ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSysDevPrinterConfigDo) Omit(cols ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSysDevPrinterConfigDo) Join(table schema.Tabler, on ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSysDevPrinterConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSysDevPrinterConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSysDevPrinterConfigDo) Group(cols ...field.Expr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSysDevPrinterConfigDo) Having(conds ...gen.Condition) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSysDevPrinterConfigDo) Limit(limit int) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSysDevPrinterConfigDo) Offset(offset int) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSysDevPrinterConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSysDevPrinterConfigDo) Unscoped() *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSysDevPrinterConfigDo) Create(values ...*insbuy.InsSysDevPrinterConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSysDevPrinterConfigDo) CreateInBatches(values []*insbuy.InsSysDevPrinterConfig, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSysDevPrinterConfigDo) Save(values ...*insbuy.InsSysDevPrinterConfig) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSysDevPrinterConfigDo) First() (*insbuy.InsSysDevPrinterConfig, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinterConfig), nil
	}
}

func (i insSysDevPrinterConfigDo) Take() (*insbuy.InsSysDevPrinterConfig, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinterConfig), nil
	}
}

func (i insSysDevPrinterConfigDo) Last() (*insbuy.InsSysDevPrinterConfig, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinterConfig), nil
	}
}

func (i insSysDevPrinterConfigDo) Find() ([]*insbuy.InsSysDevPrinterConfig, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSysDevPrinterConfig), err
}

func (i insSysDevPrinterConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSysDevPrinterConfig, err error) {
	buf := make([]*insbuy.InsSysDevPrinterConfig, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSysDevPrinterConfigDo) FindInBatches(result *[]*insbuy.InsSysDevPrinterConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSysDevPrinterConfigDo) Attrs(attrs ...field.AssignExpr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSysDevPrinterConfigDo) Assign(attrs ...field.AssignExpr) *insSysDevPrinterConfigDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSysDevPrinterConfigDo) Joins(fields ...field.RelationField) *insSysDevPrinterConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSysDevPrinterConfigDo) Preload(fields ...field.RelationField) *insSysDevPrinterConfigDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSysDevPrinterConfigDo) FirstOrInit() (*insbuy.InsSysDevPrinterConfig, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinterConfig), nil
	}
}

func (i insSysDevPrinterConfigDo) FirstOrCreate() (*insbuy.InsSysDevPrinterConfig, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinterConfig), nil
	}
}

func (i insSysDevPrinterConfigDo) FindByPage(offset int, limit int) (result []*insbuy.InsSysDevPrinterConfig, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSysDevPrinterConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSysDevPrinterConfigDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSysDevPrinterConfigDo) Delete(models ...*insbuy.InsSysDevPrinterConfig) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSysDevPrinterConfigDo) withDO(do gen.Dao) *insSysDevPrinterConfigDo {
	i.DO = *do.(*gen.DO)
	return i
}
