// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTransferProcessStep(db *gorm.DB, opts ...gen.DOOption) insTransferProcessStep {
	_insTransferProcessStep := insTransferProcessStep{}

	_insTransferProcessStep.insTransferProcessStepDo.UseDB(db, opts...)
	_insTransferProcessStep.insTransferProcessStepDo.UseModel(&insbuy.InsTransferProcessStep{})

	tableName := _insTransferProcessStep.insTransferProcessStepDo.TableName()
	_insTransferProcessStep.ALL = field.NewAsterisk(tableName)
	_insTransferProcessStep.ID = field.NewUint(tableName, "id")
	_insTransferProcessStep.CreatedAt = field.NewTime(tableName, "created_at")
	_insTransferProcessStep.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTransferProcessStep.ProcessID = field.NewUint(tableName, "process_id")
	_insTransferProcessStep.StepNumber = field.NewInt(tableName, "step_number")
	_insTransferProcessStep.InoutTypeId = field.NewUint(tableName, "inout_type_id")
	_insTransferProcessStep.IsLinkPrevStep = field.NewInt(tableName, "is_link_prev_step")
	_insTransferProcessStep.IsInStock = field.NewInt(tableName, "is_in_stock")
	_insTransferProcessStep.InoutStatus = field.NewInt(tableName, "inout_status")
	_insTransferProcessStep.Ext = field.NewField(tableName, "ext")

	_insTransferProcessStep.fillFieldMap()

	return _insTransferProcessStep
}

type insTransferProcessStep struct {
	insTransferProcessStepDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	ProcessID      field.Uint
	StepNumber     field.Int
	InoutTypeId    field.Uint
	IsLinkPrevStep field.Int
	IsInStock      field.Int
	InoutStatus    field.Int
	Ext            field.Field

	fieldMap map[string]field.Expr
}

func (i insTransferProcessStep) Table(newTableName string) *insTransferProcessStep {
	i.insTransferProcessStepDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTransferProcessStep) As(alias string) *insTransferProcessStep {
	i.insTransferProcessStepDo.DO = *(i.insTransferProcessStepDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTransferProcessStep) updateTableName(table string) *insTransferProcessStep {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ProcessID = field.NewUint(table, "process_id")
	i.StepNumber = field.NewInt(table, "step_number")
	i.InoutTypeId = field.NewUint(table, "inout_type_id")
	i.IsLinkPrevStep = field.NewInt(table, "is_link_prev_step")
	i.IsInStock = field.NewInt(table, "is_in_stock")
	i.InoutStatus = field.NewInt(table, "inout_status")
	i.Ext = field.NewField(table, "ext")

	i.fillFieldMap()

	return i
}

func (i *insTransferProcessStep) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTransferProcessStep) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["process_id"] = i.ProcessID
	i.fieldMap["step_number"] = i.StepNumber
	i.fieldMap["inout_type_id"] = i.InoutTypeId
	i.fieldMap["is_link_prev_step"] = i.IsLinkPrevStep
	i.fieldMap["is_in_stock"] = i.IsInStock
	i.fieldMap["inout_status"] = i.InoutStatus
	i.fieldMap["ext"] = i.Ext
}

func (i insTransferProcessStep) clone(db *gorm.DB) insTransferProcessStep {
	i.insTransferProcessStepDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTransferProcessStep) replaceDB(db *gorm.DB) insTransferProcessStep {
	i.insTransferProcessStepDo.ReplaceDB(db)
	return i
}

type insTransferProcessStepDo struct{ gen.DO }

func (i insTransferProcessStepDo) Debug() *insTransferProcessStepDo {
	return i.withDO(i.DO.Debug())
}

func (i insTransferProcessStepDo) WithContext(ctx context.Context) *insTransferProcessStepDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTransferProcessStepDo) ReadDB() *insTransferProcessStepDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTransferProcessStepDo) WriteDB() *insTransferProcessStepDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTransferProcessStepDo) Session(config *gorm.Session) *insTransferProcessStepDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTransferProcessStepDo) Clauses(conds ...clause.Expression) *insTransferProcessStepDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTransferProcessStepDo) Returning(value interface{}, columns ...string) *insTransferProcessStepDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTransferProcessStepDo) Not(conds ...gen.Condition) *insTransferProcessStepDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTransferProcessStepDo) Or(conds ...gen.Condition) *insTransferProcessStepDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTransferProcessStepDo) Select(conds ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTransferProcessStepDo) Where(conds ...gen.Condition) *insTransferProcessStepDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTransferProcessStepDo) Order(conds ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTransferProcessStepDo) Distinct(cols ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTransferProcessStepDo) Omit(cols ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTransferProcessStepDo) Join(table schema.Tabler, on ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTransferProcessStepDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTransferProcessStepDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTransferProcessStepDo) Group(cols ...field.Expr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTransferProcessStepDo) Having(conds ...gen.Condition) *insTransferProcessStepDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTransferProcessStepDo) Limit(limit int) *insTransferProcessStepDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTransferProcessStepDo) Offset(offset int) *insTransferProcessStepDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTransferProcessStepDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTransferProcessStepDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTransferProcessStepDo) Unscoped() *insTransferProcessStepDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTransferProcessStepDo) Create(values ...*insbuy.InsTransferProcessStep) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTransferProcessStepDo) CreateInBatches(values []*insbuy.InsTransferProcessStep, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTransferProcessStepDo) Save(values ...*insbuy.InsTransferProcessStep) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTransferProcessStepDo) First() (*insbuy.InsTransferProcessStep, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcessStep), nil
	}
}

func (i insTransferProcessStepDo) Take() (*insbuy.InsTransferProcessStep, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcessStep), nil
	}
}

func (i insTransferProcessStepDo) Last() (*insbuy.InsTransferProcessStep, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcessStep), nil
	}
}

func (i insTransferProcessStepDo) Find() ([]*insbuy.InsTransferProcessStep, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTransferProcessStep), err
}

func (i insTransferProcessStepDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTransferProcessStep, err error) {
	buf := make([]*insbuy.InsTransferProcessStep, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTransferProcessStepDo) FindInBatches(result *[]*insbuy.InsTransferProcessStep, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTransferProcessStepDo) Attrs(attrs ...field.AssignExpr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTransferProcessStepDo) Assign(attrs ...field.AssignExpr) *insTransferProcessStepDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTransferProcessStepDo) Joins(fields ...field.RelationField) *insTransferProcessStepDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTransferProcessStepDo) Preload(fields ...field.RelationField) *insTransferProcessStepDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTransferProcessStepDo) FirstOrInit() (*insbuy.InsTransferProcessStep, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcessStep), nil
	}
}

func (i insTransferProcessStepDo) FirstOrCreate() (*insbuy.InsTransferProcessStep, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTransferProcessStep), nil
	}
}

func (i insTransferProcessStepDo) FindByPage(offset int, limit int) (result []*insbuy.InsTransferProcessStep, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTransferProcessStepDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTransferProcessStepDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTransferProcessStepDo) Delete(models ...*insbuy.InsTransferProcessStep) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTransferProcessStepDo) withDO(do gen.Dao) *insTransferProcessStepDo {
	i.DO = *do.(*gen.DO)
	return i
}
