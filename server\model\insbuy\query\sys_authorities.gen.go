// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newSysAuthority(db *gorm.DB, opts ...gen.DOOption) sysAuthority {
	_sysAuthority := sysAuthority{}

	_sysAuthority.sysAuthorityDo.UseDB(db, opts...)
	_sysAuthority.sysAuthorityDo.UseModel(&system.SysAuthority{})

	tableName := _sysAuthority.sysAuthorityDo.TableName()
	_sysAuthority.ALL = field.NewAsterisk(tableName)
	_sysAuthority.CreatedAt = field.NewTime(tableName, "created_at")
	_sysAuthority.UpdatedAt = field.NewTime(tableName, "updated_at")
	_sysAuthority.DeletedAt = field.NewTime(tableName, "deleted_at")
	_sysAuthority.AuthorityId = field.NewUint(tableName, "authority_id")
	_sysAuthority.AuthorityName = field.NewString(tableName, "authority_name")
	_sysAuthority.ParentId = field.NewUint(tableName, "parent_id")
	_sysAuthority.Code = field.NewString(tableName, "code")
	_sysAuthority.IsVirtual = field.NewInt(tableName, "is_virtual")
	_sysAuthority.DefaultRouter = field.NewString(tableName, "default_router")
	_sysAuthority.WebLogin = field.NewInt(tableName, "web_login")
	_sysAuthority.DataAuthorityId = sysAuthorityManyToManyDataAuthorityId{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("DataAuthorityId", "system.SysAuthority"),
		DataAuthorityId: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("DataAuthorityId.DataAuthorityId", "system.SysAuthority"),
		},
		SysBaseMenus: struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("DataAuthorityId.SysBaseMenus", "system.SysBaseMenu"),
			Parameters: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("DataAuthorityId.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
			},
			MenuBtn: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("DataAuthorityId.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
			},
			SysAuthoritys: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("DataAuthorityId.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
			},
		},
		Users: struct {
			field.RelationField
			Authority struct {
				field.RelationField
			}
			Authorities struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("DataAuthorityId.Users", "system.SysUser"),
			Authority: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("DataAuthorityId.Users.Authority", "system.SysAuthority"),
			},
			Authorities: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("DataAuthorityId.Users.Authorities", "system.SysAuthority"),
			},
		},
	}

	_sysAuthority.SysBaseMenus = sysAuthorityManyToManySysBaseMenus{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SysBaseMenus", "system.SysBaseMenu"),
	}

	_sysAuthority.Users = sysAuthorityManyToManyUsers{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Users", "system.SysUser"),
	}

	_sysAuthority.fillFieldMap()

	return _sysAuthority
}

type sysAuthority struct {
	sysAuthorityDo

	ALL             field.Asterisk
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Time
	AuthorityId     field.Uint
	AuthorityName   field.String
	ParentId        field.Uint
	Code            field.String
	IsVirtual       field.Int
	DefaultRouter   field.String
	WebLogin        field.Int
	DataAuthorityId sysAuthorityManyToManyDataAuthorityId

	SysBaseMenus sysAuthorityManyToManySysBaseMenus

	Users sysAuthorityManyToManyUsers

	fieldMap map[string]field.Expr
}

func (s sysAuthority) Table(newTableName string) *sysAuthority {
	s.sysAuthorityDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysAuthority) As(alias string) *sysAuthority {
	s.sysAuthorityDo.DO = *(s.sysAuthorityDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysAuthority) updateTableName(table string) *sysAuthority {
	s.ALL = field.NewAsterisk(table)
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewTime(table, "deleted_at")
	s.AuthorityId = field.NewUint(table, "authority_id")
	s.AuthorityName = field.NewString(table, "authority_name")
	s.ParentId = field.NewUint(table, "parent_id")
	s.Code = field.NewString(table, "code")
	s.IsVirtual = field.NewInt(table, "is_virtual")
	s.DefaultRouter = field.NewString(table, "default_router")
	s.WebLogin = field.NewInt(table, "web_login")

	s.fillFieldMap()

	return s
}

func (s *sysAuthority) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysAuthority) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
	s.fieldMap["authority_id"] = s.AuthorityId
	s.fieldMap["authority_name"] = s.AuthorityName
	s.fieldMap["parent_id"] = s.ParentId
	s.fieldMap["code"] = s.Code
	s.fieldMap["is_virtual"] = s.IsVirtual
	s.fieldMap["default_router"] = s.DefaultRouter
	s.fieldMap["web_login"] = s.WebLogin

}

func (s sysAuthority) clone(db *gorm.DB) sysAuthority {
	s.sysAuthorityDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysAuthority) replaceDB(db *gorm.DB) sysAuthority {
	s.sysAuthorityDo.ReplaceDB(db)
	return s
}

type sysAuthorityManyToManyDataAuthorityId struct {
	db *gorm.DB

	field.RelationField

	DataAuthorityId struct {
		field.RelationField
	}
	SysBaseMenus struct {
		field.RelationField
		Parameters struct {
			field.RelationField
		}
		MenuBtn struct {
			field.RelationField
		}
		SysAuthoritys struct {
			field.RelationField
		}
	}
	Users struct {
		field.RelationField
		Authority struct {
			field.RelationField
		}
		Authorities struct {
			field.RelationField
		}
	}
}

func (a sysAuthorityManyToManyDataAuthorityId) Where(conds ...field.Expr) *sysAuthorityManyToManyDataAuthorityId {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthorityManyToManyDataAuthorityId) WithContext(ctx context.Context) *sysAuthorityManyToManyDataAuthorityId {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthorityManyToManyDataAuthorityId) Session(session *gorm.Session) *sysAuthorityManyToManyDataAuthorityId {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthorityManyToManyDataAuthorityId) Model(m *system.SysAuthority) *sysAuthorityManyToManyDataAuthorityIdTx {
	return &sysAuthorityManyToManyDataAuthorityIdTx{a.db.Model(m).Association(a.Name())}
}

type sysAuthorityManyToManyDataAuthorityIdTx struct{ tx *gorm.Association }

func (a sysAuthorityManyToManyDataAuthorityIdTx) Find() (result []*system.SysAuthority, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthorityManyToManyDataAuthorityIdTx) Append(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthorityManyToManyDataAuthorityIdTx) Replace(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthorityManyToManyDataAuthorityIdTx) Delete(values ...*system.SysAuthority) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthorityManyToManyDataAuthorityIdTx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthorityManyToManyDataAuthorityIdTx) Count() int64 {
	return a.tx.Count()
}

type sysAuthorityManyToManySysBaseMenus struct {
	db *gorm.DB

	field.RelationField
}

func (a sysAuthorityManyToManySysBaseMenus) Where(conds ...field.Expr) *sysAuthorityManyToManySysBaseMenus {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthorityManyToManySysBaseMenus) WithContext(ctx context.Context) *sysAuthorityManyToManySysBaseMenus {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthorityManyToManySysBaseMenus) Session(session *gorm.Session) *sysAuthorityManyToManySysBaseMenus {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthorityManyToManySysBaseMenus) Model(m *system.SysAuthority) *sysAuthorityManyToManySysBaseMenusTx {
	return &sysAuthorityManyToManySysBaseMenusTx{a.db.Model(m).Association(a.Name())}
}

type sysAuthorityManyToManySysBaseMenusTx struct{ tx *gorm.Association }

func (a sysAuthorityManyToManySysBaseMenusTx) Find() (result []*system.SysBaseMenu, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthorityManyToManySysBaseMenusTx) Append(values ...*system.SysBaseMenu) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthorityManyToManySysBaseMenusTx) Replace(values ...*system.SysBaseMenu) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthorityManyToManySysBaseMenusTx) Delete(values ...*system.SysBaseMenu) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthorityManyToManySysBaseMenusTx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthorityManyToManySysBaseMenusTx) Count() int64 {
	return a.tx.Count()
}

type sysAuthorityManyToManyUsers struct {
	db *gorm.DB

	field.RelationField
}

func (a sysAuthorityManyToManyUsers) Where(conds ...field.Expr) *sysAuthorityManyToManyUsers {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a sysAuthorityManyToManyUsers) WithContext(ctx context.Context) *sysAuthorityManyToManyUsers {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a sysAuthorityManyToManyUsers) Session(session *gorm.Session) *sysAuthorityManyToManyUsers {
	a.db = a.db.Session(session)
	return &a
}

func (a sysAuthorityManyToManyUsers) Model(m *system.SysAuthority) *sysAuthorityManyToManyUsersTx {
	return &sysAuthorityManyToManyUsersTx{a.db.Model(m).Association(a.Name())}
}

type sysAuthorityManyToManyUsersTx struct{ tx *gorm.Association }

func (a sysAuthorityManyToManyUsersTx) Find() (result []*system.SysUser, err error) {
	return result, a.tx.Find(&result)
}

func (a sysAuthorityManyToManyUsersTx) Append(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a sysAuthorityManyToManyUsersTx) Replace(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a sysAuthorityManyToManyUsersTx) Delete(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a sysAuthorityManyToManyUsersTx) Clear() error {
	return a.tx.Clear()
}

func (a sysAuthorityManyToManyUsersTx) Count() int64 {
	return a.tx.Count()
}

type sysAuthorityDo struct{ gen.DO }

func (s sysAuthorityDo) Debug() *sysAuthorityDo {
	return s.withDO(s.DO.Debug())
}

func (s sysAuthorityDo) WithContext(ctx context.Context) *sysAuthorityDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysAuthorityDo) ReadDB() *sysAuthorityDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysAuthorityDo) WriteDB() *sysAuthorityDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysAuthorityDo) Session(config *gorm.Session) *sysAuthorityDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysAuthorityDo) Clauses(conds ...clause.Expression) *sysAuthorityDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysAuthorityDo) Returning(value interface{}, columns ...string) *sysAuthorityDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysAuthorityDo) Not(conds ...gen.Condition) *sysAuthorityDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysAuthorityDo) Or(conds ...gen.Condition) *sysAuthorityDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysAuthorityDo) Select(conds ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysAuthorityDo) Where(conds ...gen.Condition) *sysAuthorityDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysAuthorityDo) Order(conds ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysAuthorityDo) Distinct(cols ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysAuthorityDo) Omit(cols ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysAuthorityDo) Join(table schema.Tabler, on ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysAuthorityDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysAuthorityDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysAuthorityDo) Group(cols ...field.Expr) *sysAuthorityDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysAuthorityDo) Having(conds ...gen.Condition) *sysAuthorityDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysAuthorityDo) Limit(limit int) *sysAuthorityDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysAuthorityDo) Offset(offset int) *sysAuthorityDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysAuthorityDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysAuthorityDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysAuthorityDo) Unscoped() *sysAuthorityDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysAuthorityDo) Create(values ...*system.SysAuthority) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysAuthorityDo) CreateInBatches(values []*system.SysAuthority, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysAuthorityDo) Save(values ...*system.SysAuthority) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysAuthorityDo) First() (*system.SysAuthority, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority), nil
	}
}

func (s sysAuthorityDo) Take() (*system.SysAuthority, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority), nil
	}
}

func (s sysAuthorityDo) Last() (*system.SysAuthority, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority), nil
	}
}

func (s sysAuthorityDo) Find() ([]*system.SysAuthority, error) {
	result, err := s.DO.Find()
	return result.([]*system.SysAuthority), err
}

func (s sysAuthorityDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*system.SysAuthority, err error) {
	buf := make([]*system.SysAuthority, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysAuthorityDo) FindInBatches(result *[]*system.SysAuthority, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysAuthorityDo) Attrs(attrs ...field.AssignExpr) *sysAuthorityDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysAuthorityDo) Assign(attrs ...field.AssignExpr) *sysAuthorityDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysAuthorityDo) Joins(fields ...field.RelationField) *sysAuthorityDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysAuthorityDo) Preload(fields ...field.RelationField) *sysAuthorityDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysAuthorityDo) FirstOrInit() (*system.SysAuthority, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority), nil
	}
}

func (s sysAuthorityDo) FirstOrCreate() (*system.SysAuthority, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority), nil
	}
}

func (s sysAuthorityDo) FindByPage(offset int, limit int) (result []*system.SysAuthority, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysAuthorityDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysAuthorityDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysAuthorityDo) Delete(models ...*system.SysAuthority) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysAuthorityDo) withDO(do gen.Dao) *sysAuthorityDo {
	s.DO = *do.(*gen.DO)
	return s
}
