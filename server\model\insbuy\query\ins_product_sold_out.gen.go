// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductSoldOut(db *gorm.DB, opts ...gen.DOOption) insProductSoldOut {
	_insProductSoldOut := insProductSoldOut{}

	_insProductSoldOut.insProductSoldOutDo.UseDB(db, opts...)
	_insProductSoldOut.insProductSoldOutDo.UseModel(&insbuy.InsProductSoldOut{})

	tableName := _insProductSoldOut.insProductSoldOutDo.TableName()
	_insProductSoldOut.ALL = field.NewAsterisk(tableName)
	_insProductSoldOut.ID = field.NewUint(tableName, "id")
	_insProductSoldOut.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductSoldOut.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductSoldOut.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductSoldOut.StoreId = field.NewUint(tableName, "store_id")
	_insProductSoldOut.ProductId = field.NewUint(tableName, "product_id")
	_insProductSoldOut.SoldOutMode = field.NewInt(tableName, "sold_out_mode")
	_insProductSoldOut.SoldOutDate = field.NewTime(tableName, "sold_out_date")
	_insProductSoldOut.Quantity = field.NewInt(tableName, "quantity")
	_insProductSoldOut.Reason = field.NewString(tableName, "reason")

	_insProductSoldOut.fillFieldMap()

	return _insProductSoldOut
}

type insProductSoldOut struct {
	insProductSoldOutDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	StoreId     field.Uint
	ProductId   field.Uint
	SoldOutMode field.Int
	SoldOutDate field.Time
	Quantity    field.Int
	Reason      field.String

	fieldMap map[string]field.Expr
}

func (i insProductSoldOut) Table(newTableName string) *insProductSoldOut {
	i.insProductSoldOutDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductSoldOut) As(alias string) *insProductSoldOut {
	i.insProductSoldOutDo.DO = *(i.insProductSoldOutDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductSoldOut) updateTableName(table string) *insProductSoldOut {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.ProductId = field.NewUint(table, "product_id")
	i.SoldOutMode = field.NewInt(table, "sold_out_mode")
	i.SoldOutDate = field.NewTime(table, "sold_out_date")
	i.Quantity = field.NewInt(table, "quantity")
	i.Reason = field.NewString(table, "reason")

	i.fillFieldMap()

	return i
}

func (i *insProductSoldOut) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductSoldOut) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["sold_out_mode"] = i.SoldOutMode
	i.fieldMap["sold_out_date"] = i.SoldOutDate
	i.fieldMap["quantity"] = i.Quantity
	i.fieldMap["reason"] = i.Reason
}

func (i insProductSoldOut) clone(db *gorm.DB) insProductSoldOut {
	i.insProductSoldOutDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductSoldOut) replaceDB(db *gorm.DB) insProductSoldOut {
	i.insProductSoldOutDo.ReplaceDB(db)
	return i
}

type insProductSoldOutDo struct{ gen.DO }

func (i insProductSoldOutDo) Debug() *insProductSoldOutDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductSoldOutDo) WithContext(ctx context.Context) *insProductSoldOutDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductSoldOutDo) ReadDB() *insProductSoldOutDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductSoldOutDo) WriteDB() *insProductSoldOutDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductSoldOutDo) Session(config *gorm.Session) *insProductSoldOutDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductSoldOutDo) Clauses(conds ...clause.Expression) *insProductSoldOutDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductSoldOutDo) Returning(value interface{}, columns ...string) *insProductSoldOutDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductSoldOutDo) Not(conds ...gen.Condition) *insProductSoldOutDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductSoldOutDo) Or(conds ...gen.Condition) *insProductSoldOutDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductSoldOutDo) Select(conds ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductSoldOutDo) Where(conds ...gen.Condition) *insProductSoldOutDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductSoldOutDo) Order(conds ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductSoldOutDo) Distinct(cols ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductSoldOutDo) Omit(cols ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductSoldOutDo) Join(table schema.Tabler, on ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductSoldOutDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductSoldOutDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductSoldOutDo) Group(cols ...field.Expr) *insProductSoldOutDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductSoldOutDo) Having(conds ...gen.Condition) *insProductSoldOutDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductSoldOutDo) Limit(limit int) *insProductSoldOutDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductSoldOutDo) Offset(offset int) *insProductSoldOutDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductSoldOutDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductSoldOutDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductSoldOutDo) Unscoped() *insProductSoldOutDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductSoldOutDo) Create(values ...*insbuy.InsProductSoldOut) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductSoldOutDo) CreateInBatches(values []*insbuy.InsProductSoldOut, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductSoldOutDo) Save(values ...*insbuy.InsProductSoldOut) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductSoldOutDo) First() (*insbuy.InsProductSoldOut, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSoldOut), nil
	}
}

func (i insProductSoldOutDo) Take() (*insbuy.InsProductSoldOut, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSoldOut), nil
	}
}

func (i insProductSoldOutDo) Last() (*insbuy.InsProductSoldOut, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSoldOut), nil
	}
}

func (i insProductSoldOutDo) Find() ([]*insbuy.InsProductSoldOut, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductSoldOut), err
}

func (i insProductSoldOutDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductSoldOut, err error) {
	buf := make([]*insbuy.InsProductSoldOut, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductSoldOutDo) FindInBatches(result *[]*insbuy.InsProductSoldOut, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductSoldOutDo) Attrs(attrs ...field.AssignExpr) *insProductSoldOutDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductSoldOutDo) Assign(attrs ...field.AssignExpr) *insProductSoldOutDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductSoldOutDo) Joins(fields ...field.RelationField) *insProductSoldOutDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductSoldOutDo) Preload(fields ...field.RelationField) *insProductSoldOutDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductSoldOutDo) FirstOrInit() (*insbuy.InsProductSoldOut, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSoldOut), nil
	}
}

func (i insProductSoldOutDo) FirstOrCreate() (*insbuy.InsProductSoldOut, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSoldOut), nil
	}
}

func (i insProductSoldOutDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductSoldOut, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductSoldOutDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductSoldOutDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductSoldOutDo) Delete(models ...*insbuy.InsProductSoldOut) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductSoldOutDo) withDO(do gen.Dao) *insProductSoldOutDo {
	i.DO = *do.(*gen.DO)
	return i
}
