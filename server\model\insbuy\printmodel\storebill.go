package printmodel

type TplStoreBill struct {
	TplBase
	UserDeskName string // 用户桌台名称
	DeskName     string // 桌台名称
	DeskArea     string
	//存酒编号-用于显示，后续方便查找的编号
	DepositSn string
	//点单员-存酒员
	Waiter string
	//存酒单号
	StoreSn string
	//存酒人
	StoreUser string
	//存酒人电话
	VipPhone string
	//存酒时间
	StoreTime   string
	Items       []TplStoreBillItem // 商品列表
	TotalNum    int                // 合计数量
	TotalAmount float64            // 合计金额
	Remark      string             //整单备注

	// 以下为可选项
	//是否是仓库联
	IsStore bool
	//是否是单瓶
	IsSingle bool
}

type TplStoreBillItem struct {
	Name       string  // 商品名称
	Spec       float64 // 余量
	Remark     string  // 备注（可选）
	Num        int     // 数量
	Unit       string  // 套
	ExpireTime string
}
