// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsVipScoreLog(db *gorm.DB, opts ...gen.DOOption) insVipScoreLog {
	_insVipScoreLog := insVipScoreLog{}

	_insVipScoreLog.insVipScoreLogDo.UseDB(db, opts...)
	_insVipScoreLog.insVipScoreLogDo.UseModel(&insbuy.InsVipScoreLog{})

	tableName := _insVipScoreLog.insVipScoreLogDo.TableName()
	_insVipScoreLog.ALL = field.NewAsterisk(tableName)
	_insVipScoreLog.ID = field.NewUint(tableName, "id")
	_insVipScoreLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insVipScoreLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insVipScoreLog.DeletedAt = field.NewField(tableName, "deleted_at")
	_insVipScoreLog.VipMemberId = field.NewInt(tableName, "vip_member_id")
	_insVipScoreLog.ChangeScore = field.NewInt(tableName, "change_score")
	_insVipScoreLog.TotalScore = field.NewInt(tableName, "total_score")
	_insVipScoreLog.Reason = field.NewString(tableName, "reason")
	_insVipScoreLog.OpUser = field.NewInt(tableName, "op_user")

	_insVipScoreLog.fillFieldMap()

	return _insVipScoreLog
}

type insVipScoreLog struct {
	insVipScoreLogDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	VipMemberId field.Int
	ChangeScore field.Int
	TotalScore  field.Int
	Reason      field.String
	OpUser      field.Int

	fieldMap map[string]field.Expr
}

func (i insVipScoreLog) Table(newTableName string) *insVipScoreLog {
	i.insVipScoreLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insVipScoreLog) As(alias string) *insVipScoreLog {
	i.insVipScoreLogDo.DO = *(i.insVipScoreLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insVipScoreLog) updateTableName(table string) *insVipScoreLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.VipMemberId = field.NewInt(table, "vip_member_id")
	i.ChangeScore = field.NewInt(table, "change_score")
	i.TotalScore = field.NewInt(table, "total_score")
	i.Reason = field.NewString(table, "reason")
	i.OpUser = field.NewInt(table, "op_user")

	i.fillFieldMap()

	return i
}

func (i *insVipScoreLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insVipScoreLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["vip_member_id"] = i.VipMemberId
	i.fieldMap["change_score"] = i.ChangeScore
	i.fieldMap["total_score"] = i.TotalScore
	i.fieldMap["reason"] = i.Reason
	i.fieldMap["op_user"] = i.OpUser
}

func (i insVipScoreLog) clone(db *gorm.DB) insVipScoreLog {
	i.insVipScoreLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insVipScoreLog) replaceDB(db *gorm.DB) insVipScoreLog {
	i.insVipScoreLogDo.ReplaceDB(db)
	return i
}

type insVipScoreLogDo struct{ gen.DO }

func (i insVipScoreLogDo) Debug() *insVipScoreLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insVipScoreLogDo) WithContext(ctx context.Context) *insVipScoreLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insVipScoreLogDo) ReadDB() *insVipScoreLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insVipScoreLogDo) WriteDB() *insVipScoreLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insVipScoreLogDo) Session(config *gorm.Session) *insVipScoreLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insVipScoreLogDo) Clauses(conds ...clause.Expression) *insVipScoreLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insVipScoreLogDo) Returning(value interface{}, columns ...string) *insVipScoreLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insVipScoreLogDo) Not(conds ...gen.Condition) *insVipScoreLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insVipScoreLogDo) Or(conds ...gen.Condition) *insVipScoreLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insVipScoreLogDo) Select(conds ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insVipScoreLogDo) Where(conds ...gen.Condition) *insVipScoreLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insVipScoreLogDo) Order(conds ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insVipScoreLogDo) Distinct(cols ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insVipScoreLogDo) Omit(cols ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insVipScoreLogDo) Join(table schema.Tabler, on ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insVipScoreLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insVipScoreLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insVipScoreLogDo) Group(cols ...field.Expr) *insVipScoreLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insVipScoreLogDo) Having(conds ...gen.Condition) *insVipScoreLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insVipScoreLogDo) Limit(limit int) *insVipScoreLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insVipScoreLogDo) Offset(offset int) *insVipScoreLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insVipScoreLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insVipScoreLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insVipScoreLogDo) Unscoped() *insVipScoreLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insVipScoreLogDo) Create(values ...*insbuy.InsVipScoreLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insVipScoreLogDo) CreateInBatches(values []*insbuy.InsVipScoreLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insVipScoreLogDo) Save(values ...*insbuy.InsVipScoreLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insVipScoreLogDo) First() (*insbuy.InsVipScoreLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipScoreLog), nil
	}
}

func (i insVipScoreLogDo) Take() (*insbuy.InsVipScoreLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipScoreLog), nil
	}
}

func (i insVipScoreLogDo) Last() (*insbuy.InsVipScoreLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipScoreLog), nil
	}
}

func (i insVipScoreLogDo) Find() ([]*insbuy.InsVipScoreLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsVipScoreLog), err
}

func (i insVipScoreLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsVipScoreLog, err error) {
	buf := make([]*insbuy.InsVipScoreLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insVipScoreLogDo) FindInBatches(result *[]*insbuy.InsVipScoreLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insVipScoreLogDo) Attrs(attrs ...field.AssignExpr) *insVipScoreLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insVipScoreLogDo) Assign(attrs ...field.AssignExpr) *insVipScoreLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insVipScoreLogDo) Joins(fields ...field.RelationField) *insVipScoreLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insVipScoreLogDo) Preload(fields ...field.RelationField) *insVipScoreLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insVipScoreLogDo) FirstOrInit() (*insbuy.InsVipScoreLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipScoreLog), nil
	}
}

func (i insVipScoreLogDo) FirstOrCreate() (*insbuy.InsVipScoreLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipScoreLog), nil
	}
}

func (i insVipScoreLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsVipScoreLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insVipScoreLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insVipScoreLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insVipScoreLogDo) Delete(models ...*insbuy.InsVipScoreLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insVipScoreLogDo) withDO(do gen.Dao) *insVipScoreLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
