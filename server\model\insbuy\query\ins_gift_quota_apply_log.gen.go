// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftQuotaApplyLog(db *gorm.DB, opts ...gen.DOOption) insGiftQuotaApplyLog {
	_insGiftQuotaApplyLog := insGiftQuotaApplyLog{}

	_insGiftQuotaApplyLog.insGiftQuotaApplyLogDo.UseDB(db, opts...)
	_insGiftQuotaApplyLog.insGiftQuotaApplyLogDo.UseModel(&insbuy.InsGiftQuotaApplyLog{})

	tableName := _insGiftQuotaApplyLog.insGiftQuotaApplyLogDo.TableName()
	_insGiftQuotaApplyLog.ALL = field.NewAsterisk(tableName)
	_insGiftQuotaApplyLog.ID = field.NewUint(tableName, "id")
	_insGiftQuotaApplyLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftQuotaApplyLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftQuotaApplyLog.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftQuotaApplyLog.AssignId = field.NewUint(tableName, "assign_id")
	_insGiftQuotaApplyLog.AssignDetailsId = field.NewUint(tableName, "assign_details_id")
	_insGiftQuotaApplyLog.ApplyUser = field.NewUint(tableName, "apply_user")
	_insGiftQuotaApplyLog.Quota = field.NewFloat64(tableName, "quota")
	_insGiftQuotaApplyLog.StoreId = field.NewUint(tableName, "store_id")
	_insGiftQuotaApplyLog.Status = field.NewInt(tableName, "status")
	_insGiftQuotaApplyLog.Deadline = field.NewTime(tableName, "deadline")
	_insGiftQuotaApplyLog.Operator = field.NewUint(tableName, "operator")
	_insGiftQuotaApplyLog.BusinessDay = field.NewTime(tableName, "business_day")

	_insGiftQuotaApplyLog.fillFieldMap()

	return _insGiftQuotaApplyLog
}

type insGiftQuotaApplyLog struct {
	insGiftQuotaApplyLogDo

	ALL             field.Asterisk
	ID              field.Uint
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	AssignId        field.Uint
	AssignDetailsId field.Uint
	ApplyUser       field.Uint
	Quota           field.Float64
	StoreId         field.Uint
	Status          field.Int
	Deadline        field.Time
	Operator        field.Uint
	BusinessDay     field.Time

	fieldMap map[string]field.Expr
}

func (i insGiftQuotaApplyLog) Table(newTableName string) *insGiftQuotaApplyLog {
	i.insGiftQuotaApplyLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftQuotaApplyLog) As(alias string) *insGiftQuotaApplyLog {
	i.insGiftQuotaApplyLogDo.DO = *(i.insGiftQuotaApplyLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftQuotaApplyLog) updateTableName(table string) *insGiftQuotaApplyLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.AssignId = field.NewUint(table, "assign_id")
	i.AssignDetailsId = field.NewUint(table, "assign_details_id")
	i.ApplyUser = field.NewUint(table, "apply_user")
	i.Quota = field.NewFloat64(table, "quota")
	i.StoreId = field.NewUint(table, "store_id")
	i.Status = field.NewInt(table, "status")
	i.Deadline = field.NewTime(table, "deadline")
	i.Operator = field.NewUint(table, "operator")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insGiftQuotaApplyLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftQuotaApplyLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["assign_id"] = i.AssignId
	i.fieldMap["assign_details_id"] = i.AssignDetailsId
	i.fieldMap["apply_user"] = i.ApplyUser
	i.fieldMap["quota"] = i.Quota
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["status"] = i.Status
	i.fieldMap["deadline"] = i.Deadline
	i.fieldMap["operator"] = i.Operator
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insGiftQuotaApplyLog) clone(db *gorm.DB) insGiftQuotaApplyLog {
	i.insGiftQuotaApplyLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftQuotaApplyLog) replaceDB(db *gorm.DB) insGiftQuotaApplyLog {
	i.insGiftQuotaApplyLogDo.ReplaceDB(db)
	return i
}

type insGiftQuotaApplyLogDo struct{ gen.DO }

func (i insGiftQuotaApplyLogDo) Debug() *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftQuotaApplyLogDo) WithContext(ctx context.Context) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftQuotaApplyLogDo) ReadDB() *insGiftQuotaApplyLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftQuotaApplyLogDo) WriteDB() *insGiftQuotaApplyLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftQuotaApplyLogDo) Session(config *gorm.Session) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftQuotaApplyLogDo) Clauses(conds ...clause.Expression) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftQuotaApplyLogDo) Returning(value interface{}, columns ...string) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftQuotaApplyLogDo) Not(conds ...gen.Condition) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftQuotaApplyLogDo) Or(conds ...gen.Condition) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftQuotaApplyLogDo) Select(conds ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftQuotaApplyLogDo) Where(conds ...gen.Condition) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftQuotaApplyLogDo) Order(conds ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftQuotaApplyLogDo) Distinct(cols ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftQuotaApplyLogDo) Omit(cols ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftQuotaApplyLogDo) Join(table schema.Tabler, on ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftQuotaApplyLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftQuotaApplyLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftQuotaApplyLogDo) Group(cols ...field.Expr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftQuotaApplyLogDo) Having(conds ...gen.Condition) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftQuotaApplyLogDo) Limit(limit int) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftQuotaApplyLogDo) Offset(offset int) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftQuotaApplyLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftQuotaApplyLogDo) Unscoped() *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftQuotaApplyLogDo) Create(values ...*insbuy.InsGiftQuotaApplyLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftQuotaApplyLogDo) CreateInBatches(values []*insbuy.InsGiftQuotaApplyLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftQuotaApplyLogDo) Save(values ...*insbuy.InsGiftQuotaApplyLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftQuotaApplyLogDo) First() (*insbuy.InsGiftQuotaApplyLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaApplyLog), nil
	}
}

func (i insGiftQuotaApplyLogDo) Take() (*insbuy.InsGiftQuotaApplyLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaApplyLog), nil
	}
}

func (i insGiftQuotaApplyLogDo) Last() (*insbuy.InsGiftQuotaApplyLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaApplyLog), nil
	}
}

func (i insGiftQuotaApplyLogDo) Find() ([]*insbuy.InsGiftQuotaApplyLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftQuotaApplyLog), err
}

func (i insGiftQuotaApplyLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftQuotaApplyLog, err error) {
	buf := make([]*insbuy.InsGiftQuotaApplyLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftQuotaApplyLogDo) FindInBatches(result *[]*insbuy.InsGiftQuotaApplyLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftQuotaApplyLogDo) Attrs(attrs ...field.AssignExpr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftQuotaApplyLogDo) Assign(attrs ...field.AssignExpr) *insGiftQuotaApplyLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftQuotaApplyLogDo) Joins(fields ...field.RelationField) *insGiftQuotaApplyLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftQuotaApplyLogDo) Preload(fields ...field.RelationField) *insGiftQuotaApplyLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftQuotaApplyLogDo) FirstOrInit() (*insbuy.InsGiftQuotaApplyLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaApplyLog), nil
	}
}

func (i insGiftQuotaApplyLogDo) FirstOrCreate() (*insbuy.InsGiftQuotaApplyLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaApplyLog), nil
	}
}

func (i insGiftQuotaApplyLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftQuotaApplyLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftQuotaApplyLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftQuotaApplyLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftQuotaApplyLogDo) Delete(models ...*insbuy.InsGiftQuotaApplyLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftQuotaApplyLogDo) withDO(do gen.Dao) *insGiftQuotaApplyLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
