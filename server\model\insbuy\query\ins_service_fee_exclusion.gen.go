// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsServiceFeeExclusion(db *gorm.DB, opts ...gen.DOOption) insServiceFeeExclusion {
	_insServiceFeeExclusion := insServiceFeeExclusion{}

	_insServiceFeeExclusion.insServiceFeeExclusionDo.UseDB(db, opts...)
	_insServiceFeeExclusion.insServiceFeeExclusionDo.UseModel(&insbuy.InsServiceFeeExclusion{})

	tableName := _insServiceFeeExclusion.insServiceFeeExclusionDo.TableName()
	_insServiceFeeExclusion.ALL = field.NewAsterisk(tableName)
	_insServiceFeeExclusion.ID = field.NewUint(tableName, "id")
	_insServiceFeeExclusion.CreatedAt = field.NewTime(tableName, "created_at")
	_insServiceFeeExclusion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insServiceFeeExclusion.DeletedAt = field.NewField(tableName, "deleted_at")
	_insServiceFeeExclusion.ServiceFeeId = field.NewUint(tableName, "service_fee_id")
	_insServiceFeeExclusion.ExclusionType = field.NewInt(tableName, "exclusion_type")
	_insServiceFeeExclusion.TargetId = field.NewUint(tableName, "target_id")

	_insServiceFeeExclusion.fillFieldMap()

	return _insServiceFeeExclusion
}

type insServiceFeeExclusion struct {
	insServiceFeeExclusionDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	ServiceFeeId  field.Uint
	ExclusionType field.Int
	TargetId      field.Uint

	fieldMap map[string]field.Expr
}

func (i insServiceFeeExclusion) Table(newTableName string) *insServiceFeeExclusion {
	i.insServiceFeeExclusionDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insServiceFeeExclusion) As(alias string) *insServiceFeeExclusion {
	i.insServiceFeeExclusionDo.DO = *(i.insServiceFeeExclusionDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insServiceFeeExclusion) updateTableName(table string) *insServiceFeeExclusion {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ServiceFeeId = field.NewUint(table, "service_fee_id")
	i.ExclusionType = field.NewInt(table, "exclusion_type")
	i.TargetId = field.NewUint(table, "target_id")

	i.fillFieldMap()

	return i
}

func (i *insServiceFeeExclusion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insServiceFeeExclusion) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["service_fee_id"] = i.ServiceFeeId
	i.fieldMap["exclusion_type"] = i.ExclusionType
	i.fieldMap["target_id"] = i.TargetId
}

func (i insServiceFeeExclusion) clone(db *gorm.DB) insServiceFeeExclusion {
	i.insServiceFeeExclusionDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insServiceFeeExclusion) replaceDB(db *gorm.DB) insServiceFeeExclusion {
	i.insServiceFeeExclusionDo.ReplaceDB(db)
	return i
}

type insServiceFeeExclusionDo struct{ gen.DO }

func (i insServiceFeeExclusionDo) Debug() *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Debug())
}

func (i insServiceFeeExclusionDo) WithContext(ctx context.Context) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insServiceFeeExclusionDo) ReadDB() *insServiceFeeExclusionDo {
	return i.Clauses(dbresolver.Read)
}

func (i insServiceFeeExclusionDo) WriteDB() *insServiceFeeExclusionDo {
	return i.Clauses(dbresolver.Write)
}

func (i insServiceFeeExclusionDo) Session(config *gorm.Session) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Session(config))
}

func (i insServiceFeeExclusionDo) Clauses(conds ...clause.Expression) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insServiceFeeExclusionDo) Returning(value interface{}, columns ...string) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insServiceFeeExclusionDo) Not(conds ...gen.Condition) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insServiceFeeExclusionDo) Or(conds ...gen.Condition) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insServiceFeeExclusionDo) Select(conds ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insServiceFeeExclusionDo) Where(conds ...gen.Condition) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insServiceFeeExclusionDo) Order(conds ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insServiceFeeExclusionDo) Distinct(cols ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insServiceFeeExclusionDo) Omit(cols ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insServiceFeeExclusionDo) Join(table schema.Tabler, on ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insServiceFeeExclusionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insServiceFeeExclusionDo) RightJoin(table schema.Tabler, on ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insServiceFeeExclusionDo) Group(cols ...field.Expr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insServiceFeeExclusionDo) Having(conds ...gen.Condition) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insServiceFeeExclusionDo) Limit(limit int) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insServiceFeeExclusionDo) Offset(offset int) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insServiceFeeExclusionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insServiceFeeExclusionDo) Unscoped() *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insServiceFeeExclusionDo) Create(values ...*insbuy.InsServiceFeeExclusion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insServiceFeeExclusionDo) CreateInBatches(values []*insbuy.InsServiceFeeExclusion, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insServiceFeeExclusionDo) Save(values ...*insbuy.InsServiceFeeExclusion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insServiceFeeExclusionDo) First() (*insbuy.InsServiceFeeExclusion, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeExclusion), nil
	}
}

func (i insServiceFeeExclusionDo) Take() (*insbuy.InsServiceFeeExclusion, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeExclusion), nil
	}
}

func (i insServiceFeeExclusionDo) Last() (*insbuy.InsServiceFeeExclusion, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeExclusion), nil
	}
}

func (i insServiceFeeExclusionDo) Find() ([]*insbuy.InsServiceFeeExclusion, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsServiceFeeExclusion), err
}

func (i insServiceFeeExclusionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsServiceFeeExclusion, err error) {
	buf := make([]*insbuy.InsServiceFeeExclusion, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insServiceFeeExclusionDo) FindInBatches(result *[]*insbuy.InsServiceFeeExclusion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insServiceFeeExclusionDo) Attrs(attrs ...field.AssignExpr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insServiceFeeExclusionDo) Assign(attrs ...field.AssignExpr) *insServiceFeeExclusionDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insServiceFeeExclusionDo) Joins(fields ...field.RelationField) *insServiceFeeExclusionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insServiceFeeExclusionDo) Preload(fields ...field.RelationField) *insServiceFeeExclusionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insServiceFeeExclusionDo) FirstOrInit() (*insbuy.InsServiceFeeExclusion, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeExclusion), nil
	}
}

func (i insServiceFeeExclusionDo) FirstOrCreate() (*insbuy.InsServiceFeeExclusion, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsServiceFeeExclusion), nil
	}
}

func (i insServiceFeeExclusionDo) FindByPage(offset int, limit int) (result []*insbuy.InsServiceFeeExclusion, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insServiceFeeExclusionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insServiceFeeExclusionDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insServiceFeeExclusionDo) Delete(models ...*insbuy.InsServiceFeeExclusion) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insServiceFeeExclusionDo) withDO(do gen.Dao) *insServiceFeeExclusionDo {
	i.DO = *do.(*gen.DO)
	return i
}
