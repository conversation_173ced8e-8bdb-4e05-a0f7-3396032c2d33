package test

import (
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport"
	"github.com/xtulnx/jkit-go/jtime"
	"testing"
)

func TestStoreWarehouseIn(t *testing.T) {
	prepare()
	res, err := insreport.StoreWarehouseIn(context.Background(), query.Q, insreport.FinancialWarehouseParams{
		PageNum:  1,
		PageSize: 20,
	})
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(res)
}

func TestOpenStorage(t *testing.T) {
	prepare()
	dp := insreport.DailyReportParam{
		StartDate: jtime.Str2Time("2024-05-01"),
		EndDate:   jtime.Str2Time("2024-12-09"),
		PageNum:   int(1),
		PageSize:  int(0),
	}
	resp, _, err := insreport.DailyOpenDesk(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(resp)
}
