package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseOutApi struct {
}

// ConfirmOut 确认出库
// @Tags InsWarehouseOut
// @Summary 确认出库
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.ConfirmOutReq true "确认出库"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"确认出库成功"}"
// @Router /insWarehouse/confirmOut [post]
func (InsWarehouseOutApi *InsWarehouseOutApi) ConfirmOut(c *gin.Context) {
	var req insbuyReq.ConfirmOutReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseOutService.ConfirmOut(req); err != nil {
		global.GVA_LOG.Error("确认出库失败!", zap.Error(err))
		response.FailWithMessage("确认出库失败"+err.Error(), c)
	} else {
		response.OkWithMessage("确认出库成功", c)
	}
}

// StartReturn 发起退货
// @Tags InsWarehouseOut
// @Summary 发起退货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.StartReturnReq true "发起退货"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"发起退货成功"}"
// @Router /insWarehouse/startReturn [post]
func (InsWarehouseOutApi *InsWarehouseOutApi) StartReturn(c *gin.Context) {
	var req insbuyReq.StartReturnReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err = insWarehouseOutService.StartReturn(req); err != nil {
		global.GVA_LOG.Error("发起退货失败!", zap.Error(err))
		response.FailWithMessage("发起退货失败"+err.Error(), c)
	} else {
		response.OkWithMessage("发起退货成功", c)
	}
}
