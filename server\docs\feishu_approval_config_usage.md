# 飞书审批代码配置使用指南

## 概述

本文档介绍如何使用简化的飞书审批代码配置功能，通过一个通用的配置列表管理所有审批代码。

## 配置文件结构

### 1. 配置文件位置

- 示例文件：`server/config/feishu_approval_codes.example.yaml`
- 实际配置：在现有的 `config.yaml` 文件中添加或创建独立配置文件

### 2. 简化的配置文件结构

```yaml
feishu-app:
  app-id: "your-feishu-app-id"
  app-secret: "your-feishu-app-secret"

  # 审批代码配置列表
  approval-codes:
    # 销售合同审批
    - code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
      name: "销售合同审批"
      description: "销售合同审批流程"
      enabled: true
      tags: ["contract", "sales"]

    # 大额销售合同审批
    - code: "A123B456-7890-1234-5678-90ABCDEF1234"
      name: "大额销售合同审批"
      description: "金额超过10万的销售合同审批"
      enabled: true
      tags: ["contract", "sales", "high-amount"]

    # 采购合同审批
    - code: "C789D012-3456-7890-1234-56789ABCDEF0"
      name: "采购合同审批"
      description: "采购合同审批流程"
      enabled: true
      tags: ["contract", "purchase"]

    # 报销审批
    - code: "Q012R345-6789-0123-4567-890123456789"
      name: "日常报销审批"
      description: "日常费用报销审批"
      enabled: true
      tags: ["finance", "expense"]

    # IT设备申请
    - code: "W678X901-2345-6789-0123-456789012345"
      name: "IT设备申请审批"
      description: "IT设备采购申请审批"
      enabled: true
      tags: ["it", "equipment"]

    # 禁用的审批代码示例
    - code: "C456D789-0123-4567-8901-234567890123"
      name: "会议室预订审批"
      description: "会议室预订审批流程"
      enabled: false
      tags: ["office", "meeting-room"]
```

## 代码使用示例

### 1. 获取所有启用的审批代码

```go
package main

import (
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

func main() {
    configService := &insbuy.FeishuApprovalConfigService{}

    // 获取所有启用的审批代码
    allCodes := configService.GetAllApprovalCodes()
    fmt.Printf("所有启用的审批代码: %v\n", allCodes)
}
```

### 2. 根据标签获取审批代码

```go
// 获取所有合同相关的审批代码
contractCodes := configService.GetApprovalCodesByTag("contract")
fmt.Printf("合同审批代码: %v\n", contractCodes)

// 获取销售合同审批代码
salesCodes := configService.GetApprovalCodesByTag("sales")
fmt.Printf("销售合同审批代码: %v\n", salesCodes)

// 获取同时包含 contract 和 sales 标签的审批代码
salesContractCodes := configService.GetApprovalCodesByTags([]string{"contract", "sales"})
fmt.Printf("销售合同审批代码: %v\n", salesContractCodes)
```

### 3. 获取审批代码配置信息

```go
// 获取所有配置列表
allConfigs := configService.GetApprovalConfigList()
for _, config := range allConfigs {
    fmt.Printf("代码: %s, 名称: %s, 启用: %v, 标签: %v\n",
        config.Code, config.Name, config.Enabled, config.Tags)
}

// 获取只启用的配置
enabledConfigs := configService.GetEnabledApprovalConfigs()
fmt.Printf("启用的配置数量: %d\n", len(enabledConfigs))

// 获取特定审批代码的详细信息
codeInfo, err := configService.GetApprovalCodeInfo("F523F053-7AC6-4280-A4E7-B35E0C0431B5")
if err == nil {
    fmt.Printf("审批代码信息: %+v\n", codeInfo)
}
```

### 4. 验证审批代码

```go
// 验证单个审批代码
isValid := configService.ValidateApprovalCode("F523F053-7AC6-4280-A4E7-B35E0C0431B5")
fmt.Printf("审批代码是否有效: %v\n", isValid)

// 批量验证审批代码
codes := []string{
    "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
    "INVALID-CODE-123",
    "C789D012-3456-7890-1234-56789ABCDEF0",
}
valid, invalid := configService.ValidateApprovalCodes(codes)
fmt.Printf("有效代码: %v\n", valid)
fmt.Printf("无效代码: %v\n", invalid)
```

### 4. 验证审批代码

```bash
POST /api/feishu/approval-codes/validate
Content-Type: application/json

{
  "approval_codes": [
    "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
    "INVALID-CODE-123",
    "C789D012-3456-7890-1234-56789ABCDEF0"
  ]
}
```

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "total_count": 3,
    "valid_count": 2,
    "invalid_count": 1,
    "valid_codes": [
      "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
      "C789D012-3456-7890-1234-56789ABCDEF0"
    ],
    "invalid_codes": ["INVALID-CODE-123"]
  },
  "msg": "验证完成"
}
```

### 5. 获取审批代码信息

```bash
GET /api/feishu/approval-codes/info?code=F523F053-7AC6-4280-A4E7-B35E0C0431B5
```

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
    "group_name": "contract.sales",
    "description": "销售合同审批"
  },
  "msg": "获取成功"
}
```

### 6. 获取同步配置

```bash
GET /api/feishu/approval-codes/sync-config?name=it-equipment
```

**响应示例：**
```json
{
  "code": 0,
  "data": {
    "auto_sync": true,
    "sync_interval": 30,
    "batch_size": 20,
    "page_size": 100,
    "max_retries": 3,
    "retry_delay": 5
  },
  "msg": "获取成功"
}
```

## 代码使用示例

### 1. 在服务中使用配置

```go
package main

import (
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

func main() {
    configService := &insbuy.FeishuApprovalConfigService{}
    
    // 获取所有合同相关审批代码
    contractCodes := configService.GetApprovalCodesByType("contract")
    fmt.Printf("合同审批代码: %v\n", contractCodes)
    
    // 获取销售合同审批代码
    salesCodes := configService.GetApprovalCodesByType("contract.sales")
    fmt.Printf("销售合同审批代码: %v\n", salesCodes)
    
    // 验证审批代码
    codes := []string{"F523F053-7AC6-4280-A4E7-B35E0C0431B5", "INVALID-CODE"}
    valid, invalid := configService.ValidateApprovalCodes(codes)
    fmt.Printf("有效代码: %v, 无效代码: %v\n", valid, invalid)
    
    // 获取同步配置
    syncConfig := configService.GetSyncConfig("it-equipment")
    fmt.Printf("同步配置: %+v\n", syncConfig)
}
```

### 5. 在合同同步中使用

```go
// 使用配置的审批代码进行批量同步
func syncContractsByTag(tag string) error {
    configService := &insbuy.FeishuApprovalConfigService{}
    contractService := &insbuy.InsContractService{}

    // 获取指定标签的审批代码
    approvalCodes := configService.GetApprovalCodesByTag(tag)
    if len(approvalCodes) == 0 {
        return fmt.Errorf("未找到标签 %s 的审批代码", tag)
    }

    // 构建同步请求
    req := request.ContractMultiSyncRequest{
        ApprovalCodes: approvalCodes,
        BatchSize:     20,
        PageSize:      100,
        MaxRetries:    3,
        RetryDelay:    5,
    }

    // 执行同步
    resp, err := contractService.SyncMultipleContractData(req)
    if err != nil {
        return fmt.Errorf("同步失败: %w", err)
    }

    fmt.Printf("同步完成: 成功 %d/%d 个审批代码\n",
        resp.SuccessCodes, resp.TotalCodes)

    return nil
}

// 同步多标签审批代码
func syncContractsByTags(tags []string) error {
    configService := &insbuy.FeishuApprovalConfigService{}
    contractService := &insbuy.InsContractService{}

    // 获取包含所有指定标签的审批代码
    approvalCodes := configService.GetApprovalCodesByTags(tags)
    if len(approvalCodes) == 0 {
        return fmt.Errorf("未找到包含标签 %v 的审批代码", tags)
    }

    // 构建同步请求
    req := request.ContractMultiSyncRequest{
        ApprovalCodes: approvalCodes,
        BatchSize:     20,
        PageSize:      100,
        MaxRetries:    3,
        RetryDelay:    5,
    }

    // 执行同步
    resp, err := contractService.SyncMultipleContractData(req)
    if err != nil {
        return fmt.Errorf("同步失败: %w", err)
    }

    fmt.Printf("同步完成: 成功 %d/%d 个审批代码\n",
        resp.SuccessCodes, resp.TotalCodes)

    return nil
}

// 使用示例
func main() {
    // 同步所有合同相关审批
    err := syncContractsByTag("contract")
    if err != nil {
        log.Printf("同步合同失败: %v", err)
    }

    // 同步销售合同审批
    err = syncContractsByTag("sales")
    if err != nil {
        log.Printf("同步销售合同失败: %v", err)
    }

    // 同步高金额销售合同审批
    err = syncContractsByTags([]string{"contract", "sales", "high-amount"})
    if err != nil {
        log.Printf("同步高金额销售合同失败: %v", err)
    }

    // 同步所有启用的审批代码
    configService := &insbuy.FeishuApprovalConfigService{}
    allCodes := configService.GetAllApprovalCodes()

    req := request.ContractMultiSyncRequest{
        ApprovalCodes: allCodes,
        BatchSize:     20,
        PageSize:      100,
        MaxRetries:    3,
        RetryDelay:    5,
    }

    contractService := &insbuy.InsContractService{}
    resp, err := contractService.SyncMultipleContractData(req)
    if err != nil {
        log.Printf("同步所有审批代码失败: %v", err)
    } else {
        fmt.Printf("同步所有审批代码完成: 成功 %d/%d 个\n",
            resp.SuccessCodes, resp.TotalCodes)
    }
}
```

## 配置管理最佳实践

### 1. 使用标签进行分类

```yaml
# 推荐的标签分类方式
approval-codes:
  # 按业务类型 + 子类型标签
  - code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
    name: "销售合同审批"
    enabled: true
    tags: ["contract", "sales"]

  # 添加特殊属性标签
  - code: "A123B456-7890-1234-5678-90ABCDEF1234"
    name: "大额销售合同审批"
    enabled: true
    tags: ["contract", "sales", "high-amount", "urgent"]

  # 按部门标签
  - code: "C789D012-3456-7890-1234-56789ABCDEF0"
    name: "IT部门设备采购审批"
    enabled: true
    tags: ["purchase", "it", "equipment"]
```

### 2. 环境配置管理

```yaml
# 开发环境
feishu-app:
  app-id: "dev-app-id"
  approval-codes:
    - code: "DEV-F523F053-7AC6-4280-A4E7-B35E0C0431B5"
      name: "开发环境销售合同审批"
      enabled: true
      tags: ["contract", "sales", "dev"]

# 生产环境
feishu-app:
  app-id: "prod-app-id"
  approval-codes:
    - code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
      name: "销售合同审批"
      enabled: true
      tags: ["contract", "sales", "prod"]
```

### 3. 启用/禁用管理

```yaml
# 临时禁用某个审批代码
- code: "OLD-APPROVAL-CODE"
  name: "旧版审批流程"
  enabled: false  # 禁用但保留配置
  tags: ["deprecated"]

# 新版审批代码
- code: "NEW-APPROVAL-CODE"
  name: "新版审批流程"
  enabled: true
  tags: ["contract", "sales", "v2"]
```

## 故障排查

### 1. 常见问题

**问题：获取不到审批代码**
```bash
# 检查配置文件是否正确加载
GET /api/feishu/approval-codes/groups

# 验证特定审批代码
POST /api/feishu/approval-codes/validate
```

**问题：同步失败**
```bash
# 检查审批代码是否有效
GET /api/feishu/approval-codes/info?code=YOUR_CODE

# 检查同步配置
GET /api/feishu/approval-codes/sync-config?name=YOUR_TYPE
```

### 2. 日志查看

```bash
# 查看配置加载日志
grep "FeishuApprovalConfig" /var/log/app.log

# 查看同步日志
grep "SyncMultipleContractData" /var/log/app.log
```

## 总结

通过简化的飞书审批代码配置功能，您可以：

1. **简单配置**：使用统一的配置列表管理所有审批代码
2. **标签分类**：通过标签灵活分类和查询审批代码
3. **启用控制**：通过 enabled 字段控制审批代码的启用状态
4. **代码复用**：在合同同步等功能中轻松复用配置的审批代码
5. **验证机制**：确保使用的审批代码都在配置中存在
6. **易于维护**：简化的配置结构，易于理解和维护

### 主要优势

- **配置简单**：一个列表包含所有审批代码配置
- **查询灵活**：支持按标签、启用状态等多种方式查询
- **易于扩展**：添加新的审批代码只需在列表中增加一项
- **类型安全**：通过代码验证确保配置的正确性
- **环境友好**：支持不同环境使用不同的配置

这为飞书审批数据的管理和同步提供了简洁而强大的配置能力。
