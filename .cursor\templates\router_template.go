package jyhapp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type {{ServiceName}}Router struct{}

func (r *{{ServiceName}}Router) Init{{ServiceName}}Router(PrivateGroup, JyhappGroup *gin.RouterGroup) {
	router := PrivateGroup.Group("{{service-name}}")
	routerWithoutRecord := JyhappGroup.Group("{{service-name}}")
	api := v1.ApiGroupApp.JyhappApiGroup.{{ServiceName}}Api
	{
		router.POST("create", api.Create)       // 创建
		router.PUT("update", api.Update)        // 更新
		router.DELETE("delete", api.Delete)     // 删除
		router.GET("detail", api.GetDetail)     // 获取详情
		router.GET("list", api.GetList)         // 获取列表
	}
	{
		routerWithoutRecord.POST("create", api.Create)       // 创建
		routerWithoutRecord.PUT("update", api.Update)        // 更新
		routerWithoutRecord.DELETE("delete", api.Delete)     // 删除
		routerWithoutRecord.GET("detail", api.GetDetail)     // 获取详情
		routerWithoutRecord.GET("list", api.GetList)         // 获取列表
	}
} 