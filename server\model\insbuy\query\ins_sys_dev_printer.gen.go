// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSysDevPrinter(db *gorm.DB, opts ...gen.DOOption) insSysDevPrinter {
	_insSysDevPrinter := insSysDevPrinter{}

	_insSysDevPrinter.insSysDevPrinterDo.UseDB(db, opts...)
	_insSysDevPrinter.insSysDevPrinterDo.UseModel(&insbuy.InsSysDevPrinter{})

	tableName := _insSysDevPrinter.insSysDevPrinterDo.TableName()
	_insSysDevPrinter.ALL = field.NewAsterisk(tableName)
	_insSysDevPrinter.ID = field.NewUint(tableName, "id")
	_insSysDevPrinter.CreatedAt = field.NewTime(tableName, "created_at")
	_insSysDevPrinter.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSysDevPrinter.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSysDevPrinter.CreatedBy = field.NewUint(tableName, "created_by")
	_insSysDevPrinter.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insSysDevPrinter.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insSysDevPrinter.StoreId = field.NewUint(tableName, "store_id")
	_insSysDevPrinter.Code = field.NewString(tableName, "code")
	_insSysDevPrinter.Name = field.NewString(tableName, "name")
	_insSysDevPrinter.Url = field.NewString(tableName, "url")
	_insSysDevPrinter.Proxy = field.NewString(tableName, "proxy")
	_insSysDevPrinter.Type = field.NewString(tableName, "type")
	_insSysDevPrinter.IsDefault = field.NewInt(tableName, "is_default")
	_insSysDevPrinter.Status = field.NewInt(tableName, "status")
	_insSysDevPrinter.Seq = field.NewInt(tableName, "seq")
	_insSysDevPrinter.Width = field.NewString(tableName, "width")
	_insSysDevPrinter.Location = field.NewString(tableName, "location")
	_insSysDevPrinter.Ext = field.NewString(tableName, "ext")
	_insSysDevPrinter.Remark = field.NewString(tableName, "remark")

	_insSysDevPrinter.fillFieldMap()

	return _insSysDevPrinter
}

type insSysDevPrinter struct {
	insSysDevPrinterDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	CreatedBy field.Uint
	UpdatedBy field.Uint
	DeletedBy field.Uint
	StoreId   field.Uint
	Code      field.String
	Name      field.String
	Url       field.String
	Proxy     field.String
	Type      field.String
	IsDefault field.Int
	Status    field.Int
	Seq       field.Int
	Width     field.String
	Location  field.String
	Ext       field.String
	Remark    field.String

	fieldMap map[string]field.Expr
}

func (i insSysDevPrinter) Table(newTableName string) *insSysDevPrinter {
	i.insSysDevPrinterDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSysDevPrinter) As(alias string) *insSysDevPrinter {
	i.insSysDevPrinterDo.DO = *(i.insSysDevPrinterDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSysDevPrinter) updateTableName(table string) *insSysDevPrinter {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.StoreId = field.NewUint(table, "store_id")
	i.Code = field.NewString(table, "code")
	i.Name = field.NewString(table, "name")
	i.Url = field.NewString(table, "url")
	i.Proxy = field.NewString(table, "proxy")
	i.Type = field.NewString(table, "type")
	i.IsDefault = field.NewInt(table, "is_default")
	i.Status = field.NewInt(table, "status")
	i.Seq = field.NewInt(table, "seq")
	i.Width = field.NewString(table, "width")
	i.Location = field.NewString(table, "location")
	i.Ext = field.NewString(table, "ext")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insSysDevPrinter) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSysDevPrinter) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 20)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["code"] = i.Code
	i.fieldMap["name"] = i.Name
	i.fieldMap["url"] = i.Url
	i.fieldMap["proxy"] = i.Proxy
	i.fieldMap["type"] = i.Type
	i.fieldMap["is_default"] = i.IsDefault
	i.fieldMap["status"] = i.Status
	i.fieldMap["seq"] = i.Seq
	i.fieldMap["width"] = i.Width
	i.fieldMap["location"] = i.Location
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["remark"] = i.Remark
}

func (i insSysDevPrinter) clone(db *gorm.DB) insSysDevPrinter {
	i.insSysDevPrinterDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSysDevPrinter) replaceDB(db *gorm.DB) insSysDevPrinter {
	i.insSysDevPrinterDo.ReplaceDB(db)
	return i
}

type insSysDevPrinterDo struct{ gen.DO }

func (i insSysDevPrinterDo) Debug() *insSysDevPrinterDo {
	return i.withDO(i.DO.Debug())
}

func (i insSysDevPrinterDo) WithContext(ctx context.Context) *insSysDevPrinterDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSysDevPrinterDo) ReadDB() *insSysDevPrinterDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSysDevPrinterDo) WriteDB() *insSysDevPrinterDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSysDevPrinterDo) Session(config *gorm.Session) *insSysDevPrinterDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSysDevPrinterDo) Clauses(conds ...clause.Expression) *insSysDevPrinterDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSysDevPrinterDo) Returning(value interface{}, columns ...string) *insSysDevPrinterDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSysDevPrinterDo) Not(conds ...gen.Condition) *insSysDevPrinterDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSysDevPrinterDo) Or(conds ...gen.Condition) *insSysDevPrinterDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSysDevPrinterDo) Select(conds ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSysDevPrinterDo) Where(conds ...gen.Condition) *insSysDevPrinterDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSysDevPrinterDo) Order(conds ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSysDevPrinterDo) Distinct(cols ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSysDevPrinterDo) Omit(cols ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSysDevPrinterDo) Join(table schema.Tabler, on ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSysDevPrinterDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSysDevPrinterDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSysDevPrinterDo) Group(cols ...field.Expr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSysDevPrinterDo) Having(conds ...gen.Condition) *insSysDevPrinterDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSysDevPrinterDo) Limit(limit int) *insSysDevPrinterDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSysDevPrinterDo) Offset(offset int) *insSysDevPrinterDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSysDevPrinterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSysDevPrinterDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSysDevPrinterDo) Unscoped() *insSysDevPrinterDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSysDevPrinterDo) Create(values ...*insbuy.InsSysDevPrinter) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSysDevPrinterDo) CreateInBatches(values []*insbuy.InsSysDevPrinter, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSysDevPrinterDo) Save(values ...*insbuy.InsSysDevPrinter) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSysDevPrinterDo) First() (*insbuy.InsSysDevPrinter, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinter), nil
	}
}

func (i insSysDevPrinterDo) Take() (*insbuy.InsSysDevPrinter, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinter), nil
	}
}

func (i insSysDevPrinterDo) Last() (*insbuy.InsSysDevPrinter, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinter), nil
	}
}

func (i insSysDevPrinterDo) Find() ([]*insbuy.InsSysDevPrinter, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSysDevPrinter), err
}

func (i insSysDevPrinterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSysDevPrinter, err error) {
	buf := make([]*insbuy.InsSysDevPrinter, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSysDevPrinterDo) FindInBatches(result *[]*insbuy.InsSysDevPrinter, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSysDevPrinterDo) Attrs(attrs ...field.AssignExpr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSysDevPrinterDo) Assign(attrs ...field.AssignExpr) *insSysDevPrinterDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSysDevPrinterDo) Joins(fields ...field.RelationField) *insSysDevPrinterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSysDevPrinterDo) Preload(fields ...field.RelationField) *insSysDevPrinterDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSysDevPrinterDo) FirstOrInit() (*insbuy.InsSysDevPrinter, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinter), nil
	}
}

func (i insSysDevPrinterDo) FirstOrCreate() (*insbuy.InsSysDevPrinter, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSysDevPrinter), nil
	}
}

func (i insSysDevPrinterDo) FindByPage(offset int, limit int) (result []*insbuy.InsSysDevPrinter, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSysDevPrinterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSysDevPrinterDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSysDevPrinterDo) Delete(models ...*insbuy.InsSysDevPrinter) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSysDevPrinterDo) withDO(do gen.Dao) *insSysDevPrinterDo {
	i.DO = *do.(*gen.DO)
	return i
}
