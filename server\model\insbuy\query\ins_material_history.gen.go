// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsMaterialHistory(db *gorm.DB, opts ...gen.DOOption) insMaterialHistory {
	_insMaterialHistory := insMaterialHistory{}

	_insMaterialHistory.insMaterialHistoryDo.UseDB(db, opts...)
	_insMaterialHistory.insMaterialHistoryDo.UseModel(&insbuy.InsMaterialHistory{})

	tableName := _insMaterialHistory.insMaterialHistoryDo.TableName()
	_insMaterialHistory.ALL = field.NewAsterisk(tableName)
	_insMaterialHistory.ID = field.NewUint(tableName, "id")
	_insMaterialHistory.CreatedAt = field.NewTime(tableName, "created_at")
	_insMaterialHistory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insMaterialHistory.MaterialId = field.NewUint(tableName, "material_id")
	_insMaterialHistory.OldShopPrice = field.NewFloat64(tableName, "old_shop_price")
	_insMaterialHistory.OldRebatePrice = field.NewFloat64(tableName, "old_rebate_price")
	_insMaterialHistory.OldStandardPrice = field.NewFloat64(tableName, "old_standard_price")
	_insMaterialHistory.NewShopPrice = field.NewFloat64(tableName, "new_shop_price")
	_insMaterialHistory.NewRebatePrice = field.NewFloat64(tableName, "new_rebate_price")
	_insMaterialHistory.NewStandardPrice = field.NewFloat64(tableName, "new_standard_price")
	_insMaterialHistory.Remark = field.NewString(tableName, "remark")
	_insMaterialHistory.OperatorId = field.NewUint(tableName, "operator_id")

	_insMaterialHistory.fillFieldMap()

	return _insMaterialHistory
}

type insMaterialHistory struct {
	insMaterialHistoryDo

	ALL              field.Asterisk
	ID               field.Uint
	CreatedAt        field.Time
	UpdatedAt        field.Time
	MaterialId       field.Uint
	OldShopPrice     field.Float64
	OldRebatePrice   field.Float64
	OldStandardPrice field.Float64
	NewShopPrice     field.Float64
	NewRebatePrice   field.Float64
	NewStandardPrice field.Float64
	Remark           field.String
	OperatorId       field.Uint

	fieldMap map[string]field.Expr
}

func (i insMaterialHistory) Table(newTableName string) *insMaterialHistory {
	i.insMaterialHistoryDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insMaterialHistory) As(alias string) *insMaterialHistory {
	i.insMaterialHistoryDo.DO = *(i.insMaterialHistoryDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insMaterialHistory) updateTableName(table string) *insMaterialHistory {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.MaterialId = field.NewUint(table, "material_id")
	i.OldShopPrice = field.NewFloat64(table, "old_shop_price")
	i.OldRebatePrice = field.NewFloat64(table, "old_rebate_price")
	i.OldStandardPrice = field.NewFloat64(table, "old_standard_price")
	i.NewShopPrice = field.NewFloat64(table, "new_shop_price")
	i.NewRebatePrice = field.NewFloat64(table, "new_rebate_price")
	i.NewStandardPrice = field.NewFloat64(table, "new_standard_price")
	i.Remark = field.NewString(table, "remark")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insMaterialHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insMaterialHistory) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["old_shop_price"] = i.OldShopPrice
	i.fieldMap["old_rebate_price"] = i.OldRebatePrice
	i.fieldMap["old_standard_price"] = i.OldStandardPrice
	i.fieldMap["new_shop_price"] = i.NewShopPrice
	i.fieldMap["new_rebate_price"] = i.NewRebatePrice
	i.fieldMap["new_standard_price"] = i.NewStandardPrice
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insMaterialHistory) clone(db *gorm.DB) insMaterialHistory {
	i.insMaterialHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insMaterialHistory) replaceDB(db *gorm.DB) insMaterialHistory {
	i.insMaterialHistoryDo.ReplaceDB(db)
	return i
}

type insMaterialHistoryDo struct{ gen.DO }

func (i insMaterialHistoryDo) Debug() *insMaterialHistoryDo {
	return i.withDO(i.DO.Debug())
}

func (i insMaterialHistoryDo) WithContext(ctx context.Context) *insMaterialHistoryDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insMaterialHistoryDo) ReadDB() *insMaterialHistoryDo {
	return i.Clauses(dbresolver.Read)
}

func (i insMaterialHistoryDo) WriteDB() *insMaterialHistoryDo {
	return i.Clauses(dbresolver.Write)
}

func (i insMaterialHistoryDo) Session(config *gorm.Session) *insMaterialHistoryDo {
	return i.withDO(i.DO.Session(config))
}

func (i insMaterialHistoryDo) Clauses(conds ...clause.Expression) *insMaterialHistoryDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insMaterialHistoryDo) Returning(value interface{}, columns ...string) *insMaterialHistoryDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insMaterialHistoryDo) Not(conds ...gen.Condition) *insMaterialHistoryDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insMaterialHistoryDo) Or(conds ...gen.Condition) *insMaterialHistoryDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insMaterialHistoryDo) Select(conds ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insMaterialHistoryDo) Where(conds ...gen.Condition) *insMaterialHistoryDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insMaterialHistoryDo) Order(conds ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insMaterialHistoryDo) Distinct(cols ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insMaterialHistoryDo) Omit(cols ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insMaterialHistoryDo) Join(table schema.Tabler, on ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insMaterialHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insMaterialHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insMaterialHistoryDo) Group(cols ...field.Expr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insMaterialHistoryDo) Having(conds ...gen.Condition) *insMaterialHistoryDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insMaterialHistoryDo) Limit(limit int) *insMaterialHistoryDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insMaterialHistoryDo) Offset(offset int) *insMaterialHistoryDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insMaterialHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insMaterialHistoryDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insMaterialHistoryDo) Unscoped() *insMaterialHistoryDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insMaterialHistoryDo) Create(values ...*insbuy.InsMaterialHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insMaterialHistoryDo) CreateInBatches(values []*insbuy.InsMaterialHistory, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insMaterialHistoryDo) Save(values ...*insbuy.InsMaterialHistory) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insMaterialHistoryDo) First() (*insbuy.InsMaterialHistory, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialHistory), nil
	}
}

func (i insMaterialHistoryDo) Take() (*insbuy.InsMaterialHistory, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialHistory), nil
	}
}

func (i insMaterialHistoryDo) Last() (*insbuy.InsMaterialHistory, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialHistory), nil
	}
}

func (i insMaterialHistoryDo) Find() ([]*insbuy.InsMaterialHistory, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsMaterialHistory), err
}

func (i insMaterialHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsMaterialHistory, err error) {
	buf := make([]*insbuy.InsMaterialHistory, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insMaterialHistoryDo) FindInBatches(result *[]*insbuy.InsMaterialHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insMaterialHistoryDo) Attrs(attrs ...field.AssignExpr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insMaterialHistoryDo) Assign(attrs ...field.AssignExpr) *insMaterialHistoryDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insMaterialHistoryDo) Joins(fields ...field.RelationField) *insMaterialHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insMaterialHistoryDo) Preload(fields ...field.RelationField) *insMaterialHistoryDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insMaterialHistoryDo) FirstOrInit() (*insbuy.InsMaterialHistory, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialHistory), nil
	}
}

func (i insMaterialHistoryDo) FirstOrCreate() (*insbuy.InsMaterialHistory, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsMaterialHistory), nil
	}
}

func (i insMaterialHistoryDo) FindByPage(offset int, limit int) (result []*insbuy.InsMaterialHistory, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insMaterialHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insMaterialHistoryDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insMaterialHistoryDo) Delete(models ...*insbuy.InsMaterialHistory) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insMaterialHistoryDo) withDO(do gen.Dao) *insMaterialHistoryDo {
	i.DO = *do.(*gen.DO)
	return i
}
