package paymodel

import "encoding/json"

type MerchantConfig struct {
	Appid      string `json:"appid"`       //应用id
	MerchantId string `json:"merchant_id"` //商户id
	SecretKey  string `json:"secret_key"`  //密钥key
	NotifyUrl  string `json:"notify_url"`  //回调地址
	Host       string `json:"host"`        //域名
	IsProd     string `json:"is_prod"`     //是否生产环境
}

// ToJson 转json
func (m *MerchantConfig) ToJson() string {
	if m == nil {
		return ""
	}
	marshal, _ := json.Marshal(m)
	return string(marshal)
}

// FromJson 转结构体
func (m *MerchantConfig) FromJson(str string) error {
	return json.Unmarshal([]byte(str), m)
}
