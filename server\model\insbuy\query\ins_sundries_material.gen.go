// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSundriesMaterial(db *gorm.DB, opts ...gen.DOOption) insSundriesMaterial {
	_insSundriesMaterial := insSundriesMaterial{}

	_insSundriesMaterial.insSundriesMaterialDo.UseDB(db, opts...)
	_insSundriesMaterial.insSundriesMaterialDo.UseModel(&insbuy.InsSundriesMaterial{})

	tableName := _insSundriesMaterial.insSundriesMaterialDo.TableName()
	_insSundriesMaterial.ALL = field.NewAsterisk(tableName)
	_insSundriesMaterial.ID = field.NewUint(tableName, "id")
	_insSundriesMaterial.CreatedAt = field.NewTime(tableName, "created_at")
	_insSundriesMaterial.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSundriesMaterial.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSundriesMaterial.MUnit = field.NewInt(tableName, "m_unit")
	_insSundriesMaterial.Spec = field.NewFloat64(tableName, "spec")
	_insSundriesMaterial.Unit = field.NewInt(tableName, "unit")
	_insSundriesMaterial.MaterialSn = field.NewString(tableName, "material_sn")
	_insSundriesMaterial.CategoryId = field.NewInt(tableName, "category_id")
	_insSundriesMaterial.BrandId = field.NewInt(tableName, "brand_id")
	_insSundriesMaterial.MaterialName = field.NewString(tableName, "material_name")
	_insSundriesMaterial.ShopPrice = field.NewFloat64(tableName, "shop_price")
	_insSundriesMaterial.MarketPrice = field.NewFloat64(tableName, "market_price")

	_insSundriesMaterial.fillFieldMap()

	return _insSundriesMaterial
}

type insSundriesMaterial struct {
	insSundriesMaterialDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	MUnit        field.Int
	Spec         field.Float64
	Unit         field.Int
	MaterialSn   field.String
	CategoryId   field.Int
	BrandId      field.Int
	MaterialName field.String
	ShopPrice    field.Float64
	MarketPrice  field.Float64

	fieldMap map[string]field.Expr
}

func (i insSundriesMaterial) Table(newTableName string) *insSundriesMaterial {
	i.insSundriesMaterialDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSundriesMaterial) As(alias string) *insSundriesMaterial {
	i.insSundriesMaterialDo.DO = *(i.insSundriesMaterialDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSundriesMaterial) updateTableName(table string) *insSundriesMaterial {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.MUnit = field.NewInt(table, "m_unit")
	i.Spec = field.NewFloat64(table, "spec")
	i.Unit = field.NewInt(table, "unit")
	i.MaterialSn = field.NewString(table, "material_sn")
	i.CategoryId = field.NewInt(table, "category_id")
	i.BrandId = field.NewInt(table, "brand_id")
	i.MaterialName = field.NewString(table, "material_name")
	i.ShopPrice = field.NewFloat64(table, "shop_price")
	i.MarketPrice = field.NewFloat64(table, "market_price")

	i.fillFieldMap()

	return i
}

func (i *insSundriesMaterial) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSundriesMaterial) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["m_unit"] = i.MUnit
	i.fieldMap["spec"] = i.Spec
	i.fieldMap["unit"] = i.Unit
	i.fieldMap["material_sn"] = i.MaterialSn
	i.fieldMap["category_id"] = i.CategoryId
	i.fieldMap["brand_id"] = i.BrandId
	i.fieldMap["material_name"] = i.MaterialName
	i.fieldMap["shop_price"] = i.ShopPrice
	i.fieldMap["market_price"] = i.MarketPrice
}

func (i insSundriesMaterial) clone(db *gorm.DB) insSundriesMaterial {
	i.insSundriesMaterialDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSundriesMaterial) replaceDB(db *gorm.DB) insSundriesMaterial {
	i.insSundriesMaterialDo.ReplaceDB(db)
	return i
}

type insSundriesMaterialDo struct{ gen.DO }

func (i insSundriesMaterialDo) Debug() *insSundriesMaterialDo {
	return i.withDO(i.DO.Debug())
}

func (i insSundriesMaterialDo) WithContext(ctx context.Context) *insSundriesMaterialDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSundriesMaterialDo) ReadDB() *insSundriesMaterialDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSundriesMaterialDo) WriteDB() *insSundriesMaterialDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSundriesMaterialDo) Session(config *gorm.Session) *insSundriesMaterialDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSundriesMaterialDo) Clauses(conds ...clause.Expression) *insSundriesMaterialDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSundriesMaterialDo) Returning(value interface{}, columns ...string) *insSundriesMaterialDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSundriesMaterialDo) Not(conds ...gen.Condition) *insSundriesMaterialDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSundriesMaterialDo) Or(conds ...gen.Condition) *insSundriesMaterialDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSundriesMaterialDo) Select(conds ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSundriesMaterialDo) Where(conds ...gen.Condition) *insSundriesMaterialDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSundriesMaterialDo) Order(conds ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSundriesMaterialDo) Distinct(cols ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSundriesMaterialDo) Omit(cols ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSundriesMaterialDo) Join(table schema.Tabler, on ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSundriesMaterialDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSundriesMaterialDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSundriesMaterialDo) Group(cols ...field.Expr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSundriesMaterialDo) Having(conds ...gen.Condition) *insSundriesMaterialDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSundriesMaterialDo) Limit(limit int) *insSundriesMaterialDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSundriesMaterialDo) Offset(offset int) *insSundriesMaterialDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSundriesMaterialDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSundriesMaterialDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSundriesMaterialDo) Unscoped() *insSundriesMaterialDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSundriesMaterialDo) Create(values ...*insbuy.InsSundriesMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSundriesMaterialDo) CreateInBatches(values []*insbuy.InsSundriesMaterial, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSundriesMaterialDo) Save(values ...*insbuy.InsSundriesMaterial) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSundriesMaterialDo) First() (*insbuy.InsSundriesMaterial, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterial), nil
	}
}

func (i insSundriesMaterialDo) Take() (*insbuy.InsSundriesMaterial, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterial), nil
	}
}

func (i insSundriesMaterialDo) Last() (*insbuy.InsSundriesMaterial, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterial), nil
	}
}

func (i insSundriesMaterialDo) Find() ([]*insbuy.InsSundriesMaterial, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSundriesMaterial), err
}

func (i insSundriesMaterialDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSundriesMaterial, err error) {
	buf := make([]*insbuy.InsSundriesMaterial, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSundriesMaterialDo) FindInBatches(result *[]*insbuy.InsSundriesMaterial, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSundriesMaterialDo) Attrs(attrs ...field.AssignExpr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSundriesMaterialDo) Assign(attrs ...field.AssignExpr) *insSundriesMaterialDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSundriesMaterialDo) Joins(fields ...field.RelationField) *insSundriesMaterialDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSundriesMaterialDo) Preload(fields ...field.RelationField) *insSundriesMaterialDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSundriesMaterialDo) FirstOrInit() (*insbuy.InsSundriesMaterial, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterial), nil
	}
}

func (i insSundriesMaterialDo) FirstOrCreate() (*insbuy.InsSundriesMaterial, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSundriesMaterial), nil
	}
}

func (i insSundriesMaterialDo) FindByPage(offset int, limit int) (result []*insbuy.InsSundriesMaterial, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSundriesMaterialDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSundriesMaterialDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSundriesMaterialDo) Delete(models ...*insbuy.InsSundriesMaterial) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSundriesMaterialDo) withDO(do gen.Dao) *insSundriesMaterialDo {
	i.DO = *do.(*gen.DO)
	return i
}
