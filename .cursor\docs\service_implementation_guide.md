# 服务实现规范指南

[English Version](../../docs/service_implementation_guide.md)

本指南概述了在系统中实现新服务的标准步骤和规范。

## 目录结构规范

```
server/
├── api/v1/jyhapp/        # API 层实现
│   ├── enter.go          # API 组注册
│   └── {service}.go      # 具体服务的 API 实现
├── model/jyhapp/         # 数据模型
│   ├── request/          # 请求结构体
│   └── response/         # 响应结构体
├── service/jyhapp/       # 服务层实现
│   └── {service}.go      # 具体服务实现
└── router/jyhapp/        # 路由注册
    ├── enter.go          # 路由组注册
    └── {service}.go      # 具体服务路由
```

## 实现步骤

### 1. 服务层实现 (Service Layer)

位置: `server/service/jyhapp/`

```go
// {service}.go
package jyhapp

type NewService struct{}

func (s *NewService) Create(req *request.NewCreate) error {
    // 实现逻辑
}
```

### 2. 请求/响应模型 (Request/Response Models)

位置: `server/model/jyhapp/request/`

```go
// request/{service}.go
package request

type NewCreate struct {
    Name string `json:"name" binding:"required"` // 必填字段
}
```

### 3. API 层实现 (API Layer)

位置: `server/api/v1/jyhapp/`

```go
// {service}.go
package jyhapp

type NewApi struct{}

// @Tags      NewService
// @Summary   创建
// @Security  ApiKeyAuth
// @Router    /new-service/create [post]
func (m *NewApi) Create(c *gin.Context) {
    // API 实现
}
```

### 4. 路由实现 (Router)

位置: `server/router/jyhapp/`

```go
// {service}.go
package jyhapp

type NewRouter struct{}

func (r *NewRouter) InitNewRouter(PrivateGroup, PublicGroup *gin.RouterGroup) {
    // 路由实现
}
```

## 命名规范

1. 文件命名：使用小写，下划线分隔（例如：user_service.go）
2. 结构体命名：大驼峰（例如：UserService）
3. 接口命名：大驼峰（例如：IUserService）
4. 方法命名：大驼峰（例如：CreateUser）

## 错误处理规范

1. 服务层返回 error 或 (result, error)
2. API 层使用统一响应格式：
   - 成功：response.OkWith...
   - 失败：response.FailWithMessage
3. 日志记录：使用 global.GVA_LOG.Error

## 注释规范

1. 包注释：描述包的主要功能
2. 结构体注释：描述结构体的用途
3. 方法注释：使用 Swagger 注解
4. 关键代码注释：说明复杂逻辑

## 测试规范

1. 单元测试：测试服务层方法
2. 接口测试：使用 Swagger 测试 API
3. 错误测试：验证错误处理
4. 边界测试：测试边界条件

## 使用方法

1. 在 Cursor 中打开新项目时，先查看本规范
2. 使用 `Ctrl/Cmd + P` 搜索 ".cursor/docs/service_implementation_guide.md"
3. 遵循规范中的步骤和示例进行开发
4. 使用提供的代码模板快速开始新服务的实现

## 代码模板

可以在 `.cursor/templates/` 目录下找到各层的代码模板：
- service_template.go
- api_template.go
- router_template.go
- request_template.go

## 检查清单

实现新服务时，确保：
- [ ] 完成所有必要的层级实现
- [ ] 遵循命名规范
- [ ] 添加适当的注释和文档
- [ ] 实现错误处理
- [ ] 添加必要的测试
- [ ] 注册到相应的组件中 