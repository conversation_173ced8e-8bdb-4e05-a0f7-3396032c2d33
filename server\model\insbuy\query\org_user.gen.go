// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newOrgUser(db *gorm.DB, opts ...gen.DOOption) orgUser {
	_orgUser := orgUser{}

	_orgUser.orgUserDo.UseDB(db, opts...)
	_orgUser.orgUserDo.UseModel(&model.OrgUser{})

	tableName := _orgUser.orgUserDo.TableName()
	_orgUser.ALL = field.NewAsterisk(tableName)
	_orgUser.OrganizationID = field.NewUint(tableName, "organization_id")
	_orgUser.SysUserID = field.NewUint(tableName, "sys_user_id")
	_orgUser.IsAdmin = field.NewBool(tableName, "is_admin")
	_orgUser.IsVisual = field.NewBool(tableName, "is_visual")
	_orgUser.Organization = orgUserBelongsToOrganization{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Organization", "model.Organization"),
	}

	_orgUser.SysUser = orgUserBelongsToSysUser{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SysUser", "system.SysUser"),
		Authority: struct {
			field.RelationField
			DataAuthorityId struct {
				field.RelationField
			}
			SysBaseMenus struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}
			Users struct {
				field.RelationField
			}
		}{
			RelationField: field.NewRelation("SysUser.Authority", "system.SysAuthority"),
			DataAuthorityId: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("SysUser.Authority.DataAuthorityId", "system.SysAuthority"),
			},
			SysBaseMenus: struct {
				field.RelationField
				Parameters struct {
					field.RelationField
				}
				MenuBtn struct {
					field.RelationField
				}
				SysAuthoritys struct {
					field.RelationField
				}
			}{
				RelationField: field.NewRelation("SysUser.Authority.SysBaseMenus", "system.SysBaseMenu"),
				Parameters: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("SysUser.Authority.SysBaseMenus.Parameters", "system.SysBaseMenuParameter"),
				},
				MenuBtn: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("SysUser.Authority.SysBaseMenus.MenuBtn", "system.SysBaseMenuBtn"),
				},
				SysAuthoritys: struct {
					field.RelationField
				}{
					RelationField: field.NewRelation("SysUser.Authority.SysBaseMenus.SysAuthoritys", "system.SysAuthority"),
				},
			},
			Users: struct {
				field.RelationField
			}{
				RelationField: field.NewRelation("SysUser.Authority.Users", "system.SysUser"),
			},
		},
		Authorities: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("SysUser.Authorities", "system.SysAuthority"),
		},
	}

	_orgUser.fillFieldMap()

	return _orgUser
}

type orgUser struct {
	orgUserDo

	ALL            field.Asterisk
	OrganizationID field.Uint
	SysUserID      field.Uint
	IsAdmin        field.Bool
	IsVisual       field.Bool
	Organization   orgUserBelongsToOrganization

	SysUser orgUserBelongsToSysUser

	fieldMap map[string]field.Expr
}

func (o orgUser) Table(newTableName string) *orgUser {
	o.orgUserDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orgUser) As(alias string) *orgUser {
	o.orgUserDo.DO = *(o.orgUserDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orgUser) updateTableName(table string) *orgUser {
	o.ALL = field.NewAsterisk(table)
	o.OrganizationID = field.NewUint(table, "organization_id")
	o.SysUserID = field.NewUint(table, "sys_user_id")
	o.IsAdmin = field.NewBool(table, "is_admin")
	o.IsVisual = field.NewBool(table, "is_visual")

	o.fillFieldMap()

	return o
}

func (o *orgUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orgUser) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 6)
	o.fieldMap["organization_id"] = o.OrganizationID
	o.fieldMap["sys_user_id"] = o.SysUserID
	o.fieldMap["is_admin"] = o.IsAdmin
	o.fieldMap["is_visual"] = o.IsVisual

}

func (o orgUser) clone(db *gorm.DB) orgUser {
	o.orgUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orgUser) replaceDB(db *gorm.DB) orgUser {
	o.orgUserDo.ReplaceDB(db)
	return o
}

type orgUserBelongsToOrganization struct {
	db *gorm.DB

	field.RelationField
}

func (a orgUserBelongsToOrganization) Where(conds ...field.Expr) *orgUserBelongsToOrganization {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a orgUserBelongsToOrganization) WithContext(ctx context.Context) *orgUserBelongsToOrganization {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a orgUserBelongsToOrganization) Session(session *gorm.Session) *orgUserBelongsToOrganization {
	a.db = a.db.Session(session)
	return &a
}

func (a orgUserBelongsToOrganization) Model(m *model.OrgUser) *orgUserBelongsToOrganizationTx {
	return &orgUserBelongsToOrganizationTx{a.db.Model(m).Association(a.Name())}
}

type orgUserBelongsToOrganizationTx struct{ tx *gorm.Association }

func (a orgUserBelongsToOrganizationTx) Find() (result *model.Organization, err error) {
	return result, a.tx.Find(&result)
}

func (a orgUserBelongsToOrganizationTx) Append(values ...*model.Organization) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a orgUserBelongsToOrganizationTx) Replace(values ...*model.Organization) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a orgUserBelongsToOrganizationTx) Delete(values ...*model.Organization) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a orgUserBelongsToOrganizationTx) Clear() error {
	return a.tx.Clear()
}

func (a orgUserBelongsToOrganizationTx) Count() int64 {
	return a.tx.Count()
}

type orgUserBelongsToSysUser struct {
	db *gorm.DB

	field.RelationField

	Authority struct {
		field.RelationField
		DataAuthorityId struct {
			field.RelationField
		}
		SysBaseMenus struct {
			field.RelationField
			Parameters struct {
				field.RelationField
			}
			MenuBtn struct {
				field.RelationField
			}
			SysAuthoritys struct {
				field.RelationField
			}
		}
		Users struct {
			field.RelationField
		}
	}
	Authorities struct {
		field.RelationField
	}
}

func (a orgUserBelongsToSysUser) Where(conds ...field.Expr) *orgUserBelongsToSysUser {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a orgUserBelongsToSysUser) WithContext(ctx context.Context) *orgUserBelongsToSysUser {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a orgUserBelongsToSysUser) Session(session *gorm.Session) *orgUserBelongsToSysUser {
	a.db = a.db.Session(session)
	return &a
}

func (a orgUserBelongsToSysUser) Model(m *model.OrgUser) *orgUserBelongsToSysUserTx {
	return &orgUserBelongsToSysUserTx{a.db.Model(m).Association(a.Name())}
}

type orgUserBelongsToSysUserTx struct{ tx *gorm.Association }

func (a orgUserBelongsToSysUserTx) Find() (result *system.SysUser, err error) {
	return result, a.tx.Find(&result)
}

func (a orgUserBelongsToSysUserTx) Append(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a orgUserBelongsToSysUserTx) Replace(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a orgUserBelongsToSysUserTx) Delete(values ...*system.SysUser) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a orgUserBelongsToSysUserTx) Clear() error {
	return a.tx.Clear()
}

func (a orgUserBelongsToSysUserTx) Count() int64 {
	return a.tx.Count()
}

type orgUserDo struct{ gen.DO }

func (o orgUserDo) Debug() *orgUserDo {
	return o.withDO(o.DO.Debug())
}

func (o orgUserDo) WithContext(ctx context.Context) *orgUserDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orgUserDo) ReadDB() *orgUserDo {
	return o.Clauses(dbresolver.Read)
}

func (o orgUserDo) WriteDB() *orgUserDo {
	return o.Clauses(dbresolver.Write)
}

func (o orgUserDo) Session(config *gorm.Session) *orgUserDo {
	return o.withDO(o.DO.Session(config))
}

func (o orgUserDo) Clauses(conds ...clause.Expression) *orgUserDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orgUserDo) Returning(value interface{}, columns ...string) *orgUserDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orgUserDo) Not(conds ...gen.Condition) *orgUserDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orgUserDo) Or(conds ...gen.Condition) *orgUserDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orgUserDo) Select(conds ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orgUserDo) Where(conds ...gen.Condition) *orgUserDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orgUserDo) Order(conds ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orgUserDo) Distinct(cols ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orgUserDo) Omit(cols ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orgUserDo) Join(table schema.Tabler, on ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orgUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orgUserDo) RightJoin(table schema.Tabler, on ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orgUserDo) Group(cols ...field.Expr) *orgUserDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orgUserDo) Having(conds ...gen.Condition) *orgUserDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orgUserDo) Limit(limit int) *orgUserDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orgUserDo) Offset(offset int) *orgUserDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orgUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *orgUserDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orgUserDo) Unscoped() *orgUserDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orgUserDo) Create(values ...*model.OrgUser) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orgUserDo) CreateInBatches(values []*model.OrgUser, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orgUserDo) Save(values ...*model.OrgUser) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orgUserDo) First() (*model.OrgUser, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OrgUser), nil
	}
}

func (o orgUserDo) Take() (*model.OrgUser, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OrgUser), nil
	}
}

func (o orgUserDo) Last() (*model.OrgUser, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OrgUser), nil
	}
}

func (o orgUserDo) Find() ([]*model.OrgUser, error) {
	result, err := o.DO.Find()
	return result.([]*model.OrgUser), err
}

func (o orgUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OrgUser, err error) {
	buf := make([]*model.OrgUser, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orgUserDo) FindInBatches(result *[]*model.OrgUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orgUserDo) Attrs(attrs ...field.AssignExpr) *orgUserDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orgUserDo) Assign(attrs ...field.AssignExpr) *orgUserDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orgUserDo) Joins(fields ...field.RelationField) *orgUserDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orgUserDo) Preload(fields ...field.RelationField) *orgUserDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orgUserDo) FirstOrInit() (*model.OrgUser, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OrgUser), nil
	}
}

func (o orgUserDo) FirstOrCreate() (*model.OrgUser, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OrgUser), nil
	}
}

func (o orgUserDo) FindByPage(offset int, limit int) (result []*model.OrgUser, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orgUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orgUserDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orgUserDo) Delete(models ...*model.OrgUser) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orgUserDo) withDO(do gen.Dao) *orgUserDo {
	o.DO = *do.(*gen.DO)
	return o
}
