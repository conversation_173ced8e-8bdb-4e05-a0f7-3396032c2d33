package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsWarehouseCheckApi struct {
}

// CreateCheck 创建盘点单
// @Tags InsWarehouseCheck
// @Summary 创建盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.WarehouseCheckReq true "创建盘点单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建盘点单成功"}"
// @Router /insWarehouse/createCheck [post]
func (I *InsWarehouseCheckApi) CreateCheck(c *gin.Context) {
	var req insbuyReq.WarehouseCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.CreateCheck(req)
	response.ResultErr(resp, err, c)
}

// FinishCheck 完成盘点单
// @Tags InsWarehouseCheck
// @Summary 完成盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.FinishCheckReq true "完成盘点单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"完成盘点单成功"}"
// @Router /insWarehouse/finishCheck [put]
func (I *InsWarehouseCheckApi) FinishCheck(c *gin.Context) {
	var req insbuyReq.FinishCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.FinishCheck(req)
	response.ResultErr(resp, err, c)
}

// QueryCheckDetail 查询盘点单详情
// @Tags InsWarehouseCheck
// @Summary 查询盘点单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.WarehouseCheckReq true "查询盘点单详情"
// @Success   200   {object}  response.Response{data=inswarehouse.CheckItem,msg=string}  "查询成功"
// @Router /insWarehouse/queryCheckDetails [get]
func (I *InsWarehouseCheckApi) QueryCheckDetail(c *gin.Context) {
	var req insbuyReq.WarehouseCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	resp, err := insWarehouseService.QueryCheckDetail(req)
	response.ResultErr(resp, err, c)
}

// CheckMaterialList 查询盘点单详情原料列表
// @Tags InsWarehouseCheck
// @Summary 查询盘点单详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CheckListMaterialListReq true "查询盘点单详情"
// @Success   200   {object}  response.Response{data=inswarehouse.QueryMaterialItem,msg=string}  "查询成功"
// @Router /insWarehouse/checkMaterialList [get]
func (I *InsWarehouseCheckApi) CheckMaterialList(c *gin.Context) {
	var req insbuyReq.CheckListMaterialListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehouseService.CheckMaterialList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list.List,
			Total:    list.Total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// AddCheckMaterial 添加盘点单原料
// @Tags InsWarehouseCheck
// @Summary 添加盘点单原料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CheckMaterialReq true "添加盘点单原料"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"添加盘点单原料成功"}"
// @Router /insWarehouse/check/material [post]
func (I *InsWarehouseCheckApi) AddCheckMaterial(c *gin.Context) {
	var req insbuyReq.CheckMaterialReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.AddCheckMaterial(req)
	response.ResultErr(nil, err, c)
}

// CheckList 查询盘点单列表
// @Tags InsWarehouseCheck
// @Summary 查询盘点单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CheckListReq true "查询盘点单列表"
// @Success   200   {object}  response.Response{data=inswarehouse.QueryListItem,msg=string}  "查询成功"
// @Router /insWarehouse/checkList [get]
func (I *InsWarehouseCheckApi) CheckList(c *gin.Context) {
	var req insbuyReq.CheckListReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insWarehouseService.CheckList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list.List,
			Total:    list.Total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// DeleteInventoryBySn 清除盘点结果
// @Tags InsWarehouseCheck
// @Summary 清除盘点结果
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DeleteInventoryBySnReq true "清除盘点结果"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"清除盘点结果成功"}"
// @Router /insWarehouse/deleteInventoryBySn [delete]
func (I *InsWarehouseCheckApi) DeleteInventoryBySn(c *gin.Context) {
	var req insbuyReq.DeleteInventoryBySnReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.DeleteInventoryBySn(req)
	response.ResultErr(nil, err, c)
}

// DeleteCheck 删除盘点单
// @Tags InsWarehouseCheck
// @Summary 删除盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.DeleteCheckReq true "删除盘点单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除盘点单成功"}"
// @Router /insWarehouse/check/deleteCheck [delete]
func (I *InsWarehouseCheckApi) DeleteCheck(c *gin.Context) {
	var req insbuyReq.DeleteCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.DeleteCheck(req)
	response.ResultErr(nil, err, c)
}

// BatchStartCheck 批量启动盘点
// @Tags InsWarehouseCheck
// @Summary 批量启动盘点
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchCheckReq true "批量启动盘点"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量启动盘点成功"}"
// @Router /insWarehouse/check/batchStartCheck [put]
func (I *InsWarehouseCheckApi) BatchStartCheck(c *gin.Context) {
	var req insbuyReq.BatchCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.BatchStartCheck(req)
	response.ResultErr(nil, err, c)
}

// BatchDeleteInventoryBySn 批量清除盘点结果
// @Tags InsWarehouseCheck
// @Summary 批量清除盘点结果
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.BatchCheckReq true "批量清除盘点结果"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量清除盘点结果成功"}"
// @Router /insWarehouse/check/batchDeleteInventoryBySn [put]
func (I *InsWarehouseCheckApi) BatchDeleteInventoryBySn(c *gin.Context) {
	var req insbuyReq.BatchCheckReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = insWarehouseService.BatchDeleteInventoryBySn(req)
	response.ResultErr(nil, err, c)
}
