// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPackageItemTemplateBind(db *gorm.DB, opts ...gen.DOOption) insProductPackageItemTemplateBind {
	_insProductPackageItemTemplateBind := insProductPackageItemTemplateBind{}

	_insProductPackageItemTemplateBind.insProductPackageItemTemplateBindDo.UseDB(db, opts...)
	_insProductPackageItemTemplateBind.insProductPackageItemTemplateBindDo.UseModel(&insbuy.InsProductPackageItemTemplateBind{})

	tableName := _insProductPackageItemTemplateBind.insProductPackageItemTemplateBindDo.TableName()
	_insProductPackageItemTemplateBind.ALL = field.NewAsterisk(tableName)
	_insProductPackageItemTemplateBind.ID = field.NewUint(tableName, "id")
	_insProductPackageItemTemplateBind.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPackageItemTemplateBind.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPackageItemTemplateBind.TemplateId = field.NewUint(tableName, "template_id")
	_insProductPackageItemTemplateBind.PackageId = field.NewUint(tableName, "package_id")
	_insProductPackageItemTemplateBind.IsFull = field.NewInt(tableName, "is_full")
	_insProductPackageItemTemplateBind.OptionName = field.NewString(tableName, "option_name")
	_insProductPackageItemTemplateBind.ItemName = field.NewString(tableName, "item_name")
	_insProductPackageItemTemplateBind.OptionNum = field.NewInt(tableName, "option_num")

	_insProductPackageItemTemplateBind.fillFieldMap()

	return _insProductPackageItemTemplateBind
}

type insProductPackageItemTemplateBind struct {
	insProductPackageItemTemplateBindDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	TemplateId field.Uint
	PackageId  field.Uint
	IsFull     field.Int
	OptionName field.String
	ItemName   field.String
	OptionNum  field.Int

	fieldMap map[string]field.Expr
}

func (i insProductPackageItemTemplateBind) Table(newTableName string) *insProductPackageItemTemplateBind {
	i.insProductPackageItemTemplateBindDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPackageItemTemplateBind) As(alias string) *insProductPackageItemTemplateBind {
	i.insProductPackageItemTemplateBindDo.DO = *(i.insProductPackageItemTemplateBindDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPackageItemTemplateBind) updateTableName(table string) *insProductPackageItemTemplateBind {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.TemplateId = field.NewUint(table, "template_id")
	i.PackageId = field.NewUint(table, "package_id")
	i.IsFull = field.NewInt(table, "is_full")
	i.OptionName = field.NewString(table, "option_name")
	i.ItemName = field.NewString(table, "item_name")
	i.OptionNum = field.NewInt(table, "option_num")

	i.fillFieldMap()

	return i
}

func (i *insProductPackageItemTemplateBind) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPackageItemTemplateBind) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["template_id"] = i.TemplateId
	i.fieldMap["package_id"] = i.PackageId
	i.fieldMap["is_full"] = i.IsFull
	i.fieldMap["option_name"] = i.OptionName
	i.fieldMap["item_name"] = i.ItemName
	i.fieldMap["option_num"] = i.OptionNum
}

func (i insProductPackageItemTemplateBind) clone(db *gorm.DB) insProductPackageItemTemplateBind {
	i.insProductPackageItemTemplateBindDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPackageItemTemplateBind) replaceDB(db *gorm.DB) insProductPackageItemTemplateBind {
	i.insProductPackageItemTemplateBindDo.ReplaceDB(db)
	return i
}

type insProductPackageItemTemplateBindDo struct{ gen.DO }

func (i insProductPackageItemTemplateBindDo) Debug() *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPackageItemTemplateBindDo) WithContext(ctx context.Context) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPackageItemTemplateBindDo) ReadDB() *insProductPackageItemTemplateBindDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPackageItemTemplateBindDo) WriteDB() *insProductPackageItemTemplateBindDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPackageItemTemplateBindDo) Session(config *gorm.Session) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPackageItemTemplateBindDo) Clauses(conds ...clause.Expression) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPackageItemTemplateBindDo) Returning(value interface{}, columns ...string) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPackageItemTemplateBindDo) Not(conds ...gen.Condition) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPackageItemTemplateBindDo) Or(conds ...gen.Condition) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPackageItemTemplateBindDo) Select(conds ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPackageItemTemplateBindDo) Where(conds ...gen.Condition) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPackageItemTemplateBindDo) Order(conds ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPackageItemTemplateBindDo) Distinct(cols ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPackageItemTemplateBindDo) Omit(cols ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPackageItemTemplateBindDo) Join(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPackageItemTemplateBindDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPackageItemTemplateBindDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPackageItemTemplateBindDo) Group(cols ...field.Expr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPackageItemTemplateBindDo) Having(conds ...gen.Condition) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPackageItemTemplateBindDo) Limit(limit int) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPackageItemTemplateBindDo) Offset(offset int) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPackageItemTemplateBindDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPackageItemTemplateBindDo) Unscoped() *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPackageItemTemplateBindDo) Create(values ...*insbuy.InsProductPackageItemTemplateBind) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPackageItemTemplateBindDo) CreateInBatches(values []*insbuy.InsProductPackageItemTemplateBind, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPackageItemTemplateBindDo) Save(values ...*insbuy.InsProductPackageItemTemplateBind) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPackageItemTemplateBindDo) First() (*insbuy.InsProductPackageItemTemplateBind, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateBind), nil
	}
}

func (i insProductPackageItemTemplateBindDo) Take() (*insbuy.InsProductPackageItemTemplateBind, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateBind), nil
	}
}

func (i insProductPackageItemTemplateBindDo) Last() (*insbuy.InsProductPackageItemTemplateBind, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateBind), nil
	}
}

func (i insProductPackageItemTemplateBindDo) Find() ([]*insbuy.InsProductPackageItemTemplateBind, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPackageItemTemplateBind), err
}

func (i insProductPackageItemTemplateBindDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPackageItemTemplateBind, err error) {
	buf := make([]*insbuy.InsProductPackageItemTemplateBind, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPackageItemTemplateBindDo) FindInBatches(result *[]*insbuy.InsProductPackageItemTemplateBind, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPackageItemTemplateBindDo) Attrs(attrs ...field.AssignExpr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPackageItemTemplateBindDo) Assign(attrs ...field.AssignExpr) *insProductPackageItemTemplateBindDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPackageItemTemplateBindDo) Joins(fields ...field.RelationField) *insProductPackageItemTemplateBindDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateBindDo) Preload(fields ...field.RelationField) *insProductPackageItemTemplateBindDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPackageItemTemplateBindDo) FirstOrInit() (*insbuy.InsProductPackageItemTemplateBind, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateBind), nil
	}
}

func (i insProductPackageItemTemplateBindDo) FirstOrCreate() (*insbuy.InsProductPackageItemTemplateBind, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPackageItemTemplateBind), nil
	}
}

func (i insProductPackageItemTemplateBindDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPackageItemTemplateBind, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPackageItemTemplateBindDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPackageItemTemplateBindDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPackageItemTemplateBindDo) Delete(models ...*insbuy.InsProductPackageItemTemplateBind) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPackageItemTemplateBindDo) withDO(do gen.Dao) *insProductPackageItemTemplateBindDo {
	i.DO = *do.(*gen.DO)
	return i
}
