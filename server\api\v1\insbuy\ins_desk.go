package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Validation definition
var (
	insDeskGetByStoreIdVerify = utils.Rules{
		"Page":     {utils.NotEmpty()},
		"PageSize": {utils.NotEmpty()},
		"StoreId":  {utils.NotEmpty()},
	}
)

type InsDeskApi struct {
}

// CreateInsDesk 创建InsDesk
// @Tags InsDesk
// @Summary 创建InsDesk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.CreateDesk true "创建InsDesk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/createInsDesk [post]
func (insDeskApi *InsDeskApi) CreateInsDesk(c *gin.Context) {
	var insDesk insbuyReq.CreateDesk
	if err := GinMustBind(c, &insDesk); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	insDesk.StoreId = int(utils.GetHeaderStoreIdUint(c))
	verify := utils.Rules{
		"DeskName":       {utils.NotEmpty()},
		"DeskAreaId":     {utils.NotEmpty()},
		"DeskCategoryId": {utils.NotEmpty()},
		"StoreId":        {utils.NotEmpty()},
	}
	if err := utils.Verify(insDesk, verify); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDeskService.CreateInsDesk(insDesk); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsDesk 删除InsDesk
// @Tags InsDesk
// @Summary 删除InsDesk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDesk true "删除InsDesk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDesk/deleteInsDesk [delete]
func (insDeskApi *InsDeskApi) DeleteInsDesk(c *gin.Context) {
	var insDesk insbuy.InsDesk
	if err := GinMustBind(c, &insDesk); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDeskService.DeleteInsDesk(insDesk); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDeskByIds 批量删除InsDesk
// @Tags InsDesk
// @Summary 批量删除InsDesk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDesk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDesk/deleteInsDeskByIds [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskByIds(c *gin.Context) {
	var IDS request.IdsReq
	if err := GinMustBind(c, &IDS); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	if err := insDeskService.DeleteInsDeskByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDesk 更新InsDesk
// @Tags InsDesk
// @Summary 更新InsDesk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDesk true "更新InsDesk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDesk/updateInsDesk [put]
func (insDeskApi *InsDeskApi) UpdateInsDesk(c *gin.Context) {
	var insDesk insbuy.InsDesk
	if err := GinMustBind(c, &insDesk); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	insDesk.StoreId = int(utils.GetHeaderStoreIdUint(c))
	verify := utils.Rules{
		"DeskName":       {utils.NotEmpty()},
		"DeskAreaId":     {utils.NotEmpty()},
		"DeskCategoryId": {utils.NotEmpty()},
		"StoreId":        {utils.NotEmpty()},
	}
	if err := utils.Verify(insDesk, verify); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.UpdateInsDesk(insDesk); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsDesk 用id查询InsDesk
// @Tags InsDesk
// @Summary 用id查询InsDesk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsDesk true "用id查询InsDesk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insDesk/findInsDesk [get]
func (insDeskApi *InsDeskApi) FindInsDesk(c *gin.Context) {
	var insDesk insbuy.InsDesk
	err := c.ShouldBindQuery(&insDesk)
	err = GinBindAfter(c, &insDesk, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsDesk, err := insDeskService.GetInsDesk(insDesk.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsDesk": reinsDesk}, c)
	}
}

// GetInsDeskList 分页获取InsDesk列表
// @Tags InsDesk
// @Summary 分页获取InsDesk列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskSearch true "分页获取InsDesk列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskList [get]
func (insDeskApi *InsDeskApi) GetInsDeskList(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDeskService.GetInsDeskInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDeskManage 分页获取桌位管理-区域-分类-低消
// @Tags InsDesk
// @Summary 分页获取桌位管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskSearch true "分页获取InsDesk列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskManage [get]
func (insDeskApi *InsDeskApi) GetInsDeskManage(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDeskService.GetInsDeskInfoManage(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetByStoreId
// @Tags InsDesk
// @Summary  Get paging ins tables by specified store
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskGeyByStoreId true "pagination and store id"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getByStoreId [get]
func (insDeskApi *InsDeskApi) GetByStoreId(c *gin.Context) {
	// Bind the request query parameters to an InsDeskGeyByStoreId instance
	var r insbuyReq.InsDeskGeyByStoreId
	err := c.ShouldBindQuery(&r)
	err = GinBindAfter(c, &r, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// Verify the request query parameters
	err = utils.Verify(r, insDeskGetByStoreIdVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// Paging all matched InsDesk records
	if list, total, err := insDeskService.GetByStoreId(r); err != nil {
		global.GVA_LOG.Error("Failed to call 'insDeskService.GetByStoreId'!", zap.Error(err))
		response.FailWithMessage("获取桌台失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     r.Page,
			PageSize: r.PageSize,
		}, "获取桌台成功", c)
	}
}

// CreateInsDeskCategory 创建InsDeskCategory
// @Tags InsDeskCategory
// @Summary 创建InsDeskCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskCategorySearch true "创建InsDeskCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/createInsDeskCategory [post]
func (insDeskApi *InsDeskApi) CreateInsDeskCategory(c *gin.Context) {
	var insDeskCategory insbuyReq.InsDeskCategorySearch
	err := GinMustBind(c, &insDeskCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.CreateInsDeskCategory(&insDeskCategory); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsDeskCategory 删除InsDeskCategory
// @Tags InsDeskCategory
// @Summary 删除InsDeskCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskCategorySearch true "删除InsDeskCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDesk/deleteInsDeskCategory [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskCategory(c *gin.Context) {
	var insDeskCategory insbuyReq.InsDeskCategorySearch
	err := c.ShouldBindJSON(&insDeskCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskCategory(insDeskCategory); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDeskCategoryByIds 批量删除InsDeskCategory
// @Tags InsDeskCategory
// @Summary 批量删除InsDeskCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDeskCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDesk/deleteInsDeskCategoryByIds [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskCategoryByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskCategoryByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDeskCategory 更新InsDeskCategory
// @Tags InsDeskCategory
// @Summary 更新InsDeskCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.InsDeskCategorySearch true "更新InsDeskCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDesk/updateInsDeskCategory [put]
func (insDeskApi *InsDeskApi) UpdateInsDeskCategory(c *gin.Context) {
	var insDeskCategory insbuyReq.InsDeskCategorySearch
	err := c.ShouldBindJSON(&insDeskCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.UpdateInsDeskCategory(insDeskCategory); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsDeskCategory 用id查询InsDeskCategory
// @Tags InsDeskCategory
// @Summary 用id查询InsDeskCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsDeskCategory true "用id查询InsDeskCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insDesk/findInsDeskCategory [get]
func (insDeskApi *InsDeskApi) FindInsDeskCategory(c *gin.Context) {
	var insDeskCategory insbuy.InsDeskCategory
	err := c.ShouldBindQuery(&insDeskCategory)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsDeskCategory, err := insDeskService.GetInsDeskCategory(insDeskCategory.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsDeskCategory": reinsDeskCategory}, c)
	}
}

// GetInsDeskCategoryList 分页获取InsDeskCategory列表
// @Tags InsDeskCategory
// @Summary 分页获取InsDeskCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskCategorySearch true "分页获取InsDeskCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskCategoryList [get]
func (insDeskApi *InsDeskApi) GetInsDeskCategoryList(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskCategorySearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDeskService.GetInsDeskCategoryInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsDeskArea 创建InsDeskArea
// @Tags InsDeskArea
// @Summary 创建InsDeskArea
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskArea true "创建InsDeskArea"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/createInsDeskArea [post]
func (insDeskApi *InsDeskApi) CreateInsDeskArea(c *gin.Context) {
	var insDeskArea insbuy.InsDeskArea
	err := c.ShouldBindJSON(&insDeskArea)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.CreateInsDeskArea(&insDeskArea); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsDeskArea 删除InsDeskArea
// @Tags InsDeskArea
// @Summary 删除InsDeskArea
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskArea true "删除InsDeskArea"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDesk/deleteInsDeskArea [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskArea(c *gin.Context) {
	var insDeskArea insbuy.InsDeskArea
	err := c.ShouldBindJSON(&insDeskArea)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskArea(insDeskArea); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDeskAreaByIds 批量删除InsDeskArea
// @Tags InsDeskArea
// @Summary 批量删除InsDeskArea
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDeskArea"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDesk/deleteInsDeskAreaByIds [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskAreaByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskAreaByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDeskArea 更新InsDeskArea
// @Tags InsDeskArea
// @Summary 更新InsDeskArea
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskArea true "更新InsDeskArea"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDesk/updateInsDeskArea [put]
func (insDeskApi *InsDeskApi) UpdateInsDeskArea(c *gin.Context) {
	var insDeskArea insbuy.InsDeskArea
	err := c.ShouldBindJSON(&insDeskArea)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.UpdateInsDeskArea(insDeskArea); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsDeskArea 用id查询InsDeskArea
// @Tags InsDeskArea
// @Summary 用id查询InsDeskArea
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsDeskArea true "用id查询InsDeskArea"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insDesk/findInsDeskArea [get]
func (insDeskApi *InsDeskApi) FindInsDeskArea(c *gin.Context) {
	var insDeskArea insbuy.InsDeskArea
	err := c.ShouldBindQuery(&insDeskArea)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsDeskArea, err := insDeskService.GetInsDeskArea(insDeskArea.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsDeskArea": reinsDeskArea}, c)
	}
}

// GetInsDeskAreaList 分页获取InsDeskArea列表
// @Tags InsDeskArea
// @Summary 分页获取InsDeskArea列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskAreaSearch true "分页获取InsDeskArea列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskAreaList [get]
func (insDeskApi *InsDeskApi) GetInsDeskAreaList(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskAreaSearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDeskService.GetInsDeskAreaInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CreateInsDeskPeriodMinConsumption 创建InsDeskPeriodMinConsumption
// @Tags InsDeskPeriodMinConsumption
// @Summary 创建InsDeskPeriodMinConsumption
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskPeriodMinConsumption true "创建InsDeskPeriodMinConsumption"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/createInsDeskPeriodMinConsumption [post]
func (insDeskApi *InsDeskApi) CreateInsDeskPeriodMinConsumption(c *gin.Context) {
	var insDeskPeriodMinConsumption insbuy.InsDeskPeriodMinConsumption
	err := c.ShouldBindJSON(&insDeskPeriodMinConsumption)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.CreateInsDeskPeriodMinConsumption(&insDeskPeriodMinConsumption); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsDeskPeriodMinConsumption 删除InsDeskPeriodMinConsumption
// @Tags InsDeskPeriodMinConsumption
// @Summary 删除InsDeskPeriodMinConsumption
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskPeriodMinConsumption true "删除InsDeskPeriodMinConsumption"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insDesk/deleteInsDeskPeriodMinConsumption [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskPeriodMinConsumption(c *gin.Context) {
	var insDeskPeriodMinConsumption insbuy.InsDeskPeriodMinConsumption
	err := c.ShouldBindJSON(&insDeskPeriodMinConsumption)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskPeriodMinConsumption(insDeskPeriodMinConsumption); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsDeskPeriodMinConsumptionByIds 批量删除InsDeskPeriodMinConsumption
// @Tags InsDeskPeriodMinConsumption
// @Summary 批量删除InsDeskPeriodMinConsumption
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsDeskPeriodMinConsumption"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insDesk/deleteInsDeskPeriodMinConsumptionByIds [delete]
func (insDeskApi *InsDeskApi) DeleteInsDeskPeriodMinConsumptionByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.DeleteInsDeskPeriodMinConsumptionByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsDeskPeriodMinConsumption 更新InsDeskPeriodMinConsumption
// @Tags InsDeskPeriodMinConsumption
// @Summary 更新InsDeskPeriodMinConsumption
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsDeskPeriodMinConsumption true "更新InsDeskPeriodMinConsumption"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDesk/updateInsDeskPeriodMinConsumption [put]
func (insDeskApi *InsDeskApi) UpdateInsDeskPeriodMinConsumption(c *gin.Context) {
	var insDeskPeriodMinConsumption insbuy.InsDeskPeriodMinConsumption
	err := c.ShouldBindJSON(&insDeskPeriodMinConsumption)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.UpdateInsDeskPeriodMinConsumption(insDeskPeriodMinConsumption); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsDeskPeriodMinConsumption 用id查询InsDeskPeriodMinConsumption
// @Tags InsDeskPeriodMinConsumption
// @Summary 用id查询InsDeskPeriodMinConsumption
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsDeskPeriodMinConsumption true "用id查询InsDeskPeriodMinConsumption"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insDesk/findInsDeskPeriodMinConsumption [get]
func (insDeskApi *InsDeskApi) FindInsDeskPeriodMinConsumption(c *gin.Context) {
	var insDeskPeriodMinConsumption insbuy.InsDeskPeriodMinConsumption
	err := c.ShouldBindQuery(&insDeskPeriodMinConsumption)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsDeskPeriodMinConsumption, err := insDeskService.GetInsDeskPeriodMinConsumption(insDeskPeriodMinConsumption.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsDeskPeriodMinConsumption": reinsDeskPeriodMinConsumption}, c)
	}
}

// GetInsDeskPeriodMinConsumptionList 分页获取InsDeskPeriodMinConsumption列表
// @Tags InsDeskPeriodMinConsumption
// @Summary 分页获取InsDeskPeriodMinConsumption列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskPeriodMinConsumptionSearch true "分页获取InsDeskPeriodMinConsumption列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskPeriodMinConsumptionList [get]
func (insDeskApi *InsDeskApi) GetInsDeskPeriodMinConsumptionList(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskPeriodMinConsumptionSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insDeskService.GetInsDeskPeriodMinConsumptionInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetInsDeskCategoryWithPeriod 获取桌台分类及关联低消
// @Tags InsDeskCategory
// @Summary 获取桌台分类及关联低消
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsDeskCategorySearch true "分页获取InsDeskCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insDesk/getInsDeskCategoryWithPeriod [get]
func (insDeskApi *InsDeskApi) GetInsDeskCategoryWithPeriod(c *gin.Context) {
	var pageInfo insbuyReq.InsDeskCategorySearch
	err := c.ShouldBindQuery(&pageInfo)
	err = GinBindAfter(c, &pageInfo, err)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, err := insDeskService.GetInsDeskCategoryWithPeriod(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithData(list, c)
	}
}

// UpdateInsDeskQrCode 更新InsDeskQrCode
// @Tags InsDeskQrCode
// @Summary 更新InsDeskQrCode
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "更新InsDeskQrCode"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insDesk/updateInsDeskQrCode [put]
func (insDeskApi *InsDeskApi) UpdateInsDeskQrCode(c *gin.Context) {
	var insDeskQrCode request.IdsReq
	err := c.ShouldBindJSON(&insDeskQrCode)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.UpdateInsDeskQrCode(insDeskQrCode); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// PushDeskAll 推送全部桌台
// @Tags InsDesk
// @Summary 推送全部桌台
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"推送成功"}"
// @Router /insDesk/pushDeskAll [post]
func (insDeskApi *InsDeskApi) PushDeskAll(c *gin.Context) {
	var req insbuyReq.PushDeskAllReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insDeskService.PushDeskAll(req); err != nil {
		global.GVA_LOG.Error("推送失败!", zap.Error(err))
		response.FailWithMessage("推送失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("推送成功", c)
	}
}
