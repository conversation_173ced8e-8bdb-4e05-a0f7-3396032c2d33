// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderReturn(db *gorm.DB, opts ...gen.DOOption) insOrderReturn {
	_insOrderReturn := insOrderReturn{}

	_insOrderReturn.insOrderReturnDo.UseDB(db, opts...)
	_insOrderReturn.insOrderReturnDo.UseModel(&insbuy.InsOrderReturn{})

	tableName := _insOrderReturn.insOrderReturnDo.TableName()
	_insOrderReturn.ALL = field.NewAsterisk(tableName)
	_insOrderReturn.ReturnId = field.NewUint(tableName, "return_id")
	_insOrderReturn.ReturnSn = field.NewString(tableName, "return_sn")
	_insOrderReturn.OpenDeskId = field.NewUint(tableName, "open_desk_id")
	_insOrderReturn.Status = field.NewInt(tableName, "status")
	_insOrderReturn.Remark = field.NewString(tableName, "remark")
	_insOrderReturn.RemarkExt = field.NewField(tableName, "remarkExt")
	_insOrderReturn.ReturnType = field.NewInt(tableName, "return_type")
	_insOrderReturn.RefundId = field.NewUint64(tableName, "refund_id")
	_insOrderReturn.TradeId = field.NewUint64(tableName, "trade_id")
	_insOrderReturn.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderReturn.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderReturn.OperatorId = field.NewUint(tableName, "operator_id")
	_insOrderReturn.BusinessDay = field.NewTime(tableName, "business_day")
	_insOrderReturn.InsOrderReturnDetails = insOrderReturnHasManyInsOrderReturnDetails{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("InsOrderReturnDetails", "insbuy.InsOrderReturnDetails"),
	}

	_insOrderReturn.fillFieldMap()

	return _insOrderReturn
}

type insOrderReturn struct {
	insOrderReturnDo

	ALL                   field.Asterisk
	ReturnId              field.Uint
	ReturnSn              field.String
	OpenDeskId            field.Uint
	Status                field.Int
	Remark                field.String
	RemarkExt             field.Field
	ReturnType            field.Int
	RefundId              field.Uint64
	TradeId               field.Uint64
	CreatedAt             field.Time
	UpdatedAt             field.Time
	OperatorId            field.Uint
	BusinessDay           field.Time
	InsOrderReturnDetails insOrderReturnHasManyInsOrderReturnDetails

	fieldMap map[string]field.Expr
}

func (i insOrderReturn) Table(newTableName string) *insOrderReturn {
	i.insOrderReturnDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderReturn) As(alias string) *insOrderReturn {
	i.insOrderReturnDo.DO = *(i.insOrderReturnDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderReturn) updateTableName(table string) *insOrderReturn {
	i.ALL = field.NewAsterisk(table)
	i.ReturnId = field.NewUint(table, "return_id")
	i.ReturnSn = field.NewString(table, "return_sn")
	i.OpenDeskId = field.NewUint(table, "open_desk_id")
	i.Status = field.NewInt(table, "status")
	i.Remark = field.NewString(table, "remark")
	i.RemarkExt = field.NewField(table, "remarkExt")
	i.ReturnType = field.NewInt(table, "return_type")
	i.RefundId = field.NewUint64(table, "refund_id")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insOrderReturn) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderReturn) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["return_id"] = i.ReturnId
	i.fieldMap["return_sn"] = i.ReturnSn
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["remarkExt"] = i.RemarkExt
	i.fieldMap["return_type"] = i.ReturnType
	i.fieldMap["refund_id"] = i.RefundId
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["business_day"] = i.BusinessDay

}

func (i insOrderReturn) clone(db *gorm.DB) insOrderReturn {
	i.insOrderReturnDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderReturn) replaceDB(db *gorm.DB) insOrderReturn {
	i.insOrderReturnDo.ReplaceDB(db)
	return i
}

type insOrderReturnHasManyInsOrderReturnDetails struct {
	db *gorm.DB

	field.RelationField
}

func (a insOrderReturnHasManyInsOrderReturnDetails) Where(conds ...field.Expr) *insOrderReturnHasManyInsOrderReturnDetails {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insOrderReturnHasManyInsOrderReturnDetails) WithContext(ctx context.Context) *insOrderReturnHasManyInsOrderReturnDetails {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insOrderReturnHasManyInsOrderReturnDetails) Session(session *gorm.Session) *insOrderReturnHasManyInsOrderReturnDetails {
	a.db = a.db.Session(session)
	return &a
}

func (a insOrderReturnHasManyInsOrderReturnDetails) Model(m *insbuy.InsOrderReturn) *insOrderReturnHasManyInsOrderReturnDetailsTx {
	return &insOrderReturnHasManyInsOrderReturnDetailsTx{a.db.Model(m).Association(a.Name())}
}

type insOrderReturnHasManyInsOrderReturnDetailsTx struct{ tx *gorm.Association }

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Find() (result []*insbuy.InsOrderReturnDetails, err error) {
	return result, a.tx.Find(&result)
}

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Append(values ...*insbuy.InsOrderReturnDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Replace(values ...*insbuy.InsOrderReturnDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Delete(values ...*insbuy.InsOrderReturnDetails) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Clear() error {
	return a.tx.Clear()
}

func (a insOrderReturnHasManyInsOrderReturnDetailsTx) Count() int64 {
	return a.tx.Count()
}

type insOrderReturnDo struct{ gen.DO }

func (i insOrderReturnDo) Debug() *insOrderReturnDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderReturnDo) WithContext(ctx context.Context) *insOrderReturnDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderReturnDo) ReadDB() *insOrderReturnDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderReturnDo) WriteDB() *insOrderReturnDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderReturnDo) Session(config *gorm.Session) *insOrderReturnDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderReturnDo) Clauses(conds ...clause.Expression) *insOrderReturnDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderReturnDo) Returning(value interface{}, columns ...string) *insOrderReturnDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderReturnDo) Not(conds ...gen.Condition) *insOrderReturnDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderReturnDo) Or(conds ...gen.Condition) *insOrderReturnDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderReturnDo) Select(conds ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderReturnDo) Where(conds ...gen.Condition) *insOrderReturnDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderReturnDo) Order(conds ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderReturnDo) Distinct(cols ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderReturnDo) Omit(cols ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderReturnDo) Join(table schema.Tabler, on ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderReturnDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderReturnDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderReturnDo) Group(cols ...field.Expr) *insOrderReturnDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderReturnDo) Having(conds ...gen.Condition) *insOrderReturnDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderReturnDo) Limit(limit int) *insOrderReturnDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderReturnDo) Offset(offset int) *insOrderReturnDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderReturnDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderReturnDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderReturnDo) Unscoped() *insOrderReturnDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderReturnDo) Create(values ...*insbuy.InsOrderReturn) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderReturnDo) CreateInBatches(values []*insbuy.InsOrderReturn, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderReturnDo) Save(values ...*insbuy.InsOrderReturn) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderReturnDo) First() (*insbuy.InsOrderReturn, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturn), nil
	}
}

func (i insOrderReturnDo) Take() (*insbuy.InsOrderReturn, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturn), nil
	}
}

func (i insOrderReturnDo) Last() (*insbuy.InsOrderReturn, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturn), nil
	}
}

func (i insOrderReturnDo) Find() ([]*insbuy.InsOrderReturn, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderReturn), err
}

func (i insOrderReturnDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderReturn, err error) {
	buf := make([]*insbuy.InsOrderReturn, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderReturnDo) FindInBatches(result *[]*insbuy.InsOrderReturn, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderReturnDo) Attrs(attrs ...field.AssignExpr) *insOrderReturnDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderReturnDo) Assign(attrs ...field.AssignExpr) *insOrderReturnDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderReturnDo) Joins(fields ...field.RelationField) *insOrderReturnDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderReturnDo) Preload(fields ...field.RelationField) *insOrderReturnDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderReturnDo) FirstOrInit() (*insbuy.InsOrderReturn, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturn), nil
	}
}

func (i insOrderReturnDo) FirstOrCreate() (*insbuy.InsOrderReturn, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderReturn), nil
	}
}

func (i insOrderReturnDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderReturn, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderReturnDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderReturnDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderReturnDo) Delete(models ...*insbuy.InsOrderReturn) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderReturnDo) withDO(do gen.Dao) *insOrderReturnDo {
	i.DO = *do.(*gen.DO)
	return i
}
