// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeCustom(db *gorm.DB, opts ...gen.DOOption) insTradeCustom {
	_insTradeCustom := insTradeCustom{}

	_insTradeCustom.insTradeCustomDo.UseDB(db, opts...)
	_insTradeCustom.insTradeCustomDo.UseModel(&insbuy.InsTradeCustom{})

	tableName := _insTradeCustom.insTradeCustomDo.TableName()
	_insTradeCustom.ALL = field.NewAsterisk(tableName)
	_insTradeCustom.ID = field.NewUint(tableName, "id")
	_insTradeCustom.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeCustom.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeCustom.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTradeCustom.TradePayId = field.NewUint(tableName, "trade_pay_id")
	_insTradeCustom.CustomPayId = field.NewUint(tableName, "custom_pay_id")

	_insTradeCustom.fillFieldMap()

	return _insTradeCustom
}

type insTradeCustom struct {
	insTradeCustomDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	TradePayId  field.Uint
	CustomPayId field.Uint

	fieldMap map[string]field.Expr
}

func (i insTradeCustom) Table(newTableName string) *insTradeCustom {
	i.insTradeCustomDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeCustom) As(alias string) *insTradeCustom {
	i.insTradeCustomDo.DO = *(i.insTradeCustomDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeCustom) updateTableName(table string) *insTradeCustom {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.TradePayId = field.NewUint(table, "trade_pay_id")
	i.CustomPayId = field.NewUint(table, "custom_pay_id")

	i.fillFieldMap()

	return i
}

func (i *insTradeCustom) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeCustom) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["trade_pay_id"] = i.TradePayId
	i.fieldMap["custom_pay_id"] = i.CustomPayId
}

func (i insTradeCustom) clone(db *gorm.DB) insTradeCustom {
	i.insTradeCustomDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeCustom) replaceDB(db *gorm.DB) insTradeCustom {
	i.insTradeCustomDo.ReplaceDB(db)
	return i
}

type insTradeCustomDo struct{ gen.DO }

func (i insTradeCustomDo) Debug() *insTradeCustomDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeCustomDo) WithContext(ctx context.Context) *insTradeCustomDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeCustomDo) ReadDB() *insTradeCustomDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeCustomDo) WriteDB() *insTradeCustomDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeCustomDo) Session(config *gorm.Session) *insTradeCustomDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeCustomDo) Clauses(conds ...clause.Expression) *insTradeCustomDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeCustomDo) Returning(value interface{}, columns ...string) *insTradeCustomDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeCustomDo) Not(conds ...gen.Condition) *insTradeCustomDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeCustomDo) Or(conds ...gen.Condition) *insTradeCustomDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeCustomDo) Select(conds ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeCustomDo) Where(conds ...gen.Condition) *insTradeCustomDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeCustomDo) Order(conds ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeCustomDo) Distinct(cols ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeCustomDo) Omit(cols ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeCustomDo) Join(table schema.Tabler, on ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeCustomDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeCustomDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeCustomDo) Group(cols ...field.Expr) *insTradeCustomDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeCustomDo) Having(conds ...gen.Condition) *insTradeCustomDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeCustomDo) Limit(limit int) *insTradeCustomDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeCustomDo) Offset(offset int) *insTradeCustomDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeCustomDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeCustomDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeCustomDo) Unscoped() *insTradeCustomDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeCustomDo) Create(values ...*insbuy.InsTradeCustom) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeCustomDo) CreateInBatches(values []*insbuy.InsTradeCustom, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeCustomDo) Save(values ...*insbuy.InsTradeCustom) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeCustomDo) First() (*insbuy.InsTradeCustom, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCustom), nil
	}
}

func (i insTradeCustomDo) Take() (*insbuy.InsTradeCustom, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCustom), nil
	}
}

func (i insTradeCustomDo) Last() (*insbuy.InsTradeCustom, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCustom), nil
	}
}

func (i insTradeCustomDo) Find() ([]*insbuy.InsTradeCustom, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeCustom), err
}

func (i insTradeCustomDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeCustom, err error) {
	buf := make([]*insbuy.InsTradeCustom, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeCustomDo) FindInBatches(result *[]*insbuy.InsTradeCustom, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeCustomDo) Attrs(attrs ...field.AssignExpr) *insTradeCustomDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeCustomDo) Assign(attrs ...field.AssignExpr) *insTradeCustomDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeCustomDo) Joins(fields ...field.RelationField) *insTradeCustomDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeCustomDo) Preload(fields ...field.RelationField) *insTradeCustomDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeCustomDo) FirstOrInit() (*insbuy.InsTradeCustom, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCustom), nil
	}
}

func (i insTradeCustomDo) FirstOrCreate() (*insbuy.InsTradeCustom, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeCustom), nil
	}
}

func (i insTradeCustomDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeCustom, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeCustomDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeCustomDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeCustomDo) Delete(models ...*insbuy.InsTradeCustom) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeCustomDo) withDO(do gen.Dao) *insTradeCustomDo {
	i.DO = *do.(*gen.DO)
	return i
}
