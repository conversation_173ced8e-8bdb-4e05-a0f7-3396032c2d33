# 完整的JSON示例 - ID引用支持

## 基础配置数据

### 分类配置示例
```json
{
  "cost_type_configs": [
    {
      "id": 1,
      "category_name": "营业收入",
      "category_code": "REVENUE",
      "is_calculated": false,
      "sort_order": 100,
      "level": 1,
      "parent_id": 0
    },
    {
      "id": 2,
      "category_name": "营业成本",
      "category_code": "COST",
      "is_calculated": false,
      "sort_order": 200,
      "level": 1,
      "parent_id": 0
    },
    {
      "id": 3,
      "category_name": "销售费用",
      "category_code": "SALES_EXPENSE",
      "is_calculated": false,
      "sort_order": 400,
      "level": 1,
      "parent_id": 0
    },
    {
      "id": 4,
      "category_name": "管理费用",
      "category_code": "ADMIN_EXPENSE",
      "is_calculated": false,
      "sort_order": 500,
      "level": 1,
      "parent_id": 0
    },
    {
      "id": 5,
      "category_name": "财务费用",
      "category_code": "FINANCE_EXPENSE",
      "is_calculated": false,
      "sort_order": 600,
      "level": 1,
      "parent_id": 0
    }
  ]
}
```

## 计算型分类JSON配置

### 1. 基础计算 - 毛利润

#### 使用分类名称引用（原有格式）
```json
{
  "id": 100,
  "category_name": "毛利润",
  "category_code": "GROSS_PROFIT",
  "is_calculated": true,
  "sort_order": 300,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"[营业收入] - [营业成本]\",\"references\":[1,2],\"description\":\"毛利润计算：营业收入减去营业成本\"}"
}
```

#### 使用ID引用（新格式）
```json
{
  "id": 100,
  "category_name": "毛利润",
  "category_code": "GROSS_PROFIT",
  "is_calculated": true,
  "sort_order": 300,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"#1 - #2\",\"references\":[1,2],\"description\":\"毛利润计算：使用ID引用\"}"
}
```

#### 使用混合引用
```json
{
  "id": 100,
  "category_name": "毛利润",
  "category_code": "GROSS_PROFIT",
  "is_calculated": true,
  "sort_order": 300,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"[营业收入] - #2\",\"references\":[1,2],\"description\":\"毛利润计算：混合引用格式\"}"
}
```

### 2. 百分比计算 - 毛利率

#### 使用分类名称引用
```json
{
  "id": 101,
  "category_name": "毛利率",
  "category_code": "GROSS_MARGIN",
  "is_calculated": true,
  "sort_order": 350,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"[毛利润] / [营业收入] %\",\"references\":[100,1],\"description\":\"毛利率：毛利润占营业收入的百分比\"}"
}
```

#### 使用ID引用
```json
{
  "id": 101,
  "category_name": "毛利率",
  "category_code": "GROSS_MARGIN",
  "is_calculated": true,
  "sort_order": 350,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"#100 / #1 %\",\"references\":[100,1],\"description\":\"毛利率：使用ID引用计算\"}"
}
```

#### 使用复合表达式和ID引用
```json
{
  "id": 101,
  "category_name": "毛利率",
  "category_code": "GROSS_MARGIN",
  "is_calculated": true,
  "sort_order": 350,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"(#1 - #2) / #1 %\",\"references\":[1,2],\"description\":\"毛利率：直接使用基础数据计算\"}"
}
```

### 3. 复杂计算 - 期间费用合计

#### 使用分类名称引用
```json
{
  "id": 102,
  "category_name": "期间费用合计",
  "category_code": "PERIOD_EXPENSE_TOTAL",
  "is_calculated": true,
  "sort_order": 700,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"[销售费用] + [管理费用] + [财务费用]\",\"references\":[3,4,5],\"description\":\"期间费用合计：三项费用的总和\"}"
}
```

#### 使用ID引用
```json
{
  "id": 102,
  "category_name": "期间费用合计",
  "category_code": "PERIOD_EXPENSE_TOTAL",
  "is_calculated": true,
  "sort_order": 700,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"#3 + #4 + #5\",\"references\":[3,4,5],\"description\":\"期间费用合计：使用ID引用\"}"
}
```

### 4. 多层依赖计算 - 营业利润

#### 使用分类名称引用
```json
{
  "id": 103,
  "category_name": "营业利润",
  "category_code": "OPERATING_PROFIT",
  "is_calculated": true,
  "sort_order": 800,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"[毛利润] - [期间费用合计]\",\"references\":[100,102],\"description\":\"营业利润：毛利润减去期间费用\"}"
}
```

#### 使用ID引用
```json
{
  "id": 103,
  "category_name": "营业利润",
  "category_code": "OPERATING_PROFIT",
  "is_calculated": true,
  "sort_order": 800,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"#100 - #102\",\"references\":[100,102],\"description\":\"营业利润：使用ID引用\"}"
}
```

#### 使用展开的ID引用（避免中间计算）
```json
{
  "id": 103,
  "category_name": "营业利润",
  "category_code": "OPERATING_PROFIT",
  "is_calculated": true,
  "sort_order": 800,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"(#1 - #2) - (#3 + #4 + #5)\",\"references\":[1,2,3,4,5],\"description\":\"营业利润：展开计算避免中间依赖\"}"
}
```

### 5. 复杂比率计算 - 营业利润率

#### 使用混合引用
```json
{
  "id": 104,
  "category_name": "营业利润率",
  "category_code": "OPERATING_MARGIN",
  "is_calculated": true,
  "sort_order": 850,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"#103 / [营业收入] %\",\"references\":[103,1],\"description\":\"营业利润率：混合引用计算\"}"
}
```

#### 使用完全展开的表达式
```json
{
  "id": 104,
  "category_name": "营业利润率",
  "category_code": "OPERATING_MARGIN",
  "is_calculated": true,
  "sort_order": 850,
  "level": 1,
  "parent_id": 0,
  "calculation_formula": "{\"expression\":\"((#1 - #2) - (#3 + #4 + #5)) / #1 %\",\"references\":[1,2,3,4,5],\"description\":\"营业利润率：完全展开计算\"}"
}
```

## 完整的配置中心数据示例

### 配置中心存储格式
```json
{
  "module": "report",
  "key_path": "report.group.cost.type",
  "config_data": {
    "calculated_categories": [
      {
        "id": 100,
        "category_name": "毛利润",
        "category_code": "GROSS_PROFIT",
        "is_calculated": true,
        "sort_order": 300,
        "calculation_formula": "{\"expression\":\"#1 - #2\",\"references\":[1,2],\"description\":\"毛利润计算\"}"
      },
      {
        "id": 101,
        "category_name": "毛利率",
        "category_code": "GROSS_MARGIN",
        "is_calculated": true,
        "sort_order": 350,
        "calculation_formula": "{\"expression\":\"#100 / #1 %\",\"references\":[100,1],\"description\":\"毛利率计算\"}"
      },
      {
        "id": 102,
        "category_name": "期间费用合计",
        "category_code": "PERIOD_EXPENSE_TOTAL",
        "is_calculated": true,
        "sort_order": 700,
        "calculation_formula": "{\"expression\":\"#3 + #4 + #5\",\"references\":[3,4,5],\"description\":\"期间费用合计\"}"
      },
      {
        "id": 103,
        "category_name": "营业利润",
        "category_code": "OPERATING_PROFIT",
        "is_calculated": true,
        "sort_order": 800,
        "calculation_formula": "{\"expression\":\"#100 - #102\",\"references\":[100,102],\"description\":\"营业利润计算\"}"
      },
      {
        "id": 104,
        "category_name": "营业利润率",
        "category_code": "OPERATING_MARGIN",
        "is_calculated": true,
        "sort_order": 850,
        "calculation_formula": "{\"expression\":\"#103 / #1 %\",\"references\":[103,1],\"description\":\"营业利润率计算\"}"
      }
    ]
  }
}
```

## API请求和响应示例

### 创建公式API请求
```json
{
  "method": "POST",
  "url": "/api/formula/create",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "expression": "#1 - #2",
    "description": "毛利润计算公式",
    "category_id": 100,
    "category_name": "毛利润"
  }
}
```

### API响应
```json
{
  "code": 200,
  "message": "公式创建成功",
  "data": {
    "formula_json": "{\"expression\":\"#1 - #2\",\"references\":[1,2],\"description\":\"毛利润计算公式\"}",
    "category_id": 100,
    "validation_result": {
      "is_valid": true,
      "errors": []
    }
  }
}
```

### 验证公式API请求
```json
{
  "method": "POST",
  "url": "/api/formula/validate",
  "headers": {
    "Content-Type": "application/json"
  },
  "body": {
    "formula_json": "{\"expression\":\"#1 - #999\",\"references\":[1,999],\"description\":\"测试公式\"}"
  }
}
```

### 验证失败响应
```json
{
  "code": 400,
  "message": "公式验证失败",
  "data": {
    "is_valid": false,
    "errors": [
      {
        "type": "SYNTAX_ERROR",
        "message": "表达式语法错误或引用的分类不存在",
        "field": "calculation_formula"
      }
    ]
  }
}
```

## 前端使用示例

### React组件示例
```typescript
interface FormulaConfig {
  expression: string;
  references: number[];
  description: string;
}

const FormulaEditor: React.FC = () => {
  const [formula, setFormula] = useState<FormulaConfig>({
    expression: '',
    references: [],
    description: ''
  });

  // 支持ID引用的表达式示例
  const examples = [
    { label: '毛利润', value: '#1 - #2' },
    { label: '毛利率', value: '#100 / #1 %' },
    { label: '期间费用', value: '#3 + #4 + #5' },
    { label: '混合引用', value: '[营业收入] - #2' }
  ];

  const handleSave = async () => {
    const response = await fetch('/api/formula/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formula)
    });
    
    const result = await response.json();
    console.log('保存结果:', result);
  };

  return (
    <div>
      <select onChange={(e) => setFormula({...formula, expression: e.target.value})}>
        {examples.map(ex => (
          <option key={ex.label} value={ex.value}>{ex.label}: {ex.value}</option>
        ))}
      </select>
      <input 
        value={formula.expression}
        onChange={(e) => setFormula({...formula, expression: e.target.value})}
        placeholder="输入公式，如：#1 - #2 或 [营业收入] - #2"
      />
      <button onClick={handleSave}>保存公式</button>
    </div>
  );
};
```

## 数据库存储示例

### MySQL表结构和数据
```sql
-- 分类配置表
CREATE TABLE cost_type_config (
  id INT PRIMARY KEY,
  category_name VARCHAR(100),
  category_code VARCHAR(50),
  is_calculated TINYINT(1),
  calculation_formula TEXT,
  sort_order INT
);

-- 插入基础数据
INSERT INTO cost_type_config VALUES
(1, '营业收入', 'REVENUE', 0, NULL, 100),
(2, '营业成本', 'COST', 0, NULL, 200),
(3, '销售费用', 'SALES_EXPENSE', 0, NULL, 400);

-- 插入计算型数据（使用ID引用）
INSERT INTO cost_type_config VALUES
(100, '毛利润', 'GROSS_PROFIT', 1, '{"expression":"#1 - #2","references":[1,2],"description":"毛利润计算"}', 300),
(101, '毛利率', 'GROSS_MARGIN', 1, '{"expression":"#100 / #1 %","references":[100,1],"description":"毛利率计算"}', 350);
```

这些完整的JSON示例展示了ID引用支持的所有功能，包括基础引用、混合引用、复杂表达式和实际的业务应用场景。
