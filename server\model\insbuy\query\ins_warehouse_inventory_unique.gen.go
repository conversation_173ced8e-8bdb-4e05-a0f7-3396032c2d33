// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInventoryUnique(db *gorm.DB, opts ...gen.DOOption) insWarehouseInventoryUnique {
	_insWarehouseInventoryUnique := insWarehouseInventoryUnique{}

	_insWarehouseInventoryUnique.insWarehouseInventoryUniqueDo.UseDB(db, opts...)
	_insWarehouseInventoryUnique.insWarehouseInventoryUniqueDo.UseModel(&insbuy.InsWarehouseInventoryUnique{})

	tableName := _insWarehouseInventoryUnique.insWarehouseInventoryUniqueDo.TableName()
	_insWarehouseInventoryUnique.ALL = field.NewAsterisk(tableName)
	_insWarehouseInventoryUnique.ID = field.NewUint(tableName, "id")
	_insWarehouseInventoryUnique.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInventoryUnique.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInventoryUnique.InventoryId = field.NewInt(tableName, "inventory_id")
	_insWarehouseInventoryUnique.MaterialId = field.NewInt(tableName, "material_id")
	_insWarehouseInventoryUnique.UniqueQrcode = field.NewString(tableName, "unique_qrcode")

	_insWarehouseInventoryUnique.fillFieldMap()

	return _insWarehouseInventoryUnique
}

type insWarehouseInventoryUnique struct {
	insWarehouseInventoryUniqueDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	InventoryId  field.Int
	MaterialId   field.Int
	UniqueQrcode field.String

	fieldMap map[string]field.Expr
}

func (i insWarehouseInventoryUnique) Table(newTableName string) *insWarehouseInventoryUnique {
	i.insWarehouseInventoryUniqueDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInventoryUnique) As(alias string) *insWarehouseInventoryUnique {
	i.insWarehouseInventoryUniqueDo.DO = *(i.insWarehouseInventoryUniqueDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInventoryUnique) updateTableName(table string) *insWarehouseInventoryUnique {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.InventoryId = field.NewInt(table, "inventory_id")
	i.MaterialId = field.NewInt(table, "material_id")
	i.UniqueQrcode = field.NewString(table, "unique_qrcode")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInventoryUnique) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInventoryUnique) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["inventory_id"] = i.InventoryId
	i.fieldMap["material_id"] = i.MaterialId
	i.fieldMap["unique_qrcode"] = i.UniqueQrcode
}

func (i insWarehouseInventoryUnique) clone(db *gorm.DB) insWarehouseInventoryUnique {
	i.insWarehouseInventoryUniqueDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInventoryUnique) replaceDB(db *gorm.DB) insWarehouseInventoryUnique {
	i.insWarehouseInventoryUniqueDo.ReplaceDB(db)
	return i
}

type insWarehouseInventoryUniqueDo struct{ gen.DO }

func (i insWarehouseInventoryUniqueDo) Debug() *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInventoryUniqueDo) WithContext(ctx context.Context) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInventoryUniqueDo) ReadDB() *insWarehouseInventoryUniqueDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInventoryUniqueDo) WriteDB() *insWarehouseInventoryUniqueDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInventoryUniqueDo) Session(config *gorm.Session) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInventoryUniqueDo) Clauses(conds ...clause.Expression) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInventoryUniqueDo) Returning(value interface{}, columns ...string) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInventoryUniqueDo) Not(conds ...gen.Condition) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInventoryUniqueDo) Or(conds ...gen.Condition) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInventoryUniqueDo) Select(conds ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInventoryUniqueDo) Where(conds ...gen.Condition) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInventoryUniqueDo) Order(conds ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInventoryUniqueDo) Distinct(cols ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInventoryUniqueDo) Omit(cols ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInventoryUniqueDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInventoryUniqueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInventoryUniqueDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInventoryUniqueDo) Group(cols ...field.Expr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInventoryUniqueDo) Having(conds ...gen.Condition) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInventoryUniqueDo) Limit(limit int) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInventoryUniqueDo) Offset(offset int) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInventoryUniqueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInventoryUniqueDo) Unscoped() *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInventoryUniqueDo) Create(values ...*insbuy.InsWarehouseInventoryUnique) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInventoryUniqueDo) CreateInBatches(values []*insbuy.InsWarehouseInventoryUnique, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInventoryUniqueDo) Save(values ...*insbuy.InsWarehouseInventoryUnique) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInventoryUniqueDo) First() (*insbuy.InsWarehouseInventoryUnique, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryUnique), nil
	}
}

func (i insWarehouseInventoryUniqueDo) Take() (*insbuy.InsWarehouseInventoryUnique, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryUnique), nil
	}
}

func (i insWarehouseInventoryUniqueDo) Last() (*insbuy.InsWarehouseInventoryUnique, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryUnique), nil
	}
}

func (i insWarehouseInventoryUniqueDo) Find() ([]*insbuy.InsWarehouseInventoryUnique, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInventoryUnique), err
}

func (i insWarehouseInventoryUniqueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInventoryUnique, err error) {
	buf := make([]*insbuy.InsWarehouseInventoryUnique, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInventoryUniqueDo) FindInBatches(result *[]*insbuy.InsWarehouseInventoryUnique, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInventoryUniqueDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInventoryUniqueDo) Assign(attrs ...field.AssignExpr) *insWarehouseInventoryUniqueDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInventoryUniqueDo) Joins(fields ...field.RelationField) *insWarehouseInventoryUniqueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInventoryUniqueDo) Preload(fields ...field.RelationField) *insWarehouseInventoryUniqueDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInventoryUniqueDo) FirstOrInit() (*insbuy.InsWarehouseInventoryUnique, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryUnique), nil
	}
}

func (i insWarehouseInventoryUniqueDo) FirstOrCreate() (*insbuy.InsWarehouseInventoryUnique, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInventoryUnique), nil
	}
}

func (i insWarehouseInventoryUniqueDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInventoryUnique, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInventoryUniqueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInventoryUniqueDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInventoryUniqueDo) Delete(models ...*insbuy.InsWarehouseInventoryUnique) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInventoryUniqueDo) withDO(do gen.Dao) *insWarehouseInventoryUniqueDo {
	i.DO = *do.(*gen.DO)
	return i
}
