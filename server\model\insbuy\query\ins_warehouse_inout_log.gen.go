// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsWarehouseInoutLog(db *gorm.DB, opts ...gen.DOOption) insWarehouseInoutLog {
	_insWarehouseInoutLog := insWarehouseInoutLog{}

	_insWarehouseInoutLog.insWarehouseInoutLogDo.UseDB(db, opts...)
	_insWarehouseInoutLog.insWarehouseInoutLogDo.UseModel(&insbuy.InsWarehouseInoutLog{})

	tableName := _insWarehouseInoutLog.insWarehouseInoutLogDo.TableName()
	_insWarehouseInoutLog.ALL = field.NewAsterisk(tableName)
	_insWarehouseInoutLog.Id = field.NewInt(tableName, "id")
	_insWarehouseInoutLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insWarehouseInoutLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insWarehouseInoutLog.LogType = field.NewInt(tableName, "log_type")
	_insWarehouseInoutLog.OperationTime = field.NewTime(tableName, "operation_time")
	_insWarehouseInoutLog.OperatorId = field.NewInt(tableName, "operator_id")
	_insWarehouseInoutLog.Details = field.NewString(tableName, "details")
	_insWarehouseInoutLog.InoutId = field.NewInt(tableName, "inout_id")

	_insWarehouseInoutLog.fillFieldMap()

	return _insWarehouseInoutLog
}

type insWarehouseInoutLog struct {
	insWarehouseInoutLogDo

	ALL           field.Asterisk
	Id            field.Int
	CreatedAt     field.Time
	UpdatedAt     field.Time
	LogType       field.Int
	OperationTime field.Time
	OperatorId    field.Int
	Details       field.String
	InoutId       field.Int

	fieldMap map[string]field.Expr
}

func (i insWarehouseInoutLog) Table(newTableName string) *insWarehouseInoutLog {
	i.insWarehouseInoutLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insWarehouseInoutLog) As(alias string) *insWarehouseInoutLog {
	i.insWarehouseInoutLogDo.DO = *(i.insWarehouseInoutLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insWarehouseInoutLog) updateTableName(table string) *insWarehouseInoutLog {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.LogType = field.NewInt(table, "log_type")
	i.OperationTime = field.NewTime(table, "operation_time")
	i.OperatorId = field.NewInt(table, "operator_id")
	i.Details = field.NewString(table, "details")
	i.InoutId = field.NewInt(table, "inout_id")

	i.fillFieldMap()

	return i
}

func (i *insWarehouseInoutLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insWarehouseInoutLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.Id
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["log_type"] = i.LogType
	i.fieldMap["operation_time"] = i.OperationTime
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["details"] = i.Details
	i.fieldMap["inout_id"] = i.InoutId
}

func (i insWarehouseInoutLog) clone(db *gorm.DB) insWarehouseInoutLog {
	i.insWarehouseInoutLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insWarehouseInoutLog) replaceDB(db *gorm.DB) insWarehouseInoutLog {
	i.insWarehouseInoutLogDo.ReplaceDB(db)
	return i
}

type insWarehouseInoutLogDo struct{ gen.DO }

func (i insWarehouseInoutLogDo) Debug() *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insWarehouseInoutLogDo) WithContext(ctx context.Context) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insWarehouseInoutLogDo) ReadDB() *insWarehouseInoutLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insWarehouseInoutLogDo) WriteDB() *insWarehouseInoutLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insWarehouseInoutLogDo) Session(config *gorm.Session) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insWarehouseInoutLogDo) Clauses(conds ...clause.Expression) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insWarehouseInoutLogDo) Returning(value interface{}, columns ...string) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insWarehouseInoutLogDo) Not(conds ...gen.Condition) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insWarehouseInoutLogDo) Or(conds ...gen.Condition) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insWarehouseInoutLogDo) Select(conds ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insWarehouseInoutLogDo) Where(conds ...gen.Condition) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insWarehouseInoutLogDo) Order(conds ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insWarehouseInoutLogDo) Distinct(cols ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insWarehouseInoutLogDo) Omit(cols ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insWarehouseInoutLogDo) Join(table schema.Tabler, on ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insWarehouseInoutLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insWarehouseInoutLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insWarehouseInoutLogDo) Group(cols ...field.Expr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insWarehouseInoutLogDo) Having(conds ...gen.Condition) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insWarehouseInoutLogDo) Limit(limit int) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insWarehouseInoutLogDo) Offset(offset int) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insWarehouseInoutLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insWarehouseInoutLogDo) Unscoped() *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insWarehouseInoutLogDo) Create(values ...*insbuy.InsWarehouseInoutLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insWarehouseInoutLogDo) CreateInBatches(values []*insbuy.InsWarehouseInoutLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insWarehouseInoutLogDo) Save(values ...*insbuy.InsWarehouseInoutLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insWarehouseInoutLogDo) First() (*insbuy.InsWarehouseInoutLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutLog), nil
	}
}

func (i insWarehouseInoutLogDo) Take() (*insbuy.InsWarehouseInoutLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutLog), nil
	}
}

func (i insWarehouseInoutLogDo) Last() (*insbuy.InsWarehouseInoutLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutLog), nil
	}
}

func (i insWarehouseInoutLogDo) Find() ([]*insbuy.InsWarehouseInoutLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsWarehouseInoutLog), err
}

func (i insWarehouseInoutLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsWarehouseInoutLog, err error) {
	buf := make([]*insbuy.InsWarehouseInoutLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insWarehouseInoutLogDo) FindInBatches(result *[]*insbuy.InsWarehouseInoutLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insWarehouseInoutLogDo) Attrs(attrs ...field.AssignExpr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insWarehouseInoutLogDo) Assign(attrs ...field.AssignExpr) *insWarehouseInoutLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insWarehouseInoutLogDo) Joins(fields ...field.RelationField) *insWarehouseInoutLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insWarehouseInoutLogDo) Preload(fields ...field.RelationField) *insWarehouseInoutLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insWarehouseInoutLogDo) FirstOrInit() (*insbuy.InsWarehouseInoutLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutLog), nil
	}
}

func (i insWarehouseInoutLogDo) FirstOrCreate() (*insbuy.InsWarehouseInoutLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsWarehouseInoutLog), nil
	}
}

func (i insWarehouseInoutLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsWarehouseInoutLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insWarehouseInoutLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insWarehouseInoutLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insWarehouseInoutLogDo) Delete(models ...*insbuy.InsWarehouseInoutLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insWarehouseInoutLogDo) withDO(do gen.Dao) *insWarehouseInoutLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
