package productmodel

import "github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"

// PromotionDetail 明细数据
type PromotionDetail struct {
	PromotionWeekday []PromotionWeekday `json:"promotionWeekday" form:"promotionWeekday"`
	PromotionTime    []PromotionTime    `json:"promotionTime" form:"promotionTime"`
	ExcludeCategory  []ExcludeCategory  `json:"excludeCategory" form:"excludeCategory"`
	ExcludeProduct   []ExcludeProduct   `json:"excludeProduct" form:"excludeProduct"`
	AssignProduct    []AssignProduct    `json:"assignProduct" form:"assignProduct"`
	AssignCategory   []AssignCategory   `json:"assignCategory" form:"assignCategory"`
	NotPromotionDate []NotPromotionDate `json:"notPromotionDate" form:"notPromotionDate"`
}

// ServiceFeeDetail 服务费
type ServiceFeeDetail struct {
	PromotionWeekday []PromotionWeekday `json:"promotionWeekday" form:"promotionWeekday"`
	PromotionTime    []PromotionTime    `json:"promotionTime" form:"promotionTime"`
	ExcludeCategory  []ExcludeCategory  `json:"excludeCategory" form:"excludeCategory"`
	ExcludeProduct   []ExcludeProduct   `json:"excludeProduct" form:"excludeProduct"`
	AssignProduct    []AssignProduct    `json:"assignProduct" form:"assignProduct"`
	AssignCategory   []AssignCategory   `json:"assignCategory" form:"assignCategory"`
	NotPromotionDate []NotPromotionDate `json:"notPromotionDate" form:"notPromotionDate"`
	AssignDesk       []AssignDesk       `json:"assignDesk" form:"assignDesk"`
}

// AssignDesk 适用桌台
type AssignDesk struct {
	DeskId   uint   `json:"deskId" form:"deskId"`
	DeskName string `json:"deskName" form:"deskName"`
}

// NotPromotionDate 不可用日期
type NotPromotionDate struct {
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}

// PromotionWeekday 时段
type PromotionWeekday struct {
	Weekday int `json:"weekday" form:"weekday"`
}

// PromotionTime 时段
type PromotionTime struct {
	StartTime string `json:"startTime" form:"startTime"` //时分秒
	EndTime   string `json:"endTime" form:"endTime"`     //时分秒
}

// ExcludeCategory 排除分类
type ExcludeCategory struct {
	CategoryId   uint   `json:"categoryId" form:"categoryId"`
	CategoryName string `json:"categoryName" form:"categoryName"`
}

// ExcludeProduct 排除商品
type ExcludeProduct struct {
	ProductId   uint   `json:"productId" form:"productId"`
	ProductName string `json:"productName" form:"productName"`
}

// AssignProduct 指定商品
type AssignProduct struct {
	ProductId    uint          `json:"productId" form:"productId"`
	ProductName  string        `json:"productName" form:"productName"`
	DiscountRate jtypes.JPrice `json:"discountRate" form:"discountRate"`
}

// AssignCategory 指定分类
type AssignCategory struct {
	CategoryId   uint          `json:"categoryId" form:"categoryId"`
	CategoryName string        `json:"categoryName" form:"categoryName"`
	DiscountRate jtypes.JPrice `json:"discountRate" form:"discountRate"`
}
