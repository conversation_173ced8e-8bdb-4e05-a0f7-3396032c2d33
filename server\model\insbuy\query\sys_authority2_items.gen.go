// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newSysAuthority2Item(db *gorm.DB, opts ...gen.DOOption) sysAuthority2Item {
	_sysAuthority2Item := sysAuthority2Item{}

	_sysAuthority2Item.sysAuthority2ItemDo.UseDB(db, opts...)
	_sysAuthority2Item.sysAuthority2ItemDo.UseModel(&system.SysAuthority2Item{})

	tableName := _sysAuthority2Item.sysAuthority2ItemDo.TableName()
	_sysAuthority2Item.ALL = field.NewAsterisk(tableName)
	_sysAuthority2Item.ID = field.NewUint(tableName, "id")
	_sysAuthority2Item.RuleId = field.NewUint(tableName, "rule_id")
	_sysAuthority2Item.Item = field.NewString(tableName, "item")
	_sysAuthority2Item.Val = field.NewInt(tableName, "val")

	_sysAuthority2Item.fillFieldMap()

	return _sysAuthority2Item
}

type sysAuthority2Item struct {
	sysAuthority2ItemDo

	ALL    field.Asterisk
	ID     field.Uint
	RuleId field.Uint
	Item   field.String
	Val    field.Int

	fieldMap map[string]field.Expr
}

func (s sysAuthority2Item) Table(newTableName string) *sysAuthority2Item {
	s.sysAuthority2ItemDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s sysAuthority2Item) As(alias string) *sysAuthority2Item {
	s.sysAuthority2ItemDo.DO = *(s.sysAuthority2ItemDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *sysAuthority2Item) updateTableName(table string) *sysAuthority2Item {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewUint(table, "id")
	s.RuleId = field.NewUint(table, "rule_id")
	s.Item = field.NewString(table, "item")
	s.Val = field.NewInt(table, "val")

	s.fillFieldMap()

	return s
}

func (s *sysAuthority2Item) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *sysAuthority2Item) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 4)
	s.fieldMap["id"] = s.ID
	s.fieldMap["rule_id"] = s.RuleId
	s.fieldMap["item"] = s.Item
	s.fieldMap["val"] = s.Val
}

func (s sysAuthority2Item) clone(db *gorm.DB) sysAuthority2Item {
	s.sysAuthority2ItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s sysAuthority2Item) replaceDB(db *gorm.DB) sysAuthority2Item {
	s.sysAuthority2ItemDo.ReplaceDB(db)
	return s
}

type sysAuthority2ItemDo struct{ gen.DO }

func (s sysAuthority2ItemDo) Debug() *sysAuthority2ItemDo {
	return s.withDO(s.DO.Debug())
}

func (s sysAuthority2ItemDo) WithContext(ctx context.Context) *sysAuthority2ItemDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s sysAuthority2ItemDo) ReadDB() *sysAuthority2ItemDo {
	return s.Clauses(dbresolver.Read)
}

func (s sysAuthority2ItemDo) WriteDB() *sysAuthority2ItemDo {
	return s.Clauses(dbresolver.Write)
}

func (s sysAuthority2ItemDo) Session(config *gorm.Session) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Session(config))
}

func (s sysAuthority2ItemDo) Clauses(conds ...clause.Expression) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s sysAuthority2ItemDo) Returning(value interface{}, columns ...string) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s sysAuthority2ItemDo) Not(conds ...gen.Condition) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s sysAuthority2ItemDo) Or(conds ...gen.Condition) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s sysAuthority2ItemDo) Select(conds ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s sysAuthority2ItemDo) Where(conds ...gen.Condition) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s sysAuthority2ItemDo) Order(conds ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s sysAuthority2ItemDo) Distinct(cols ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s sysAuthority2ItemDo) Omit(cols ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s sysAuthority2ItemDo) Join(table schema.Tabler, on ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s sysAuthority2ItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s sysAuthority2ItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s sysAuthority2ItemDo) Group(cols ...field.Expr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s sysAuthority2ItemDo) Having(conds ...gen.Condition) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s sysAuthority2ItemDo) Limit(limit int) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s sysAuthority2ItemDo) Offset(offset int) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s sysAuthority2ItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s sysAuthority2ItemDo) Unscoped() *sysAuthority2ItemDo {
	return s.withDO(s.DO.Unscoped())
}

func (s sysAuthority2ItemDo) Create(values ...*system.SysAuthority2Item) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s sysAuthority2ItemDo) CreateInBatches(values []*system.SysAuthority2Item, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s sysAuthority2ItemDo) Save(values ...*system.SysAuthority2Item) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s sysAuthority2ItemDo) First() (*system.SysAuthority2Item, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2Item), nil
	}
}

func (s sysAuthority2ItemDo) Take() (*system.SysAuthority2Item, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2Item), nil
	}
}

func (s sysAuthority2ItemDo) Last() (*system.SysAuthority2Item, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2Item), nil
	}
}

func (s sysAuthority2ItemDo) Find() ([]*system.SysAuthority2Item, error) {
	result, err := s.DO.Find()
	return result.([]*system.SysAuthority2Item), err
}

func (s sysAuthority2ItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*system.SysAuthority2Item, err error) {
	buf := make([]*system.SysAuthority2Item, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s sysAuthority2ItemDo) FindInBatches(result *[]*system.SysAuthority2Item, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s sysAuthority2ItemDo) Attrs(attrs ...field.AssignExpr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s sysAuthority2ItemDo) Assign(attrs ...field.AssignExpr) *sysAuthority2ItemDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s sysAuthority2ItemDo) Joins(fields ...field.RelationField) *sysAuthority2ItemDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s sysAuthority2ItemDo) Preload(fields ...field.RelationField) *sysAuthority2ItemDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s sysAuthority2ItemDo) FirstOrInit() (*system.SysAuthority2Item, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2Item), nil
	}
}

func (s sysAuthority2ItemDo) FirstOrCreate() (*system.SysAuthority2Item, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*system.SysAuthority2Item), nil
	}
}

func (s sysAuthority2ItemDo) FindByPage(offset int, limit int) (result []*system.SysAuthority2Item, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s sysAuthority2ItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s sysAuthority2ItemDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s sysAuthority2ItemDo) Delete(models ...*system.SysAuthority2Item) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *sysAuthority2ItemDo) withDO(do gen.Dao) *sysAuthority2ItemDo {
	s.DO = *do.(*gen.DO)
	return s
}
