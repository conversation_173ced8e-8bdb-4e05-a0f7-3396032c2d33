package test

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore"
	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/debug"
	"testing"
)

var me insstore.KtvMe

func init() {
	c := colly.NewCollector(colly.Debugger(&debug.LogDebugger{}))
	me = insstore.KtvMe{
		UserName:  "13420923236",
		Password:  "29012235",
		Collector: c,
	}
}

// 测试
func TestLogin(t *testing.T) {
	prepare()
	err := me.<PERSON>gin(context.Background())
	if err != nil {
		return
	}
}

func TestGetTokenId(t *testing.T) {
	prepare()
	err := me.Login(context.Background())
	if err != nil {
		return
	}
	err = me.GetTokenId()
	if err != nil {
		return
	}
}

func TestGetPaySession(t *testing.T) {
	prepare()
	err := me.<PERSON><PERSON>(context.Background())
	if err != nil {
		return
	}
	err = me.GetTokenId()
	if err != nil {
		return
	}
	err = me.GetPaySession(context.Background())
	if err != nil {
		return
	}
}

func TestLoginCompany(t *testing.T) {
	prepare()
	err := me.LoginCompany(context.Background())
	if err != nil {
		return
	}
}

func TestExportOrder(t *testing.T) {
	prepare()
	err := me.Login(context.Background())
	if err != nil {
		return
	}
	err = me.GetTokenId()
	if err != nil {
		return
	}
	err = me.GetPaySession(context.Background())
	if err != nil {
		return
	}
	err = me.ExportOrder(context.Background(), insstore.ExportOrderParams{
		Sdate:    "2024-11-01",
		Edate:    "2024-11-22",
		FileName: global.GVA_CONFIG.Excel.Dir + "ketme/" + "111.xls",
	})
	if err != nil {
		return
	}
}

func TestParseExcelToIns(t *testing.T) {
	prepare()
	me = insstore.KtvMe{
		UserName: "",
		Password: "",
	}
	res, err := me.ParseExcelToIns(context.Background(), insstore.ParseExcelParams{
		FileName: "./resource/excel/ketme/wwww.xls",
	})
	if err != nil {
		t.Fatalf("解析excel错误%v", err.Error())
		return
	}
	t.Fatalf("%+v\n", res)
	return
}

func TestParseAllPayExcelToIns2(t *testing.T) {
	prepare()
	s := &insstore.AllPaySrv{
		StoreId: 6,
	}
	res, err := s.ParseExcelToIns(context.Background(), insstore.ParseExcelParams{
		FileName: "./resource/excel/allpay/563290058133ZNH.xlsx",
	})
	if err != nil {
		t.Fatalf("解析excel错误%v", err.Error())
		return
	}
	t.Fatalf("%+v\n", res)
	return
}
