package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsExcelApi struct {
}

// ExcelCommonImport
// @Tags      insExcelApi
// @Summary   导入原料excel
// @Security  ApiKeyAuth
// @accept    multipart/form-data
// @Produce   application/json
// @Param data body insbuyReq.ImportInoutDetailsReq true "导入请求参数"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"导入成功"}"
// @Router    /insExcelApi/excelCommonImport [post]
func (p *InsExcelApi) ExcelCommonImport(c *gin.Context) {
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		global.GVA_LOG.Error("接收文件失败!", zap.Error(err))
		response.FailWithMessage("接收文件失败", c)
		return
	}
	req := insbuyReq.ImportInoutDetailsReq{}
	if err = GinMustBind(c, &req); err != nil {
		response.ResultErr(nil, err, c)
		return
	}
	req.Header = header
	resp, err := insImportService.ExcelCommonImport(req) // 文件上传后拿到文件路径
	if err != nil {
		global.GVA_LOG.Error("导入失败!", zap.Error(err))
		response.FailWithMessage("导入失败:"+err.Error(), c)
		return
	}
	response.OkWithData(resp, c)
}

// FindExcelTemplate 用id查询导入模板
// @Tags insExcelApi
// @Summary 用id查询导入模板
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.FindExcelTemplateReq true "用id查询导入模板"
// @Success   200   {object}  response.Response{data=insbuy.InsExcelTemplate,msg=string}  "用id查询导入模板"
// @Router /insExcelApi/findExcelTemplate [get]
func (p *InsExcelApi) FindExcelTemplate(c *gin.Context) {
	var req insbuyReq.FindExcelTemplateReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insImportService.FindExcelTemplateByImportType(req.ImportType); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}

// DownloadImportResult 下载导入结果
// @Tags insExcelApi
// @Summary 下载导入结果
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.DownloadImportResultReq true "下载导入结果"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"导出成功"}"
// @Router /insExcelApi/downloadImportResult [get]
func (p *InsExcelApi) DownloadImportResult(c *gin.Context) {
	var req insbuyReq.DownloadImportResultReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if resp, err := insImportService.DownloadImportResult(c, req); err != nil {
		global.GVA_LOG.Error("导出失败!", zap.Error(err))
		response.FailWithMessage("导出失败", c)
	} else {
		response.OkWithData(resp, c)
	}
}
