<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="<EMAIL>">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||aliyun_root|127.0.0.1|ALTER|G
|root||aurora||ALTER|G
|root||insbuy_root||ALTER|G
|root||aliyun_root|127.0.0.1|ALTER ROUTINE|G
|root||aurora||ALTER ROUTINE|G
|root||insbuy_root||ALTER ROUTINE|G
|root||aliyun_root|127.0.0.1|APPLICATION_PASSWORD_ADMIN|G
|root||aurora||APPLICATION_PASSWORD_ADMIN|G
|root||aliyun_root|127.0.0.1|AUDIT_ABORT_EXEMPT|G
|root||aurora||AUDIT_ABORT_EXEMPT|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||aliyun_root|127.0.0.1|AUDIT_ADMIN|G
|root||aurora||AUDIT_ADMIN|G
|root||aliyun_root|127.0.0.1|AUTHENTICATION_POLICY_ADMIN|G
|root||aurora||AUTHENTICATION_POLICY_ADMIN|G
|root||aliyun_root|127.0.0.1|BACKUP_ADMIN|G
|root||aurora||BACKUP_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||aliyun_root|127.0.0.1|BINLOG_ADMIN|G
|root||aurora||BINLOG_ADMIN|G
|root||aliyun_root|127.0.0.1|BINLOG_ENCRYPTION_ADMIN|G
|root||aurora||BINLOG_ENCRYPTION_ADMIN|G
|root||aliyun_root|127.0.0.1|CLONE_ADMIN|G
|root||aurora||CLONE_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||aliyun_root|127.0.0.1|CONNECTION_ADMIN|G
|root||aurora||CONNECTION_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||replicator||CONNECTION_ADMIN|G
|root||aliyun_root|127.0.0.1|CREATE|G
|root||aurora||CREATE|G
|root||insbuy_root||CREATE|G
|root||aliyun_root|127.0.0.1|CREATE ROLE|G
|root||aurora||CREATE ROLE|G
|root||aliyun_root|127.0.0.1|CREATE ROUTINE|G
|root||aurora||CREATE ROUTINE|G
|root||insbuy_root||CREATE ROUTINE|G
|root||aliyun_root|127.0.0.1|CREATE TABLESPACE|G
|root||aurora||CREATE TABLESPACE|G
|root||aliyun_root|127.0.0.1|CREATE TEMPORARY TABLES|G
|root||aurora||CREATE TEMPORARY TABLES|G
|root||insbuy_root||CREATE TEMPORARY TABLES|G
|root||aliyun_root|127.0.0.1|CREATE USER|G
|root||aurora||CREATE USER|G
|root||insbuy_root||CREATE USER|G
|root||aliyun_root|127.0.0.1|CREATE VIEW|G
|root||aurora||CREATE VIEW|G
|root||insbuy_root||CREATE VIEW|G
|root||aliyun_root|127.0.0.1|DELETE|G
|root||aurora||DELETE|G
|root||insbuy_root||DELETE|G
|root||aliyun_root|127.0.0.1|DROP|G
|root||aurora||DROP|G
|root||insbuy_root||DROP|G
|root||aliyun_root|127.0.0.1|DROP ROLE|G
|root||aurora||DROP ROLE|G
|root||aliyun_root|127.0.0.1|ENCRYPTION_KEY_ADMIN|G
|root||aurora||ENCRYPTION_KEY_ADMIN|G
|root||aliyun_root|127.0.0.1|EVENT|G
|root||aurora||EVENT|G
|root||insbuy_root||EVENT|G
|root||aliyun_root|127.0.0.1|EXECUTE|G
|root||aurora||EXECUTE|G
|root||insbuy_root||EXECUTE|G
|root||aliyun_root|127.0.0.1|FILE|G
|root||aurora||FILE|G
|root||aliyun_root|127.0.0.1|FLUSH_OPTIMIZER_COSTS|G
|root||aurora||FLUSH_OPTIMIZER_COSTS|G
|root||aliyun_root|127.0.0.1|FLUSH_STATUS|G
|root||aurora||FLUSH_STATUS|G
|root||aliyun_root|127.0.0.1|FLUSH_TABLES|G
|root||aurora||FLUSH_TABLES|G
|root||aliyun_root|127.0.0.1|FLUSH_USER_RESOURCES|G
|root||aurora||FLUSH_USER_RESOURCES|G
|root||aliyun_root|127.0.0.1|GROUP_REPLICATION_ADMIN|G
|root||aurora||GROUP_REPLICATION_ADMIN|G
|root||aliyun_root|127.0.0.1|GROUP_REPLICATION_STREAM|G
|root||aurora||GROUP_REPLICATION_STREAM|G
|root||replicator||GROUP_REPLICATION_STREAM|G
|root||aliyun_root|127.0.0.1|INDEX|G
|root||aurora||INDEX|G
|root||insbuy_root||INDEX|G
|root||aliyun_root|127.0.0.1|INNODB_REDO_LOG_ARCHIVE|G
|root||aurora||INNODB_REDO_LOG_ARCHIVE|G
|root||aliyun_root|127.0.0.1|INNODB_REDO_LOG_ENABLE|G
|root||aurora||INNODB_REDO_LOG_ENABLE|G
|root||aliyun_root|127.0.0.1|INSERT|G
|root||aurora||INSERT|G
|root||insbuy_root||INSERT|G
|root||aliyun_root|127.0.0.1|LOCK TABLES|G
|root||aurora||LOCK TABLES|G
|root||insbuy_root||LOCK TABLES|G
|root||aliyun_root|127.0.0.1|PASSWORDLESS_USER_ADMIN|G
|root||aurora||PASSWORDLESS_USER_ADMIN|G
|root||aliyun_root|127.0.0.1|PERSIST_RO_VARIABLES_ADMIN|G
|root||aurora||PERSIST_RO_VARIABLES_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|PROCESS|G
|root||aurora||PROCESS|G
|root||insbuy_root||PROCESS|G
|root||aliyun_root|127.0.0.1|REFERENCES|G
|root||aurora||REFERENCES|G
|root||insbuy_root||REFERENCES|G
|root||aliyun_root|127.0.0.1|RELOAD|G
|root||aurora||RELOAD|G
|root||insbuy_root||RELOAD|G
|root||aliyun_root|127.0.0.1|REPLICATION CLIENT|G
|root||aurora||REPLICATION CLIENT|G
|root||insbuy_root||REPLICATION CLIENT|G
|root||replicator||REPLICATION CLIENT|G
|root||aliyun_root|127.0.0.1|REPLICATION SLAVE|G
|root||aurora||REPLICATION SLAVE|G
|root||insbuy_root||REPLICATION SLAVE|G
|root||replicator||REPLICATION SLAVE|G
|root||aliyun_root|127.0.0.1|REPLICATION_APPLIER|G
|root||aurora||REPLICATION_APPLIER|G
|root||aliyun_root|127.0.0.1|REPLICATION_SLAVE_ADMIN|G
|root||aurora||REPLICATION_SLAVE_ADMIN|G
|root||aliyun_root|127.0.0.1|RESOURCE_GROUP_ADMIN|G
|root||aurora||RESOURCE_GROUP_ADMIN|G
|root||aliyun_root|127.0.0.1|RESOURCE_GROUP_USER|G
|root||aurora||RESOURCE_GROUP_USER|G
|root||aliyun_root|127.0.0.1|ROLE_ADMIN|G
|root||aurora||ROLE_ADMIN|G
|root||aliyun_root|127.0.0.1|SELECT|G
|root||aurora||SELECT|G
|root||insbuy_root||SELECT|G
|root||mysql.infoschema|localhost|SELECT|G
|root||aliyun_root|127.0.0.1|SERVICE_CONNECTION_ADMIN|G
|root||aurora||SERVICE_CONNECTION_ADMIN|G
|root||aliyun_root|127.0.0.1|SESSION_VARIABLES_ADMIN|G
|root||aurora||SESSION_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|SET_USER_ID|G
|root||aurora||SET_USER_ID|G
|root||aliyun_root|127.0.0.1|SHOW DATABASES|G
|root||aurora||SHOW DATABASES|G
|root||aliyun_root|127.0.0.1|SHOW VIEW|G
|root||aurora||SHOW VIEW|G
|root||insbuy_root||SHOW VIEW|G
|root||aliyun_root|127.0.0.1|SHOW_ROUTINE|G
|root||aurora||SHOW_ROUTINE|G
|root||aliyun_root|127.0.0.1|SHUTDOWN|G
|root||aurora||SHUTDOWN|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||aliyun_root|127.0.0.1|SUPER|G
|root||aurora||SUPER|G
|root||mysql.session|localhost|SUPER|G
|root||aliyun_root|127.0.0.1|SYSTEM_USER|G
|root||aurora||SYSTEM_USER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||aliyun_root|127.0.0.1|SYSTEM_VARIABLES_ADMIN|G
|root||aurora||SYSTEM_VARIABLES_ADMIN|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||aliyun_root|127.0.0.1|TABLE_ENCRYPTION_ADMIN|G
|root||aurora||TABLE_ENCRYPTION_ADMIN|G
|root||aliyun_root|127.0.0.1|TRIGGER|G
|root||aurora||TRIGGER|G
|root||insbuy_root||TRIGGER|G
|root||aliyun_root|127.0.0.1|UPDATE|G
|root||aurora||UPDATE|G
|root||insbuy_root||UPDATE|G
|root||aliyun_root|127.0.0.1|XA_RECOVER_ADMIN|G
|root||aurora||XA_RECOVER_ADMIN|G
|root||aliyun_root|127.0.0.1|grant option|G
|root||aurora||grant option|G
|root||insbuy_root||grant option|G
mysql|schema||aurora||ALTER|G
mysql|schema||aurora||ALTER ROUTINE|G
mysql|schema||aurora||CREATE|G
mysql|schema||aurora||CREATE ROUTINE|G
mysql|schema||aurora||CREATE TEMPORARY TABLES|G
mysql|schema||aurora||CREATE VIEW|G
mysql|schema||aurora||DELETE|G
mysql|schema||aurora||DROP|G
mysql|schema||aurora||EVENT|G
mysql|schema||aurora||EXECUTE|G
mysql|schema||aurora||INDEX|G
mysql|schema||aurora||INSERT|G
mysql|schema||aurora||LOCK TABLES|G
mysql|schema||aurora||REFERENCES|G
mysql|schema||aurora||SELECT|G
mysql|schema||aurora||SHOW VIEW|G
mysql|schema||aurora||TRIGGER|G
mysql|schema||aurora||UPDATE|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G
test|schema||aurora||ALTER|G
test|schema||aurora||ALTER ROUTINE|G
test|schema||aurora||CREATE|G
test|schema||aurora||CREATE ROUTINE|G
test|schema||aurora||CREATE TEMPORARY TABLES|G
test|schema||aurora||CREATE VIEW|G
test|schema||aurora||DELETE|G
test|schema||aurora||DROP|G
test|schema||aurora||EVENT|G
test|schema||aurora||EXECUTE|G
test|schema||aurora||INDEX|G
test|schema||aurora||INSERT|G
test|schema||aurora||LOCK TABLES|G
test|schema||aurora||REFERENCES|G
test|schema||aurora||SELECT|G
test|schema||aurora||SHOW VIEW|G
test|schema||aurora||TRIGGER|G
test|schema||aurora||UPDATE|G</Grants>
      <ServerVersion>8.0.28</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8_tolower_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="274" parent="1" name="mysql">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="275" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="276" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="277" parent="1" name="__recycle_bin__">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="278" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="279" parent="1" name="ins-prod">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="280" parent="1" name="ins-pay">
      <Current>1</Current>
      <IntrospectionTimestamp>2024-03-20.19:12:08</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2024-03-20.03:12:08</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="281" parent="1" name="ins_tmp">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="282" parent="1" name="ins-prod_backup">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <user id="283" parent="1" name="aurora"/>
    <user id="284" parent="1" name="insbuy_root"/>
    <user id="285" parent="1" name="replicator"/>
    <user id="286" parent="1" name="aliyun_root">
      <Host>127.0.0.1</Host>
    </user>
    <user id="287" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="288" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="289" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="290" parent="280" name="inspay_config">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="291" parent="280" name="inspay_config_app">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="292" parent="280" name="inspay_config_app_log">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="293" parent="280" name="inspay_config_merchant">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="294" parent="280" name="inspay_gift_card">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="295" parent="280" name="inspay_gift_card_act">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="296" parent="280" name="inspay_gift_card_log">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="297" parent="280" name="inspay_order">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="298" parent="280" name="inspay_order_log">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="299" parent="280" name="inspay_order_notify_log">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="300" parent="280" name="inspay_order_refund">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="301" parent="280" name="inspay_order_status">
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="302" parent="280" name="inspay_sys_counter">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="303" parent="280" name="inspay_version">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="304" parent="290" name="id">
      <AutoIncrement>26</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="305" parent="290" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="306" parent="290" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="307" parent="290" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="308" parent="290" name="merchant_id">
      <Comment>商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="309" parent="290" name="app_id">
      <Comment>应用id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="310" parent="290" name="pay_type">
      <Comment>支付类型</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="311" parent="290" name="pay_app_id">
      <Comment>支付对应的appid</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="312" parent="290" name="pay_cusid">
      <Comment>支付对应商户id</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="313" parent="290" name="pay_private_key">
      <Comment>私钥</Comment>
      <DasType>text|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="314" parent="290" name="public_key">
      <Comment>公钥</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="315" parent="290" name="is_prod">
      <Comment>是否是正式</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="316" parent="290" name="notify_url">
      <Comment>回调地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="317" parent="290" name="sub_appid">
      <Comment>微信小程序/微信公众号/APP的appid</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <index id="318" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="319" parent="290" name="pt">
      <ColNames>merchant_id
app_id
pay_type</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="320" parent="290" name="idx_inspay_config_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="321" parent="290" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="322" parent="290" name="a">
      <ColNames>app_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="323" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="324" parent="290" name="pt">
      <UnderlyingIndexName>pt</UnderlyingIndexName>
    </key>
    <column id="325" parent="291" name="id">
      <AutoIncrement>26</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="326" parent="291" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="327" parent="291" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="328" parent="291" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="329" parent="291" name="merchant_id">
      <Comment>商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="330" parent="291" name="white_ip">
      <Comment>IP白名单 空表示不限制，多个值用逗号连接</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="331" parent="291" name="name">
      <Comment>应用名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="332" parent="291" name="secret_key">
      <Comment>应用密钥</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="333" parent="291" name="code">
      <Comment>应用code</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="334" parent="291" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>10</Position>
    </column>
    <index id="335" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="336" parent="291" name="code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="337" parent="291" name="idx_inspay_config_app_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="338" parent="291" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="339" parent="291" name="c">
      <ColNames>code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="340" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="341" parent="291" name="code">
      <UnderlyingIndexName>code</UnderlyingIndexName>
    </key>
    <column id="342" parent="292" name="id">
      <AutoIncrement>26</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="343" parent="292" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="344" parent="292" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="345" parent="292" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="346" parent="292" name="app_id">
      <Comment>应用id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="347" parent="292" name="merchant_id">
      <Comment>商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="348" parent="292" name="content">
      <Comment>变更内容</Comment>
      <DasType>text|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="349" parent="292" name="ip">
      <Comment>操作者IP地址</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>8</Position>
    </column>
    <index id="350" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="351" parent="292" name="idx_inspay_config_app_log_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="352" parent="292" name="a">
      <ColNames>app_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="353" parent="292" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="354" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="355" parent="293" name="id">
      <AutoIncrement>26</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="356" parent="293" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="357" parent="293" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="358" parent="293" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="359" parent="293" name="name">
      <Comment>商户名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="360" parent="293" name="code">
      <Comment>商户code</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="361" parent="293" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>7</Position>
    </column>
    <index id="362" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="363" parent="293" name="code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="364" parent="293" name="idx_inspay_config_merchant_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="365" parent="293" name="c">
      <ColNames>code</ColNames>
      <Type>btree</Type>
    </index>
    <key id="366" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="367" parent="293" name="code">
      <UnderlyingIndexName>code</UnderlyingIndexName>
    </key>
    <column id="368" parent="294" name="id">
      <AutoIncrement>6058</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="369" parent="294" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="370" parent="294" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="371" parent="294" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="372" parent="294" name="code">
      <Comment>唯一激活码</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="373" parent="294" name="app_id">
      <Comment>所属应用id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="374" parent="294" name="act_id">
      <Comment>活动批次id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="375" parent="294" name="seq">
      <Comment>组内序号 累加</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="376" parent="294" name="amount">
      <Comment>面额 冗余、单位：分</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="377" parent="294" name="currency">
      <Comment>币种 冗余</Comment>
      <DasType>varchar(8)|0s</DasType>
      <DefaultExpression>&apos;CNY&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="378" parent="294" name="passwd">
      <Comment>密码</Comment>
      <DasType>varchar(8)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="379" parent="294" name="status">
      <Comment>状态 1待使用 2已使用 3已禁用</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="380" parent="294" name="notify_status">
      <Comment>通知状态 0未通知 1已通知 2通知失败</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="381" parent="294" name="bind_acct">
      <Comment>绑定的账号 由cp传入</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="382" parent="294" name="bind_ip">
      <Comment>绑定ip</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="383" parent="294" name="bind_amount">
      <Comment>消费金额 冗余、单位：分</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="384" parent="294" name="bind_at">
      <Comment>绑定时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="385" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="386" parent="294" name="c">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="387" parent="294" name="idx_inspay_gift_card_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="388" parent="294" name="a">
      <ColNames>app_id
act_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="389" parent="294" name="s">
      <ColNames>act_id
seq</ColNames>
      <Type>btree</Type>
    </index>
    <key id="390" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="391" parent="294" name="c">
      <UnderlyingIndexName>c</UnderlyingIndexName>
    </key>
    <column id="392" parent="295" name="id">
      <AutoIncrement>13</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="393" parent="295" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="394" parent="295" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="395" parent="295" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="396" parent="295" name="merchant_id">
      <Comment>所属商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="397" parent="295" name="app_id">
      <Comment>所属应用id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="398" parent="295" name="batch_code">
      <Comment>批次号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="399" parent="295" name="amount">
      <Comment>面额 单位：分</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="400" parent="295" name="gift_amount">
      <Comment>赠送部分 包含在面额中，单位：分</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="401" parent="295" name="currency">
      <Comment>币种</Comment>
      <DasType>varchar(8)|0s</DasType>
      <DefaultExpression>&apos;CNY&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="402" parent="295" name="quantity">
      <Comment>数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="403" parent="295" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="404" parent="295" name="expire_time">
      <Comment>过期时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="405" parent="295" name="act_seq">
      <Comment>批次内序号 从1开始，累加</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="406" parent="295" name="card_seq_base">
      <Comment>本组卡号起始编号，默认为1</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="407" parent="295" name="card_seq_last">
      <Comment>本组卡号结束编号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="408" parent="295" name="status">
      <Comment>状态 0默认 1正常 2已禁用</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="409" parent="295" name="caption">
      <Comment>标题</Comment>
      <DasType>varchar(128)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="410" parent="295" name="notify_url">
      <Comment>消费回调地址 可选</Comment>
      <DasType>varchar(512)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>19</Position>
    </column>
    <column id="411" parent="295" name="remark">
      <Comment>活动说明</Comment>
      <DasType>varchar(512)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="412" parent="295" name="ext_info">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>21</Position>
    </column>
    <index id="413" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="414" parent="295" name="idx_inspay_gift_card_act_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="415" parent="295" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="416" parent="295" name="a">
      <ColNames>app_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="417" parent="295" name="b">
      <ColNames>batch_code
amount
currency</ColNames>
      <Type>btree</Type>
    </index>
    <key id="418" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="419" parent="296" name="id">
      <AutoIncrement>1099</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="420" parent="296" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="421" parent="296" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="422" parent="296" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="423" parent="296" name="app_id">
      <Comment>所属应用id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="424" parent="296" name="app_code">
      <Comment>应用编码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="425" parent="296" name="merchant_id">
      <Comment>所属商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="426" parent="296" name="merchant_code">
      <Comment>商户编码</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="427" parent="296" name="batch_code">
      <Comment>批次号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="428" parent="296" name="act_id">
      <Comment>活动批次id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="429" parent="296" name="act_seq">
      <Comment>组内序号 累加</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="430" parent="296" name="total_amount">
      <Comment>涉及金额</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="431" parent="296" name="total_quantity">
      <Comment>涉及数量</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="432" parent="296" name="content">
      <Comment>内容</Comment>
      <DasType>json|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="433" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="434" parent="296" name="idx_inspay_gift_card_log_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="435" parent="296" name="a">
      <ColNames>app_id
act_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="436" parent="296" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="437" parent="296" name="b">
      <ColNames>batch_code</ColNames>
      <Type>btree</Type>
    </index>
    <index id="438" parent="296" name="s">
      <ColNames>act_id
act_seq</ColNames>
      <Type>btree</Type>
    </index>
    <key id="439" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="440" parent="297" name="id">
      <AutoIncrement>60708</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="441" parent="297" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="442" parent="297" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="443" parent="297" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="444" parent="297" name="merchant_id">
      <Comment>商户ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="445" parent="297" name="app_id">
      <Comment>应用ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="446" parent="297" name="cp_order">
      <Comment>商户订单号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="447" parent="297" name="cp_account">
      <Comment>用户的账号 可选</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="448" parent="297" name="cp_msg">
      <Comment>透传信息</Comment>
      <DasType>varchar(512)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="449" parent="297" name="amount">
      <Comment>订单金额 单位分</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="450" parent="297" name="pay_amount">
      <Comment>成交金额 单位分</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="451" parent="297" name="currency">
      <Comment>币种</Comment>
      <DasType>varchar(8)|0s</DasType>
      <DefaultExpression>&apos;CNY&apos;</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="452" parent="297" name="notify_url">
      <Comment>回调通知地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="453" parent="297" name="callback_status">
      <Comment>回调状态 1已回调</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="454" parent="297" name="ext">
      <Comment>扩展信息</Comment>
      <DasType>json|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="455" parent="297" name="fin_time">
      <Comment>完成时间</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="456" parent="297" name="conf_id">
      <Comment>配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="457" parent="297" name="pay_currency">
      <Comment>成交币种</Comment>
      <DasType>varchar(8)|0s</DasType>
      <DefaultExpression>&apos;CNY&apos;</DefaultExpression>
      <Position>18</Position>
    </column>
    <column id="458" parent="297" name="trxid">
      <Comment>第三方流水号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="459" parent="297" name="chnltrxid">
      <Comment>渠道流水号</Comment>
      <DasType>varchar(80)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="460" parent="297" name="auth_code">
      <Comment>支付授权码</Comment>
      <DasType>varchar(80)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="461" parent="297" name="terminfo">
      <Comment>终端信息</Comment>
      <DasType>varchar(300)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>22</Position>
    </column>
    <column id="462" parent="297" name="trx_code">
      <Comment>交易类型</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>23</Position>
    </column>
    <index id="463" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="464" parent="297" name="idx_inspay_order_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="465" parent="297" name="m">
      <ColNames>merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="466" parent="297" name="a">
      <ColNames>app_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="467" parent="297" name="cp_order">
      <ColNames>cp_order
app_id
merchant_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="468" parent="297" name="c">
      <ColNames>conf_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="469" parent="297" name="o1">
      <ColNames>trxid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="470" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="471" parent="298" name="id">
      <AutoIncrement>84176</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="472" parent="298" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="473" parent="298" name="order_id">
      <Comment>订单表id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="474" parent="298" name="event">
      <Comment>事件</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="475" parent="298" name="app_code">
      <Comment>应用code</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="476" parent="298" name="merchant_code">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="477" parent="298" name="conf_id">
      <Comment>配置ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="478" parent="298" name="pay_type">
      <Comment>支付类型</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="479" parent="298" name="pay_app_id">
      <Comment>支付对应的appid</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="480" parent="298" name="pay_cusid">
      <Comment>支付对应商户id</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="481" parent="298" name="content">
      <Comment>日志内容</Comment>
      <DasType>json|0s</DasType>
      <Position>11</Position>
    </column>
    <index id="482" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="483" parent="298" name="o">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="484" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="485" parent="299" name="id">
      <AutoIncrement>48946</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="486" parent="299" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="487" parent="299" name="order_id">
      <Comment>订单表id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="488" parent="299" name="notify_type">
      <Comment>通知类型</Comment>
      <DasType>varchar(8)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="489" parent="299" name="notify_url">
      <Comment>回调通知地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="490" parent="299" name="msg">
      <Comment>回调通知内容</Comment>
      <DasType>varchar(1024)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="491" parent="299" name="resp_time">
      <Comment>回调通知时间</Comment>
      <DasType>datetime(3)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="492" parent="299" name="resp_code">
      <Comment>回调通知返回状态码</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="493" parent="299" name="resp_msg">
      <Comment>回调通知返回内容或者错误信息</Comment>
      <DasType>varchar(512)|0s</DasType>
      <Position>9</Position>
    </column>
    <index id="494" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="495" parent="299" name="o">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="496" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="497" parent="300" name="id">
      <AutoIncrement>2908</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="498" parent="300" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="499" parent="300" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="500" parent="300" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="501" parent="300" name="order_id">
      <Comment>订单表id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="502" parent="300" name="refund_sn">
      <Comment>退款单号</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="503" parent="300" name="old_order_sn">
      <Comment>原订单号,等同于订单表order_sn</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="504" parent="300" name="pay_status">
      <Comment>支付状态</Comment>
      <DasType>tinyint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="505" parent="300" name="allin_pay_status">
      <Comment>通联支付状态</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="506" parent="300" name="allin_pay_code">
      <Comment>通联支付code</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="507" parent="300" name="err_msg">
      <Comment>错误信息用于调试</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="508" parent="300" name="chnltrxid">
      <Comment>渠道流水号</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="509" parent="300" name="allin_fin_time">
      <Comment>通联完成时间</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="510" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="511" parent="300" name="idx_inspay_order_refund_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="512" parent="300" name="o">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="513" parent="300" name="refund_sn">
      <ColNames>refund_sn</ColNames>
      <Type>btree</Type>
    </index>
    <index id="514" parent="300" name="old_sn">
      <ColNames>old_order_sn</ColNames>
      <Type>btree</Type>
    </index>
    <index id="515" parent="300" name="c">
      <ColNames>chnltrxid</ColNames>
      <Type>btree</Type>
    </index>
    <key id="516" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="517" parent="301" name="id">
      <AutoIncrement>60708</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="518" parent="301" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="519" parent="301" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="520" parent="301" name="deleted_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="521" parent="301" name="order_id">
      <Comment>订单表id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="522" parent="301" name="order_status">
      <Comment>订单状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="523" parent="301" name="pay_status">
      <Comment>支付状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="524" parent="301" name="acct">
      <Comment>支付账号 扫码支付时微信openid</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="525" parent="301" name="err_msg">
      <Comment>错误信息 用于调试</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="526" parent="301" name="notify_status">
      <Comment>回调状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="527" parent="301" name="refund_status">
      <Comment>退款状态 0未退款 1已退款 2退款失败</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="528" parent="301" name="refund_notify_status">
      <Comment>退款回调状态</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="529" parent="301" name="refund_sn">
      <Comment>退款单号 成功退款时记录，目前是全额退款</Comment>
      <DasType>varchar(30)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="530" parent="301" name="refund_time">
      <Comment>退款时间</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="531" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="532" parent="301" name="order_id">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="533" parent="301" name="idx_inspay_order_status_deleted_at">
      <ColNames>deleted_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="534" parent="301" name="o">
      <ColNames>order_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="535" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="536" parent="301" name="order_id">
      <UnderlyingIndexName>order_id</UnderlyingIndexName>
    </key>
    <column id="537" parent="302" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="538" parent="302" name="code">
      <Comment>编码，组合值</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="539" parent="302" name="serial">
      <Comment>序号</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="540" parent="302" name="created_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="541" parent="302" name="updated_at">
      <DasType>datetime(3)|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="542" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="543" parent="302" name="code">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="544" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="545" parent="302" name="code">
      <UnderlyingIndexName>code</UnderlyingIndexName>
    </key>
    <column id="546" parent="303" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="547" parent="303" name="version">
      <Comment>版本号</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="548" parent="303" name="code">
      <Comment>版本号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="549" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="550" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>