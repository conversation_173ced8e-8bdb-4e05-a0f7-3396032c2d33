// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsExtStoreTicket(db *gorm.DB, opts ...gen.DOOption) insExtStoreTicket {
	_insExtStoreTicket := insExtStoreTicket{}

	_insExtStoreTicket.insExtStoreTicketDo.UseDB(db, opts...)
	_insExtStoreTicket.insExtStoreTicketDo.UseModel(&insbuy.InsExtStoreTicket{})

	tableName := _insExtStoreTicket.insExtStoreTicketDo.TableName()
	_insExtStoreTicket.ALL = field.NewAsterisk(tableName)
	_insExtStoreTicket.ID = field.NewUint(tableName, "id")
	_insExtStoreTicket.BusinessDay = field.NewTime(tableName, "business_day")
	_insExtStoreTicket.DataType = field.NewInt(tableName, "data_type")
	_insExtStoreTicket.ForActivity = field.NewInt(tableName, "for_activity")
	_insExtStoreTicket.ForTimes = field.NewInt(tableName, "for_times")
	_insExtStoreTicket.VerifyNum = field.NewInt(tableName, "verify_num")
	_insExtStoreTicket.VerifyAmount = field.NewFloat64(tableName, "verify_amount")
	_insExtStoreTicket.SaleNum = field.NewInt(tableName, "sale_num")
	_insExtStoreTicket.SaleAmount = field.NewFloat64(tableName, "sale_amount")
	_insExtStoreTicket.TicketNum = field.NewInt(tableName, "ticket_num")
	_insExtStoreTicket.TicketAmount = field.NewFloat64(tableName, "ticket_amount")
	_insExtStoreTicket.GoodAmount = field.NewFloat64(tableName, "good_amount")
	_insExtStoreTicket.AlreadyRefundAmount = field.NewFloat64(tableName, "already_refund_amount")
	_insExtStoreTicket.StoreId = field.NewUint(tableName, "store_id")
	_insExtStoreTicket.StoreCode = field.NewString(tableName, "store_code")
	_insExtStoreTicket.Ext = field.NewField(tableName, "ext")
	_insExtStoreTicket.CreatedAt = field.NewTime(tableName, "created_at")
	_insExtStoreTicket.CreatedBy = field.NewUint(tableName, "created_by")
	_insExtStoreTicket.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insExtStoreTicket.UpdatedBy = field.NewUint(tableName, "updated_by")

	_insExtStoreTicket.fillFieldMap()

	return _insExtStoreTicket
}

type insExtStoreTicket struct {
	insExtStoreTicketDo

	ALL                 field.Asterisk
	ID                  field.Uint
	BusinessDay         field.Time
	DataType            field.Int
	ForActivity         field.Int
	ForTimes            field.Int
	VerifyNum           field.Int
	VerifyAmount        field.Float64
	SaleNum             field.Int
	SaleAmount          field.Float64
	TicketNum           field.Int
	TicketAmount        field.Float64
	GoodAmount          field.Float64
	AlreadyRefundAmount field.Float64
	StoreId             field.Uint
	StoreCode           field.String
	Ext                 field.Field
	CreatedAt           field.Time
	CreatedBy           field.Uint
	UpdatedAt           field.Time
	UpdatedBy           field.Uint

	fieldMap map[string]field.Expr
}

func (i insExtStoreTicket) Table(newTableName string) *insExtStoreTicket {
	i.insExtStoreTicketDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insExtStoreTicket) As(alias string) *insExtStoreTicket {
	i.insExtStoreTicketDo.DO = *(i.insExtStoreTicketDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insExtStoreTicket) updateTableName(table string) *insExtStoreTicket {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.BusinessDay = field.NewTime(table, "business_day")
	i.DataType = field.NewInt(table, "data_type")
	i.ForActivity = field.NewInt(table, "for_activity")
	i.ForTimes = field.NewInt(table, "for_times")
	i.VerifyNum = field.NewInt(table, "verify_num")
	i.VerifyAmount = field.NewFloat64(table, "verify_amount")
	i.SaleNum = field.NewInt(table, "sale_num")
	i.SaleAmount = field.NewFloat64(table, "sale_amount")
	i.TicketNum = field.NewInt(table, "ticket_num")
	i.TicketAmount = field.NewFloat64(table, "ticket_amount")
	i.GoodAmount = field.NewFloat64(table, "good_amount")
	i.AlreadyRefundAmount = field.NewFloat64(table, "already_refund_amount")
	i.StoreId = field.NewUint(table, "store_id")
	i.StoreCode = field.NewString(table, "store_code")
	i.Ext = field.NewField(table, "ext")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.UpdatedBy = field.NewUint(table, "updated_by")

	i.fillFieldMap()

	return i
}

func (i *insExtStoreTicket) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insExtStoreTicket) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 20)
	i.fieldMap["id"] = i.ID
	i.fieldMap["business_day"] = i.BusinessDay
	i.fieldMap["data_type"] = i.DataType
	i.fieldMap["for_activity"] = i.ForActivity
	i.fieldMap["for_times"] = i.ForTimes
	i.fieldMap["verify_num"] = i.VerifyNum
	i.fieldMap["verify_amount"] = i.VerifyAmount
	i.fieldMap["sale_num"] = i.SaleNum
	i.fieldMap["sale_amount"] = i.SaleAmount
	i.fieldMap["ticket_num"] = i.TicketNum
	i.fieldMap["ticket_amount"] = i.TicketAmount
	i.fieldMap["good_amount"] = i.GoodAmount
	i.fieldMap["already_refund_amount"] = i.AlreadyRefundAmount
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["store_code"] = i.StoreCode
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["updated_by"] = i.UpdatedBy
}

func (i insExtStoreTicket) clone(db *gorm.DB) insExtStoreTicket {
	i.insExtStoreTicketDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insExtStoreTicket) replaceDB(db *gorm.DB) insExtStoreTicket {
	i.insExtStoreTicketDo.ReplaceDB(db)
	return i
}

type insExtStoreTicketDo struct{ gen.DO }

func (i insExtStoreTicketDo) Debug() *insExtStoreTicketDo {
	return i.withDO(i.DO.Debug())
}

func (i insExtStoreTicketDo) WithContext(ctx context.Context) *insExtStoreTicketDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insExtStoreTicketDo) ReadDB() *insExtStoreTicketDo {
	return i.Clauses(dbresolver.Read)
}

func (i insExtStoreTicketDo) WriteDB() *insExtStoreTicketDo {
	return i.Clauses(dbresolver.Write)
}

func (i insExtStoreTicketDo) Session(config *gorm.Session) *insExtStoreTicketDo {
	return i.withDO(i.DO.Session(config))
}

func (i insExtStoreTicketDo) Clauses(conds ...clause.Expression) *insExtStoreTicketDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insExtStoreTicketDo) Returning(value interface{}, columns ...string) *insExtStoreTicketDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insExtStoreTicketDo) Not(conds ...gen.Condition) *insExtStoreTicketDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insExtStoreTicketDo) Or(conds ...gen.Condition) *insExtStoreTicketDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insExtStoreTicketDo) Select(conds ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insExtStoreTicketDo) Where(conds ...gen.Condition) *insExtStoreTicketDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insExtStoreTicketDo) Order(conds ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insExtStoreTicketDo) Distinct(cols ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insExtStoreTicketDo) Omit(cols ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insExtStoreTicketDo) Join(table schema.Tabler, on ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insExtStoreTicketDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insExtStoreTicketDo) RightJoin(table schema.Tabler, on ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insExtStoreTicketDo) Group(cols ...field.Expr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insExtStoreTicketDo) Having(conds ...gen.Condition) *insExtStoreTicketDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insExtStoreTicketDo) Limit(limit int) *insExtStoreTicketDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insExtStoreTicketDo) Offset(offset int) *insExtStoreTicketDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insExtStoreTicketDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insExtStoreTicketDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insExtStoreTicketDo) Unscoped() *insExtStoreTicketDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insExtStoreTicketDo) Create(values ...*insbuy.InsExtStoreTicket) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insExtStoreTicketDo) CreateInBatches(values []*insbuy.InsExtStoreTicket, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insExtStoreTicketDo) Save(values ...*insbuy.InsExtStoreTicket) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insExtStoreTicketDo) First() (*insbuy.InsExtStoreTicket, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreTicket), nil
	}
}

func (i insExtStoreTicketDo) Take() (*insbuy.InsExtStoreTicket, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreTicket), nil
	}
}

func (i insExtStoreTicketDo) Last() (*insbuy.InsExtStoreTicket, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreTicket), nil
	}
}

func (i insExtStoreTicketDo) Find() ([]*insbuy.InsExtStoreTicket, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsExtStoreTicket), err
}

func (i insExtStoreTicketDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsExtStoreTicket, err error) {
	buf := make([]*insbuy.InsExtStoreTicket, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insExtStoreTicketDo) FindInBatches(result *[]*insbuy.InsExtStoreTicket, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insExtStoreTicketDo) Attrs(attrs ...field.AssignExpr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insExtStoreTicketDo) Assign(attrs ...field.AssignExpr) *insExtStoreTicketDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insExtStoreTicketDo) Joins(fields ...field.RelationField) *insExtStoreTicketDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insExtStoreTicketDo) Preload(fields ...field.RelationField) *insExtStoreTicketDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insExtStoreTicketDo) FirstOrInit() (*insbuy.InsExtStoreTicket, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreTicket), nil
	}
}

func (i insExtStoreTicketDo) FirstOrCreate() (*insbuy.InsExtStoreTicket, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsExtStoreTicket), nil
	}
}

func (i insExtStoreTicketDo) FindByPage(offset int, limit int) (result []*insbuy.InsExtStoreTicket, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insExtStoreTicketDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insExtStoreTicketDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insExtStoreTicketDo) Delete(models ...*insbuy.InsExtStoreTicket) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insExtStoreTicketDo) withDO(do gen.Dao) *insExtStoreTicketDo {
	i.DO = *do.(*gen.DO)
	return i
}
