# 飞书合同数据同步功能完整实现

## 实现概述

本文档详细说明了完整实现的飞书合同数据同步功能，包括多审批代码批量同步、分页处理、数据一致性保证和错误处理机制。

## 核心实现流程

### 1. 整体同步流程

```
SyncMultipleContractData (主入口)
    ├── validateMultiSyncRequest (参数验证)
    ├── createFeishuContractService (创建飞书服务)
    └── 遍历每个审批代码
        └── syncSingleApprovalCode (单个审批代码同步)
            ├── createSyncLog (创建同步日志)
            ├── 分页遍历
            │   └── processSinglePage (处理单页数据)
            │       ├── GetContractList (获取合同列表)
            │       ├── GetContractDetails (获取合同详情)
            │       └── saveContractDataBatch (批量保存数据)
            └── updateSyncLog (更新同步日志)
```

### 2. 单页数据处理流程

```mermaid
graph TD
    A[processSinglePage] --> B[调用飞书API获取合同列表]
    B --> C{API调用成功?}
    C -->|否| D[记录错误并返回]
    C -->|是| E[检查返回数据]
    E --> F{有合同数据?}
    F -->|否| G[返回空结果]
    F -->|是| H[批量获取合同详情]
    H --> I{详情获取成功?}
    I -->|部分失败| J[记录失败的合同]
    I -->|成功| K[批量保存到数据库]
    J --> K
    K --> L[返回处理结果]
```

## 关键方法实现

### 1. processSinglePage - 单页数据处理

```go
func (s *InsContractService) processSinglePage(
    ctx context.Context, 
    feishuService *insfinance.FeishuContractService, 
    approvalCode, pageToken string, 
    req request.ContractMultiSyncRequest
) response.ContractSyncPageResult {
    
    // 步骤1: 获取合同列表
    listReq := insfinance.ContractListRequest{
        ApprovalCode: approvalCode,
        StartTime:    *req.StartTime,
        EndTime:      *req.EndTime,
        PageSize:     req.PageSize,
        PageToken:    pageToken,
    }
    
    listResp, err := feishuService.GetContractList(ctx, listReq)
    if err != nil || !listResp.Success {
        // 错误处理
        return errorResult
    }
    
    // 步骤2: 批量获取合同详情
    detailReq := insfinance.ContractDetailRequest{
        InstanceCodes: listResp.InstanceCodeList,
    }
    
    detailResp, err := feishuService.GetContractDetails(ctx, detailReq)
    if err != nil {
        // 错误处理
        return errorResult
    }
    
    // 步骤3: 批量保存合同数据
    newCount, updateCount, failCount, saveErrors := s.saveContractDataBatch(detailResp.Contracts)
    
    // 步骤4: 返回处理结果
    return result
}
```

### 2. saveContractDataBatch - 批量数据保存

```go
func (s *InsContractService) saveContractDataBatch(
    contracts []insfinance.ContractDetails
) (newCount, updateCount, failCount int, errors []string) {
    
    // 使用事务确保数据一致性
    err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
        for _, contract := range contracts {
            // 转换数据模型
            dbContract, err := s.convertFeishuContractToModel(contract)
            if err != nil {
                failCount++
                continue
            }
            
            // 检查是否存在
            var existingContract insbuy.InsContract
            err = tx.Where("instance_code = ?", contract.InstanceCode).First(&existingContract).Error
            
            if err == gorm.ErrRecordNotFound {
                // 创建新记录
                err = tx.Create(dbContract).Error
                if err == nil {
                    s.saveContractRelatedDataInTx(tx, dbContract.ID, contract)
                    newCount++
                }
            } else if err == nil {
                // 更新现有记录
                updates := s.buildContractUpdateMap(dbContract)
                err = tx.Model(&existingContract).Updates(updates).Error
                if err == nil {
                    s.updateContractRelatedDataInTx(tx, existingContract.ID, contract)
                    updateCount++
                }
            }
            
            if err != nil {
                failCount++
                errors = append(errors, err.Error())
            }
        }
        return nil
    })
    
    return newCount, updateCount, failCount, errors
}
```

### 3. convertFeishuContractToModel - 数据转换

```go
func (s *InsContractService) convertFeishuContractToModel(
    contract insfinance.ContractDetails
) (*insbuy.InsContract, error) {
    
    // 时间解析
    var startTime, endTime *time.Time
    if contract.StartTime != "" {
        if t, err := s.parseFeishuTime(contract.StartTime); err == nil {
            startTime = &t
        }
    }
    
    // 表单数据解析
    var formData datatypes.JSON
    if contract.Form != "" {
        formData = datatypes.JSON(contract.Form)
    }
    
    // 创建数据库模型
    dbContract := &insbuy.InsContract{
        ApprovalCode: contract.ApprovalCode,
        ApprovalName: contract.ApprovalName,
        InstanceCode: contract.InstanceCode,
        // ... 其他字段映射
        Form: formData,
    }
    
    // 提取业务字段
    s.extractBusinessFields(dbContract, contract.Form)
    
    return dbContract, nil
}
```

## 数据一致性保证

### 1. 事务处理

```go
// 所有数据库操作都在事务中进行
err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
    // 主表操作
    err := tx.Create(dbContract).Error
    if err != nil {
        return err // 自动回滚
    }
    
    // 关联表操作
    err = s.saveContractRelatedDataInTx(tx, dbContract.ID, contract)
    if err != nil {
        return err // 自动回滚
    }
    
    return nil // 提交事务
})
```

### 2. 去重处理

```go
// 基于InstanceCode进行去重
var existingContract insbuy.InsContract
err = tx.Where("instance_code = ?", contract.InstanceCode).First(&existingContract).Error

if err == gorm.ErrRecordNotFound {
    // 新增记录
    err = tx.Create(dbContract).Error
} else if err == nil {
    // 更新现有记录
    updates := s.buildContractUpdateMap(dbContract)
    err = tx.Model(&existingContract).Updates(updates).Error
}
```

### 3. 关联数据处理

```go
// 更新时先删除旧的关联数据，再插入新数据
func (s *InsContractService) updateContractRelatedDataInTx(
    tx *gorm.DB, contractId uint, contract insfinance.ContractDetails
) error {
    // 删除现有关联数据
    tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractComment{})
    tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractTask{})
    tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractTimeline{})
    tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractFile{})
    
    // 重新保存关联数据
    return s.saveContractRelatedDataInTx(tx, contractId, contract)
}
```

## 错误处理机制

### 1. 分层错误处理

```go
// API层错误
if err != nil || !listResp.Success {
    errorMsg := fmt.Sprintf("获取合同列表失败: %v", err)
    result.ErrorMessages = append(result.ErrorMessages, errorMsg)
    return result
}

// 数据转换错误
dbContract, err := s.convertFeishuContractToModel(contract)
if err != nil {
    errorMsg := fmt.Sprintf("转换合同数据失败 [%s]: %v", contract.InstanceCode, err)
    errors = append(errors, errorMsg)
    failCount++
    continue // 继续处理其他记录
}

// 数据库操作错误
err = tx.Create(dbContract).Error
if err != nil {
    errorMsg := fmt.Sprintf("创建合同失败 [%s]: %v", contract.InstanceCode, err)
    errors = append(errors, errorMsg)
    failCount++
    continue
}
```

### 2. 部分失败处理

```go
// 单个记录失败不影响其他记录
for _, contract := range contracts {
    // 处理单个合同
    if err != nil {
        failCount++
        errors = append(errors, err.Error())
        continue // 继续处理下一个
    }
    successCount++
}

// 返回详细的处理结果
return newCount, updateCount, failCount, errors
```

## 性能优化

### 1. 批量操作

```go
// 批量获取合同详情而不是逐个获取
detailReq := insfinance.ContractDetailRequest{
    InstanceCodes: listResp.InstanceCodeList, // 一次获取多个
}

// 批量保存而不是逐个保存
func (s *InsContractService) saveContractDataBatch(contracts []insfinance.ContractDetails)
```

### 2. API限流控制

```go
// 页面间延迟
time.Sleep(200 * time.Millisecond)

// 审批代码间延迟
if i < len(req.ApprovalCodes)-1 {
    time.Sleep(time.Duration(req.RetryDelay) * time.Second)
}

// 批量获取详情时的延迟
time.Sleep(100 * time.Millisecond)
```

### 3. 内存优化

```go
// 分页处理避免一次性加载大量数据
for {
    pageResult := s.processSinglePage(ctx, feishuService, approvalCode, pageToken, req)
    
    // 处理当前页数据
    // ...
    
    if !pageResult.HasMore {
        break // 没有更多数据时退出
    }
    
    pageToken = pageResult.NextPageToken
}
```

## 监控和日志

### 1. 结构化日志

```go
global.GVA_LOG.Info("开始同步审批代码", 
    zap.String("approval_code", approvalCode),
    zap.Int("index", i+1),
    zap.Int("total", len(req.ApprovalCodes)),
)

global.GVA_LOG.Info("分页处理完成", 
    zap.String("approval_code", approvalCode),
    zap.Int("page_num", pageNum),
    zap.Int("record_count", pageResult.RecordCount),
    zap.Int("success_count", pageResult.SuccessCount),
    zap.Int("failed_count", pageResult.FailedCount),
    zap.Duration("page_duration", time.Since(pageStartTime)),
)
```

### 2. 同步日志记录

```go
// 创建同步日志
syncLogId, err := s.createSyncLog(approvalCode, "multi_sync")

// 更新同步日志
s.updateSyncLog(syncLogId, status, result.TotalRecords, result.ErrorMsg)
```

## 使用示例

### 1. 基本使用

```go
contractService := &insbuy.InsContractService{}

req := request.ContractMultiSyncRequest{
    ApprovalCodes: []string{
        "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
        "A123B456-7890-1234-5678-90ABCDEF1234",
    },
    BatchSize:  20,
    PageSize:   100,
    MaxRetries: 3,
    RetryDelay: 5,
}

resp, err := contractService.SyncMultipleContractData(req)
if err != nil {
    log.Printf("同步失败: %v", err)
    return
}

fmt.Printf("同步完成: 成功 %d/%d 个审批代码，处理 %d 条记录\n", 
    resp.SuccessCodes, resp.TotalCodes, resp.TotalRecords)
```

### 2. 错误处理

```go
if !resp.Success {
    fmt.Printf("同步部分失败: %s\n", resp.ErrorMsg)
    
    for _, result := range resp.Results {
        if !result.Success {
            fmt.Printf("审批代码 %s 失败: %s\n", result.ApprovalCode, result.ErrorMsg)
        }
    }
}
```

## 测试

项目包含完整的单元测试：

- `TestProcessSinglePage` - 测试单页处理逻辑
- `TestConvertFeishuContractToModel` - 测试数据转换
- `TestBuildContractUpdateMap` - 测试更新字段构建
- `TestValidateMultiSyncRequest` - 测试参数验证

运行测试：
```bash
go test ./server/service/insbuy -v
```

## 总结

完整实现的飞书合同数据同步功能具备：

✅ **完整的数据流程**: 从飞书API获取到数据库保存的完整链路
✅ **数据一致性**: 事务处理、去重机制、关联数据同步
✅ **错误处理**: 分层错误处理、部分失败恢复、详细错误信息
✅ **性能优化**: 批量操作、API限流、内存优化
✅ **监控日志**: 结构化日志、同步状态跟踪
✅ **测试覆盖**: 完整的单元测试和集成测试

该实现为企业级飞书合同数据管理提供了可靠、高效的数据同步解决方案。
