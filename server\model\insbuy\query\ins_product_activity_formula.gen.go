// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductActivityFormula(db *gorm.DB, opts ...gen.DOOption) insProductActivityFormula {
	_insProductActivityFormula := insProductActivityFormula{}

	_insProductActivityFormula.insProductActivityFormulaDo.UseDB(db, opts...)
	_insProductActivityFormula.insProductActivityFormulaDo.UseModel(&insbuy.InsProductActivityFormula{})

	tableName := _insProductActivityFormula.insProductActivityFormulaDo.TableName()
	_insProductActivityFormula.ALL = field.NewAsterisk(tableName)
	_insProductActivityFormula.ID = field.NewUint(tableName, "id")
	_insProductActivityFormula.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductActivityFormula.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductActivityFormula.Name = field.NewString(tableName, "name")
	_insProductActivityFormula.Formula = field.NewString(tableName, "formula")

	_insProductActivityFormula.fillFieldMap()

	return _insProductActivityFormula
}

type insProductActivityFormula struct {
	insProductActivityFormulaDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	Name      field.String
	Formula   field.String

	fieldMap map[string]field.Expr
}

func (i insProductActivityFormula) Table(newTableName string) *insProductActivityFormula {
	i.insProductActivityFormulaDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductActivityFormula) As(alias string) *insProductActivityFormula {
	i.insProductActivityFormulaDo.DO = *(i.insProductActivityFormulaDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductActivityFormula) updateTableName(table string) *insProductActivityFormula {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.Name = field.NewString(table, "name")
	i.Formula = field.NewString(table, "formula")

	i.fillFieldMap()

	return i
}

func (i *insProductActivityFormula) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductActivityFormula) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 5)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["name"] = i.Name
	i.fieldMap["formula"] = i.Formula
}

func (i insProductActivityFormula) clone(db *gorm.DB) insProductActivityFormula {
	i.insProductActivityFormulaDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductActivityFormula) replaceDB(db *gorm.DB) insProductActivityFormula {
	i.insProductActivityFormulaDo.ReplaceDB(db)
	return i
}

type insProductActivityFormulaDo struct{ gen.DO }

func (i insProductActivityFormulaDo) Debug() *insProductActivityFormulaDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductActivityFormulaDo) WithContext(ctx context.Context) *insProductActivityFormulaDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductActivityFormulaDo) ReadDB() *insProductActivityFormulaDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductActivityFormulaDo) WriteDB() *insProductActivityFormulaDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductActivityFormulaDo) Session(config *gorm.Session) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductActivityFormulaDo) Clauses(conds ...clause.Expression) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductActivityFormulaDo) Returning(value interface{}, columns ...string) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductActivityFormulaDo) Not(conds ...gen.Condition) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductActivityFormulaDo) Or(conds ...gen.Condition) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductActivityFormulaDo) Select(conds ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductActivityFormulaDo) Where(conds ...gen.Condition) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductActivityFormulaDo) Order(conds ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductActivityFormulaDo) Distinct(cols ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductActivityFormulaDo) Omit(cols ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductActivityFormulaDo) Join(table schema.Tabler, on ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductActivityFormulaDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductActivityFormulaDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductActivityFormulaDo) Group(cols ...field.Expr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductActivityFormulaDo) Having(conds ...gen.Condition) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductActivityFormulaDo) Limit(limit int) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductActivityFormulaDo) Offset(offset int) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductActivityFormulaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductActivityFormulaDo) Unscoped() *insProductActivityFormulaDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductActivityFormulaDo) Create(values ...*insbuy.InsProductActivityFormula) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductActivityFormulaDo) CreateInBatches(values []*insbuy.InsProductActivityFormula, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductActivityFormulaDo) Save(values ...*insbuy.InsProductActivityFormula) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductActivityFormulaDo) First() (*insbuy.InsProductActivityFormula, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityFormula), nil
	}
}

func (i insProductActivityFormulaDo) Take() (*insbuy.InsProductActivityFormula, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityFormula), nil
	}
}

func (i insProductActivityFormulaDo) Last() (*insbuy.InsProductActivityFormula, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityFormula), nil
	}
}

func (i insProductActivityFormulaDo) Find() ([]*insbuy.InsProductActivityFormula, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductActivityFormula), err
}

func (i insProductActivityFormulaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductActivityFormula, err error) {
	buf := make([]*insbuy.InsProductActivityFormula, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductActivityFormulaDo) FindInBatches(result *[]*insbuy.InsProductActivityFormula, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductActivityFormulaDo) Attrs(attrs ...field.AssignExpr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductActivityFormulaDo) Assign(attrs ...field.AssignExpr) *insProductActivityFormulaDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductActivityFormulaDo) Joins(fields ...field.RelationField) *insProductActivityFormulaDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductActivityFormulaDo) Preload(fields ...field.RelationField) *insProductActivityFormulaDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductActivityFormulaDo) FirstOrInit() (*insbuy.InsProductActivityFormula, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityFormula), nil
	}
}

func (i insProductActivityFormulaDo) FirstOrCreate() (*insbuy.InsProductActivityFormula, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductActivityFormula), nil
	}
}

func (i insProductActivityFormulaDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductActivityFormula, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductActivityFormulaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductActivityFormulaDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductActivityFormulaDo) Delete(models ...*insbuy.InsProductActivityFormula) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductActivityFormulaDo) withDO(do gen.Dao) *insProductActivityFormulaDo {
	i.DO = *do.(*gen.DO)
	return i
}
