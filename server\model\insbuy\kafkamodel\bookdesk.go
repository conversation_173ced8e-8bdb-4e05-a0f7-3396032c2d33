package kafkamodel

import (
	"encoding/json"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"time"
)

// 预约卡座的活动

// 基础属性
type BookDeskBase struct {
	EventName string `json:"event_name"` // 事件名称
	EventTime int64  `json:"event_time"` // 事件时间
}

// 店铺属性
type BookDeskStoreInfo struct {
	StoreId      string `json:"store_id"`       // 店铺 ID
	StoreName    string `json:"store_name"`     // 店铺名称
	InsStoreCode string `json:"ins_store_code"` // INS系统内店铺唯一id
}

// 卡座属性
type BookDeskDeskInfo struct {
	DeskId     string `json:"desk_id"`      // 卡座 id
	FromDeskId string `json:"from_desk_id"` //来源桌台id-只有转台时有
	DeskName   string `json:"desk_name"`    // 卡座名称
	DeskOpenConsume
}

type BookDeskDeskInfoEx struct {
	BookDeskDeskInfo
	Capacity   string `json:"capacity"`    // 容纳人数
	LatestTime string `json:"latest_time"` // 最晚到店时间
}

// BookDeskCfgDeskMessage 桌台配置事件，包括上架、下架
//
//	唯一键 store_id + book_date
type BookDeskCfgDeskMessage struct {
	BookDeskBase
	BookDate string `json:"book_date"` // 预约日期, 即到店日期，格式 yyyy-MM-dd
	BookDeskStoreInfo
	BeginTime string `json:"begin_time"` // 可预约的开始时间
	EndTime   string `json:"end_time"`   // 可预约的结束时间

	DeskList []BookDeskDeskInfoEx `json:"desk_list"` // 卡座清单
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

type BookDeskPackageProduct struct {
	ProductId   string `json:"product_id"`   // 商品 id
	ProductName string `json:"product_name"` // 商品名称
	Price       int    `json:"price"`        // 市场价
	Currency    string `json:"currency"`     // 币种，默认是 CNY
	Quantity    int    `json:"quantity"`     // 数量
	Unit        string `json:"unit"`         // 单位
	//IfGive      int    `json:"if_give"`      // 是否赠送 0不赠送 1赠送，仅在可选商品组里有效
}

type BookDeskPackageProductOptional struct {
	ItemName         string `json:"item_name"`         // 选项名称
	OptionalName     string `json:"optional_name"`     // 选项名称
	OptionalQuantity int    `json:"optional_quantity"` // 可选数量，如 4选2 中的 2

	ProductList []BookDeskPackageProduct `json:"product_list"` // 商品清单
}

type BookDeskPackageInfo struct {
	PackageId     string `json:"package_id"`   // 套餐 id
	PackageName   string `json:"package_name"` // 套餐名称
	Price         int    `json:"price"`        // 价格
	Currency      string `json:"currency"`     // 币种
	Desc          string `json:"desc"`         // 描述 可选
	RefundEnabled int    `json:"refund"`       // 是否支持退款 0不允许 1允许

	PackageData string `json:"package_data"` // 套餐原始数据，json格式

	ProductList  []BookDeskPackageProduct         `json:"product_list"`  // 商品清单
	OptionalList []BookDeskPackageProductOptional `json:"optional_list"` // 选项清单

	// 2024-03-12 新增需求 https://gvdvfqwnqm.feishu.cn/docx/UXQndK4B3oA3KRxO9WYcKnmynEd
	CoverUrl      string                   `json:"cover_url"`      // 套餐头图地址，正方形
	OriginPrice   int                      `json:"origin_price"`   // 原价，可选
	Notice        string                   `json:"notice"`         // 购买须知，必须
	PromotionMsg  string                   `json:"promotion_msg"`  // 优惠文案，展示在套餐列表，一句话，可选
	SpecialMsg    string                   `json:"special_msg"`    // 特殊说明，第一行是标题，支持多行文本，可选
	PackageImages []string                 `json:"package_images"` // 套餐图片地址，可选
	ExtraList     []BookDeskPackageProduct `json:"extra_list"`     // 额外赠送商品清单
}

type BookDeskPackageProductMinify struct {
	ProductId   string `json:"a,omitempty"`
	ProductName string `json:"b,omitempty"`
	Price       int    `json:"c,omitempty"`
	Currency    string `json:"d,omitempty"`
	Quantity    int    `json:"e,omitempty"`
	Unit        string `json:"f,omitempty"`
	//IfGive      int    `json:"g,omitempty"`
}
type BookDeskPackageProductOptionalMinify struct {
	ItemName         string `json:"a,omitempty"`
	OptionalName     string `json:"b,omitempty"`
	OptionalQuantity int    `json:"c,omitempty"`

	ProductList []BookDeskPackageProductMinify `json:"d,omitempty"`
}
type BookDeskPackageInfoMinify struct {
	PackageId     string `json:"a,omitempty"` // 套餐 id
	PackageName   string `json:"b,omitempty"` // 套餐名称j
	Price         int    `json:"c,omitempty"`
	Currency      string `json:"d,omitempty"`
	RefundEnabled int    `json:"e,omitempty"`

	ProductList  []BookDeskPackageProductMinify         `json:"f,omitempty"`
	OptionalList []BookDeskPackageProductOptionalMinify `json:"g,omitempty"`
}

// BookDeskCfgPackageMessage 卡座关联的套餐配置事件，
//
//	唯一键 store_id + book_date + desk_id
type BookDeskCfgPackageMessage struct {
	BookDeskBase
	BookDate string `json:"book_date"` // 预约日期, 即到店日期，格式 yyyy-MM-dd
	BookDeskStoreInfo
	BookDeskDeskInfo

	PackageList []BookDeskPackageInfo `json:"package_list"` // 套餐清单
}

// -o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-o-

type BookDeskContactInfo struct {
	ContactPhone string `json:"contact_phone"` // 联系人手机号
	ContactName  string `json:"contact_name"`  // 联系人姓名
}

type BookDeskActBook struct {
	BookDeskBase
	BookDeskStoreInfo
	BookDeskDeskInfo // 卡座属性｛店铺id、店铺名称、卡座id、卡座名称｝
	BookDeskContactInfo

	TraceId string `json:"trace_id"` // 业务流水号（唯一预约号）

}

type BookDeskSampleMessage struct {
	BookDeskBase
	Data interface{} `json:"-"`
	Ext  interface{} `json:"-"`
}

func kitStruct2JSONRaw(v interface{}) (map[string]json.RawMessage, error) {
	if v == nil {
		return nil, nil
	}
	b1, e1 := json.Marshal(v)
	if e1 != nil {
		return nil, e1
	}
	var m1 map[string]json.RawMessage
	e1 = json.Unmarshal(b1, &m1)
	return m1, e1
}

func (p BookDeskSampleMessage) MarshalJSON() ([]byte, error) {
	type P_ BookDeskSampleMessage
	if p.Data == nil && p.Ext == nil {
		return json.Marshal(P_(p))
	}
	m, err := kitStruct2JSONRaw(P_(p))
	if err != nil {
		return nil, err
	}
	if m1, e1 := kitStruct2JSONRaw(p.Data); e1 != nil {
		return nil, e1
	} else if m1 != nil {
		for k, v := range m1 {
			m[k] = v
		}
	}
	if m1, e1 := kitStruct2JSONRaw(p.Ext); e1 != nil {
		return nil, e1
	} else if m1 != nil {
		for k, v := range m1 {
			m[k] = v
		}
	}
	return json.Marshal(m)
}

func NewSampleMessage(eventName string, data, ext interface{}) BookDeskSampleMessage {
	return BookDeskSampleMessage{
		BookDeskBase: BookDeskBase{
			EventName: eventName,
			EventTime: time.Now().Unix(),
		},
		Data: data,
		Ext:  ext,
	}
}

/**

事件流水号、事件时间、业务流水号（唯一预约号）、会员手机号、动作｛预约/取消/核销/变更/退款/结账｝、会员名称、卡座属性｛店铺id、店铺名称、卡座id、卡座名称｝、联系人属性｛手机号、姓名｝、事件数据｛预约套餐等｝、预约支付金额、预估流水（若退款则为0）
*/

type BookDeskEventBase struct {
	BookDeskStoreInfo
	BookDeskDeskInfo // 卡座属性｛店铺id、店铺名称、卡座id、卡座名称｝
	BookDeskContactInfo

	BookId     string           `json:"book_id"`     // 业务流水号（唯一预约号）
	Phone      string           `json:"phone"`       // 会员手机号
	MemberName string           `json:"member_name"` // 会员名称
	CpOrder    string           `json:"cp_order"`    // 预约支付订单号
	BookDate   jtypes.JDate     `json:"book_date"`   // 预约日期
	BookAt     jtypes.JDateTime `json:"book_at"`     // 预约操作时间
	PackageId  string           `json:"package_id"`  // 套餐 id

	Status insbuy.TActBookDeskStatus `json:"status"` // 预约状态

	RecordId      uint            `json:"record_id,omitempty"`       // 预约记录主键 id（可选）
	ActId         uint            `json:"act_id,omitempty"`          // 预约配置主键 id（可选）
	ActStatus     insbuy.TypeStat `json:"act_status,omitempty"`      // 预约配置状态（可选）
	PackagePrice  int             `json:"package_price,omitempty"`   // 预付套餐价格，单位 分（可选）
	CpOrderAmount int             `json:"cp_order_amount,omitempty"` // 预付订单的价值 退款时清0、取消预约时保留（可选）
	PackageData   string          `json:"package_data,omitempty"`    // 套餐原始数据（可选）
	Remark        string          `json:"remark,omitempty"`          // 预约备注（可选）
	MemberId      uint            `json:"member_id,omitempty"`       // 会员ID（可选）
	PackageName   string          `json:"package_name,omitempty"`    // 套餐名称（可选）

	// 桌台状态、
}

type BookDeskEventMultiBase struct {
	BookDeskStoreInfo
	BookDeskDeskInfo                    // 卡座属性｛店铺id、店铺名称、卡座id、卡座名称｝兼容1.0版本只有一个桌台 暂时保留
	DeskInfos        []BookDeskDeskInfo `json:"desk_infos"` // 卡座属性｛店铺id、店铺名称、卡座id、卡座名称｝v2版本多桌台
	BookDeskContactInfo

	BookId     string           `json:"book_id"`     // 业务流水号（唯一预约号）
	Phone      string           `json:"phone"`       // 会员手机号
	MemberName string           `json:"member_name"` // 会员名称
	CpOrder    string           `json:"cp_order"`    // 预约支付订单号
	BookDate   jtypes.JDate     `json:"book_date"`   // 预约日期
	BookAt     jtypes.JDateTime `json:"book_at"`     // 预约操作时间
	PackageId  string           `json:"package_id"`  // 套餐 id

	Status insbuy.TActBookDeskStatus `json:"status"` // 预约状态

	RecordId      uint            `json:"record_id,omitempty"`       // 预约记录主键 id（可选）
	ActId         uint            `json:"act_id,omitempty"`          // 预约配置主键 id（可选）
	ActStatus     insbuy.TypeStat `json:"act_status,omitempty"`      // 预约配置状态（可选）
	PackagePrice  int             `json:"package_price,omitempty"`   // 预付套餐价格，单位 分（可选）
	CpOrderAmount int             `json:"cp_order_amount,omitempty"` // 预付订单的价值 退款时清0、取消预约时保留（可选）
	PackageData   string          `json:"package_data,omitempty"`    // 套餐原始数据（可选）
	Remark        string          `json:"remark,omitempty"`          // 预约备注（可选）
	MemberId      uint            `json:"member_id,omitempty"`       // 会员ID（可选）
	PackageName   string          `json:"package_name,omitempty"`    // 套餐名称（可选）

	// 桌台状态、
}
type DeskOpenConsume struct {
	OtherExpenses   int64 `json:"other_expenses,omitempty"`   // 其它消费
	OfflineExpenses int64 `json:"offline_expenses,omitempty"` // 线下消费
	//DiscountAmount  int64 `json:"discount_amount,omitempty"`  //优惠金额
}

// 桌台事件: 开台、关台、换台、预约、取消预约、核销、变更、退款、结账
type DeskOpenEventBase struct {
	BookDeskStoreInfo
	BookDeskDeskInfo

	RecordId  uint            `json:"record_id,omitempty"`  // 预约记录主键 id（可选）
	ActId     uint            `json:"act_id,omitempty"`     // 预约配置主键 id（可选）
	ActStatus insbuy.TypeStat `json:"act_status,omitempty"` // 预约配置状态（可选）

	RecordStatus insbuy.TActBookDeskStatus `json:"record_status,omitempty"` // 预约记录状态（可选）

	MemberId    uint   `json:"member_id,omitempty"`    // 会员ID（可选）
	MemberName  string `json:"member_name,omitempty"`  // 会员名称（可选）
	MemberPhone string `json:"member_phone,omitempty"` // 会员手机号（可选）

	BookId   string       `json:"book_id"`          // 业务流水号（唯一预约号）
	CpOrder  string       `json:"cp_order"`         // 预约支付订单号
	BookDate jtypes.JDate `json:"book_date"`        // 预约日期
	Phone    string       `json:"phone"`            // 会员手机号
	Remark   string       `json:"remark,omitempty"` // 预约备注（可选）
}

// DeskEventBase 桌台事件基础信息
type DeskEventBase struct {
	BookDeskBase
	BookDeskStoreInfo
	DeskInfo []DeskInfo `json:"desk_info"`
}

type DeskInfo struct {
	DeskId    uint   `json:"desk_id"`   //桌台id
	DeskName  string `json:"desk_name"` //桌台名称
	DeskCode  string `json:"desk_code"` //桌台code
	CateName  string `json:"cate_name"` //分类名称
	AreaName  string `json:"area_name"` //区域名称
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
	DeletedAt string `json:"deleted_at"`
	Source    int    `json:"source"` //添加来源
	Ext       string `json:"ext"`
	IsSync    int    `json:"is_sync"`
}
