# 实际使用示例 - ID引用功能完整指南

## 完整的业务场景示例

### 场景：某公司2025年第一季度财务报表计算

#### 1. 基础数据配置
```json
{
  "基础分类配置": [
    {"id": 1, "name": "营业收入", "code": "REVENUE", "is_calculated": false},
    {"id": 2, "name": "营业成本", "code": "COST", "is_calculated": false},
    {"id": 3, "name": "销售费用", "code": "SALES_EXPENSE", "is_calculated": false},
    {"id": 4, "name": "管理费用", "code": "ADMIN_EXPENSE", "is_calculated": false},
    {"id": 5, "name": "财务费用", "code": "FINANCE_EXPENSE", "is_calculated": false}
  ]
}
```

#### 2. 实际财务数据（单位：万元）
```json
{
  "财务数据": {
    "营业收入": {"2025-01": 1000, "2025-02": 1200, "2025-03": 1100, "总计": 3300},
    "营业成本": {"2025-01": 600, "2025-02": 720, "2025-03": 660, "总计": 1980},
    "销售费用": {"2025-01": 100, "2025-02": 120, "2025-03": 110, "总计": 330},
    "管理费用": {"2025-01": 80, "2025-02": 96, "2025-03": 88, "总计": 264},
    "财务费用": {"2025-01": 20, "2025-02": 24, "2025-03": 22, "总计": 66}
  }
}
```

#### 3. 计算型分类配置（使用ID引用）

##### 毛利润计算
```json
{
  "id": 100,
  "category_name": "毛利润",
  "category_code": "GROSS_PROFIT",
  "is_calculated": true,
  "sort_order": 300,
  "calculation_formula": "{\"expression\":\"#1 - #2\",\"references\":[1,2],\"description\":\"毛利润 = 营业收入 - 营业成本\"}"
}
```

**计算结果：**
- 2025-01: 1000 - 600 = 400万
- 2025-02: 1200 - 720 = 480万  
- 2025-03: 1100 - 660 = 440万
- 总计: 3300 - 1980 = 1320万

##### 毛利率计算
```json
{
  "id": 101,
  "category_name": "毛利率",
  "category_code": "GROSS_MARGIN",
  "is_calculated": true,
  "sort_order": 350,
  "calculation_formula": "{\"expression\":\"#100 / #1 %\",\"references\":[100,1],\"description\":\"毛利率 = 毛利润 / 营业收入 × 100%\"}"
}
```

**计算结果：**
- 2025-01: 400/1000 × 100% = 40%
- 2025-02: 480/1200 × 100% = 40%
- 2025-03: 440/1100 × 100% = 40%
- 总计: 1320/3300 × 100% = 40%

##### 期间费用合计
```json
{
  "id": 102,
  "category_name": "期间费用合计",
  "category_code": "PERIOD_EXPENSE_TOTAL",
  "is_calculated": true,
  "sort_order": 700,
  "calculation_formula": "{\"expression\":\"#3 + #4 + #5\",\"references\":[3,4,5],\"description\":\"期间费用合计 = 销售费用 + 管理费用 + 财务费用\"}"
}
```

**计算结果：**
- 2025-01: 100 + 80 + 20 = 200万
- 2025-02: 120 + 96 + 24 = 240万
- 2025-03: 110 + 88 + 22 = 220万
- 总计: 330 + 264 + 66 = 660万

##### 营业利润计算
```json
{
  "id": 103,
  "category_name": "营业利润",
  "category_code": "OPERATING_PROFIT",
  "is_calculated": true,
  "sort_order": 800,
  "calculation_formula": "{\"expression\":\"#100 - #102\",\"references\":[100,102],\"description\":\"营业利润 = 毛利润 - 期间费用合计\"}"
}
```

**计算结果：**
- 2025-01: 400 - 200 = 200万
- 2025-02: 480 - 240 = 240万
- 2025-03: 440 - 220 = 220万
- 总计: 1320 - 660 = 660万

##### 营业利润率计算
```json
{
  "id": 104,
  "category_name": "营业利润率",
  "category_code": "OPERATING_MARGIN",
  "is_calculated": true,
  "sort_order": 850,
  "calculation_formula": "{\"expression\":\"#103 / #1 %\",\"references\":[103,1],\"description\":\"营业利润率 = 营业利润 / 营业收入 × 100%\"}"
}
```

**计算结果：**
- 2025-01: 200/1000 × 100% = 20%
- 2025-02: 240/1200 × 100% = 20%
- 2025-03: 220/1100 × 100% = 20%
- 总计: 660/3300 × 100% = 20%

## 代码实现示例

### Go后端实现
```go
package main

import (
    "fmt"
    "log"
    "github.com/your-project/insreport"
)

func main() {
    // 1. 获取配置数据
    configs := insreport.GetTestCostTypeConfigs()
    
    // 2. 创建处理器
    processor := insreport.NewSimpleJSONProcessor(configs)
    
    // 3. 获取基础财务数据
    items := insreport.GetTestFinancialSummaryItems()
    timeColumns := []string{"2025-01", "2025-02", "2025-03"}
    
    // 4. 处理计算型分类
    result := processor.ProcessCalculatedCategoriesJSON(items, configs, timeColumns)
    
    // 5. 输出结果
    fmt.Println("=== 财务报表计算结果 ===")
    for _, item := range result {
        fmt.Printf("分类: %s (ID: %d)\n", item.CategoryName, item.CategoryId)
        fmt.Printf("  2025-01: %.2f万\n", float64(item.MonthlyAmounts["2025-01"])/10000)
        fmt.Printf("  2025-02: %.2f万\n", float64(item.MonthlyAmounts["2025-02"])/10000)
        fmt.Printf("  2025-03: %.2f万\n", float64(item.MonthlyAmounts["2025-03"])/10000)
        fmt.Printf("  总计: %.2f万\n", float64(item.TotalAmount)/10000)
        fmt.Println()
    }
}
```

### 前端React组件
```typescript
import React, { useState, useEffect } from 'react';

interface FinancialData {
  categoryId: number;
  categoryName: string;
  monthlyAmounts: Record<string, number>;
  totalAmount: number;
}

const FinancialReport: React.FC = () => {
  const [data, setData] = useState<FinancialData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchFinancialData();
  }, []);

  const fetchFinancialData = async () => {
    try {
      const response = await fetch('/api/financial/calculate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timeColumns: ['2025-01', '2025-02', '2025-03'],
          includeCalculated: true
        })
      });
      
      const result = await response.json();
      setData(result.data);
    } catch (error) {
      console.error('获取财务数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (amount: number) => {
    return (amount / 10000).toFixed(2) + '万';
  };

  const formatPercentage = (amount: number) => {
    return (amount / 100).toFixed(2) + '%';
  };

  if (loading) return <div>加载中...</div>;

  return (
    <div className="financial-report">
      <h2>2025年第一季度财务报表</h2>
      <table className="report-table">
        <thead>
          <tr>
            <th>项目</th>
            <th>2025-01</th>
            <th>2025-02</th>
            <th>2025-03</th>
            <th>总计</th>
          </tr>
        </thead>
        <tbody>
          {data.map(item => (
            <tr key={item.categoryId}>
              <td>{item.categoryName}</td>
              <td>
                {item.categoryName.includes('率') 
                  ? formatPercentage(item.monthlyAmounts['2025-01'])
                  : formatAmount(item.monthlyAmounts['2025-01'])
                }
              </td>
              <td>
                {item.categoryName.includes('率')
                  ? formatPercentage(item.monthlyAmounts['2025-02'])
                  : formatAmount(item.monthlyAmounts['2025-02'])
                }
              </td>
              <td>
                {item.categoryName.includes('率')
                  ? formatPercentage(item.monthlyAmounts['2025-03'])
                  : formatAmount(item.monthlyAmounts['2025-03'])
                }
              </td>
              <td>
                {item.categoryName.includes('率')
                  ? formatPercentage(item.totalAmount)
                  : formatAmount(item.totalAmount)
                }
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default FinancialReport;
```

## API接口示例

### 计算财务报表接口
```json
{
  "接口": "POST /api/financial/calculate",
  "请求参数": {
    "timeColumns": ["2025-01", "2025-02", "2025-03"],
    "includeCalculated": true,
    "formulaType": "id_reference"
  },
  "响应数据": {
    "code": 200,
    "message": "计算成功",
    "data": [
      {
        "categoryId": 1,
        "categoryName": "营业收入",
        "categoryCode": "REVENUE",
        "monthlyAmounts": {
          "2025-01": 100000000,
          "2025-02": 120000000,
          "2025-03": 110000000
        },
        "totalAmount": 330000000,
        "isCalculated": false
      },
      {
        "categoryId": 100,
        "categoryName": "毛利润",
        "categoryCode": "GROSS_PROFIT",
        "monthlyAmounts": {
          "2025-01": 40000000,
          "2025-02": 48000000,
          "2025-03": 44000000
        },
        "totalAmount": 132000000,
        "isCalculated": true,
        "formula": "#1 - #2"
      },
      {
        "categoryId": 101,
        "categoryName": "毛利率",
        "categoryCode": "GROSS_MARGIN",
        "monthlyAmounts": {
          "2025-01": 4000,
          "2025-02": 4000,
          "2025-03": 4000
        },
        "totalAmount": 4000,
        "isCalculated": true,
        "formula": "#100 / #1 %"
      }
    ]
  }
}
```

### 公式验证接口
```json
{
  "接口": "POST /api/formula/validate",
  "请求参数": {
    "expression": "#1 - #2",
    "description": "毛利润计算"
  },
  "响应数据": {
    "code": 200,
    "message": "验证成功",
    "data": {
      "isValid": true,
      "formulaJson": "{\"expression\":\"#1 - #2\",\"references\":[1,2],\"description\":\"毛利润计算\"}",
      "referencedCategories": [
        {"id": 1, "name": "营业收入"},
        {"id": 2, "name": "营业成本"}
      ],
      "errors": []
    }
  }
}
```

## 最佳实践建议

### 1. 公式设计原则
- **简洁性**：优先使用ID引用，表达式更简洁
- **可读性**：在用户界面显示时转换为分类名称
- **性能**：复杂计算使用展开表达式避免多层依赖

### 2. 错误处理策略
```go
// 验证公式有效性
errors := processor.ValidateFormulaJSON(formulaJSON)
if len(errors) > 0 {
    for _, err := range errors {
        log.Printf("公式验证失败: %s - %s", err.Type, err.Message)
    }
    return nil, fmt.Errorf("公式验证失败")
}
```

### 3. 性能优化建议
- 使用ID引用减少字符串匹配
- 缓存计算结果避免重复计算
- 合理设计依赖关系避免循环依赖

这个完整的示例展示了ID引用功能在实际业务场景中的应用，包括配置、计算、展示和API交互的全流程。
