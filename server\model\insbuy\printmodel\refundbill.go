package printmodel

// TplRefundBill 退单
type TplRefundBill struct {
	TplBase
	DeskArea      string // 桌台区域
	DeskName      string // 桌台名称
	Waiter        string // 退单员
	OpenSn        string // 开台号
	ReturnSn      string // 退单号
	ReturnTime    string // 退单时间
	ShipmentSn    string // 出品单号
	OrderingClerk string //点单员

	Items []TplRefundBillItem // 商品列表

	TotalNum    int     // 合计数量
	TotalAmount float64 // 合计金额
	// 优惠金额
	DiscountAmount float64
	ReturnAmount   float64                //退款金额
	Payments       []TplRefundBillPayment // 付款方式
	Remark         string
}

type TplRefundBillPayment struct {
	Name    string  // 支付方式
	VipName string  // 会员名称，可选
	Account string  // 会员账号，可选
	Amount  float64 // 金额
}

type TplRefundBillItem struct {
	WarehouseId    int     //出品仓库
	OrderDetailsId uint64  //订单详情id
	ProductId      uint    //商品id
	Name           string  // 商品名称
	Remark         string  // 备注（可选）
	Num            int     // 规格
	OrderNum       int     //订单数量
	Unit           string  // 套
	Price          float64 // 单价
	Total          float64 // 售价
}
