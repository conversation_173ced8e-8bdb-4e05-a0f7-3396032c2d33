// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductStall(db *gorm.DB, opts ...gen.DOOption) insProductStall {
	_insProductStall := insProductStall{}

	_insProductStall.insProductStallDo.UseDB(db, opts...)
	_insProductStall.insProductStallDo.UseModel(&insbuy.InsProductStall{})

	tableName := _insProductStall.insProductStallDo.TableName()
	_insProductStall.ALL = field.NewAsterisk(tableName)
	_insProductStall.ID = field.NewUint(tableName, "id")
	_insProductStall.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductStall.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductStall.ProductId = field.NewUint(tableName, "product_id")
	_insProductStall.StallId = field.NewUint(tableName, "stall_id")
	_insProductStall.CategoryId = field.NewUint(tableName, "category_id")

	_insProductStall.fillFieldMap()

	return _insProductStall
}

type insProductStall struct {
	insProductStallDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	ProductId  field.Uint
	StallId    field.Uint
	CategoryId field.Uint

	fieldMap map[string]field.Expr
}

func (i insProductStall) Table(newTableName string) *insProductStall {
	i.insProductStallDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductStall) As(alias string) *insProductStall {
	i.insProductStallDo.DO = *(i.insProductStallDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductStall) updateTableName(table string) *insProductStall {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ProductId = field.NewUint(table, "product_id")
	i.StallId = field.NewUint(table, "stall_id")
	i.CategoryId = field.NewUint(table, "category_id")

	i.fillFieldMap()

	return i
}

func (i *insProductStall) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductStall) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["product_id"] = i.ProductId
	i.fieldMap["stall_id"] = i.StallId
	i.fieldMap["category_id"] = i.CategoryId
}

func (i insProductStall) clone(db *gorm.DB) insProductStall {
	i.insProductStallDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductStall) replaceDB(db *gorm.DB) insProductStall {
	i.insProductStallDo.ReplaceDB(db)
	return i
}

type insProductStallDo struct{ gen.DO }

func (i insProductStallDo) Debug() *insProductStallDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductStallDo) WithContext(ctx context.Context) *insProductStallDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductStallDo) ReadDB() *insProductStallDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductStallDo) WriteDB() *insProductStallDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductStallDo) Session(config *gorm.Session) *insProductStallDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductStallDo) Clauses(conds ...clause.Expression) *insProductStallDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductStallDo) Returning(value interface{}, columns ...string) *insProductStallDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductStallDo) Not(conds ...gen.Condition) *insProductStallDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductStallDo) Or(conds ...gen.Condition) *insProductStallDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductStallDo) Select(conds ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductStallDo) Where(conds ...gen.Condition) *insProductStallDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductStallDo) Order(conds ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductStallDo) Distinct(cols ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductStallDo) Omit(cols ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductStallDo) Join(table schema.Tabler, on ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductStallDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductStallDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductStallDo) Group(cols ...field.Expr) *insProductStallDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductStallDo) Having(conds ...gen.Condition) *insProductStallDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductStallDo) Limit(limit int) *insProductStallDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductStallDo) Offset(offset int) *insProductStallDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductStallDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductStallDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductStallDo) Unscoped() *insProductStallDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductStallDo) Create(values ...*insbuy.InsProductStall) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductStallDo) CreateInBatches(values []*insbuy.InsProductStall, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductStallDo) Save(values ...*insbuy.InsProductStall) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductStallDo) First() (*insbuy.InsProductStall, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStall), nil
	}
}

func (i insProductStallDo) Take() (*insbuy.InsProductStall, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStall), nil
	}
}

func (i insProductStallDo) Last() (*insbuy.InsProductStall, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStall), nil
	}
}

func (i insProductStallDo) Find() ([]*insbuy.InsProductStall, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductStall), err
}

func (i insProductStallDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductStall, err error) {
	buf := make([]*insbuy.InsProductStall, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductStallDo) FindInBatches(result *[]*insbuy.InsProductStall, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductStallDo) Attrs(attrs ...field.AssignExpr) *insProductStallDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductStallDo) Assign(attrs ...field.AssignExpr) *insProductStallDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductStallDo) Joins(fields ...field.RelationField) *insProductStallDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductStallDo) Preload(fields ...field.RelationField) *insProductStallDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductStallDo) FirstOrInit() (*insbuy.InsProductStall, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStall), nil
	}
}

func (i insProductStallDo) FirstOrCreate() (*insbuy.InsProductStall, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductStall), nil
	}
}

func (i insProductStallDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductStall, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductStallDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductStallDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductStallDo) Delete(models ...*insbuy.InsProductStall) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductStallDo) withDO(do gen.Dao) *insProductStallDo {
	i.DO = *do.(*gen.DO)
	return i
}
