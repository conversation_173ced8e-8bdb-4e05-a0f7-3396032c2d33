// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftRuleDetail(db *gorm.DB, opts ...gen.DOOption) insGiftRuleDetail {
	_insGiftRuleDetail := insGiftRuleDetail{}

	_insGiftRuleDetail.insGiftRuleDetailDo.UseDB(db, opts...)
	_insGiftRuleDetail.insGiftRuleDetailDo.UseModel(&insbuy.InsGiftRuleDetail{})

	tableName := _insGiftRuleDetail.insGiftRuleDetailDo.TableName()
	_insGiftRuleDetail.ALL = field.NewAsterisk(tableName)
	_insGiftRuleDetail.ID = field.NewUint(tableName, "id")
	_insGiftRuleDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftRuleDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftRuleDetail.RuleId = field.NewInt(tableName, "rule_id")
	_insGiftRuleDetail.DateType = field.NewInt(tableName, "date_type")
	_insGiftRuleDetail.PriceType = field.NewInt(tableName, "price_type")
	_insGiftRuleDetail.Amount = field.NewFloat64(tableName, "amount")
	_insGiftRuleDetail.Percent = field.NewInt(tableName, "percent")
	_insGiftRuleDetail.Limit_ = field.NewFloat64(tableName, "limit")
	_insGiftRuleDetail.ProductPrice = field.NewFloat64(tableName, "product_price")
	_insGiftRuleDetail.BaseQuota = field.NewFloat64(tableName, "base_quota")
	_insGiftRuleDetail.Cycle = field.NewInt(tableName, "cycle")
	_insGiftRuleDetail.HalfMonthQuota = field.NewInt(tableName, "half_month_quota")

	_insGiftRuleDetail.fillFieldMap()

	return _insGiftRuleDetail
}

type insGiftRuleDetail struct {
	insGiftRuleDetailDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	RuleId         field.Int
	DateType       field.Int
	PriceType      field.Int
	Amount         field.Float64
	Percent        field.Int
	Limit_         field.Float64
	ProductPrice   field.Float64
	BaseQuota      field.Float64
	Cycle          field.Int
	HalfMonthQuota field.Int

	fieldMap map[string]field.Expr
}

func (i insGiftRuleDetail) Table(newTableName string) *insGiftRuleDetail {
	i.insGiftRuleDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftRuleDetail) As(alias string) *insGiftRuleDetail {
	i.insGiftRuleDetailDo.DO = *(i.insGiftRuleDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftRuleDetail) updateTableName(table string) *insGiftRuleDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.RuleId = field.NewInt(table, "rule_id")
	i.DateType = field.NewInt(table, "date_type")
	i.PriceType = field.NewInt(table, "price_type")
	i.Amount = field.NewFloat64(table, "amount")
	i.Percent = field.NewInt(table, "percent")
	i.Limit_ = field.NewFloat64(table, "limit")
	i.ProductPrice = field.NewFloat64(table, "product_price")
	i.BaseQuota = field.NewFloat64(table, "base_quota")
	i.Cycle = field.NewInt(table, "cycle")
	i.HalfMonthQuota = field.NewInt(table, "half_month_quota")

	i.fillFieldMap()

	return i
}

func (i *insGiftRuleDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftRuleDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["rule_id"] = i.RuleId
	i.fieldMap["date_type"] = i.DateType
	i.fieldMap["price_type"] = i.PriceType
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["percent"] = i.Percent
	i.fieldMap["limit"] = i.Limit_
	i.fieldMap["product_price"] = i.ProductPrice
	i.fieldMap["base_quota"] = i.BaseQuota
	i.fieldMap["cycle"] = i.Cycle
	i.fieldMap["half_month_quota"] = i.HalfMonthQuota
}

func (i insGiftRuleDetail) clone(db *gorm.DB) insGiftRuleDetail {
	i.insGiftRuleDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftRuleDetail) replaceDB(db *gorm.DB) insGiftRuleDetail {
	i.insGiftRuleDetailDo.ReplaceDB(db)
	return i
}

type insGiftRuleDetailDo struct{ gen.DO }

func (i insGiftRuleDetailDo) Debug() *insGiftRuleDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftRuleDetailDo) WithContext(ctx context.Context) *insGiftRuleDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftRuleDetailDo) ReadDB() *insGiftRuleDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftRuleDetailDo) WriteDB() *insGiftRuleDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftRuleDetailDo) Session(config *gorm.Session) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftRuleDetailDo) Clauses(conds ...clause.Expression) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftRuleDetailDo) Returning(value interface{}, columns ...string) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftRuleDetailDo) Not(conds ...gen.Condition) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftRuleDetailDo) Or(conds ...gen.Condition) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftRuleDetailDo) Select(conds ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftRuleDetailDo) Where(conds ...gen.Condition) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftRuleDetailDo) Order(conds ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftRuleDetailDo) Distinct(cols ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftRuleDetailDo) Omit(cols ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftRuleDetailDo) Join(table schema.Tabler, on ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftRuleDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftRuleDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftRuleDetailDo) Group(cols ...field.Expr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftRuleDetailDo) Having(conds ...gen.Condition) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftRuleDetailDo) Limit(limit int) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftRuleDetailDo) Offset(offset int) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftRuleDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftRuleDetailDo) Unscoped() *insGiftRuleDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftRuleDetailDo) Create(values ...*insbuy.InsGiftRuleDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftRuleDetailDo) CreateInBatches(values []*insbuy.InsGiftRuleDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftRuleDetailDo) Save(values ...*insbuy.InsGiftRuleDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftRuleDetailDo) First() (*insbuy.InsGiftRuleDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleDetail), nil
	}
}

func (i insGiftRuleDetailDo) Take() (*insbuy.InsGiftRuleDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleDetail), nil
	}
}

func (i insGiftRuleDetailDo) Last() (*insbuy.InsGiftRuleDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleDetail), nil
	}
}

func (i insGiftRuleDetailDo) Find() ([]*insbuy.InsGiftRuleDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftRuleDetail), err
}

func (i insGiftRuleDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftRuleDetail, err error) {
	buf := make([]*insbuy.InsGiftRuleDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftRuleDetailDo) FindInBatches(result *[]*insbuy.InsGiftRuleDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftRuleDetailDo) Attrs(attrs ...field.AssignExpr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftRuleDetailDo) Assign(attrs ...field.AssignExpr) *insGiftRuleDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftRuleDetailDo) Joins(fields ...field.RelationField) *insGiftRuleDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftRuleDetailDo) Preload(fields ...field.RelationField) *insGiftRuleDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftRuleDetailDo) FirstOrInit() (*insbuy.InsGiftRuleDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleDetail), nil
	}
}

func (i insGiftRuleDetailDo) FirstOrCreate() (*insbuy.InsGiftRuleDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRuleDetail), nil
	}
}

func (i insGiftRuleDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftRuleDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftRuleDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftRuleDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftRuleDetailDo) Delete(models ...*insbuy.InsGiftRuleDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftRuleDetailDo) withDO(do gen.Dao) *insGiftRuleDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
