// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderBill(db *gorm.DB, opts ...gen.DOOption) insOrderBill {
	_insOrderBill := insOrderBill{}

	_insOrderBill.insOrderBillDo.UseDB(db, opts...)
	_insOrderBill.insOrderBillDo.UseModel(&insbuy.InsOrderBill{})

	tableName := _insOrderBill.insOrderBillDo.TableName()
	_insOrderBill.ALL = field.NewAsterisk(tableName)
	_insOrderBill.ID = field.NewUint(tableName, "id")
	_insOrderBill.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderBill.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderBill.DeletedAt = field.NewField(tableName, "deleted_at")
	_insOrderBill.BillType = field.NewInt(tableName, "bill_type")
	_insOrderBill.Paytype = field.NewInt(tableName, "paytype")
	_insOrderBill.Amount = field.NewFloat64(tableName, "amount")
	_insOrderBill.RefundBillId = field.NewInt(tableName, "refund_bill_id")
	_insOrderBill.RefundAmount = field.NewFloat64(tableName, "refund_amount")
	_insOrderBill.Currency = field.NewString(tableName, "currency")
	_insOrderBill.ThirdOrder = field.NewString(tableName, "third_order")
	_insOrderBill.Others = field.NewString(tableName, "others")
	_insOrderBill.BillSn = field.NewString(tableName, "bill_sn")
	_insOrderBill.OpenDeskId = field.NewInt(tableName, "open_desk_id")
	_insOrderBill.PayTime = field.NewTime(tableName, "pay_time")
	_insOrderBill.TradePayId = field.NewUint64(tableName, "trade_pay_id")
	_insOrderBill.BillName = field.NewString(tableName, "bill_name")

	_insOrderBill.fillFieldMap()

	return _insOrderBill
}

type insOrderBill struct {
	insOrderBillDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	BillType     field.Int
	Paytype      field.Int
	Amount       field.Float64
	RefundBillId field.Int
	RefundAmount field.Float64
	Currency     field.String
	ThirdOrder   field.String
	Others       field.String
	BillSn       field.String
	OpenDeskId   field.Int
	PayTime      field.Time
	TradePayId   field.Uint64
	BillName     field.String

	fieldMap map[string]field.Expr
}

func (i insOrderBill) Table(newTableName string) *insOrderBill {
	i.insOrderBillDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderBill) As(alias string) *insOrderBill {
	i.insOrderBillDo.DO = *(i.insOrderBillDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderBill) updateTableName(table string) *insOrderBill {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.BillType = field.NewInt(table, "bill_type")
	i.Paytype = field.NewInt(table, "paytype")
	i.Amount = field.NewFloat64(table, "amount")
	i.RefundBillId = field.NewInt(table, "refund_bill_id")
	i.RefundAmount = field.NewFloat64(table, "refund_amount")
	i.Currency = field.NewString(table, "currency")
	i.ThirdOrder = field.NewString(table, "third_order")
	i.Others = field.NewString(table, "others")
	i.BillSn = field.NewString(table, "bill_sn")
	i.OpenDeskId = field.NewInt(table, "open_desk_id")
	i.PayTime = field.NewTime(table, "pay_time")
	i.TradePayId = field.NewUint64(table, "trade_pay_id")
	i.BillName = field.NewString(table, "bill_name")

	i.fillFieldMap()

	return i
}

func (i *insOrderBill) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderBill) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["bill_type"] = i.BillType
	i.fieldMap["paytype"] = i.Paytype
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["refund_bill_id"] = i.RefundBillId
	i.fieldMap["refund_amount"] = i.RefundAmount
	i.fieldMap["currency"] = i.Currency
	i.fieldMap["third_order"] = i.ThirdOrder
	i.fieldMap["others"] = i.Others
	i.fieldMap["bill_sn"] = i.BillSn
	i.fieldMap["open_desk_id"] = i.OpenDeskId
	i.fieldMap["pay_time"] = i.PayTime
	i.fieldMap["trade_pay_id"] = i.TradePayId
	i.fieldMap["bill_name"] = i.BillName
}

func (i insOrderBill) clone(db *gorm.DB) insOrderBill {
	i.insOrderBillDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderBill) replaceDB(db *gorm.DB) insOrderBill {
	i.insOrderBillDo.ReplaceDB(db)
	return i
}

type insOrderBillDo struct{ gen.DO }

func (i insOrderBillDo) Debug() *insOrderBillDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderBillDo) WithContext(ctx context.Context) *insOrderBillDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderBillDo) ReadDB() *insOrderBillDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderBillDo) WriteDB() *insOrderBillDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderBillDo) Session(config *gorm.Session) *insOrderBillDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderBillDo) Clauses(conds ...clause.Expression) *insOrderBillDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderBillDo) Returning(value interface{}, columns ...string) *insOrderBillDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderBillDo) Not(conds ...gen.Condition) *insOrderBillDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderBillDo) Or(conds ...gen.Condition) *insOrderBillDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderBillDo) Select(conds ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderBillDo) Where(conds ...gen.Condition) *insOrderBillDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderBillDo) Order(conds ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderBillDo) Distinct(cols ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderBillDo) Omit(cols ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderBillDo) Join(table schema.Tabler, on ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderBillDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderBillDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderBillDo) Group(cols ...field.Expr) *insOrderBillDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderBillDo) Having(conds ...gen.Condition) *insOrderBillDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderBillDo) Limit(limit int) *insOrderBillDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderBillDo) Offset(offset int) *insOrderBillDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderBillDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderBillDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderBillDo) Unscoped() *insOrderBillDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderBillDo) Create(values ...*insbuy.InsOrderBill) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderBillDo) CreateInBatches(values []*insbuy.InsOrderBill, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderBillDo) Save(values ...*insbuy.InsOrderBill) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderBillDo) First() (*insbuy.InsOrderBill, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderBill), nil
	}
}

func (i insOrderBillDo) Take() (*insbuy.InsOrderBill, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderBill), nil
	}
}

func (i insOrderBillDo) Last() (*insbuy.InsOrderBill, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderBill), nil
	}
}

func (i insOrderBillDo) Find() ([]*insbuy.InsOrderBill, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderBill), err
}

func (i insOrderBillDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderBill, err error) {
	buf := make([]*insbuy.InsOrderBill, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderBillDo) FindInBatches(result *[]*insbuy.InsOrderBill, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderBillDo) Attrs(attrs ...field.AssignExpr) *insOrderBillDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderBillDo) Assign(attrs ...field.AssignExpr) *insOrderBillDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderBillDo) Joins(fields ...field.RelationField) *insOrderBillDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderBillDo) Preload(fields ...field.RelationField) *insOrderBillDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderBillDo) FirstOrInit() (*insbuy.InsOrderBill, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderBill), nil
	}
}

func (i insOrderBillDo) FirstOrCreate() (*insbuy.InsOrderBill, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderBill), nil
	}
}

func (i insOrderBillDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderBill, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderBillDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderBillDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderBillDo) Delete(models ...*insbuy.InsOrderBill) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderBillDo) withDO(do gen.Dao) *insOrderBillDo {
	i.DO = *do.(*gen.DO)
	return i
}
