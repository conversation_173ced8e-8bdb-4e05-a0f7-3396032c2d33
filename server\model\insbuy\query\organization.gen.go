// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newOrganization(db *gorm.DB, opts ...gen.DOOption) organization {
	_organization := organization{}

	_organization.organizationDo.UseDB(db, opts...)
	_organization.organizationDo.UseModel(&model.Organization{})

	tableName := _organization.organizationDo.TableName()
	_organization.ALL = field.NewAsterisk(tableName)
	_organization.ID = field.NewUint(tableName, "id")
	_organization.CreatedAt = field.NewTime(tableName, "created_at")
	_organization.UpdatedAt = field.NewTime(tableName, "updated_at")
	_organization.DeletedAt = field.NewField(tableName, "deleted_at")
	_organization.Name = field.NewString(tableName, "name")
	_organization.ParentID = field.NewUint(tableName, "parent_id")
	_organization.StoreId = field.NewInt(tableName, "store_id")

	_organization.fillFieldMap()

	return _organization
}

type organization struct {
	organizationDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Name      field.String
	ParentID  field.Uint
	StoreId   field.Int

	fieldMap map[string]field.Expr
}

func (o organization) Table(newTableName string) *organization {
	o.organizationDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o organization) As(alias string) *organization {
	o.organizationDo.DO = *(o.organizationDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *organization) updateTableName(table string) *organization {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewUint(table, "id")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.DeletedAt = field.NewField(table, "deleted_at")
	o.Name = field.NewString(table, "name")
	o.ParentID = field.NewUint(table, "parent_id")
	o.StoreId = field.NewInt(table, "store_id")

	o.fillFieldMap()

	return o
}

func (o *organization) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *organization) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 7)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["deleted_at"] = o.DeletedAt
	o.fieldMap["name"] = o.Name
	o.fieldMap["parent_id"] = o.ParentID
	o.fieldMap["store_id"] = o.StoreId
}

func (o organization) clone(db *gorm.DB) organization {
	o.organizationDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o organization) replaceDB(db *gorm.DB) organization {
	o.organizationDo.ReplaceDB(db)
	return o
}

type organizationDo struct{ gen.DO }

func (o organizationDo) Debug() *organizationDo {
	return o.withDO(o.DO.Debug())
}

func (o organizationDo) WithContext(ctx context.Context) *organizationDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o organizationDo) ReadDB() *organizationDo {
	return o.Clauses(dbresolver.Read)
}

func (o organizationDo) WriteDB() *organizationDo {
	return o.Clauses(dbresolver.Write)
}

func (o organizationDo) Session(config *gorm.Session) *organizationDo {
	return o.withDO(o.DO.Session(config))
}

func (o organizationDo) Clauses(conds ...clause.Expression) *organizationDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o organizationDo) Returning(value interface{}, columns ...string) *organizationDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o organizationDo) Not(conds ...gen.Condition) *organizationDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o organizationDo) Or(conds ...gen.Condition) *organizationDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o organizationDo) Select(conds ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o organizationDo) Where(conds ...gen.Condition) *organizationDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o organizationDo) Order(conds ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o organizationDo) Distinct(cols ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o organizationDo) Omit(cols ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o organizationDo) Join(table schema.Tabler, on ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o organizationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *organizationDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o organizationDo) RightJoin(table schema.Tabler, on ...field.Expr) *organizationDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o organizationDo) Group(cols ...field.Expr) *organizationDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o organizationDo) Having(conds ...gen.Condition) *organizationDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o organizationDo) Limit(limit int) *organizationDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o organizationDo) Offset(offset int) *organizationDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o organizationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *organizationDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o organizationDo) Unscoped() *organizationDo {
	return o.withDO(o.DO.Unscoped())
}

func (o organizationDo) Create(values ...*model.Organization) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o organizationDo) CreateInBatches(values []*model.Organization, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o organizationDo) Save(values ...*model.Organization) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o organizationDo) First() (*model.Organization, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Organization), nil
	}
}

func (o organizationDo) Take() (*model.Organization, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Organization), nil
	}
}

func (o organizationDo) Last() (*model.Organization, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Organization), nil
	}
}

func (o organizationDo) Find() ([]*model.Organization, error) {
	result, err := o.DO.Find()
	return result.([]*model.Organization), err
}

func (o organizationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Organization, err error) {
	buf := make([]*model.Organization, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o organizationDo) FindInBatches(result *[]*model.Organization, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o organizationDo) Attrs(attrs ...field.AssignExpr) *organizationDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o organizationDo) Assign(attrs ...field.AssignExpr) *organizationDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o organizationDo) Joins(fields ...field.RelationField) *organizationDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o organizationDo) Preload(fields ...field.RelationField) *organizationDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o organizationDo) FirstOrInit() (*model.Organization, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Organization), nil
	}
}

func (o organizationDo) FirstOrCreate() (*model.Organization, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Organization), nil
	}
}

func (o organizationDo) FindByPage(offset int, limit int) (result []*model.Organization, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o organizationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o organizationDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o organizationDo) Delete(models ...*model.Organization) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *organizationDo) withDO(do gen.Dao) *organizationDo {
	o.DO = *do.(*gen.DO)
	return o
}
