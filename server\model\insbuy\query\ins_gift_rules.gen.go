// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftRules(db *gorm.DB, opts ...gen.DOOption) insGiftRules {
	_insGiftRules := insGiftRules{}

	_insGiftRules.insGiftRulesDo.UseDB(db, opts...)
	_insGiftRules.insGiftRulesDo.UseModel(&insbuy.InsGiftRules{})

	tableName := _insGiftRules.insGiftRulesDo.TableName()
	_insGiftRules.ALL = field.NewAsterisk(tableName)
	_insGiftRules.ID = field.NewUint(tableName, "id")
	_insGiftRules.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftRules.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftRules.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftRules.StoreId = field.NewUint(tableName, "store_id")
	_insGiftRules.Name = field.NewString(tableName, "name")
	_insGiftRules.Status = field.NewInt(tableName, "status")
	_insGiftRules.Remark = field.NewString(tableName, "remark")
	_insGiftRules.OperatorId = field.NewUint(tableName, "operator_id")

	_insGiftRules.fillFieldMap()

	return _insGiftRules
}

type insGiftRules struct {
	insGiftRulesDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	StoreId    field.Uint
	Name       field.String
	Status     field.Int
	Remark     field.String
	OperatorId field.Uint

	fieldMap map[string]field.Expr
}

func (i insGiftRules) Table(newTableName string) *insGiftRules {
	i.insGiftRulesDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftRules) As(alias string) *insGiftRules {
	i.insGiftRulesDo.DO = *(i.insGiftRulesDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftRules) updateTableName(table string) *insGiftRules {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.Name = field.NewString(table, "name")
	i.Status = field.NewInt(table, "status")
	i.Remark = field.NewString(table, "remark")
	i.OperatorId = field.NewUint(table, "operator_id")

	i.fillFieldMap()

	return i
}

func (i *insGiftRules) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftRules) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["name"] = i.Name
	i.fieldMap["status"] = i.Status
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["operator_id"] = i.OperatorId
}

func (i insGiftRules) clone(db *gorm.DB) insGiftRules {
	i.insGiftRulesDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftRules) replaceDB(db *gorm.DB) insGiftRules {
	i.insGiftRulesDo.ReplaceDB(db)
	return i
}

type insGiftRulesDo struct{ gen.DO }

func (i insGiftRulesDo) Debug() *insGiftRulesDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftRulesDo) WithContext(ctx context.Context) *insGiftRulesDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftRulesDo) ReadDB() *insGiftRulesDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftRulesDo) WriteDB() *insGiftRulesDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftRulesDo) Session(config *gorm.Session) *insGiftRulesDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftRulesDo) Clauses(conds ...clause.Expression) *insGiftRulesDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftRulesDo) Returning(value interface{}, columns ...string) *insGiftRulesDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftRulesDo) Not(conds ...gen.Condition) *insGiftRulesDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftRulesDo) Or(conds ...gen.Condition) *insGiftRulesDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftRulesDo) Select(conds ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftRulesDo) Where(conds ...gen.Condition) *insGiftRulesDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftRulesDo) Order(conds ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftRulesDo) Distinct(cols ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftRulesDo) Omit(cols ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftRulesDo) Join(table schema.Tabler, on ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftRulesDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftRulesDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftRulesDo) Group(cols ...field.Expr) *insGiftRulesDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftRulesDo) Having(conds ...gen.Condition) *insGiftRulesDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftRulesDo) Limit(limit int) *insGiftRulesDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftRulesDo) Offset(offset int) *insGiftRulesDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftRulesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftRulesDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftRulesDo) Unscoped() *insGiftRulesDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftRulesDo) Create(values ...*insbuy.InsGiftRules) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftRulesDo) CreateInBatches(values []*insbuy.InsGiftRules, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftRulesDo) Save(values ...*insbuy.InsGiftRules) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftRulesDo) First() (*insbuy.InsGiftRules, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRules), nil
	}
}

func (i insGiftRulesDo) Take() (*insbuy.InsGiftRules, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRules), nil
	}
}

func (i insGiftRulesDo) Last() (*insbuy.InsGiftRules, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRules), nil
	}
}

func (i insGiftRulesDo) Find() ([]*insbuy.InsGiftRules, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftRules), err
}

func (i insGiftRulesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftRules, err error) {
	buf := make([]*insbuy.InsGiftRules, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftRulesDo) FindInBatches(result *[]*insbuy.InsGiftRules, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftRulesDo) Attrs(attrs ...field.AssignExpr) *insGiftRulesDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftRulesDo) Assign(attrs ...field.AssignExpr) *insGiftRulesDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftRulesDo) Joins(fields ...field.RelationField) *insGiftRulesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftRulesDo) Preload(fields ...field.RelationField) *insGiftRulesDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftRulesDo) FirstOrInit() (*insbuy.InsGiftRules, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRules), nil
	}
}

func (i insGiftRulesDo) FirstOrCreate() (*insbuy.InsGiftRules, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftRules), nil
	}
}

func (i insGiftRulesDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftRules, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftRulesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftRulesDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftRulesDo) Delete(models ...*insbuy.InsGiftRules) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftRulesDo) withDO(do gen.Dao) *insGiftRulesDo {
	i.DO = *do.(*gen.DO)
	return i
}
