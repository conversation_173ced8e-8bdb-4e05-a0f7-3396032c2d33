package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InsSalerApi struct {
}

// CreateInsSaler 创建InsSaler
// @Tags InsSaler
// @Summary 创建InsSaler
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSaler true "创建InsSaler"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSaler/createInsSaler [post]
func (insSalerApi *InsSalerApi) CreateInsSaler(c *gin.Context) {
	var insSaler insbuy.InsSaler
	err := c.ShouldBindJSON(&insSaler)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.CreateInsSaler(&insSaler); err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
	} else {
		response.OkWithMessage("创建成功", c)
	}
}

// DeleteInsSaler 删除InsSaler
// @Tags InsSaler
// @Summary 删除InsSaler
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSaler true "删除InsSaler"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /insSaler/deleteInsSaler [delete]
func (insSalerApi *InsSalerApi) DeleteInsSaler(c *gin.Context) {
	var insSaler insbuy.InsSaler
	err := c.ShouldBindJSON(&insSaler)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.DeleteInsSaler(insSaler); err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
	} else {
		response.OkWithMessage("删除成功", c)
	}
}

// DeleteInsSalerByIds 批量删除InsSaler
// @Tags InsSaler
// @Summary 批量删除InsSaler
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除InsSaler"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /insSaler/deleteInsSalerByIds [delete]
func (insSalerApi *InsSalerApi) DeleteInsSalerByIds(c *gin.Context) {
	var IDS request.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.DeleteInsSalerByIds(IDS); err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
	} else {
		response.OkWithMessage("批量删除成功", c)
	}
}

// UpdateInsSaler 更新InsSaler
// @Tags InsSaler
// @Summary 更新InsSaler
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuy.InsSaler true "更新InsSaler"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSaler/updateInsSaler [put]
func (insSalerApi *InsSalerApi) UpdateInsSaler(c *gin.Context) {
	var insSaler insbuy.InsSaler
	err := c.ShouldBindJSON(&insSaler)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.UpdateInsSaler(insSaler); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// FindInsSaler 用id查询InsSaler
// @Tags InsSaler
// @Summary 用id查询InsSaler
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuy.InsSaler true "用id查询InsSaler"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /insSaler/findInsSaler [get]
func (insSalerApi *InsSalerApi) FindInsSaler(c *gin.Context) {
	var insSaler insbuy.InsSaler
	err := c.ShouldBindQuery(&insSaler)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if reinsSaler, err := insSalerService.GetInsSaler(insSaler.ID); err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
	} else {
		response.OkWithData(gin.H{"reinsSaler": reinsSaler}, c)
	}
}

// GetInsSalerList 分页获取InsSaler列表
// @Tags InsSaler
// @Summary 分页获取InsSaler列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSalerSearch true "分页获取InsSaler列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSaler/getInsSalerList [get]
func (insSalerApi *InsSalerApi) GetInsSalerList(c *gin.Context) {
	var pageInfo insbuyReq.InsSalerSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSalerService.GetInsSalerInfoList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GiveSalerList 分页获取可赠送的销售列表
// @Tags InsSaler
// @Summary 分页获取可赠送的销售列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.InsSalerSearch true "分页获取可赠送的销售列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSaler/giveSalerList [get]
func (insSalerApi *InsSalerApi) GiveSalerList(c *gin.Context) {
	var pageInfo insbuyReq.InsSalerSearch
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSalerService.GiveSalerList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetStoreSalesList 获取分店的销售列表
// @Tags InsSaler
// @Summary 获取分店的销售列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.GetStoreSalesListReq true "分页获取可赠送的销售列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSaler/getStoreSalesList [get]
func (insSalerApi *InsSalerApi) GetStoreSalesList(c *gin.Context) {
	var pageInfo insbuyReq.GetStoreSalesListReq
	err := GinMustBind(c, &pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if list, total, err := insSalerService.GetStoreSalesList(pageInfo); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// CheckInsSalerCode 销售超级验证码验证
// @Tags InsSaler
// @Summary 销售超级验证码验证
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.CheckInsSalerCodeReq true "销售超级验证码验证"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /insSaler/checkInsSalerCode [get]
func (insSalerApi *InsSalerApi) CheckInsSalerCode(c *gin.Context) {
	var req insbuyReq.CheckInsSalerCodeReq
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.CheckInsSalerCode(req); err != nil {
		global.GVA_LOG.Error("验证失败!", zap.Error(err))
		response.FailWithMessage("验证失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("验证成功", c)
	}
}

// UpdateInsSalerCode 更新销售超级验证码
// @Tags InsSaler
// @Summary 更新销售超级验证码
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query insbuyReq.UpdateInsSalerCodeReq true "更新销售超级验证码"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /insSaler/updateInsSalerCode [put]
func (insSalerApi *InsSalerApi) UpdateInsSalerCode(c *gin.Context) {
	var req insbuyReq.UpdateInsSalerCodeReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if err := insSalerService.UpdateInsSalerCode(req); err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
	} else {
		response.OkWithMessage("更新成功", c)
	}
}

// GiftLogList 获取赠送记录列表
// @Tags insSaler
// @Summary 获取赠送记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body insbuyReq.GiftLogReq true "获取赠送记录列表"
// @Success   200   {object}  response.Response{data=insbuyResp.GiftLogResp,msg=string}  "获取赠送记录列表"
// @Router /insSaler/giftLogList [get]
func (insSalerApi *InsSalerApi) GiftLogList(c *gin.Context) {
	var req insbuyReq.GiftLogReq
	err := GinMustBind(c, &req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list := make([]insbuyResp.GiftLogResp, 0)
	var total int64 = 0
	if list, total, err = insSalerService.GiftLogList(req); err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
	} else {
		if req.Export() {
			_, e := insImportService.ExcelCommonList(c, insbuy.ETGiftRecordList.ToInt(), list)
			if e != nil {
				response.FailWithMessage("导出失败", c)
				return
			}
			c.Abort()
			return
		}
		response.OkWithDetailed(response.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}
