// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsRechargeRule(db *gorm.DB, opts ...gen.DOOption) insRechargeRule {
	_insRechargeRule := insRechargeRule{}

	_insRechargeRule.insRechargeRuleDo.UseDB(db, opts...)
	_insRechargeRule.insRechargeRuleDo.UseModel(&insbuy.InsRechargeRule{})

	tableName := _insRechargeRule.insRechargeRuleDo.TableName()
	_insRechargeRule.ALL = field.NewAsterisk(tableName)
	_insRechargeRule.ID = field.NewUint(tableName, "id")
	_insRechargeRule.CreatedAt = field.NewTime(tableName, "created_at")
	_insRechargeRule.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insRechargeRule.StoreId = field.NewInt(tableName, "store_id")
	_insRechargeRule.Desc = field.NewString(tableName, "desc")
	_insRechargeRule.Remark = field.NewString(tableName, "remark")
	_insRechargeRule.OperatorId = field.NewInt(tableName, "operator_id")
	_insRechargeRule.RuleType = field.NewInt(tableName, "rule_type")
	_insRechargeRule.ValidTimeType = field.NewInt(tableName, "valid_time_type")
	_insRechargeRule.RuleParameters = field.NewString(tableName, "rule_parameters")
	_insRechargeRule.StartTime = field.NewTime(tableName, "start_time")
	_insRechargeRule.EndTime = field.NewTime(tableName, "end_time")

	_insRechargeRule.fillFieldMap()

	return _insRechargeRule
}

type insRechargeRule struct {
	insRechargeRuleDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	StoreId        field.Int
	Desc           field.String
	Remark         field.String
	OperatorId     field.Int
	RuleType       field.Int
	ValidTimeType  field.Int
	RuleParameters field.String
	StartTime      field.Time
	EndTime        field.Time

	fieldMap map[string]field.Expr
}

func (i insRechargeRule) Table(newTableName string) *insRechargeRule {
	i.insRechargeRuleDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insRechargeRule) As(alias string) *insRechargeRule {
	i.insRechargeRuleDo.DO = *(i.insRechargeRuleDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insRechargeRule) updateTableName(table string) *insRechargeRule {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.StoreId = field.NewInt(table, "store_id")
	i.Desc = field.NewString(table, "desc")
	i.Remark = field.NewString(table, "remark")
	i.OperatorId = field.NewInt(table, "operator_id")
	i.RuleType = field.NewInt(table, "rule_type")
	i.ValidTimeType = field.NewInt(table, "valid_time_type")
	i.RuleParameters = field.NewString(table, "rule_parameters")
	i.StartTime = field.NewTime(table, "start_time")
	i.EndTime = field.NewTime(table, "end_time")

	i.fillFieldMap()

	return i
}

func (i *insRechargeRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insRechargeRule) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["desc"] = i.Desc
	i.fieldMap["remark"] = i.Remark
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["rule_type"] = i.RuleType
	i.fieldMap["valid_time_type"] = i.ValidTimeType
	i.fieldMap["rule_parameters"] = i.RuleParameters
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
}

func (i insRechargeRule) clone(db *gorm.DB) insRechargeRule {
	i.insRechargeRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insRechargeRule) replaceDB(db *gorm.DB) insRechargeRule {
	i.insRechargeRuleDo.ReplaceDB(db)
	return i
}

type insRechargeRuleDo struct{ gen.DO }

func (i insRechargeRuleDo) Debug() *insRechargeRuleDo {
	return i.withDO(i.DO.Debug())
}

func (i insRechargeRuleDo) WithContext(ctx context.Context) *insRechargeRuleDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insRechargeRuleDo) ReadDB() *insRechargeRuleDo {
	return i.Clauses(dbresolver.Read)
}

func (i insRechargeRuleDo) WriteDB() *insRechargeRuleDo {
	return i.Clauses(dbresolver.Write)
}

func (i insRechargeRuleDo) Session(config *gorm.Session) *insRechargeRuleDo {
	return i.withDO(i.DO.Session(config))
}

func (i insRechargeRuleDo) Clauses(conds ...clause.Expression) *insRechargeRuleDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insRechargeRuleDo) Returning(value interface{}, columns ...string) *insRechargeRuleDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insRechargeRuleDo) Not(conds ...gen.Condition) *insRechargeRuleDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insRechargeRuleDo) Or(conds ...gen.Condition) *insRechargeRuleDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insRechargeRuleDo) Select(conds ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insRechargeRuleDo) Where(conds ...gen.Condition) *insRechargeRuleDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insRechargeRuleDo) Order(conds ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insRechargeRuleDo) Distinct(cols ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insRechargeRuleDo) Omit(cols ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insRechargeRuleDo) Join(table schema.Tabler, on ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insRechargeRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insRechargeRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insRechargeRuleDo) Group(cols ...field.Expr) *insRechargeRuleDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insRechargeRuleDo) Having(conds ...gen.Condition) *insRechargeRuleDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insRechargeRuleDo) Limit(limit int) *insRechargeRuleDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insRechargeRuleDo) Offset(offset int) *insRechargeRuleDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insRechargeRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insRechargeRuleDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insRechargeRuleDo) Unscoped() *insRechargeRuleDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insRechargeRuleDo) Create(values ...*insbuy.InsRechargeRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insRechargeRuleDo) CreateInBatches(values []*insbuy.InsRechargeRule, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insRechargeRuleDo) Save(values ...*insbuy.InsRechargeRule) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insRechargeRuleDo) First() (*insbuy.InsRechargeRule, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeRule), nil
	}
}

func (i insRechargeRuleDo) Take() (*insbuy.InsRechargeRule, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeRule), nil
	}
}

func (i insRechargeRuleDo) Last() (*insbuy.InsRechargeRule, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeRule), nil
	}
}

func (i insRechargeRuleDo) Find() ([]*insbuy.InsRechargeRule, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsRechargeRule), err
}

func (i insRechargeRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsRechargeRule, err error) {
	buf := make([]*insbuy.InsRechargeRule, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insRechargeRuleDo) FindInBatches(result *[]*insbuy.InsRechargeRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insRechargeRuleDo) Attrs(attrs ...field.AssignExpr) *insRechargeRuleDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insRechargeRuleDo) Assign(attrs ...field.AssignExpr) *insRechargeRuleDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insRechargeRuleDo) Joins(fields ...field.RelationField) *insRechargeRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insRechargeRuleDo) Preload(fields ...field.RelationField) *insRechargeRuleDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insRechargeRuleDo) FirstOrInit() (*insbuy.InsRechargeRule, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeRule), nil
	}
}

func (i insRechargeRuleDo) FirstOrCreate() (*insbuy.InsRechargeRule, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsRechargeRule), nil
	}
}

func (i insRechargeRuleDo) FindByPage(offset int, limit int) (result []*insbuy.InsRechargeRule, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insRechargeRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insRechargeRuleDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insRechargeRuleDo) Delete(models ...*insbuy.InsRechargeRule) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insRechargeRuleDo) withDO(do gen.Dao) *insRechargeRuleDo {
	i.DO = *do.(*gen.DO)
	return i
}
