package insreport

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// FormulaJSON 简化的JSON格式公式定义
type FormulaJSON struct {
	Expression  string `json:"expression"`  // 显示用的表达式（使用名称）
	References  []uint `json:"references"`  // 引用的分类ID列表
	Description string `json:"description"` // 公式描述
}

// SimpleFormulaBuilder 简化的公式构建器
type SimpleFormulaBuilder struct {
	configs map[uint]CostTypeConfigItem // 分类配置映射
	nameMap map[string]uint             // 名称到ID的映射
}

// NewSimpleFormulaBuilder 创建简化公式构建器
func NewSimpleFormulaBuilder(configs []CostTypeConfigItem) *SimpleFormulaBuilder {
	configMap := make(map[uint]CostTypeConfigItem)
	nameMap := make(map[string]uint)

	for _, config := range configs {
		configMap[config.Id] = config
		nameMap[config.CategoryName] = config.Id
	}

	return &SimpleFormulaBuilder{
		configs: configMap,
		nameMap: nameMap,
	}
}

// BuildFromExpression 从表达式构建简化JSON公式
func (builder *SimpleFormulaBuilder) BuildFromExpression(displayExpr string, description string) (*FormulaJSON, error) {
	if displayExpr == "" {
		return nil, fmt.Errorf("表达式不能为空")
	}

	// 解析引用的分类ID
	refs, err := builder.parseReferences(displayExpr)
	if err != nil {
		return nil, err
	}

	// 构建简化的JSON公式
	formulaJSON := &FormulaJSON{
		Expression:  displayExpr,
		References:  refs,
		Description: description,
	}

	return formulaJSON, nil
}

// parseReferences 解析表达式中的引用
func (builder *SimpleFormulaBuilder) parseReferences(expression string) ([]uint, error) {
	var refs []uint

	// 使用正则表达式匹配 [分类名称] 格式
	namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
	matches := namePattern.FindAllStringSubmatch(expression, -1)

	for _, match := range matches {
		if len(match) >= 2 {
			categoryName := match[1]

			// 查找对应的ID
			if id, exists := builder.nameMap[categoryName]; exists {
				// 避免重复
				found := false
				for _, existingId := range refs {
					if existingId == id {
						found = true
						break
					}
				}
				if !found {
					refs = append(refs, id)
				}
			} else {
				return nil, fmt.Errorf("找不到分类: %s", categoryName)
			}
		}
	}

	return refs, nil
}

// BuildFromJSON 从JSON字符串构建公式对象
func (builder *SimpleFormulaBuilder) BuildFromJSON(jsonStr string) (*FormulaJSON, error) {
	if jsonStr == "" {
		return &FormulaJSON{}, nil
	}

	var formulaJSON FormulaJSON
	if err := json.Unmarshal([]byte(jsonStr), &formulaJSON); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	// 验证引用的有效性
	err := builder.validateReferences(&formulaJSON)
	if err != nil {
		return nil, err
	}

	return &formulaJSON, nil
}

// validateReferences 验证引用的有效性
func (builder *SimpleFormulaBuilder) validateReferences(formulaJSON *FormulaJSON) error {
	var validRefs []uint

	for _, refId := range formulaJSON.References {
		if _, exists := builder.configs[refId]; exists {
			validRefs = append(validRefs, refId)
		} else {
			return fmt.Errorf("引用的分类ID %d 不存在", refId)
		}
	}

	formulaJSON.References = validRefs
	return nil
}

// ConvertToStorageFormat 转换为存储格式（ID引用）
func (builder *SimpleFormulaBuilder) ConvertToStorageFormat(formulaJSON *FormulaJSON) (string, error) {
	storageExpr := formulaJSON.Expression

	// 将分类名称替换为ID引用
	namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
	matches := namePattern.FindAllStringSubmatch(formulaJSON.Expression, -1)

	for _, match := range matches {
		if len(match) >= 2 {
			categoryName := match[1]
			fullMatch := match[0] // [分类名称]

			// 查找对应的ID
			if id, exists := builder.nameMap[categoryName]; exists {
				idRef := fmt.Sprintf("#%d", id)
				storageExpr = strings.Replace(storageExpr, fullMatch, idRef, 1)
			} else {
				return "", fmt.Errorf("找不到分类: %s", categoryName)
			}
		}
	}

	return storageExpr, nil
}

// ConvertToDisplayFormat 转换为显示格式（名称引用）
func (builder *SimpleFormulaBuilder) ConvertToDisplayFormat(storageExpr string) (string, error) {
	displayExpr := storageExpr

	// 将ID引用替换为分类名称
	idPattern := regexp.MustCompile(`#(\d+)`)
	matches := idPattern.FindAllStringSubmatch(storageExpr, -1)

	for _, match := range matches {
		if len(match) >= 2 {
			idStr := match[1]
			fullMatch := match[0] // #123

			if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
				categoryId := uint(id)
				if config, exists := builder.configs[categoryId]; exists {
					nameRef := fmt.Sprintf("[%s]", config.CategoryName)
					displayExpr = strings.Replace(displayExpr, fullMatch, nameRef, 1)
				} else {
					return "", fmt.Errorf("找不到ID为 %d 的分类", categoryId)
				}
			}
		}
	}

	return displayExpr, nil
}

// ToJSON 转换为JSON字符串
func (formulaJSON *FormulaJSON) ToJSON() (string, error) {
	data, err := json.Marshal(formulaJSON)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetReferencedIDs 获取引用的分类ID列表
func (formulaJSON *FormulaJSON) GetReferencedIDs() []uint {
	return formulaJSON.References
}

// UpdateExpression 更新表达式
func (formulaJSON *FormulaJSON) UpdateExpression(newExpression string) {
	formulaJSON.Expression = newExpression
}

// UpdateDescription 更新描述
func (formulaJSON *FormulaJSON) UpdateDescription(newDescription string) {
	formulaJSON.Description = newDescription
}
