// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsGiftQuotaAssignLogDetail(db *gorm.DB, opts ...gen.DOOption) insGiftQuotaAssignLogDetail {
	_insGiftQuotaAssignLogDetail := insGiftQuotaAssignLogDetail{}

	_insGiftQuotaAssignLogDetail.insGiftQuotaAssignLogDetailDo.UseDB(db, opts...)
	_insGiftQuotaAssignLogDetail.insGiftQuotaAssignLogDetailDo.UseModel(&insbuy.InsGiftQuotaAssignLogDetail{})

	tableName := _insGiftQuotaAssignLogDetail.insGiftQuotaAssignLogDetailDo.TableName()
	_insGiftQuotaAssignLogDetail.ALL = field.NewAsterisk(tableName)
	_insGiftQuotaAssignLogDetail.ID = field.NewUint(tableName, "id")
	_insGiftQuotaAssignLogDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_insGiftQuotaAssignLogDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insGiftQuotaAssignLogDetail.DeletedAt = field.NewField(tableName, "deleted_at")
	_insGiftQuotaAssignLogDetail.ChangeType = field.NewInt(tableName, "change_type")
	_insGiftQuotaAssignLogDetail.SourceType = field.NewInt(tableName, "source_type")
	_insGiftQuotaAssignLogDetail.AssignId = field.NewUint(tableName, "assign_id")
	_insGiftQuotaAssignLogDetail.Quota = field.NewFloat64(tableName, "quota")
	_insGiftQuotaAssignLogDetail.StoreId = field.NewUint(tableName, "store_id")
	_insGiftQuotaAssignLogDetail.BusinessDay = field.NewTime(tableName, "business_day")

	_insGiftQuotaAssignLogDetail.fillFieldMap()

	return _insGiftQuotaAssignLogDetail
}

type insGiftQuotaAssignLogDetail struct {
	insGiftQuotaAssignLogDetailDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	ChangeType  field.Int
	SourceType  field.Int
	AssignId    field.Uint
	Quota       field.Float64
	StoreId     field.Uint
	BusinessDay field.Time

	fieldMap map[string]field.Expr
}

func (i insGiftQuotaAssignLogDetail) Table(newTableName string) *insGiftQuotaAssignLogDetail {
	i.insGiftQuotaAssignLogDetailDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insGiftQuotaAssignLogDetail) As(alias string) *insGiftQuotaAssignLogDetail {
	i.insGiftQuotaAssignLogDetailDo.DO = *(i.insGiftQuotaAssignLogDetailDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insGiftQuotaAssignLogDetail) updateTableName(table string) *insGiftQuotaAssignLogDetail {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ChangeType = field.NewInt(table, "change_type")
	i.SourceType = field.NewInt(table, "source_type")
	i.AssignId = field.NewUint(table, "assign_id")
	i.Quota = field.NewFloat64(table, "quota")
	i.StoreId = field.NewUint(table, "store_id")
	i.BusinessDay = field.NewTime(table, "business_day")

	i.fillFieldMap()

	return i
}

func (i *insGiftQuotaAssignLogDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insGiftQuotaAssignLogDetail) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["change_type"] = i.ChangeType
	i.fieldMap["source_type"] = i.SourceType
	i.fieldMap["assign_id"] = i.AssignId
	i.fieldMap["quota"] = i.Quota
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["business_day"] = i.BusinessDay
}

func (i insGiftQuotaAssignLogDetail) clone(db *gorm.DB) insGiftQuotaAssignLogDetail {
	i.insGiftQuotaAssignLogDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insGiftQuotaAssignLogDetail) replaceDB(db *gorm.DB) insGiftQuotaAssignLogDetail {
	i.insGiftQuotaAssignLogDetailDo.ReplaceDB(db)
	return i
}

type insGiftQuotaAssignLogDetailDo struct{ gen.DO }

func (i insGiftQuotaAssignLogDetailDo) Debug() *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Debug())
}

func (i insGiftQuotaAssignLogDetailDo) WithContext(ctx context.Context) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insGiftQuotaAssignLogDetailDo) ReadDB() *insGiftQuotaAssignLogDetailDo {
	return i.Clauses(dbresolver.Read)
}

func (i insGiftQuotaAssignLogDetailDo) WriteDB() *insGiftQuotaAssignLogDetailDo {
	return i.Clauses(dbresolver.Write)
}

func (i insGiftQuotaAssignLogDetailDo) Session(config *gorm.Session) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Session(config))
}

func (i insGiftQuotaAssignLogDetailDo) Clauses(conds ...clause.Expression) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Returning(value interface{}, columns ...string) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insGiftQuotaAssignLogDetailDo) Not(conds ...gen.Condition) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Or(conds ...gen.Condition) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Select(conds ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Where(conds ...gen.Condition) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Order(conds ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Distinct(cols ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insGiftQuotaAssignLogDetailDo) Omit(cols ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insGiftQuotaAssignLogDetailDo) Join(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insGiftQuotaAssignLogDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insGiftQuotaAssignLogDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insGiftQuotaAssignLogDetailDo) Group(cols ...field.Expr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insGiftQuotaAssignLogDetailDo) Having(conds ...gen.Condition) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insGiftQuotaAssignLogDetailDo) Limit(limit int) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insGiftQuotaAssignLogDetailDo) Offset(offset int) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insGiftQuotaAssignLogDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insGiftQuotaAssignLogDetailDo) Unscoped() *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insGiftQuotaAssignLogDetailDo) Create(values ...*insbuy.InsGiftQuotaAssignLogDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insGiftQuotaAssignLogDetailDo) CreateInBatches(values []*insbuy.InsGiftQuotaAssignLogDetail, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insGiftQuotaAssignLogDetailDo) Save(values ...*insbuy.InsGiftQuotaAssignLogDetail) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insGiftQuotaAssignLogDetailDo) First() (*insbuy.InsGiftQuotaAssignLogDetail, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignLogDetail), nil
	}
}

func (i insGiftQuotaAssignLogDetailDo) Take() (*insbuy.InsGiftQuotaAssignLogDetail, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignLogDetail), nil
	}
}

func (i insGiftQuotaAssignLogDetailDo) Last() (*insbuy.InsGiftQuotaAssignLogDetail, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignLogDetail), nil
	}
}

func (i insGiftQuotaAssignLogDetailDo) Find() ([]*insbuy.InsGiftQuotaAssignLogDetail, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsGiftQuotaAssignLogDetail), err
}

func (i insGiftQuotaAssignLogDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsGiftQuotaAssignLogDetail, err error) {
	buf := make([]*insbuy.InsGiftQuotaAssignLogDetail, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insGiftQuotaAssignLogDetailDo) FindInBatches(result *[]*insbuy.InsGiftQuotaAssignLogDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insGiftQuotaAssignLogDetailDo) Attrs(attrs ...field.AssignExpr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insGiftQuotaAssignLogDetailDo) Assign(attrs ...field.AssignExpr) *insGiftQuotaAssignLogDetailDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insGiftQuotaAssignLogDetailDo) Joins(fields ...field.RelationField) *insGiftQuotaAssignLogDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insGiftQuotaAssignLogDetailDo) Preload(fields ...field.RelationField) *insGiftQuotaAssignLogDetailDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insGiftQuotaAssignLogDetailDo) FirstOrInit() (*insbuy.InsGiftQuotaAssignLogDetail, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignLogDetail), nil
	}
}

func (i insGiftQuotaAssignLogDetailDo) FirstOrCreate() (*insbuy.InsGiftQuotaAssignLogDetail, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsGiftQuotaAssignLogDetail), nil
	}
}

func (i insGiftQuotaAssignLogDetailDo) FindByPage(offset int, limit int) (result []*insbuy.InsGiftQuotaAssignLogDetail, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insGiftQuotaAssignLogDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insGiftQuotaAssignLogDetailDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insGiftQuotaAssignLogDetailDo) Delete(models ...*insbuy.InsGiftQuotaAssignLogDetail) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insGiftQuotaAssignLogDetailDo) withDO(do gen.Dao) *insGiftQuotaAssignLogDetailDo {
	i.DO = *do.(*gen.DO)
	return i
}
