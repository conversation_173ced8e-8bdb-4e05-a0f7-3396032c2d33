// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradePay(db *gorm.DB, opts ...gen.DOOption) insTradePay {
	_insTradePay := insTradePay{}

	_insTradePay.insTradePayDo.UseDB(db, opts...)
	_insTradePay.insTradePayDo.UseModel(&insbuy.InsTradePay{})

	tableName := _insTradePay.insTradePayDo.TableName()
	_insTradePay.ALL = field.NewAsterisk(tableName)
	_insTradePay.ID = field.NewUint64(tableName, "id")
	_insTradePay.Amount = field.NewFloat64(tableName, "amount")
	_insTradePay.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradePay.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradePay.DeletedAt = field.NewField(tableName, "deleted_at")
	_insTradePay.StoreId = field.NewUint(tableName, "store_id")
	_insTradePay.PayStatus = field.NewUint(tableName, "pay_status")
	_insTradePay.PaySn = field.NewString(tableName, "pay_sn")
	_insTradePay.PayCode = field.NewString(tableName, "pay_code")
	_insTradePay.PayId = field.NewUint(tableName, "pay_id")
	_insTradePay.BusinessId = field.NewUint(tableName, "business_id")
	_insTradePay.BusinessCode = field.NewString(tableName, "business_code")
	_insTradePay.CallbackStatus = field.NewInt(tableName, "callback_status")
	_insTradePay.TradeId = field.NewUint64(tableName, "trade_id")
	_insTradePay.PayTime = field.NewTime(tableName, "pay_time")
	_insTradePay.PayAmount = field.NewFloat64(tableName, "pay_amount")
	_insTradePay.DiscountAmount = field.NewFloat64(tableName, "discount_amount")
	_insTradePay.RealAmount = field.NewFloat64(tableName, "real_amount")
	_insTradePay.ThirdPaySn = field.NewString(tableName, "third_pay_sn")
	_insTradePay.ChannelPaySn = field.NewString(tableName, "channel_pay_sn")
	_insTradePay.TrxCode = field.NewString(tableName, "trx_code")
	_insTradePay.RefundAmount = field.NewFloat64(tableName, "refund_amount")
	_insTradePay.TraderMemberId = field.NewUint(tableName, "trader_member_id")
	_insTradePay.IsReverse = field.NewInt(tableName, "is_reverse")
	_insTradePay.PayParams = field.NewString(tableName, "pay_params")

	_insTradePay.fillFieldMap()

	return _insTradePay
}

type insTradePay struct {
	insTradePayDo

	ALL            field.Asterisk
	ID             field.Uint64
	Amount         field.Float64
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	StoreId        field.Uint
	PayStatus      field.Uint
	PaySn          field.String
	PayCode        field.String
	PayId          field.Uint
	BusinessId     field.Uint
	BusinessCode   field.String
	CallbackStatus field.Int
	TradeId        field.Uint64
	PayTime        field.Time
	PayAmount      field.Float64
	DiscountAmount field.Float64
	RealAmount     field.Float64
	ThirdPaySn     field.String
	ChannelPaySn   field.String
	TrxCode        field.String
	RefundAmount   field.Float64
	TraderMemberId field.Uint
	IsReverse      field.Int
	PayParams      field.String

	fieldMap map[string]field.Expr
}

func (i insTradePay) Table(newTableName string) *insTradePay {
	i.insTradePayDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradePay) As(alias string) *insTradePay {
	i.insTradePayDo.DO = *(i.insTradePayDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradePay) updateTableName(table string) *insTradePay {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint64(table, "id")
	i.Amount = field.NewFloat64(table, "amount")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.PayStatus = field.NewUint(table, "pay_status")
	i.PaySn = field.NewString(table, "pay_sn")
	i.PayCode = field.NewString(table, "pay_code")
	i.PayId = field.NewUint(table, "pay_id")
	i.BusinessId = field.NewUint(table, "business_id")
	i.BusinessCode = field.NewString(table, "business_code")
	i.CallbackStatus = field.NewInt(table, "callback_status")
	i.TradeId = field.NewUint64(table, "trade_id")
	i.PayTime = field.NewTime(table, "pay_time")
	i.PayAmount = field.NewFloat64(table, "pay_amount")
	i.DiscountAmount = field.NewFloat64(table, "discount_amount")
	i.RealAmount = field.NewFloat64(table, "real_amount")
	i.ThirdPaySn = field.NewString(table, "third_pay_sn")
	i.ChannelPaySn = field.NewString(table, "channel_pay_sn")
	i.TrxCode = field.NewString(table, "trx_code")
	i.RefundAmount = field.NewFloat64(table, "refund_amount")
	i.TraderMemberId = field.NewUint(table, "trader_member_id")
	i.IsReverse = field.NewInt(table, "is_reverse")
	i.PayParams = field.NewString(table, "pay_params")

	i.fillFieldMap()

	return i
}

func (i *insTradePay) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradePay) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 25)
	i.fieldMap["id"] = i.ID
	i.fieldMap["amount"] = i.Amount
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["pay_status"] = i.PayStatus
	i.fieldMap["pay_sn"] = i.PaySn
	i.fieldMap["pay_code"] = i.PayCode
	i.fieldMap["pay_id"] = i.PayId
	i.fieldMap["business_id"] = i.BusinessId
	i.fieldMap["business_code"] = i.BusinessCode
	i.fieldMap["callback_status"] = i.CallbackStatus
	i.fieldMap["trade_id"] = i.TradeId
	i.fieldMap["pay_time"] = i.PayTime
	i.fieldMap["pay_amount"] = i.PayAmount
	i.fieldMap["discount_amount"] = i.DiscountAmount
	i.fieldMap["real_amount"] = i.RealAmount
	i.fieldMap["third_pay_sn"] = i.ThirdPaySn
	i.fieldMap["channel_pay_sn"] = i.ChannelPaySn
	i.fieldMap["trx_code"] = i.TrxCode
	i.fieldMap["refund_amount"] = i.RefundAmount
	i.fieldMap["trader_member_id"] = i.TraderMemberId
	i.fieldMap["is_reverse"] = i.IsReverse
	i.fieldMap["pay_params"] = i.PayParams
}

func (i insTradePay) clone(db *gorm.DB) insTradePay {
	i.insTradePayDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradePay) replaceDB(db *gorm.DB) insTradePay {
	i.insTradePayDo.ReplaceDB(db)
	return i
}

type insTradePayDo struct{ gen.DO }

func (i insTradePayDo) Debug() *insTradePayDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradePayDo) WithContext(ctx context.Context) *insTradePayDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradePayDo) ReadDB() *insTradePayDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradePayDo) WriteDB() *insTradePayDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradePayDo) Session(config *gorm.Session) *insTradePayDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradePayDo) Clauses(conds ...clause.Expression) *insTradePayDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradePayDo) Returning(value interface{}, columns ...string) *insTradePayDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradePayDo) Not(conds ...gen.Condition) *insTradePayDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradePayDo) Or(conds ...gen.Condition) *insTradePayDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradePayDo) Select(conds ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradePayDo) Where(conds ...gen.Condition) *insTradePayDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradePayDo) Order(conds ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradePayDo) Distinct(cols ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradePayDo) Omit(cols ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradePayDo) Join(table schema.Tabler, on ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradePayDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradePayDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradePayDo) Group(cols ...field.Expr) *insTradePayDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradePayDo) Having(conds ...gen.Condition) *insTradePayDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradePayDo) Limit(limit int) *insTradePayDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradePayDo) Offset(offset int) *insTradePayDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradePayDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradePayDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradePayDo) Unscoped() *insTradePayDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradePayDo) Create(values ...*insbuy.InsTradePay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradePayDo) CreateInBatches(values []*insbuy.InsTradePay, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradePayDo) Save(values ...*insbuy.InsTradePay) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradePayDo) First() (*insbuy.InsTradePay, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradePay), nil
	}
}

func (i insTradePayDo) Take() (*insbuy.InsTradePay, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradePay), nil
	}
}

func (i insTradePayDo) Last() (*insbuy.InsTradePay, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradePay), nil
	}
}

func (i insTradePayDo) Find() ([]*insbuy.InsTradePay, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradePay), err
}

func (i insTradePayDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradePay, err error) {
	buf := make([]*insbuy.InsTradePay, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradePayDo) FindInBatches(result *[]*insbuy.InsTradePay, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradePayDo) Attrs(attrs ...field.AssignExpr) *insTradePayDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradePayDo) Assign(attrs ...field.AssignExpr) *insTradePayDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradePayDo) Joins(fields ...field.RelationField) *insTradePayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradePayDo) Preload(fields ...field.RelationField) *insTradePayDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradePayDo) FirstOrInit() (*insbuy.InsTradePay, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradePay), nil
	}
}

func (i insTradePayDo) FirstOrCreate() (*insbuy.InsTradePay, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradePay), nil
	}
}

func (i insTradePayDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradePay, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradePayDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradePayDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradePayDo) Delete(models ...*insbuy.InsTradePay) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradePayDo) withDO(do gen.Dao) *insTradePayDo {
	i.DO = *do.(*gen.DO)
	return i
}
