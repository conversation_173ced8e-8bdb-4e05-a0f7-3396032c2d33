// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsTradeReverseLog(db *gorm.DB, opts ...gen.DOOption) insTradeReverseLog {
	_insTradeReverseLog := insTradeReverseLog{}

	_insTradeReverseLog.insTradeReverseLogDo.UseDB(db, opts...)
	_insTradeReverseLog.insTradeReverseLogDo.UseModel(&insbuy.InsTradeReverseLog{})

	tableName := _insTradeReverseLog.insTradeReverseLogDo.TableName()
	_insTradeReverseLog.ALL = field.NewAsterisk(tableName)
	_insTradeReverseLog.ID = field.NewUint(tableName, "id")
	_insTradeReverseLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insTradeReverseLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insTradeReverseLog.ReverseType = field.NewInt(tableName, "reverse_type")
	_insTradeReverseLog.ReverseStatus = field.NewInt(tableName, "reverse_status")
	_insTradeReverseLog.OldTradePayId = field.NewUint64(tableName, "old_trade_id")
	_insTradeReverseLog.NewTradePayId = field.NewUint64(tableName, "new_trade_id")
	_insTradeReverseLog.ReverseAmount = field.NewFloat64(tableName, "reverse_amount")
	_insTradeReverseLog.ReversePayId = field.NewUint(tableName, "reverse_pay_id")
	_insTradeReverseLog.ReversePayCode = field.NewString(tableName, "reverse_pay_code")
	_insTradeReverseLog.OperatorParams = field.NewString(tableName, "operator_params")
	_insTradeReverseLog.OperatorId = field.NewUint(tableName, "operator_id")
	_insTradeReverseLog.RemarkExt = field.NewField(tableName, "remark_ext")

	_insTradeReverseLog.fillFieldMap()

	return _insTradeReverseLog
}

type insTradeReverseLog struct {
	insTradeReverseLogDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	ReverseType    field.Int
	ReverseStatus  field.Int
	OldTradePayId  field.Uint64
	NewTradePayId  field.Uint64
	ReverseAmount  field.Float64
	ReversePayId   field.Uint
	ReversePayCode field.String
	OperatorParams field.String
	OperatorId     field.Uint
	RemarkExt      field.Field

	fieldMap map[string]field.Expr
}

func (i insTradeReverseLog) Table(newTableName string) *insTradeReverseLog {
	i.insTradeReverseLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insTradeReverseLog) As(alias string) *insTradeReverseLog {
	i.insTradeReverseLogDo.DO = *(i.insTradeReverseLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insTradeReverseLog) updateTableName(table string) *insTradeReverseLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.ReverseType = field.NewInt(table, "reverse_type")
	i.ReverseStatus = field.NewInt(table, "reverse_status")
	i.OldTradePayId = field.NewUint64(table, "old_trade_id")
	i.NewTradePayId = field.NewUint64(table, "new_trade_id")
	i.ReverseAmount = field.NewFloat64(table, "reverse_amount")
	i.ReversePayId = field.NewUint(table, "reverse_pay_id")
	i.ReversePayCode = field.NewString(table, "reverse_pay_code")
	i.OperatorParams = field.NewString(table, "operator_params")
	i.OperatorId = field.NewUint(table, "operator_id")
	i.RemarkExt = field.NewField(table, "remark_ext")

	i.fillFieldMap()

	return i
}

func (i *insTradeReverseLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insTradeReverseLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 13)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["reverse_type"] = i.ReverseType
	i.fieldMap["reverse_status"] = i.ReverseStatus
	i.fieldMap["old_trade_id"] = i.OldTradePayId
	i.fieldMap["new_trade_id"] = i.NewTradePayId
	i.fieldMap["reverse_amount"] = i.ReverseAmount
	i.fieldMap["reverse_pay_id"] = i.ReversePayId
	i.fieldMap["reverse_pay_code"] = i.ReversePayCode
	i.fieldMap["operator_params"] = i.OperatorParams
	i.fieldMap["operator_id"] = i.OperatorId
	i.fieldMap["remark_ext"] = i.RemarkExt
}

func (i insTradeReverseLog) clone(db *gorm.DB) insTradeReverseLog {
	i.insTradeReverseLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insTradeReverseLog) replaceDB(db *gorm.DB) insTradeReverseLog {
	i.insTradeReverseLogDo.ReplaceDB(db)
	return i
}

type insTradeReverseLogDo struct{ gen.DO }

func (i insTradeReverseLogDo) Debug() *insTradeReverseLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insTradeReverseLogDo) WithContext(ctx context.Context) *insTradeReverseLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insTradeReverseLogDo) ReadDB() *insTradeReverseLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insTradeReverseLogDo) WriteDB() *insTradeReverseLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insTradeReverseLogDo) Session(config *gorm.Session) *insTradeReverseLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insTradeReverseLogDo) Clauses(conds ...clause.Expression) *insTradeReverseLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insTradeReverseLogDo) Returning(value interface{}, columns ...string) *insTradeReverseLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insTradeReverseLogDo) Not(conds ...gen.Condition) *insTradeReverseLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insTradeReverseLogDo) Or(conds ...gen.Condition) *insTradeReverseLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insTradeReverseLogDo) Select(conds ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insTradeReverseLogDo) Where(conds ...gen.Condition) *insTradeReverseLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insTradeReverseLogDo) Order(conds ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insTradeReverseLogDo) Distinct(cols ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insTradeReverseLogDo) Omit(cols ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insTradeReverseLogDo) Join(table schema.Tabler, on ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insTradeReverseLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insTradeReverseLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insTradeReverseLogDo) Group(cols ...field.Expr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insTradeReverseLogDo) Having(conds ...gen.Condition) *insTradeReverseLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insTradeReverseLogDo) Limit(limit int) *insTradeReverseLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insTradeReverseLogDo) Offset(offset int) *insTradeReverseLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insTradeReverseLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insTradeReverseLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insTradeReverseLogDo) Unscoped() *insTradeReverseLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insTradeReverseLogDo) Create(values ...*insbuy.InsTradeReverseLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insTradeReverseLogDo) CreateInBatches(values []*insbuy.InsTradeReverseLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insTradeReverseLogDo) Save(values ...*insbuy.InsTradeReverseLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insTradeReverseLogDo) First() (*insbuy.InsTradeReverseLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeReverseLog), nil
	}
}

func (i insTradeReverseLogDo) Take() (*insbuy.InsTradeReverseLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeReverseLog), nil
	}
}

func (i insTradeReverseLogDo) Last() (*insbuy.InsTradeReverseLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeReverseLog), nil
	}
}

func (i insTradeReverseLogDo) Find() ([]*insbuy.InsTradeReverseLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsTradeReverseLog), err
}

func (i insTradeReverseLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsTradeReverseLog, err error) {
	buf := make([]*insbuy.InsTradeReverseLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insTradeReverseLogDo) FindInBatches(result *[]*insbuy.InsTradeReverseLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insTradeReverseLogDo) Attrs(attrs ...field.AssignExpr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insTradeReverseLogDo) Assign(attrs ...field.AssignExpr) *insTradeReverseLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insTradeReverseLogDo) Joins(fields ...field.RelationField) *insTradeReverseLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insTradeReverseLogDo) Preload(fields ...field.RelationField) *insTradeReverseLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insTradeReverseLogDo) FirstOrInit() (*insbuy.InsTradeReverseLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeReverseLog), nil
	}
}

func (i insTradeReverseLogDo) FirstOrCreate() (*insbuy.InsTradeReverseLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsTradeReverseLog), nil
	}
}

func (i insTradeReverseLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsTradeReverseLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insTradeReverseLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insTradeReverseLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insTradeReverseLogDo) Delete(models ...*insbuy.InsTradeReverseLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insTradeReverseLogDo) withDO(do gen.Dao) *insTradeReverseLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
