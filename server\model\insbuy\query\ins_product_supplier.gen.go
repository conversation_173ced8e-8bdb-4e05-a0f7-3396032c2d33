// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductSupplier(db *gorm.DB, opts ...gen.DOOption) insProductSupplier {
	_insProductSupplier := insProductSupplier{}

	_insProductSupplier.insProductSupplierDo.UseDB(db, opts...)
	_insProductSupplier.insProductSupplierDo.UseModel(&insbuy.InsProductSupplier{})

	tableName := _insProductSupplier.insProductSupplierDo.TableName()
	_insProductSupplier.ALL = field.NewAsterisk(tableName)
	_insProductSupplier.Id = field.NewInt(tableName, "id")
	_insProductSupplier.SupplierId = field.NewInt(tableName, "supplier_id")
	_insProductSupplier.ProductId = field.NewInt(tableName, "product_id")

	_insProductSupplier.fillFieldMap()

	return _insProductSupplier
}

type insProductSupplier struct {
	insProductSupplierDo

	ALL        field.Asterisk
	Id         field.Int
	SupplierId field.Int
	ProductId  field.Int

	fieldMap map[string]field.Expr
}

func (i insProductSupplier) Table(newTableName string) *insProductSupplier {
	i.insProductSupplierDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductSupplier) As(alias string) *insProductSupplier {
	i.insProductSupplierDo.DO = *(i.insProductSupplierDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductSupplier) updateTableName(table string) *insProductSupplier {
	i.ALL = field.NewAsterisk(table)
	i.Id = field.NewInt(table, "id")
	i.SupplierId = field.NewInt(table, "supplier_id")
	i.ProductId = field.NewInt(table, "product_id")

	i.fillFieldMap()

	return i
}

func (i *insProductSupplier) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductSupplier) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 3)
	i.fieldMap["id"] = i.Id
	i.fieldMap["supplier_id"] = i.SupplierId
	i.fieldMap["product_id"] = i.ProductId
}

func (i insProductSupplier) clone(db *gorm.DB) insProductSupplier {
	i.insProductSupplierDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductSupplier) replaceDB(db *gorm.DB) insProductSupplier {
	i.insProductSupplierDo.ReplaceDB(db)
	return i
}

type insProductSupplierDo struct{ gen.DO }

func (i insProductSupplierDo) Debug() *insProductSupplierDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductSupplierDo) WithContext(ctx context.Context) *insProductSupplierDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductSupplierDo) ReadDB() *insProductSupplierDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductSupplierDo) WriteDB() *insProductSupplierDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductSupplierDo) Session(config *gorm.Session) *insProductSupplierDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductSupplierDo) Clauses(conds ...clause.Expression) *insProductSupplierDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductSupplierDo) Returning(value interface{}, columns ...string) *insProductSupplierDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductSupplierDo) Not(conds ...gen.Condition) *insProductSupplierDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductSupplierDo) Or(conds ...gen.Condition) *insProductSupplierDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductSupplierDo) Select(conds ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductSupplierDo) Where(conds ...gen.Condition) *insProductSupplierDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductSupplierDo) Order(conds ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductSupplierDo) Distinct(cols ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductSupplierDo) Omit(cols ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductSupplierDo) Join(table schema.Tabler, on ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductSupplierDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductSupplierDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductSupplierDo) Group(cols ...field.Expr) *insProductSupplierDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductSupplierDo) Having(conds ...gen.Condition) *insProductSupplierDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductSupplierDo) Limit(limit int) *insProductSupplierDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductSupplierDo) Offset(offset int) *insProductSupplierDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductSupplierDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductSupplierDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductSupplierDo) Unscoped() *insProductSupplierDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductSupplierDo) Create(values ...*insbuy.InsProductSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductSupplierDo) CreateInBatches(values []*insbuy.InsProductSupplier, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductSupplierDo) Save(values ...*insbuy.InsProductSupplier) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductSupplierDo) First() (*insbuy.InsProductSupplier, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSupplier), nil
	}
}

func (i insProductSupplierDo) Take() (*insbuy.InsProductSupplier, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSupplier), nil
	}
}

func (i insProductSupplierDo) Last() (*insbuy.InsProductSupplier, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSupplier), nil
	}
}

func (i insProductSupplierDo) Find() ([]*insbuy.InsProductSupplier, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductSupplier), err
}

func (i insProductSupplierDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductSupplier, err error) {
	buf := make([]*insbuy.InsProductSupplier, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductSupplierDo) FindInBatches(result *[]*insbuy.InsProductSupplier, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductSupplierDo) Attrs(attrs ...field.AssignExpr) *insProductSupplierDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductSupplierDo) Assign(attrs ...field.AssignExpr) *insProductSupplierDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductSupplierDo) Joins(fields ...field.RelationField) *insProductSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductSupplierDo) Preload(fields ...field.RelationField) *insProductSupplierDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductSupplierDo) FirstOrInit() (*insbuy.InsProductSupplier, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSupplier), nil
	}
}

func (i insProductSupplierDo) FirstOrCreate() (*insbuy.InsProductSupplier, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductSupplier), nil
	}
}

func (i insProductSupplierDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductSupplier, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductSupplierDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductSupplierDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductSupplierDo) Delete(models ...*insbuy.InsProductSupplier) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductSupplierDo) withDO(do gen.Dao) *insProductSupplierDo {
	i.DO = *do.(*gen.DO)
	return i
}
