// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsReportField(db *gorm.DB, opts ...gen.DOOption) insReportField {
	_insReportField := insReportField{}

	_insReportField.insReportFieldDo.UseDB(db, opts...)
	_insReportField.insReportFieldDo.UseModel(&insbuy.InsReportField{})

	tableName := _insReportField.insReportFieldDo.TableName()
	_insReportField.ALL = field.NewAsterisk(tableName)
	_insReportField.ID = field.NewUint(tableName, "id")
	_insReportField.CreatedAt = field.NewTime(tableName, "created_at")
	_insReportField.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insReportField.DeletedAt = field.NewField(tableName, "deleted_at")
	_insReportField.ReportID = field.NewUint(tableName, "report_id")
	_insReportField.FieldName = field.NewString(tableName, "field_name")
	_insReportField.FieldExpression = field.NewString(tableName, "field_expression")
	_insReportField.Alias_ = field.NewString(tableName, "alias")
	_insReportField.AggregationType = field.NewString(tableName, "aggregation_type")
	_insReportField.Dependency = field.NewString(tableName, "dependency")

	_insReportField.fillFieldMap()

	return _insReportField
}

type insReportField struct {
	insReportFieldDo

	ALL             field.Asterisk
	ID              field.Uint
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field
	ReportID        field.Uint
	FieldName       field.String
	FieldExpression field.String
	Alias_          field.String
	AggregationType field.String
	Dependency      field.String

	fieldMap map[string]field.Expr
}

func (i insReportField) Table(newTableName string) *insReportField {
	i.insReportFieldDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insReportField) As(alias string) *insReportField {
	i.insReportFieldDo.DO = *(i.insReportFieldDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insReportField) updateTableName(table string) *insReportField {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ReportID = field.NewUint(table, "report_id")
	i.FieldName = field.NewString(table, "field_name")
	i.FieldExpression = field.NewString(table, "field_expression")
	i.Alias_ = field.NewString(table, "alias")
	i.AggregationType = field.NewString(table, "aggregation_type")
	i.Dependency = field.NewString(table, "dependency")

	i.fillFieldMap()

	return i
}

func (i *insReportField) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insReportField) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["report_id"] = i.ReportID
	i.fieldMap["field_name"] = i.FieldName
	i.fieldMap["field_expression"] = i.FieldExpression
	i.fieldMap["alias"] = i.Alias_
	i.fieldMap["aggregation_type"] = i.AggregationType
	i.fieldMap["dependency"] = i.Dependency
}

func (i insReportField) clone(db *gorm.DB) insReportField {
	i.insReportFieldDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insReportField) replaceDB(db *gorm.DB) insReportField {
	i.insReportFieldDo.ReplaceDB(db)
	return i
}

type insReportFieldDo struct{ gen.DO }

func (i insReportFieldDo) Debug() *insReportFieldDo {
	return i.withDO(i.DO.Debug())
}

func (i insReportFieldDo) WithContext(ctx context.Context) *insReportFieldDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insReportFieldDo) ReadDB() *insReportFieldDo {
	return i.Clauses(dbresolver.Read)
}

func (i insReportFieldDo) WriteDB() *insReportFieldDo {
	return i.Clauses(dbresolver.Write)
}

func (i insReportFieldDo) Session(config *gorm.Session) *insReportFieldDo {
	return i.withDO(i.DO.Session(config))
}

func (i insReportFieldDo) Clauses(conds ...clause.Expression) *insReportFieldDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insReportFieldDo) Returning(value interface{}, columns ...string) *insReportFieldDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insReportFieldDo) Not(conds ...gen.Condition) *insReportFieldDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insReportFieldDo) Or(conds ...gen.Condition) *insReportFieldDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insReportFieldDo) Select(conds ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insReportFieldDo) Where(conds ...gen.Condition) *insReportFieldDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insReportFieldDo) Order(conds ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insReportFieldDo) Distinct(cols ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insReportFieldDo) Omit(cols ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insReportFieldDo) Join(table schema.Tabler, on ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insReportFieldDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insReportFieldDo) RightJoin(table schema.Tabler, on ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insReportFieldDo) Group(cols ...field.Expr) *insReportFieldDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insReportFieldDo) Having(conds ...gen.Condition) *insReportFieldDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insReportFieldDo) Limit(limit int) *insReportFieldDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insReportFieldDo) Offset(offset int) *insReportFieldDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insReportFieldDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insReportFieldDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insReportFieldDo) Unscoped() *insReportFieldDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insReportFieldDo) Create(values ...*insbuy.InsReportField) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insReportFieldDo) CreateInBatches(values []*insbuy.InsReportField, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insReportFieldDo) Save(values ...*insbuy.InsReportField) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insReportFieldDo) First() (*insbuy.InsReportField, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportField), nil
	}
}

func (i insReportFieldDo) Take() (*insbuy.InsReportField, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportField), nil
	}
}

func (i insReportFieldDo) Last() (*insbuy.InsReportField, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportField), nil
	}
}

func (i insReportFieldDo) Find() ([]*insbuy.InsReportField, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsReportField), err
}

func (i insReportFieldDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsReportField, err error) {
	buf := make([]*insbuy.InsReportField, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insReportFieldDo) FindInBatches(result *[]*insbuy.InsReportField, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insReportFieldDo) Attrs(attrs ...field.AssignExpr) *insReportFieldDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insReportFieldDo) Assign(attrs ...field.AssignExpr) *insReportFieldDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insReportFieldDo) Joins(fields ...field.RelationField) *insReportFieldDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insReportFieldDo) Preload(fields ...field.RelationField) *insReportFieldDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insReportFieldDo) FirstOrInit() (*insbuy.InsReportField, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportField), nil
	}
}

func (i insReportFieldDo) FirstOrCreate() (*insbuy.InsReportField, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsReportField), nil
	}
}

func (i insReportFieldDo) FindByPage(offset int, limit int) (result []*insbuy.InsReportField, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insReportFieldDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insReportFieldDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insReportFieldDo) Delete(models ...*insbuy.InsReportField) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insReportFieldDo) withDO(do gen.Dao) *insReportFieldDo {
	i.DO = *do.(*gen.DO)
	return i
}
