// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotion(db *gorm.DB, opts ...gen.DOOption) insProductPromotion {
	_insProductPromotion := insProductPromotion{}

	_insProductPromotion.insProductPromotionDo.UseDB(db, opts...)
	_insProductPromotion.insProductPromotionDo.UseModel(&insbuy.InsProductPromotion{})

	tableName := _insProductPromotion.insProductPromotionDo.TableName()
	_insProductPromotion.ALL = field.NewAsterisk(tableName)
	_insProductPromotion.ID = field.NewUint(tableName, "id")
	_insProductPromotion.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotion.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotion.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotion.CreatedBy = field.NewUint(tableName, "created_by")
	_insProductPromotion.UpdatedBy = field.NewUint(tableName, "updated_by")
	_insProductPromotion.DeletedBy = field.NewUint(tableName, "deleted_by")
	_insProductPromotion.StoreId = field.NewUint(tableName, "store_id")
	_insProductPromotion.Title = field.NewString(tableName, "title")
	_insProductPromotion.Status = field.NewInt(tableName, "status")
	_insProductPromotion.PromotionType = field.NewInt(tableName, "promotion_type")
	_insProductPromotion.AllDay = field.NewInt(tableName, "all_day")
	_insProductPromotion.StartDate = field.NewTime(tableName, "start_date")
	_insProductPromotion.EndDate = field.NewTime(tableName, "end_date")
	_insProductPromotion.DiscountRate = field.NewFloat64(tableName, "discount_rate")
	_insProductPromotion.ApplicableTo = field.NewInt(tableName, "applicable_to")
	_insProductPromotion.Remark = field.NewString(tableName, "remark")

	_insProductPromotion.fillFieldMap()

	return _insProductPromotion
}

type insProductPromotion struct {
	insProductPromotionDo

	ALL           field.Asterisk
	ID            field.Uint
	CreatedAt     field.Time
	UpdatedAt     field.Time
	DeletedAt     field.Field
	CreatedBy     field.Uint
	UpdatedBy     field.Uint
	DeletedBy     field.Uint
	StoreId       field.Uint
	Title         field.String
	Status        field.Int
	PromotionType field.Int
	AllDay        field.Int
	StartDate     field.Time
	EndDate       field.Time
	DiscountRate  field.Float64
	ApplicableTo  field.Int
	Remark        field.String

	fieldMap map[string]field.Expr
}

func (i insProductPromotion) Table(newTableName string) *insProductPromotion {
	i.insProductPromotionDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotion) As(alias string) *insProductPromotion {
	i.insProductPromotionDo.DO = *(i.insProductPromotionDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotion) updateTableName(table string) *insProductPromotion {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.CreatedBy = field.NewUint(table, "created_by")
	i.UpdatedBy = field.NewUint(table, "updated_by")
	i.DeletedBy = field.NewUint(table, "deleted_by")
	i.StoreId = field.NewUint(table, "store_id")
	i.Title = field.NewString(table, "title")
	i.Status = field.NewInt(table, "status")
	i.PromotionType = field.NewInt(table, "promotion_type")
	i.AllDay = field.NewInt(table, "all_day")
	i.StartDate = field.NewTime(table, "start_date")
	i.EndDate = field.NewTime(table, "end_date")
	i.DiscountRate = field.NewFloat64(table, "discount_rate")
	i.ApplicableTo = field.NewInt(table, "applicable_to")
	i.Remark = field.NewString(table, "remark")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotion) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotion) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 17)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["created_by"] = i.CreatedBy
	i.fieldMap["updated_by"] = i.UpdatedBy
	i.fieldMap["deleted_by"] = i.DeletedBy
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["title"] = i.Title
	i.fieldMap["status"] = i.Status
	i.fieldMap["promotion_type"] = i.PromotionType
	i.fieldMap["all_day"] = i.AllDay
	i.fieldMap["start_date"] = i.StartDate
	i.fieldMap["end_date"] = i.EndDate
	i.fieldMap["discount_rate"] = i.DiscountRate
	i.fieldMap["applicable_to"] = i.ApplicableTo
	i.fieldMap["remark"] = i.Remark
}

func (i insProductPromotion) clone(db *gorm.DB) insProductPromotion {
	i.insProductPromotionDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotion) replaceDB(db *gorm.DB) insProductPromotion {
	i.insProductPromotionDo.ReplaceDB(db)
	return i
}

type insProductPromotionDo struct{ gen.DO }

func (i insProductPromotionDo) Debug() *insProductPromotionDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionDo) WithContext(ctx context.Context) *insProductPromotionDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionDo) ReadDB() *insProductPromotionDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionDo) WriteDB() *insProductPromotionDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionDo) Session(config *gorm.Session) *insProductPromotionDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionDo) Clauses(conds ...clause.Expression) *insProductPromotionDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionDo) Returning(value interface{}, columns ...string) *insProductPromotionDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionDo) Not(conds ...gen.Condition) *insProductPromotionDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionDo) Or(conds ...gen.Condition) *insProductPromotionDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionDo) Select(conds ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionDo) Where(conds ...gen.Condition) *insProductPromotionDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionDo) Order(conds ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionDo) Distinct(cols ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionDo) Omit(cols ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionDo) Group(cols ...field.Expr) *insProductPromotionDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionDo) Having(conds ...gen.Condition) *insProductPromotionDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionDo) Limit(limit int) *insProductPromotionDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionDo) Offset(offset int) *insProductPromotionDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionDo) Unscoped() *insProductPromotionDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionDo) Create(values ...*insbuy.InsProductPromotion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionDo) CreateInBatches(values []*insbuy.InsProductPromotion, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionDo) Save(values ...*insbuy.InsProductPromotion) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionDo) First() (*insbuy.InsProductPromotion, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotion), nil
	}
}

func (i insProductPromotionDo) Take() (*insbuy.InsProductPromotion, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotion), nil
	}
}

func (i insProductPromotionDo) Last() (*insbuy.InsProductPromotion, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotion), nil
	}
}

func (i insProductPromotionDo) Find() ([]*insbuy.InsProductPromotion, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotion), err
}

func (i insProductPromotionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotion, err error) {
	buf := make([]*insbuy.InsProductPromotion, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionDo) FindInBatches(result *[]*insbuy.InsProductPromotion, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionDo) Assign(attrs ...field.AssignExpr) *insProductPromotionDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionDo) Joins(fields ...field.RelationField) *insProductPromotionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionDo) Preload(fields ...field.RelationField) *insProductPromotionDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionDo) FirstOrInit() (*insbuy.InsProductPromotion, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotion), nil
	}
}

func (i insProductPromotionDo) FirstOrCreate() (*insbuy.InsProductPromotion, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotion), nil
	}
}

func (i insProductPromotionDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotion, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionDo) Delete(models ...*insbuy.InsProductPromotion) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionDo) withDO(do gen.Dao) *insProductPromotionDo {
	i.DO = *do.(*gen.DO)
	return i
}
