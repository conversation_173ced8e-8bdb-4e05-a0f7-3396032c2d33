// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsProductPromotionTime(db *gorm.DB, opts ...gen.DOOption) insProductPromotionTime {
	_insProductPromotionTime := insProductPromotionTime{}

	_insProductPromotionTime.insProductPromotionTimeDo.UseDB(db, opts...)
	_insProductPromotionTime.insProductPromotionTimeDo.UseModel(&insbuy.InsProductPromotionTime{})

	tableName := _insProductPromotionTime.insProductPromotionTimeDo.TableName()
	_insProductPromotionTime.ALL = field.NewAsterisk(tableName)
	_insProductPromotionTime.ID = field.NewUint(tableName, "id")
	_insProductPromotionTime.CreatedAt = field.NewTime(tableName, "created_at")
	_insProductPromotionTime.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insProductPromotionTime.DeletedAt = field.NewField(tableName, "deleted_at")
	_insProductPromotionTime.PromotionId = field.NewUint(tableName, "promotion_id")
	_insProductPromotionTime.WeekDay = field.NewInt(tableName, "week_day")
	_insProductPromotionTime.TimeRanges = field.NewField(tableName, "time_ranges")

	_insProductPromotionTime.fillFieldMap()

	return _insProductPromotionTime
}

type insProductPromotionTime struct {
	insProductPromotionTimeDo

	ALL         field.Asterisk
	ID          field.Uint
	CreatedAt   field.Time
	UpdatedAt   field.Time
	DeletedAt   field.Field
	PromotionId field.Uint
	WeekDay     field.Int
	TimeRanges  field.Field

	fieldMap map[string]field.Expr
}

func (i insProductPromotionTime) Table(newTableName string) *insProductPromotionTime {
	i.insProductPromotionTimeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insProductPromotionTime) As(alias string) *insProductPromotionTime {
	i.insProductPromotionTimeDo.DO = *(i.insProductPromotionTimeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insProductPromotionTime) updateTableName(table string) *insProductPromotionTime {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.PromotionId = field.NewUint(table, "promotion_id")
	i.WeekDay = field.NewInt(table, "week_day")
	i.TimeRanges = field.NewField(table, "time_ranges")

	i.fillFieldMap()

	return i
}

func (i *insProductPromotionTime) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insProductPromotionTime) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 7)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["promotion_id"] = i.PromotionId
	i.fieldMap["week_day"] = i.WeekDay
	i.fieldMap["time_ranges"] = i.TimeRanges
}

func (i insProductPromotionTime) clone(db *gorm.DB) insProductPromotionTime {
	i.insProductPromotionTimeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insProductPromotionTime) replaceDB(db *gorm.DB) insProductPromotionTime {
	i.insProductPromotionTimeDo.ReplaceDB(db)
	return i
}

type insProductPromotionTimeDo struct{ gen.DO }

func (i insProductPromotionTimeDo) Debug() *insProductPromotionTimeDo {
	return i.withDO(i.DO.Debug())
}

func (i insProductPromotionTimeDo) WithContext(ctx context.Context) *insProductPromotionTimeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insProductPromotionTimeDo) ReadDB() *insProductPromotionTimeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insProductPromotionTimeDo) WriteDB() *insProductPromotionTimeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insProductPromotionTimeDo) Session(config *gorm.Session) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insProductPromotionTimeDo) Clauses(conds ...clause.Expression) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insProductPromotionTimeDo) Returning(value interface{}, columns ...string) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insProductPromotionTimeDo) Not(conds ...gen.Condition) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insProductPromotionTimeDo) Or(conds ...gen.Condition) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insProductPromotionTimeDo) Select(conds ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insProductPromotionTimeDo) Where(conds ...gen.Condition) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insProductPromotionTimeDo) Order(conds ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insProductPromotionTimeDo) Distinct(cols ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insProductPromotionTimeDo) Omit(cols ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insProductPromotionTimeDo) Join(table schema.Tabler, on ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insProductPromotionTimeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insProductPromotionTimeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insProductPromotionTimeDo) Group(cols ...field.Expr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insProductPromotionTimeDo) Having(conds ...gen.Condition) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insProductPromotionTimeDo) Limit(limit int) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insProductPromotionTimeDo) Offset(offset int) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insProductPromotionTimeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insProductPromotionTimeDo) Unscoped() *insProductPromotionTimeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insProductPromotionTimeDo) Create(values ...*insbuy.InsProductPromotionTime) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insProductPromotionTimeDo) CreateInBatches(values []*insbuy.InsProductPromotionTime, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insProductPromotionTimeDo) Save(values ...*insbuy.InsProductPromotionTime) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insProductPromotionTimeDo) First() (*insbuy.InsProductPromotionTime, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionTime), nil
	}
}

func (i insProductPromotionTimeDo) Take() (*insbuy.InsProductPromotionTime, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionTime), nil
	}
}

func (i insProductPromotionTimeDo) Last() (*insbuy.InsProductPromotionTime, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionTime), nil
	}
}

func (i insProductPromotionTimeDo) Find() ([]*insbuy.InsProductPromotionTime, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsProductPromotionTime), err
}

func (i insProductPromotionTimeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsProductPromotionTime, err error) {
	buf := make([]*insbuy.InsProductPromotionTime, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insProductPromotionTimeDo) FindInBatches(result *[]*insbuy.InsProductPromotionTime, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insProductPromotionTimeDo) Attrs(attrs ...field.AssignExpr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insProductPromotionTimeDo) Assign(attrs ...field.AssignExpr) *insProductPromotionTimeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insProductPromotionTimeDo) Joins(fields ...field.RelationField) *insProductPromotionTimeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insProductPromotionTimeDo) Preload(fields ...field.RelationField) *insProductPromotionTimeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insProductPromotionTimeDo) FirstOrInit() (*insbuy.InsProductPromotionTime, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionTime), nil
	}
}

func (i insProductPromotionTimeDo) FirstOrCreate() (*insbuy.InsProductPromotionTime, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsProductPromotionTime), nil
	}
}

func (i insProductPromotionTimeDo) FindByPage(offset int, limit int) (result []*insbuy.InsProductPromotionTime, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insProductPromotionTimeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insProductPromotionTimeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insProductPromotionTimeDo) Delete(models ...*insbuy.InsProductPromotionTime) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insProductPromotionTimeDo) withDO(do gen.Dao) *insProductPromotionTimeDo {
	i.DO = *do.(*gen.DO)
	return i
}
