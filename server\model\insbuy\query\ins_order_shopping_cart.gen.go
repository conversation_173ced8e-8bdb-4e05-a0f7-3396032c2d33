// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsOrderShoppingCart(db *gorm.DB, opts ...gen.DOOption) insOrderShoppingCart {
	_insOrderShoppingCart := insOrderShoppingCart{}

	_insOrderShoppingCart.insOrderShoppingCartDo.UseDB(db, opts...)
	_insOrderShoppingCart.insOrderShoppingCartDo.UseModel(&insbuy.InsOrderShoppingCart{})

	tableName := _insOrderShoppingCart.insOrderShoppingCartDo.TableName()
	_insOrderShoppingCart.ALL = field.NewAsterisk(tableName)
	_insOrderShoppingCart.ID = field.NewUint(tableName, "id")
	_insOrderShoppingCart.CreatedAt = field.NewTime(tableName, "created_at")
	_insOrderShoppingCart.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insOrderShoppingCart.CartNumber = field.NewString(tableName, "cart_number")
	_insOrderShoppingCart.DeskId = field.NewInt(tableName, "desk_id")
	_insOrderShoppingCart.SalesmanId = field.NewInt(tableName, "salesman_id")
	_insOrderShoppingCart.Notes = field.NewString(tableName, "notes")
	_insOrderShoppingCart.OpenDeskId = field.NewInt(tableName, "open_desk_id")

	_insOrderShoppingCart.fillFieldMap()

	return _insOrderShoppingCart
}

type insOrderShoppingCart struct {
	insOrderShoppingCartDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	CartNumber field.String
	DeskId     field.Int
	SalesmanId field.Int
	Notes      field.String
	OpenDeskId field.Int

	fieldMap map[string]field.Expr
}

func (i insOrderShoppingCart) Table(newTableName string) *insOrderShoppingCart {
	i.insOrderShoppingCartDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insOrderShoppingCart) As(alias string) *insOrderShoppingCart {
	i.insOrderShoppingCartDo.DO = *(i.insOrderShoppingCartDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insOrderShoppingCart) updateTableName(table string) *insOrderShoppingCart {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.CartNumber = field.NewString(table, "cart_number")
	i.DeskId = field.NewInt(table, "desk_id")
	i.SalesmanId = field.NewInt(table, "salesman_id")
	i.Notes = field.NewString(table, "notes")
	i.OpenDeskId = field.NewInt(table, "open_desk_id")

	i.fillFieldMap()

	return i
}

func (i *insOrderShoppingCart) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insOrderShoppingCart) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 8)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["cart_number"] = i.CartNumber
	i.fieldMap["desk_id"] = i.DeskId
	i.fieldMap["salesman_id"] = i.SalesmanId
	i.fieldMap["notes"] = i.Notes
	i.fieldMap["open_desk_id"] = i.OpenDeskId
}

func (i insOrderShoppingCart) clone(db *gorm.DB) insOrderShoppingCart {
	i.insOrderShoppingCartDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insOrderShoppingCart) replaceDB(db *gorm.DB) insOrderShoppingCart {
	i.insOrderShoppingCartDo.ReplaceDB(db)
	return i
}

type insOrderShoppingCartDo struct{ gen.DO }

func (i insOrderShoppingCartDo) Debug() *insOrderShoppingCartDo {
	return i.withDO(i.DO.Debug())
}

func (i insOrderShoppingCartDo) WithContext(ctx context.Context) *insOrderShoppingCartDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insOrderShoppingCartDo) ReadDB() *insOrderShoppingCartDo {
	return i.Clauses(dbresolver.Read)
}

func (i insOrderShoppingCartDo) WriteDB() *insOrderShoppingCartDo {
	return i.Clauses(dbresolver.Write)
}

func (i insOrderShoppingCartDo) Session(config *gorm.Session) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Session(config))
}

func (i insOrderShoppingCartDo) Clauses(conds ...clause.Expression) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insOrderShoppingCartDo) Returning(value interface{}, columns ...string) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insOrderShoppingCartDo) Not(conds ...gen.Condition) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insOrderShoppingCartDo) Or(conds ...gen.Condition) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insOrderShoppingCartDo) Select(conds ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insOrderShoppingCartDo) Where(conds ...gen.Condition) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insOrderShoppingCartDo) Order(conds ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insOrderShoppingCartDo) Distinct(cols ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insOrderShoppingCartDo) Omit(cols ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insOrderShoppingCartDo) Join(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insOrderShoppingCartDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insOrderShoppingCartDo) RightJoin(table schema.Tabler, on ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insOrderShoppingCartDo) Group(cols ...field.Expr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insOrderShoppingCartDo) Having(conds ...gen.Condition) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insOrderShoppingCartDo) Limit(limit int) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insOrderShoppingCartDo) Offset(offset int) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insOrderShoppingCartDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insOrderShoppingCartDo) Unscoped() *insOrderShoppingCartDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insOrderShoppingCartDo) Create(values ...*insbuy.InsOrderShoppingCart) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insOrderShoppingCartDo) CreateInBatches(values []*insbuy.InsOrderShoppingCart, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insOrderShoppingCartDo) Save(values ...*insbuy.InsOrderShoppingCart) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insOrderShoppingCartDo) First() (*insbuy.InsOrderShoppingCart, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCart), nil
	}
}

func (i insOrderShoppingCartDo) Take() (*insbuy.InsOrderShoppingCart, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCart), nil
	}
}

func (i insOrderShoppingCartDo) Last() (*insbuy.InsOrderShoppingCart, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCart), nil
	}
}

func (i insOrderShoppingCartDo) Find() ([]*insbuy.InsOrderShoppingCart, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsOrderShoppingCart), err
}

func (i insOrderShoppingCartDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsOrderShoppingCart, err error) {
	buf := make([]*insbuy.InsOrderShoppingCart, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insOrderShoppingCartDo) FindInBatches(result *[]*insbuy.InsOrderShoppingCart, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insOrderShoppingCartDo) Attrs(attrs ...field.AssignExpr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insOrderShoppingCartDo) Assign(attrs ...field.AssignExpr) *insOrderShoppingCartDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insOrderShoppingCartDo) Joins(fields ...field.RelationField) *insOrderShoppingCartDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insOrderShoppingCartDo) Preload(fields ...field.RelationField) *insOrderShoppingCartDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insOrderShoppingCartDo) FirstOrInit() (*insbuy.InsOrderShoppingCart, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCart), nil
	}
}

func (i insOrderShoppingCartDo) FirstOrCreate() (*insbuy.InsOrderShoppingCart, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsOrderShoppingCart), nil
	}
}

func (i insOrderShoppingCartDo) FindByPage(offset int, limit int) (result []*insbuy.InsOrderShoppingCart, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insOrderShoppingCartDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insOrderShoppingCartDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insOrderShoppingCartDo) Delete(models ...*insbuy.InsOrderShoppingCart) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insOrderShoppingCartDo) withDO(do gen.Dao) *insOrderShoppingCartDo {
	i.DO = *do.(*gen.DO)
	return i
}
