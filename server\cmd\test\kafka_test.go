//go:build kafka
// +build kafka

package test

import (
	"fmt"
	kafka2 "github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/kafkamodel"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jkafka"
	"os"
	"testing"
	"time"
)

func testKafkaConsumer(t *testing.T) {
	c1, e1 := jkafka.CreateConsumer(jkafka.TopicBookDesk)
	if e1 != nil {
		t.Fatal(e1)
	}
	for {
		msg, err := c1.ReadMessage(-1)
		if err != nil {
			t.Fatal(err)
		} else {
			t.Log(string(msg.Value))
		}
	}
}

func testKafkaAdmin(t *testing.T) {
	k1 := jkafka.GetDefault()
	if k1 != nil {
		cf1, e1 := k1.GenConfig(true, nil)
		if e1 != nil {
			t.Fatal(e1)
		}
		a1, e1 := kafka2.NewAdminClient(cf1)
		if e1 != nil {
			t.Fatal(e1)
		}
		var topic1 string
		if m1, e1 := a1.GetMetadata(&topic1, true, 1000); e1 != nil {
			t.Fatal(e1)
		} else {
			for t2, m2 := range m1.Topics {
				fmt.Printf("topic %s, partitions size: %v\n", t2, len(m2.Partitions))
			}
		}
	}
}

func testKafkaProducer(t *testing.T) {
	//kafka.SendMessage(kafka.TopicBookDesk, []byte("s1"), []byte("test"))

	e1 := jkafka.SendMsg(jkafka.TopicBookDesk, kafkamodel.BookDeskCfgDeskMessage{
		BookDeskBase: kafkamodel.BookDeskBase{
			EventName: global.KAFKAEVENT_BookDesk_ConfigActive,
			EventTime: time.Now().Unix(),
		},
		BookDeskStoreInfo: kafkamodel.BookDeskStoreInfo{
			StoreId:      "6",
			StoreName:    "测试-有这村没这店",
			InsStoreCode: "pg",
		},
		BeginTime: "2023-12-13 10:00:00",
		EndTime:   "2023-12-13 10:01:00",
		BookDate:  "2023-12-14",
		DeskList: []kafkamodel.BookDeskDeskInfo{
			{"12", "R01"},
		},
	})
	if e1 != nil {
		t.Fatal(e1)
	} else {
		t.Log("send ok")
	}

	time.Sleep(time.Second * 20)
}

func testKafkaCheck(t *testing.T) {
	if global.GVA_CONFIG.Kafka.CaFile != "" {
		f1, e1 := os.Stat(global.GVA_CONFIG.Kafka.CaFile)
		if e1 != nil {
			t.Fatal(e1)
		} else {
			t.Log(f1)
		}
	}
}

func TestKafka(t *testing.T) {
	//kafka00(t)

	prepare()

	defer func() {
		_ = jkafka.Close()
	}()

	//testKafkaCheck(t)

	//testKafkaAdmin(t)
	//go testKafkaConsumer(t)
	//testKafkaProducer(t)

	t.Log("done")
}
