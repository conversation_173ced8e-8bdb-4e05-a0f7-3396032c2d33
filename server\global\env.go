package global

import (
	"errors"
	"reflect"
)

// 版本信息
var (
	Version      = "0.0.1"                    // 版本号
	GitTag       = "2023.07.17.debug"         // 代码版本
	GitAuthor    = "jason.liao <>"            // 代码作者
	GitCommitMsg = "Init"                     // 提交信息
	GitCommitLog = ""                         // 提交历史
	BuildTime    = "2023-07-17T17:36:00+0800" // 编译时间

	ConfEnvPrefix = "ins"
)

var ErrNotFound = errors.New("not found")

type ScopeVar interface {
	// 根据类型获取变量

	Set(value interface{})                             // 设置变量
	Get(defaultValue interface{}) (interface{}, error) // 获取变量，如果不存在则返回默认值

	// 支持指定名称 todo: 以下方法暂时不实现

	//SetByKey(key string, value interface{}) // 设置变量
	//GetByKey(key string, defaultValue interface{}) (interface{}, error)
}

type tScopeVar struct {
	vars map[reflect.Type]interface{}
}

func NewScopeVar() ScopeVar {
	return &tScopeVar{vars: map[reflect.Type]interface{}{}}
}

func (s *tScopeVar) Set(value interface{}) {
	t := reflect.TypeOf(value)
	s.vars[t] = value
}

func (s *tScopeVar) Get(defaultValue interface{}) (interface{}, error) {
	t := reflect.TypeOf(defaultValue)
	if val, ok := s.vars[t]; ok {
		return val, nil
	}
	return defaultValue, ErrNotFound
}

func (s *tScopeVar) GetString() (string, error) {
	val, err := s.Get(reflect.TypeOf(""))
	if err != nil {
		return "", err
	}
	return val.(string), nil
}

func (s *tScopeVar) GetInt() (int, error) {
	val, err := s.Get(reflect.TypeOf(0))
	if err != nil {
		return 0, err
	}
	return val.(int), nil
}

func (s *tScopeVar) GetBool() (bool, error) {
	val, err := s.Get(reflect.TypeOf(true))
	if err != nil {
		return false, err
	}
	return val.(bool), nil
}
