# 合同同步定时任务使用指南

## 概述

本文档介绍如何使用合同同步定时任务功能，该功能结合飞书审批代码配置，提供了灵活的定时同步方案。

## 功能特性

### 1. 多种同步策略

- **全量同步**: 同步所有启用的审批代码
- **标签同步**: 根据标签同步特定类型的审批代码
- **多标签组合同步**: 根据多个标签组合同步审批代码
- **紧急同步**: 高频同步紧急和大额合同

### 2. 灵活的时间配置

- **不同的时间范围**: 根据任务类型设置不同的数据时间范围
- **不同的执行频率**: 从15分钟到每日的不同执行频率
- **工作时间限制**: 支持仅在工作时间执行的任务

### 3. 完整的错误处理

- **任务级别的错误处理**: 单个任务失败不影响其他任务
- **详细的日志记录**: 完整的执行日志和错误信息
- **异常恢复**: 支持panic恢复和告警通知

## 配置文件示例

首先需要在配置文件中设置审批代码：

```yaml
feishu-app:
  app-id: "your-feishu-app-id"
  app-secret: "your-feishu-app-secret"
  
  approval-codes:
    # 销售合同审批
    - code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
      name: "销售合同审批"
      description: "销售合同审批流程"
      enabled: true
      tags: ["contract", "sales"]
    
    # 大额销售合同审批
    - code: "A123B456-7890-1234-5678-90ABCDEF1234"
      name: "大额销售合同审批"
      description: "金额超过10万的销售合同审批"
      enabled: true
      tags: ["contract", "sales", "high-amount", "urgent"]
    
    # 采购合同审批
    - code: "C789D012-3456-7890-1234-56789ABCDEF0"
      name: "采购合同审批"
      description: "采购合同审批流程"
      enabled: true
      tags: ["contract", "purchase"]
    
    # 财务报销审批
    - code: "Q012R345-6789-0123-4567-890123456789"
      name: "财务报销审批"
      description: "财务报销审批流程"
      enabled: true
      tags: ["finance", "expense"]
```

## 定时任务方法

### 1. 基础定时任务方法

```go
// 每日全量同步任务
func DailyFullSyncTask()

// 工作时间增量同步任务
func WorktimeIncrementalSyncTask()

// 紧急合同同步任务
func UrgentContractSyncTask()

// 销售合同同步任务
func SalesContractSyncTask()

// 财务审批同步任务
func FinanceSyncTask()

// 大额合同同步任务
func HighAmountContractSyncTask()

// 周末维护同步任务
func WeekendMaintenanceSyncTask()
```

### 2. 包装的定时任务方法（推荐使用）

```go
// 提供统一的错误处理和监控
func WrappedDailyFullSyncTask()
func WrappedWorktimeIncrementalSyncTask()
func WrappedUrgentContractSyncTask()
func WrappedSalesContractSyncTask()
func WrappedFinanceSyncTask()
func WrappedHighAmountContractSyncTask()
func WrappedWeekendMaintenanceSyncTask()
```

## 定时任务配置

### 1. 使用 robfig/cron 配置

```go
package main

import (
    "github.com/robfig/cron/v3"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

func setupContractSyncTasks() {
    c := cron.New()

    // 每日凌晨2点执行全量同步
    c.AddFunc("0 2 * * *", insbuy.WrappedDailyFullSyncTask)

    // 工作时间每30分钟执行增量同步（周一到周五，9点到18点）
    c.AddFunc("*/30 9-18 * * 1-5", insbuy.WrappedWorktimeIncrementalSyncTask)

    // 每15分钟执行紧急合同同步
    c.AddFunc("*/15 * * * *", insbuy.WrappedUrgentContractSyncTask)

    // 每小时执行销售合同同步
    c.AddFunc("0 * * * *", insbuy.WrappedSalesContractSyncTask)

    // 每2小时执行财务审批同步
    c.AddFunc("0 */2 * * *", insbuy.WrappedFinanceSyncTask)

    // 每30分钟执行大额合同同步
    c.AddFunc("*/30 * * * *", insbuy.WrappedHighAmountContractSyncTask)

    // 周末上午10点执行维护同步
    c.AddFunc("0 10 * * 6,0", insbuy.WrappedWeekendMaintenanceSyncTask)

    // 启动定时任务
    c.Start()
}
```

### 2. 使用 gocron 配置

```go
package main

import (
    "github.com/go-co-op/gocron"
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
    "time"
)

func setupContractSyncTasksWithGocron() {
    s := gocron.NewScheduler(time.UTC)

    // 每日凌晨2点执行全量同步
    s.Every(1).Day().At("02:00").Do(insbuy.WrappedDailyFullSyncTask)

    // 工作时间每30分钟执行增量同步
    s.Every(30).Minutes().Between("09:00", "18:00").Weekdays().Do(insbuy.WrappedWorktimeIncrementalSyncTask)

    // 每15分钟执行紧急合同同步
    s.Every(15).Minutes().Do(insbuy.WrappedUrgentContractSyncTask)

    // 每小时执行销售合同同步
    s.Every(1).Hour().Do(insbuy.WrappedSalesContractSyncTask)

    // 每2小时执行财务审批同步
    s.Every(2).Hours().Do(insbuy.WrappedFinanceSyncTask)

    // 每30分钟执行大额合同同步
    s.Every(30).Minutes().Do(insbuy.WrappedHighAmountContractSyncTask)

    // 周末上午10点执行维护同步
    s.Every(1).Week().Saturday().At("10:00").Do(insbuy.WrappedWeekendMaintenanceSyncTask)
    s.Every(1).Week().Sunday().At("10:00").Do(insbuy.WrappedWeekendMaintenanceSyncTask)

    // 启动定时任务
    s.StartAsync()
}
```

## 手动执行方法

### 1. 手动同步特定标签

```go
package main

import (
    "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
    "log"
)

func manualSyncExamples() {
    // 手动同步销售合同
    err := insbuy.ManualSyncByTag("sales")
    if err != nil {
        log.Printf("手动同步销售合同失败: %v", err)
    }

    // 手动同步财务审批
    err = insbuy.ManualSyncByTag("finance")
    if err != nil {
        log.Printf("手动同步财务审批失败: %v", err)
    }

    // 手动同步大额销售合同
    err = insbuy.ManualSyncByTags([]string{"contract", "sales", "high-amount"})
    if err != nil {
        log.Printf("手动同步大额销售合同失败: %v", err)
    }

    // 手动全量同步
    err = insbuy.ManualFullSync()
    if err != nil {
        log.Printf("手动全量同步失败: %v", err)
    }
}
```

### 2. 获取同步状态

```go
func checkSyncStatus() {
    status := insbuy.GetSyncStatus()
    log.Printf("同步状态: %+v", status)
    
    // 输出示例:
    // {
    //   "total_configs": 4,
    //   "enabled_configs": 4,
    //   "enabled_codes": 4,
    //   "tag_stats": {
    //     "contract": 3,
    //     "sales": 2,
    //     "finance": 1,
    //     "high-amount": 1,
    //     "urgent": 1
    //   },
    //   "last_check": "2024-01-15 10:30:00"
    // }
}
```

## 任务执行特点

### 1. 不同任务的配置特点

| 任务名称 | 执行频率 | 时间范围 | 批次大小 | 适用场景 |
|---------|---------|---------|---------|---------|
| 全量同步 | 每日1次 | 30天 | 20 | 数据完整性保证 |
| 增量同步 | 工作时间30分钟 | 7天 | 20 | 日常数据更新 |
| 紧急同步 | 15分钟 | 1天 | 10 | 紧急业务处理 |
| 销售同步 | 1小时 | 7天 | 20 | 销售业务专项 |
| 财务同步 | 2小时 | 7天 | 20 | 财务业务专项 |
| 大额同步 | 30分钟 | 3天 | 20 | 重要合同监控 |
| 维护同步 | 周末1次 | 90天 | 30 | 数据维护清理 |

### 2. 错误处理策略

- **任务级隔离**: 单个任务失败不影响其他任务
- **自动重试**: 内置重试机制，可配置重试次数和延迟
- **异常恢复**: 支持panic恢复，避免整个程序崩溃
- **详细日志**: 完整的执行日志，便于问题排查

### 3. 性能优化

- **批次控制**: 根据任务类型调整批次大小
- **API限流**: 设置合理的延迟，避免API限制
- **时间窗口**: 不同任务使用不同的时间范围，减少数据量

## 监控和告警

### 1. 日志监控

```bash
# 查看定时任务执行日志
grep "DailyFullSyncTask\|WorktimeIncrementalSyncTask" /var/log/app.log

# 查看同步结果统计
grep "同步完成" /var/log/app.log | tail -10

# 查看错误日志
grep "同步失败\|ERROR" /var/log/app.log | tail -10
```

### 2. 健康检查

```go
// 定期检查同步状态
func healthCheck() {
    status := insbuy.GetSyncStatus()
    
    enabledCount := status["enabled_codes"].(int)
    if enabledCount == 0 {
        // 发送告警：没有启用的审批代码
        log.Printf("告警：没有启用的审批代码")
    }
    
    // 检查各个标签的配置数量
    tagStats := status["tag_stats"].(map[string]int)
    if tagStats["contract"] == 0 {
        // 发送告警：没有合同相关的审批代码
        log.Printf("告警：没有合同相关的审批代码")
    }
}
```

## 最佳实践

### 1. 配置建议

- **标签设计**: 使用清晰的标签分类，便于任务调度
- **启用控制**: 通过 `enabled` 字段灵活控制审批代码
- **时间范围**: 根据业务需求设置合理的时间范围

### 2. 部署建议

- **错误监控**: 设置日志监控和告警机制
- **资源控制**: 避免同时执行过多同步任务
- **数据备份**: 定期备份同步日志和配置数据

### 3. 运维建议

- **定期检查**: 定期检查任务执行状态和结果
- **配置更新**: 及时更新审批代码配置
- **性能监控**: 监控API调用频率和响应时间

通过以上配置和使用方法，您可以建立一个完整的合同数据同步体系，确保飞书审批数据的及时同步和业务连续性。
