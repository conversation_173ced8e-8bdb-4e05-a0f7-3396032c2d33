package productmodel

import "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore/lang"

// ClientSideOptionItems 客户端可选商品
type ClientSideOptionItems struct {
	ProductItems  []ClientSideProductItems `json:"productItems"`
	PackageItemId int                      `json:"packageItemId"`
}

// 可选套餐
type ClientSideProductItems struct {
	PackageItemId int                        `json:"packageItemId"`
	IfGive        int                        `json:"ifGive"` //是否赠送
	ProductId     int                        `json:"productId"`
	ProductNum    int                        `json:"productNum"`
	ProductName   string                     `json:"productName"`
	OldProductNum int                        `json:"oldProductNum"`
	Unit          int                        `json:"unit"`
	Lang          map[string]lang.CommonJson `json:"langData"`
}
