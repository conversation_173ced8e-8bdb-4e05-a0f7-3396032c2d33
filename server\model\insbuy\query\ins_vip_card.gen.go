// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsVipCard(db *gorm.DB, opts ...gen.DOOption) insVipCard {
	_insVipCard := insVipCard{}

	_insVipCard.insVipCardDo.UseDB(db, opts...)
	_insVipCard.insVipCardDo.UseModel(&insbuy.InsVipCard{})

	tableName := _insVipCard.insVipCardDo.TableName()
	_insVipCard.ALL = field.NewAsterisk(tableName)
	_insVipCard.ID = field.NewUint(tableName, "id")
	_insVipCard.CreatedAt = field.NewTime(tableName, "created_at")
	_insVipCard.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insVipCard.DeletedAt = field.NewField(tableName, "deleted_at")
	_insVipCard.StoreId = field.NewUint(tableName, "store_id")
	_insVipCard.MemberId = field.NewUint(tableName, "member_id")
	_insVipCard.CardSn = field.NewUint(tableName, "card_sn")
	_insVipCard.Balance = field.NewFloat64(tableName, "balance")
	_insVipCard.GiftBalance = field.NewFloat64(tableName, "gift_balance")
	_insVipCard.BalanceVersion = field.NewInt(tableName, "balance_version")

	_insVipCard.fillFieldMap()

	return _insVipCard
}

type insVipCard struct {
	insVipCardDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	StoreId        field.Uint
	MemberId       field.Uint
	CardSn         field.Uint
	Balance        field.Float64
	GiftBalance    field.Float64
	BalanceVersion field.Int

	fieldMap map[string]field.Expr
}

func (i insVipCard) Table(newTableName string) *insVipCard {
	i.insVipCardDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insVipCard) As(alias string) *insVipCard {
	i.insVipCardDo.DO = *(i.insVipCardDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insVipCard) updateTableName(table string) *insVipCard {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.StoreId = field.NewUint(table, "store_id")
	i.MemberId = field.NewUint(table, "member_id")
	i.CardSn = field.NewUint(table, "card_sn")
	i.Balance = field.NewFloat64(table, "balance")
	i.GiftBalance = field.NewFloat64(table, "gift_balance")
	i.BalanceVersion = field.NewInt(table, "balance_version")

	i.fillFieldMap()

	return i
}

func (i *insVipCard) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insVipCard) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["store_id"] = i.StoreId
	i.fieldMap["member_id"] = i.MemberId
	i.fieldMap["card_sn"] = i.CardSn
	i.fieldMap["balance"] = i.Balance
	i.fieldMap["gift_balance"] = i.GiftBalance
	i.fieldMap["balance_version"] = i.BalanceVersion
}

func (i insVipCard) clone(db *gorm.DB) insVipCard {
	i.insVipCardDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insVipCard) replaceDB(db *gorm.DB) insVipCard {
	i.insVipCardDo.ReplaceDB(db)
	return i
}

type insVipCardDo struct{ gen.DO }

func (i insVipCardDo) Debug() *insVipCardDo {
	return i.withDO(i.DO.Debug())
}

func (i insVipCardDo) WithContext(ctx context.Context) *insVipCardDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insVipCardDo) ReadDB() *insVipCardDo {
	return i.Clauses(dbresolver.Read)
}

func (i insVipCardDo) WriteDB() *insVipCardDo {
	return i.Clauses(dbresolver.Write)
}

func (i insVipCardDo) Session(config *gorm.Session) *insVipCardDo {
	return i.withDO(i.DO.Session(config))
}

func (i insVipCardDo) Clauses(conds ...clause.Expression) *insVipCardDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insVipCardDo) Returning(value interface{}, columns ...string) *insVipCardDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insVipCardDo) Not(conds ...gen.Condition) *insVipCardDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insVipCardDo) Or(conds ...gen.Condition) *insVipCardDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insVipCardDo) Select(conds ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insVipCardDo) Where(conds ...gen.Condition) *insVipCardDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insVipCardDo) Order(conds ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insVipCardDo) Distinct(cols ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insVipCardDo) Omit(cols ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insVipCardDo) Join(table schema.Tabler, on ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insVipCardDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insVipCardDo) RightJoin(table schema.Tabler, on ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insVipCardDo) Group(cols ...field.Expr) *insVipCardDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insVipCardDo) Having(conds ...gen.Condition) *insVipCardDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insVipCardDo) Limit(limit int) *insVipCardDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insVipCardDo) Offset(offset int) *insVipCardDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insVipCardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insVipCardDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insVipCardDo) Unscoped() *insVipCardDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insVipCardDo) Create(values ...*insbuy.InsVipCard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insVipCardDo) CreateInBatches(values []*insbuy.InsVipCard, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insVipCardDo) Save(values ...*insbuy.InsVipCard) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insVipCardDo) First() (*insbuy.InsVipCard, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipCard), nil
	}
}

func (i insVipCardDo) Take() (*insbuy.InsVipCard, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipCard), nil
	}
}

func (i insVipCardDo) Last() (*insbuy.InsVipCard, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipCard), nil
	}
}

func (i insVipCardDo) Find() ([]*insbuy.InsVipCard, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsVipCard), err
}

func (i insVipCardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsVipCard, err error) {
	buf := make([]*insbuy.InsVipCard, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insVipCardDo) FindInBatches(result *[]*insbuy.InsVipCard, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insVipCardDo) Attrs(attrs ...field.AssignExpr) *insVipCardDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insVipCardDo) Assign(attrs ...field.AssignExpr) *insVipCardDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insVipCardDo) Joins(fields ...field.RelationField) *insVipCardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insVipCardDo) Preload(fields ...field.RelationField) *insVipCardDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insVipCardDo) FirstOrInit() (*insbuy.InsVipCard, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipCard), nil
	}
}

func (i insVipCardDo) FirstOrCreate() (*insbuy.InsVipCard, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsVipCard), nil
	}
}

func (i insVipCardDo) FindByPage(offset int, limit int) (result []*insbuy.InsVipCard, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insVipCardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insVipCardDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insVipCardDo) Delete(models ...*insbuy.InsVipCard) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insVipCardDo) withDO(do gen.Dao) *insVipCardDo {
	i.DO = *do.(*gen.DO)
	return i
}
