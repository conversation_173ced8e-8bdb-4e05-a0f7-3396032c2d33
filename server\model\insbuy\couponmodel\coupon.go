package couponmodel

import (
	"encoding/json"
	"gorm.io/datatypes"
)

// ScopeType 优惠券适用范围类型
type ScopeType int

// 1: 指定商品 2: 指定分类 3: 排除商品 4: 排除分类
const (
	STAssignProduct   ScopeType = 1 // 1: 指定商品
	STAssignCategory  ScopeType = 2 // 2: 指定分类
	STExcludeProduct  ScopeType = 3 // 3: 排除商品
	STExcludeCategory ScopeType = 4 // 4: 排除分类
)

// 转int
func (s ScopeType) Int() int {
	return int(s)
}

// CouponType 优惠券类型
type CouponType int

// 优惠券类型 1 折扣券 2 代金券 3 商品券
const (
	CTDiscount CouponType = 1 // 折扣券
	CTCash     CouponType = 2 // 代金券
	CTProduct  CouponType = 3 // 商品券
)

// 转int
func (c CouponType) Int() int {
	return int(c)
}

type CouponDetail struct {
	CouponAttr
	ScopeType     ScopeType       `json:"scopeType" form:"scopeType"`                  //生效范围
	ApplyScope    []ApplyScope    `json:"applyScope" form:"applyScope" gorm:"-"`       //生效范围数据
	CouponWeekday []CouponWeekday `json:"couponWeekday" form:"couponWeekday" gorm:"-"` //适用时段周一到周日
	CouponTime    []CouponTime    `json:"couponTime" form:"couponTime" gorm:"-"`       //时段详细 时分秒
	//ExcludeCategory []ExcludeCategory `json:"excludeCategory" form:"excludeCategory"` //排除分类
	//ExcludeProduct  []ExcludeProduct  `json:"excludeProduct" form:"excludeProduct"`   //排除商品
	//AssignProduct   []AssignProduct   `json:"assignProduct" form:"assignProduct"`     //指定商品
	//AssignCategory  []AssignCategory  `json:"assignCategory" form:"assignCategory"`   //指定分类
}

// CouponWeekday 适用时段
type CouponWeekday struct {
	Weekday int `json:"weekday" form:"weekday"`
}

// CouponTime 时段
type CouponTime struct {
	StartTime string `json:"startTime" form:"startTime"` //时分秒
	EndTime   string `json:"endTime" form:"endTime"`     //时分秒
}

type ApplyScope struct {
	TargetId   uint   `json:"targetId" form:"targetId"`
	TargetName string `json:"targetName" form:"targetName"`
}

// ExcludeCategory 排除分类
type ExcludeCategory struct {
	CategoryId   uint   `json:"categoryId" form:"categoryId"`
	CategoryName string `json:"categoryName" form:"categoryName"`
}

// ExcludeProduct 排除商品
type ExcludeProduct struct {
	ProductId   uint   `json:"productId" form:"productId"`
	ProductName string `json:"productName" form:"productName"`
}

// AssignProduct 指定商品
type AssignProduct struct {
	ProductId   uint   `json:"productId" form:"productId"`
	ProductName string `json:"productName" form:"productName"`
}

// AssignCategory 指定分类
type AssignCategory struct {
	CategoryId   uint   `json:"categoryId" form:"categoryId"`
	CategoryName string `json:"categoryName" form:"categoryName"`
}

type CouponAttr struct {
	CouponType int `json:"couponType" form:"couponType"` // 优惠券类型 1 折扣券 2 代金券 3 商品券
	CouponDiscount
	CouponCash
	CouponProduct
}

// CouponDiscount 折扣券属性
type CouponDiscount struct {
	DiscountRate float64 `json:"discountRate" form:"discountRate"` //折扣率百分比
	DiscountCap  float64 `json:"discountCap" form:"discountCap"`   //折扣上限
}

// CouponCash 代金券属性
type CouponCash struct {
	DiscountAmount float64 `json:"discountAmount" form:"discountAmount"` //抵扣金额
}

// CouponProduct 商品券属性
type CouponProduct struct {
	DiscountType      int     `json:"discountType" form:"discountType"`           //抵扣类型 1:抵扣固定金额 2:抵扣至固定金额
	Discount          float64 `json:"discount" form:"discount"`                   //抵扣金额
	DiscountTo        float64 `json:"discountTo" form:"discountTo"`               //抵扣至固定金额
	DiscountMagnitude int     `json:"discountMagnitude" form:"discountMagnitude"` //优惠幅度 1 按最高幅度 2 按最低幅度
}

type SendType []int

// 转json
func (s SendType) ToJSON() (res datatypes.JSON) {
	marshal, _ := json.Marshal(s)
	return marshal
}

// UserQuery
type UserQuery struct {
	ManualAdd         []ManualAdd       `json:"manualAdd" form:"manualAdd"`                          //手动添加
	LastToStore       LastToStore       `json:"lastToStore" form:"lastToStore" gorm:"-"`             //最近到店
	TotalConsumption  TotalConsumption  `json:"totalConsumption" form:"totalConsumption" gorm:"-"`   //累计消费
	MemberCardBalance MemberCardBalance `json:"memberCardBalance" form:"memberCardBalance" gorm:"-"` //会员卡余额
	HistoryToStore    HistoryToStore    `json:"historyToStore" form:"historyToStore" gorm:"-"`       //历史到店次数
}

// ToJSON 转换为datatypes.JSON
func (u UserQuery) ToJSON() (res datatypes.JSON) {
	marshal, _ := json.Marshal(u)
	return marshal
}

type ManualAdd struct {
	MemberId   uint   `json:"memberId" form:"memberId"`
	MemberName string `json:"memberName" form:"memberName"`
}

// LastToStore 最近到店
type LastToStore struct {
	BeforeDate string `json:"beforeDate" form:"beforeDate"` //指定日期之前
	AfterDate  string `json:"afterDate" form:"afterDate"`   //指定日期之后
}

// TotalConsumption  累计消费
type TotalConsumption struct {
	//大于指定金额
	GtAmount float64 `json:"gtAmount" form:"gtAmount"`
	//小于指定金额
	LtAmount float64 `json:"ltAmount" form:"ltAmount"`
}

// MemberCardBalance 会员卡余额
type MemberCardBalance struct {
	//大于指定金额
	GtAmount float64 `json:"gtAmount" form:"gtAmount"`
	//小于指定金额
	LtAmount float64 `json:"ltAmount" form:"ltAmount"`
}

// HistoryToStore 历史到店次数
type HistoryToStore struct {
	//大于指定次数
	GtNum int `json:"gtNum" form:"gtNum"`
	//小于指定次数
	LtNum int `json:"ltNum" form:"ltNum"`
}
