// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsSalerCode(db *gorm.DB, opts ...gen.DOOption) insSalerCode {
	_insSalerCode := insSalerCode{}

	_insSalerCode.insSalerCodeDo.UseDB(db, opts...)
	_insSalerCode.insSalerCodeDo.UseModel(&insbuy.InsSalerCode{})

	tableName := _insSalerCode.insSalerCodeDo.TableName()
	_insSalerCode.ALL = field.NewAsterisk(tableName)
	_insSalerCode.ID = field.NewUint(tableName, "id")
	_insSalerCode.CreatedAt = field.NewTime(tableName, "created_at")
	_insSalerCode.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insSalerCode.DeletedAt = field.NewField(tableName, "deleted_at")
	_insSalerCode.Code = field.NewString(tableName, "code")
	_insSalerCode.UserId = field.NewUint(tableName, "user_id")

	_insSalerCode.fillFieldMap()

	return _insSalerCode
}

type insSalerCode struct {
	insSalerCodeDo

	ALL       field.Asterisk
	ID        field.Uint
	CreatedAt field.Time
	UpdatedAt field.Time
	DeletedAt field.Field
	Code      field.String
	UserId    field.Uint

	fieldMap map[string]field.Expr
}

func (i insSalerCode) Table(newTableName string) *insSalerCode {
	i.insSalerCodeDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insSalerCode) As(alias string) *insSalerCode {
	i.insSalerCodeDo.DO = *(i.insSalerCodeDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insSalerCode) updateTableName(table string) *insSalerCode {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.Code = field.NewString(table, "code")
	i.UserId = field.NewUint(table, "user_id")

	i.fillFieldMap()

	return i
}

func (i *insSalerCode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insSalerCode) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 6)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["code"] = i.Code
	i.fieldMap["user_id"] = i.UserId
}

func (i insSalerCode) clone(db *gorm.DB) insSalerCode {
	i.insSalerCodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insSalerCode) replaceDB(db *gorm.DB) insSalerCode {
	i.insSalerCodeDo.ReplaceDB(db)
	return i
}

type insSalerCodeDo struct{ gen.DO }

func (i insSalerCodeDo) Debug() *insSalerCodeDo {
	return i.withDO(i.DO.Debug())
}

func (i insSalerCodeDo) WithContext(ctx context.Context) *insSalerCodeDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insSalerCodeDo) ReadDB() *insSalerCodeDo {
	return i.Clauses(dbresolver.Read)
}

func (i insSalerCodeDo) WriteDB() *insSalerCodeDo {
	return i.Clauses(dbresolver.Write)
}

func (i insSalerCodeDo) Session(config *gorm.Session) *insSalerCodeDo {
	return i.withDO(i.DO.Session(config))
}

func (i insSalerCodeDo) Clauses(conds ...clause.Expression) *insSalerCodeDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insSalerCodeDo) Returning(value interface{}, columns ...string) *insSalerCodeDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insSalerCodeDo) Not(conds ...gen.Condition) *insSalerCodeDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insSalerCodeDo) Or(conds ...gen.Condition) *insSalerCodeDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insSalerCodeDo) Select(conds ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insSalerCodeDo) Where(conds ...gen.Condition) *insSalerCodeDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insSalerCodeDo) Order(conds ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insSalerCodeDo) Distinct(cols ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insSalerCodeDo) Omit(cols ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insSalerCodeDo) Join(table schema.Tabler, on ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insSalerCodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insSalerCodeDo) RightJoin(table schema.Tabler, on ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insSalerCodeDo) Group(cols ...field.Expr) *insSalerCodeDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insSalerCodeDo) Having(conds ...gen.Condition) *insSalerCodeDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insSalerCodeDo) Limit(limit int) *insSalerCodeDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insSalerCodeDo) Offset(offset int) *insSalerCodeDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insSalerCodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insSalerCodeDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insSalerCodeDo) Unscoped() *insSalerCodeDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insSalerCodeDo) Create(values ...*insbuy.InsSalerCode) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insSalerCodeDo) CreateInBatches(values []*insbuy.InsSalerCode, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insSalerCodeDo) Save(values ...*insbuy.InsSalerCode) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insSalerCodeDo) First() (*insbuy.InsSalerCode, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerCode), nil
	}
}

func (i insSalerCodeDo) Take() (*insbuy.InsSalerCode, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerCode), nil
	}
}

func (i insSalerCodeDo) Last() (*insbuy.InsSalerCode, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerCode), nil
	}
}

func (i insSalerCodeDo) Find() ([]*insbuy.InsSalerCode, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsSalerCode), err
}

func (i insSalerCodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsSalerCode, err error) {
	buf := make([]*insbuy.InsSalerCode, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insSalerCodeDo) FindInBatches(result *[]*insbuy.InsSalerCode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insSalerCodeDo) Attrs(attrs ...field.AssignExpr) *insSalerCodeDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insSalerCodeDo) Assign(attrs ...field.AssignExpr) *insSalerCodeDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insSalerCodeDo) Joins(fields ...field.RelationField) *insSalerCodeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insSalerCodeDo) Preload(fields ...field.RelationField) *insSalerCodeDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insSalerCodeDo) FirstOrInit() (*insbuy.InsSalerCode, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerCode), nil
	}
}

func (i insSalerCodeDo) FirstOrCreate() (*insbuy.InsSalerCode, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsSalerCode), nil
	}
}

func (i insSalerCodeDo) FindByPage(offset int, limit int) (result []*insbuy.InsSalerCode, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insSalerCodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insSalerCodeDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insSalerCodeDo) Delete(models ...*insbuy.InsSalerCode) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insSalerCodeDo) withDO(do gen.Dao) *insSalerCodeDo {
	i.DO = *do.(*gen.DO)
	return i
}
