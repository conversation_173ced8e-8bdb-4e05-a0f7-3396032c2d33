package example

import (
	"fmt"
	"log"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

// FeishuApprovalConfigExample 飞书审批代码配置使用示例
type FeishuApprovalConfigExample struct{}

// BasicUsage 基本使用示例
func (e *FeishuApprovalConfigExample) BasicUsage() {
	fmt.Println("=== 飞书审批代码配置基本使用示例 ===")
	
	configService := &insbuy.FeishuApprovalConfigService{}
	
	// 1. 获取所有启用的审批代码
	fmt.Println("\n1. 获取所有启用的审批代码:")
	allCodes := configService.GetAllApprovalCodes()
	fmt.Printf("   总共 %d 个启用的审批代码: %v\n", len(allCodes), allCodes)
	
	// 2. 获取所有配置信息
	fmt.Println("\n2. 获取所有配置信息:")
	allConfigs := configService.GetApprovalConfigList()
	for i, config := range allConfigs {
		fmt.Printf("   [%d] 代码: %s, 名称: %s, 启用: %v, 标签: %v\n", 
			i+1, config.Code, config.Name, config.Enabled, config.Tags)
	}
	
	// 3. 获取只启用的配置
	fmt.Println("\n3. 获取启用的配置:")
	enabledConfigs := configService.GetEnabledApprovalConfigs()
	fmt.Printf("   启用的配置数量: %d\n", len(enabledConfigs))
	
	// 4. 验证审批代码
	fmt.Println("\n4. 验证审批代码:")
	testCodes := []string{
		"F523F053-7AC6-4280-A4E7-B35E0C0431B5", // 假设这个存在
		"INVALID-CODE-123",                      // 无效代码
	}
	
	valid, invalid := configService.ValidateApprovalCodes(testCodes)
	fmt.Printf("   有效代码: %v\n", valid)
	fmt.Printf("   无效代码: %v\n", invalid)
}

// TagBasedUsage 基于标签的使用示例
func (e *FeishuApprovalConfigExample) TagBasedUsage() {
	fmt.Println("\n=== 基于标签的使用示例 ===")
	
	configService := &insbuy.FeishuApprovalConfigService{}
	
	// 1. 根据单个标签获取审批代码
	fmt.Println("\n1. 根据标签获取审批代码:")
	
	contractCodes := configService.GetApprovalCodesByTag("contract")
	fmt.Printf("   合同相关审批代码 (%d个): %v\n", len(contractCodes), contractCodes)
	
	salesCodes := configService.GetApprovalCodesByTag("sales")
	fmt.Printf("   销售相关审批代码 (%d个): %v\n", len(salesCodes), salesCodes)
	
	financeCodes := configService.GetApprovalCodesByTag("finance")
	fmt.Printf("   财务相关审批代码 (%d个): %v\n", len(financeCodes), financeCodes)
	
	// 2. 根据多个标签获取审批代码
	fmt.Println("\n2. 根据多个标签获取审批代码:")
	
	salesContractCodes := configService.GetApprovalCodesByTags([]string{"contract", "sales"})
	fmt.Printf("   销售合同审批代码 (%d个): %v\n", len(salesContractCodes), salesContractCodes)
	
	highAmountCodes := configService.GetApprovalCodesByTags([]string{"contract", "sales", "high-amount"})
	fmt.Printf("   大额销售合同审批代码 (%d个): %v\n", len(highAmountCodes), highAmountCodes)
}

// ContractSyncUsage 在合同同步中的使用示例
func (e *FeishuApprovalConfigExample) ContractSyncUsage() {
	fmt.Println("\n=== 合同同步使用示例 ===")
	
	configService := &insbuy.FeishuApprovalConfigService{}
	contractService := &insbuy.InsContractService{}
	
	// 1. 同步所有合同相关审批
	fmt.Println("\n1. 同步所有合同相关审批:")
	contractCodes := configService.GetApprovalCodesByTag("contract")
	if len(contractCodes) > 0 {
		req := request.ContractMultiSyncRequest{
			ApprovalCodes: contractCodes,
			BatchSize:     20,
			PageSize:      100,
			MaxRetries:    3,
			RetryDelay:    5,
		}
		
		fmt.Printf("   准备同步 %d 个合同审批代码\n", len(contractCodes))
		
		// 注意：这里只是示例，实际使用时需要确保数据库连接等环境正确
		resp, err := contractService.SyncMultipleContractData(req)
		if err != nil {
			fmt.Printf("   同步失败: %v\n", err)
		} else {
			fmt.Printf("   同步完成: 成功 %d/%d 个审批代码，处理 %d 条记录\n", 
				resp.SuccessCodes, resp.TotalCodes, resp.TotalRecords)
		}
	} else {
		fmt.Println("   未找到合同相关的审批代码")
	}
	
	// 2. 同步特定类型的审批
	fmt.Println("\n2. 同步销售合同审批:")
	salesCodes := configService.GetApprovalCodesByTag("sales")
	if len(salesCodes) > 0 {
		fmt.Printf("   找到 %d 个销售相关审批代码: %v\n", len(salesCodes), salesCodes)
		
		// 构建同步请求
		req := request.ContractMultiSyncRequest{
			ApprovalCodes: salesCodes,
			BatchSize:     20,
			PageSize:      100,
			MaxRetries:    3,
			RetryDelay:    5,
		}
		
		fmt.Printf("   准备同步销售合同审批代码...\n")
		// 实际同步调用
		// resp, err := contractService.SyncMultipleContractData(req)
		fmt.Printf("   (示例中跳过实际同步调用)\n")
	} else {
		fmt.Println("   未找到销售相关的审批代码")
	}
}

// ErrorHandlingUsage 错误处理使用示例
func (e *FeishuApprovalConfigExample) ErrorHandlingUsage() {
	fmt.Println("\n=== 错误处理使用示例 ===")
	
	configService := &insbuy.FeishuApprovalConfigService{}
	
	// 1. 获取不存在的审批代码信息
	fmt.Println("\n1. 获取审批代码详细信息:")
	
	// 假设这个代码存在
	if len(configService.GetAllApprovalCodes()) > 0 {
		firstCode := configService.GetAllApprovalCodes()[0]
		codeInfo, err := configService.GetApprovalCodeInfo(firstCode)
		if err == nil {
			fmt.Printf("   找到审批代码 %s:\n", firstCode)
			fmt.Printf("     名称: %s\n", codeInfo.Name)
			fmt.Printf("     描述: %s\n", codeInfo.Description)
			fmt.Printf("     标签: %v\n", codeInfo.Tags)
		} else {
			fmt.Printf("   获取审批代码信息失败: %v\n", err)
		}
	}
	
	// 2. 获取不存在的审批代码信息
	fmt.Println("\n2. 获取不存在的审批代码信息:")
	_, err := configService.GetApprovalCodeInfo("NON-EXISTENT-CODE")
	if err != nil {
		fmt.Printf("   预期的错误: %v\n", err)
	}
	
	// 3. 验证混合的审批代码列表
	fmt.Println("\n3. 验证混合的审批代码列表:")
	allCodes := configService.GetAllApprovalCodes()
	testCodes := append(allCodes, "INVALID-CODE-1", "INVALID-CODE-2")
	
	valid, invalid := configService.ValidateApprovalCodes(testCodes)
	fmt.Printf("   总共验证 %d 个代码\n", len(testCodes))
	fmt.Printf("   有效代码 %d 个: %v\n", len(valid), valid)
	fmt.Printf("   无效代码 %d 个: %v\n", len(invalid), invalid)
}

// RunAllExamples 运行所有示例
func (e *FeishuApprovalConfigExample) RunAllExamples() {
	fmt.Println("开始运行飞书审批代码配置使用示例...")
	
	// 基本使用
	e.BasicUsage()
	
	// 基于标签的使用
	e.TagBasedUsage()
	
	// 合同同步使用
	e.ContractSyncUsage()
	
	// 错误处理
	e.ErrorHandlingUsage()
	
	fmt.Println("\n所有示例运行完成！")
}

// 使用示例的入口函数
func RunFeishuApprovalConfigExample() {
	example := &FeishuApprovalConfigExample{}
	
	// 运行所有示例
	example.RunAllExamples()
}

// 简单的使用示例
func SimpleExample() {
	fmt.Println("=== 简单使用示例 ===")
	
	configService := &insbuy.FeishuApprovalConfigService{}
	
	// 获取所有启用的审批代码
	allCodes := configService.GetAllApprovalCodes()
	fmt.Printf("所有启用的审批代码: %v\n", allCodes)
	
	// 获取合同相关的审批代码
	contractCodes := configService.GetApprovalCodesByTag("contract")
	fmt.Printf("合同相关审批代码: %v\n", contractCodes)
	
	// 验证审批代码
	if len(allCodes) > 0 {
		isValid := configService.ValidateApprovalCode(allCodes[0])
		fmt.Printf("审批代码 %s 是否有效: %v\n", allCodes[0], isValid)
	}
}

// 在main函数中调用示例
func init() {
	// 注册示例函数，可以在需要时调用
	log.Println("飞书审批代码配置示例已加载")
}
