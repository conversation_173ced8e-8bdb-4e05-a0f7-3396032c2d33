package jyhapp

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/jyhapp/request"
	"go.uber.org/zap"
)

// {{ServiceName}}Service 服务
type {{ServiceName}}Service struct{}

// Create 创建
func (s *{{ServiceName}}Service) Create(req *request.{{ServiceName}}Create) error {
	// TODO: 实现创建逻辑
	return nil
}

// Update 更新
func (s *{{ServiceName}}Service) Update(req *request.{{ServiceName}}Update) error {
	// TODO: 实现更新逻辑
	return nil
}

// Delete 删除
func (s *{{ServiceName}}Service) Delete(id uint) error {
	// TODO: 实现删除逻辑
	return nil
}

// GetDetail 获取详情
func (s *{{ServiceName}}Service) GetDetail(id uint) (*jyhapp.{{ServiceName}}, error) {
	// TODO: 实现获取详情逻辑
	return nil, nil
}

// GetList 获取列表
func (s *{{ServiceName}}Service) GetList(req *request.{{ServiceName}}Search) ([]*jyhapp.{{ServiceName}}, int64, error) {
	// TODO: 实现获取列表逻辑
	return nil, 0, nil
} 