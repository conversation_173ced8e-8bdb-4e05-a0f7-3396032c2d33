<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="上海ins只读库">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||readonly||PROCESS|G
|root||readonly||REPLICATION CLIENT|G
|root||readonly||REPLICATION SLAVE|G
|root||readonly||XA_RECOVER_ADMIN|G
vspn\\_oa|schema||readonly||LOCK TABLES|G
vspn\\_oa|schema||readonly||SELECT|G
vspn\\_oa|schema||readonly||SHOW VIEW|G
vspo\\_admin|schema||readonly||LOCK TABLES|G
vspo\\_admin|schema||readonly||SELECT|G
vspo\\_admin|schema||readonly||SHOW VIEW|G
vspo\\_ins|schema||readonly||LOCK TABLES|G
vspo\\_ins|schema||readonly||SELECT|G
vspo\\_ins|schema||readonly||SHOW VIEW|G
vspo\\_ins\\_app|schema||readonly||LOCK TABLES|G
vspo\\_ins\\_app|schema||readonly||SELECT|G
vspo\\_ins\\_app|schema||readonly||SHOW VIEW|G
vspo\\_kezee|schema||readonly||LOCK TABLES|G
vspo\\_kezee|schema||readonly||SELECT|G
vspo\\_kezee|schema||readonly||SHOW VIEW|G
wechat\\_nacos|schema||readonly||LOCK TABLES|G
wechat\\_nacos|schema||readonly||SELECT|G
wechat\\_nacos|schema||readonly||SHOW VIEW|G
wechat\\_xxl\\_job|schema||readonly||LOCK TABLES|G
wechat\\_xxl\\_job|schema||readonly||SELECT|G
wechat\\_xxl\\_job|schema||readonly||SHOW VIEW|G
xxl\\_job|schema||readonly||LOCK TABLES|G
xxl\\_job|schema||readonly||SELECT|G
xxl\\_job|schema||readonly||SHOW VIEW|G</Grants>
      <ServerVersion>8.0.28</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8_tolower_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="274" parent="1" name="mysql">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="275" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="276" parent="1" name="xxl_job">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="277" parent="1" name="vspn_oa">
      <CollationName>utf8mb4_bin</CollationName>
    </schema>
    <schema id="278" parent="1" name="wechat_nacos">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="279" parent="1" name="wechat_xxl_job">
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="280" parent="1" name="vspo_kezee">
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="281" parent="1" name="vspo_ins">
      <IntrospectionTimestamp>2024-06-19.23:21:13</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2024-06-19.07:21:13</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="282" parent="1" name="vspo_admin">
      <Current>1</Current>
      <IntrospectionTimestamp>2024-10-25.21:44:50</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2024-10-25.05:44:50</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="283" parent="1" name="vspo_ins_app">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="284" parent="1" name="readonly"/>
    <table id="285" parent="282" name="adm_ins_app_dynamic_statistics">
      <Comment>动态数据统计表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="286" parent="282" name="adm_ins_data_scope">
      <Comment>Ins管理员数据权限</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="287" parent="282" name="adm_ins_event_bury">
      <Comment>Ins事件埋点表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="288" parent="282" name="adm_menu">
      <Comment>菜单表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="289" parent="282" name="adm_organization">
      <Comment>机构表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="290" parent="282" name="adm_organization_menu">
      <Comment>机构菜单关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="291" parent="282" name="adm_role">
      <Comment>角色管理表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="292" parent="282" name="adm_role_menu">
      <Comment>角色菜单关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="293" parent="282" name="adm_upload_file">
      <Comment>导入导出oss文件表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="294" parent="282" name="adm_user">
      <Comment>用户管理表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="282" name="adm_user_ext">
      <Comment>用户扩展表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="296" parent="282" name="adm_user_role">
      <Comment>用户角色关联表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="297" parent="282" name="ins_face_camera">
      <Comment>摄像头配置表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="298" parent="282" name="ins_face_camera_group_config">
      <Comment>分组配置表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="299" parent="282" name="ins_face_camera_group_config_ref">
      <Comment>分组配置关联摄像头表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="300" parent="282" name="ins_face_camera_group_config_ref_history">
      <Comment>分组配置关联摄像头表的历史表 - 统计时候的留痕</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="301" parent="282" name="ins_face_stat_detail">
      <Comment>人流统计数据明细</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="282" name="ins_face_stat_group_detail">
      <Comment>分组统计明细 - 每隔一小时 统计一次</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="303" parent="282" name="ins_face_stat_group_statistical">
      <Comment>分组统计</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="304" parent="282" name="ins_face_stat_statistical">
      <Comment>人流统计数据统计表</Comment>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="305" parent="285" name="id">
      <AutoIncrement>4119</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="306" parent="285" name="dynamic_id">
      <Comment>动态id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="307" parent="285" name="feed_num">
      <Comment>进FEED流次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="308" parent="285" name="detail_pv">
      <Comment>查看详情数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="309" parent="285" name="detail_uv">
      <Comment>查看详情人数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="310" parent="285" name="share_wx_friend_num">
      <Comment>分享微信好友次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="311" parent="285" name="share_friend_circle_num">
      <Comment>分享朋友圈次数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="312" parent="285" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="313" parent="285" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="314" parent="285" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="315" parent="285" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="316" parent="286" name="id">
      <AutoIncrement>4822</AutoIncrement>
      <Comment>主键id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="317" parent="286" name="user_id">
      <Comment>管理员用户id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="318" parent="286" name="ins_merchant_id">
      <Comment>ins店铺id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="319" parent="286" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="320" parent="286" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="321" parent="286" name="uk_user_id_merchant_id">
      <ColNames>user_id
ins_merchant_id</ColNames>
      <Comment>用户店铺唯一索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="322" parent="286" name="idx_user_id">
      <ColNames>user_id</ColNames>
      <Comment>用户id索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="323" parent="286" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="324" parent="286" name="uk_user_id_merchant_id">
      <UnderlyingIndexName>uk_user_id_merchant_id</UnderlyingIndexName>
    </key>
    <column id="325" parent="287" name="id">
      <AutoIncrement>1943996</AutoIncrement>
      <Comment>事件id</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="326" parent="287" name="create_time">
      <Comment>埋点时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="327" parent="287" name="event_type">
      <Comment>事件类型(sns_levitate，app_levitate，lottery_page,lottery_btn, im_chat)</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="328" parent="287" name="event_source">
      <Comment>事件来源（app，mini）</Comment>
      <DasType>varchar(10)|0s</DasType>
      <DefaultExpression>&apos;app&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="329" parent="287" name="sub_event_type">
      <Comment>子事件类型(im_chat_dynamic_list、im_chat_dynamic_detail、im_chat_main_page、im_chat_meet、im_chat_list)</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="330" parent="287" name="user_id">
      <Comment>用户id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="331" parent="287" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="332" parent="287" name="ext_lottery_activity_id">
      <Comment>抽奖活动id</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="333" parent="287" name="ext_biz_id">
      <Comment>业务id</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="334" parent="287" name="from_user_id">
      <Comment>im 聊天 发起人</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="335" parent="287" name="to_user_id">
      <Comment>im 聊天接受人</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="336" parent="287" name="im_msg_type">
      <Comment>im 消息类型  TIMCustomElem 、 TIMImageElem 、 TIMTextElem、  TIMFaceElem</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="337" parent="287" name="status">
      <Comment>im发送状态  1 : 发送成功 0: 发送失败</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="338" parent="287" name="ext_1">
      <Comment>扩展1</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="339" parent="287" name="ext_2">
      <Comment>扩展2</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="340" parent="287" name="ext_3">
      <Comment>扩展3</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="341" parent="287" name="ext_4">
      <Comment>扩展4</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="342" parent="287" name="ext_5">
      <Comment>扩展5</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>18</Position>
    </column>
    <index id="343" parent="287" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="344" parent="287" name="idx_create_time_event_bury">
      <ColNames>create_time
event_type</ColNames>
      <Comment>时间和事件类型联合索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="345" parent="287" name="idx_ext_biz_id">
      <ColNames>ext_biz_id</ColNames>
      <Comment>业务id索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="346" parent="287" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="347" parent="288" name="id">
      <AutoIncrement>172</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="348" parent="288" name="parent_id">
      <Comment>父级id，默认为0</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="349" parent="288" name="name">
      <Comment>菜单名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="350" parent="288" name="path">
      <Comment>菜单路径</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="351" parent="288" name="component">
      <Comment>路由地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="352" parent="288" name="component_name">
      <Comment>路由名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="353" parent="288" name="icon">
      <Comment>菜单图标</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="354" parent="288" name="visible">
      <Comment>是否显示</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="355" parent="288" name="keep_alive">
      <Comment>是否缓存</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>9</Position>
    </column>
    <column id="356" parent="288" name="always_show">
      <Comment>是否总是显示</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="357" parent="288" name="level">
      <Comment>层级，默认为1</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="358" parent="288" name="tree_path">
      <Comment>层级路径，默认为,0,</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;,0,&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="359" parent="288" name="type">
      <Comment>菜单类型，1：目录，2：菜单，默认为1</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="360" parent="288" name="order_num">
      <Comment>排序顺序，默认为1</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="361" parent="288" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="362" parent="288" name="adm_flag">
      <Comment>是否只属于超管菜单，0：否，1：是，默认为0</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="363" parent="288" name="enable">
      <Comment>启用状态：0：禁用，1：启用，默认为1</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="364" parent="288" name="active_menu">
      <Comment>高亮显示路由路径</Comment>
      <DasType>varchar(128)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="365" parent="288" name="status">
      <Comment>状态：0：无效，1：有效</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="366" parent="288" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="367" parent="288" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="368" parent="288" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>22</Position>
    </column>
    <column id="369" parent="288" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>23</Position>
    </column>
    <index id="370" parent="288" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="371" parent="288" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="372" parent="289" name="id">
      <AutoIncrement>26</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="373" parent="289" name="parent_id">
      <Comment>父级id，为0代表顶级机构，默认为0</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="374" parent="289" name="level">
      <Comment>层级，默认为1</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="375" parent="289" name="tree_path">
      <Comment>层级路径，默认为,0,</Comment>
      <DasType>varchar(64)|0s</DasType>
      <DefaultExpression>&apos;,0,&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="376" parent="289" name="name">
      <Comment>机构名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="377" parent="289" name="status">
      <Comment>状态：0：无效，1：有效</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="378" parent="289" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="379" parent="289" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="380" parent="289" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="381" parent="289" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="382" parent="289" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <index id="383" parent="289" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="384" parent="289" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="385" parent="290" name="id">
      <AutoIncrement>7652</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="386" parent="290" name="parent_organization_id">
      <Comment>父级机构id，默认为0</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="387" parent="290" name="organization_id">
      <Comment>机构id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="388" parent="290" name="menu_id">
      <Comment>菜单id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="389" parent="290" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="390" parent="290" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <index id="391" parent="290" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="392" parent="290" name="uk_organization_id_menu_id">
      <ColNames>organization_id
menu_id</ColNames>
      <Comment>唯一索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="393" parent="290" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="394" parent="290" name="uk_organization_id_menu_id">
      <UnderlyingIndexName>uk_organization_id_menu_id</UnderlyingIndexName>
    </key>
    <column id="395" parent="291" name="id">
      <AutoIncrement>42</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="396" parent="291" name="organization_id">
      <Comment>机构id</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="397" parent="291" name="name">
      <Comment>角色名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="398" parent="291" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="399" parent="291" name="status">
      <Comment>状态：0：无效，1：有效</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="400" parent="291" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="401" parent="291" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="402" parent="291" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="403" parent="291" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="404" parent="291" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="405" parent="291" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="406" parent="292" name="id">
      <AutoIncrement>4779</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="407" parent="292" name="role_id">
      <Comment>角色id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="408" parent="292" name="menu_id">
      <Comment>菜单id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="409" parent="292" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="410" parent="292" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="411" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="412" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="413" parent="293" name="id">
      <AutoIncrement>1467</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="414" parent="293" name="user_account">
      <Comment>用户帐号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="415" parent="293" name="page_path">
      <Comment>页面路径</Comment>
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="416" parent="293" name="task_type">
      <Comment>任务类型：1：导出，2：导入</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="417" parent="293" name="task_status">
      <Comment>任务状态：1：进行中，2：已完成</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="418" parent="293" name="number">
      <Comment>数据量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="419" parent="293" name="explain">
      <Comment>说明</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="420" parent="293" name="oss_file_path">
      <Comment>oss文件路径</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="421" parent="293" name="create_id">
      <Comment>创建人id（操作用户）</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="422" parent="293" name="create_time">
      <Comment>创建时间（任务时间）</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="423" parent="293" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="424" parent="293" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>12</Position>
    </column>
    <column id="425" parent="293" name="enable">
      <Comment>启用状态，0：禁用 1：启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <index id="426" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="427" parent="293" name="idx_task_type">
      <ColNames>task_type</ColNames>
      <Comment>任务类型索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="428" parent="293" name="idx_task_status">
      <ColNames>task_status</ColNames>
      <Comment>任务状态索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="429" parent="293" name="idx_create_id">
      <ColNames>create_id</ColNames>
      <Comment>操作用户索引</Comment>
      <Type>btree</Type>
    </index>
    <index id="430" parent="293" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Comment>任务时间索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="431" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="432" parent="294" name="id">
      <AutoIncrement>114</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="433" parent="294" name="organization_id">
      <Comment>机构id，为0代表本系统</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="434" parent="294" name="account">
      <Comment>帐号</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="435" parent="294" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="436" parent="294" name="enable">
      <Comment>启用状态，0：禁用 1：启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="437" parent="294" name="status">
      <Comment>状态：0：无效，1：有效</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="438" parent="294" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="439" parent="294" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="440" parent="294" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="441" parent="294" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>10</Position>
    </column>
    <index id="442" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="443" parent="294" name="idx_account">
      <ColNames>account</ColNames>
      <Comment>普通索引账号</Comment>
      <Type>btree</Type>
    </index>
    <key id="444" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="445" parent="295" name="id">
      <AutoIncrement>114</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="446" parent="295" name="user_id">
      <Comment>用户id</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="447" parent="295" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="448" parent="295" name="salt">
      <Comment>盐</Comment>
      <DasType>varchar(512)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="449" parent="295" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="450" parent="295" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="451" parent="295" name="update_id">
      <Comment>更新人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="452" parent="295" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="453" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="454" parent="295" name="uk_user_id">
      <ColNames>user_id</ColNames>
      <Comment>用户id</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="455" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="456" parent="295" name="uk_user_id">
      <UnderlyingIndexName>uk_user_id</UnderlyingIndexName>
    </key>
    <column id="457" parent="296" name="id">
      <AutoIncrement>196</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="458" parent="296" name="user_id">
      <Comment>用户id</Comment>
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="459" parent="296" name="role_id">
      <Comment>角色id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="460" parent="296" name="create_id">
      <Comment>创建人id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="461" parent="296" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <index id="462" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="463" parent="296" name="uk_user_id_role_id">
      <ColNames>user_id
role_id</ColNames>
      <Comment>唯一索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="464" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="465" parent="296" name="uk_user_id_role_id">
      <UnderlyingIndexName>uk_user_id_role_id</UnderlyingIndexName>
    </key>
    <column id="466" parent="297" name="id">
      <AutoIncrement>34</AutoIncrement>
      <Comment>主键，自增</Comment>
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="467" parent="297" name="camera_index_code">
      <Comment>相机标识</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="468" parent="297" name="camera_name">
      <Comment>相机名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="469" parent="297" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="470" parent="297" name="status">
      <Comment>状态，0：无效，1：有效</Comment>
      <DasType>tinyint|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="471" parent="297" name="create_id">
      <Comment>创建人userid</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="472" parent="297" name="update_id">
      <Comment>修改人userid</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="473" parent="297" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="474" parent="297" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="475" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="476" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="477" parent="298" name="id">
      <AutoIncrement>27</AutoIncrement>
      <Comment>主键id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="478" parent="298" name="group_code">
      <Comment>分组code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="479" parent="298" name="group_name">
      <Comment>分组name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="480" parent="298" name="status">
      <Comment>1:有效 0:无效</Comment>
      <DasType>tinyint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="481" parent="298" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="482" parent="298" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <index id="483" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="484" parent="298" name="uk_code">
      <ColNames>group_code</ColNames>
      <Comment>分组code 唯一索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="485" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="486" parent="298" name="uk_code">
      <UnderlyingIndexName>uk_code</UnderlyingIndexName>
    </key>
    <column id="487" parent="299" name="id">
      <AutoIncrement>45</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="488" parent="299" name="group_code">
      <Comment>分组code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="489" parent="299" name="group_name">
      <Comment>分组name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="490" parent="299" name="camera_name">
      <Comment>摄像头name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="491" parent="299" name="camera_index_code">
      <Comment>摄像头code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <index id="492" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="493" parent="299" name="uk_group_code_camera_code">
      <ColNames>group_code
camera_index_code</ColNames>
      <Comment>分组code、摄像头code 联合唯一索引</Comment>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="494" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="495" parent="299" name="uk_group_code_camera_code">
      <UnderlyingIndexName>uk_group_code_camera_code</UnderlyingIndexName>
    </key>
    <column id="496" parent="300" name="id">
      <AutoIncrement>6989</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="497" parent="300" name="group_code">
      <Comment>分组code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="498" parent="300" name="group_name">
      <Comment>分组name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="499" parent="300" name="camera_name">
      <Comment>摄像头name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="500" parent="300" name="camera_index_code">
      <Comment>摄像头code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="501" parent="300" name="biz_id">
      <Comment>group_detail表或 group_statistical的主键id</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="502" parent="300" name="scene_code">
      <Comment>group_detail; group_statistical</Comment>
      <DasType>varchar(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="503" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="504" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="505" parent="301" name="id">
      <AutoIncrement>5471</AutoIncrement>
      <Comment>主键，自增</Comment>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="506" parent="301" name="camera_index_code">
      <Comment>相机标识</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="507" parent="301" name="camera_name">
      <Comment>相机name</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="508" parent="301" name="uv">
      <Comment>uv数量,去重后的数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="509" parent="301" name="pv">
      <Comment>pv数量,拍摄的全部照片数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="510" parent="301" name="start_time">
      <Comment>拍摄开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="511" parent="301" name="end_time">
      <Comment>拍摄结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="512" parent="301" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="513" parent="301" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="514" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="515" parent="301" name="idx_start_time_code">
      <ColNames>start_time
camera_index_code</ColNames>
      <Comment>相机开始时间 code 联合普通索引</Comment>
      <Type>btree</Type>
    </index>
    <key id="516" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="517" parent="302" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="518" parent="302" name="group_code">
      <Comment>分组code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="519" parent="302" name="group_name">
      <Comment>分组name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="520" parent="302" name="pv">
      <Comment>pv</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="521" parent="302" name="uv">
      <Comment>uv</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="522" parent="302" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="523" parent="302" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="524" parent="302" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <index id="525" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="526" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="527" parent="303" name="id">
      <AutoIncrement>3892</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="528" parent="303" name="group_code">
      <Comment>分组code</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="529" parent="303" name="group_name">
      <Comment>分组name</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="530" parent="303" name="uv">
      <Comment>uv</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="531" parent="303" name="pv">
      <Comment>pv</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="532" parent="303" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="533" parent="303" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="534" parent="303" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="535" parent="303" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="536" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="537" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="538" parent="304" name="id">
      <AutoIncrement>591</AutoIncrement>
      <Comment>主键，自增</Comment>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="539" parent="304" name="camera_index_code">
      <Comment>相机标识</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="540" parent="304" name="camera_name">
      <Comment>相机name</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="541" parent="304" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="542" parent="304" name="uv">
      <Comment>uv数量,去重后的数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="543" parent="304" name="pv">
      <Comment>pv数量,拍摄的全部照片数量</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="544" parent="304" name="start_time">
      <Comment>统计开始时间 昨天18:00-今天6:00</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="545" parent="304" name="end_time">
      <Comment>统计结束时间 昨天18:00-今天6:00</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="546" parent="304" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <index id="547" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="548" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>