package eventsmodel

import (
	"encoding/json"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"time"
)

type Event struct {
	Type          EventType     //事件的类型，帮助区分不同的业务事件
	AggregateID   string        //聚合根的唯一标识符
	AggregateType AggregateType //聚合根的类型，提供额外的上下文信息
	Version       int
	Ip            string
	Method        string
	Path          string
	Agent         string
	UserID        int
}

// AggregateType 聚合根类型
type AggregateType string

// 客户端-收银台----
const (
	AtOrder AggregateType = "ins.cash.order" //收银订单
	AtTable AggregateType = "ins.cash.table" //收银桌台
)

// 后台----
const (
	AtBackendProduct    AggregateType = "ins.backend.product"     //商品管理
	AtBackendPackage    AggregateType = "ins.backend.package"     //套餐管理
	AtBackendGiftRule   AggregateType = "ins.backend.gift.rule"   //赠送规则
	AtBackendCreditLine AggregateType = "ins.backend.credit.line" //挂账额度
	AtBackendRole       AggregateType = "ins.backend.role"        //角色管理
	AtBackendPermission AggregateType = "ins.backend.permission"  //权限管理

)

// AggregateTypeMap 聚合根类型map
var AggregateTypeMap = map[AggregateType]string{
	AtOrder: "收银订单",
	AtTable: "收银桌台",

	AtBackendProduct:    "商品管理",
	AtBackendPackage:    "套餐管理",
	AtBackendGiftRule:   "赠送规则",
	AtBackendCreditLine: "挂账额度",
	AtBackendRole:       "角色管理",
	AtBackendPermission: "权限管理",
}

// NewAggregateType 实例化一个聚合根类型
func NewAggregateType(aggregateType string) AggregateType {
	return AggregateType(aggregateType)
}

// 转字符串
func (a AggregateType) String() string {
	return string(a)
}

// AggregateName 获取聚合根名称
func (a AggregateType) AggregateName() string {
	if d, ok := AggregateTypeMap[a]; ok {
		return d
	}
	return a.String()
}

// EventType 事件类型
type EventType string

const (
	ETOrderPackageDetailsReplaced EventType = "order.package.details.replace" //订单套餐明细替换事件
)

// 商品
const (
	ETProductCreated EventType = "product.created" //商品创建
	ETProductUpdated EventType = "product.updated" //商品更新
	ETProductDeleted EventType = "product.deleted" //商品删除
)

// 桌台
const (
	ETTableOpened  EventType = "table.opened"  //开台
	ETTableChanged EventType = "table.changed" //换台
	ETTableMerged  EventType = "table.merged"  //并台
	ETTableUpdated EventType = "table.updated" //桌台修改
)

// EventNameMap 事件名称map
var EventNameMap = map[EventType]string{
	ETOrderPackageDetailsReplaced: "订单套餐明细替换",

	ETProductCreated: "商品创建",
	ETProductUpdated: "商品更新",
	ETProductDeleted: "商品删除",

	ETTableOpened:  "开台",
	ETTableChanged: "换台",
	ETTableMerged:  "并台",
	ETTableUpdated: "桌台修改",
}

// EventName 获取事件名称
func (e EventType) EventName() string {
	return EventNameMap[e]
}

type OrderPackageDetailsReplaced struct {
	OrderID               string    `json:"orderID,omitempty"` // 订单ID
	OrderPackageDetailsId string    `json:"orderPackageDetailsId,omitempty"`
	PackageID             string    `json:"packageID,omitempty"`      // 套餐ID
	OldProductID          uint      `json:"oldProductID,omitempty"`   // 被替换商品明细的ID
	NewProductID          uint      `json:"newProductID,omitempty"`   // 新商品明细的ID
	NewItemDetails        string    `json:"newItemDetails,omitempty"` // 新商品明细的详细信息，例如JSON字符串
	Reason                string    `json:"reason,omitempty"`         // 替换的原因
	ReplacedAt            time.Time `json:"replacedAt"`               // 替换发生的时间
	ReplacedId            uint      `json:"replacedId,omitempty"`     //执行替换操作的用户或系统标识id
	ReplacedBy            string    `json:"replacedBy,omitempty"`     // 执行替换操作的用户或系统标识
}

func (S *OrderPackageDetailsReplaced) ToDataTypesJSON() datatypes.JSON {
	logger := global.GVA_LOG.With(zap.String("func", "InsEventsService.CreateRecord"), zap.Any("Events", S))
	marshal, err := json.Marshal(S)
	if err != nil {
		logger.Error("ToDataTypesJSON", zap.Error(err))
		return nil // 或返回一个错误信息，取决于你的错误处理策略
	}

	var dtJSON datatypes.JSON = marshal
	return dtJSON
}
